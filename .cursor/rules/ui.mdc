---
description: General Shadcn and TailwindCSS usage rules
globs: "**/*.{ts,tsx,js,jsx}"
alwaysApply: true
---
# UI Components

- Use Shadcn UI, Radix UI and Tailwind for components and styling.
- Make use of the components, hooks and utils available in `@lilypad/ui/components`, `@lilypad/ui/hooks` and `@lilypad/ui/lib`.

## Styling
- Styling is done using Tailwind CSS. We use the `cn` function from the `@lilypad/ui/lib/utils` package to generate class names.
- Avoid fixed classes such as "bg-gray-500". Instead, use Shadcn classes such as "bg-background", "text-secondary-foreground", "text-muted-foreground", etc.
- Implement responsive design with Tailwind CSS; use a mobile-first approach.
