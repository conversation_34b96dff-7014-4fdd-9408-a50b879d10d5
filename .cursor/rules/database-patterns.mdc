---
description: Drizzle configuration and usage rules
globs: "**/*.{ts,tsx,js,jsx}"
alwaysApply: true
---
# Database Patterns - Supabase & Drizzle Rules

## Database Architecture Overview

This project uses:
- **Supabase**: PostgreSQL hosting, Auth, and Realtime
- **Drizzle ORM**: Type-safe database queries and schema management
- **Package Structure**: Database logic centralized in `@lilypad/db`

## Schema Organization Rules

### Schema Location Strategy
```
packages/db/src/schema/
├── auth.ts                    # Supabase Auth-related tables
├── enums.ts                   # Enums
├── index.ts                   # Re-export tables.ts, psychological-testing.ts, enums.ts, and types.ts, 
├── psychological-testing.ts   # Table definitions related to psychological testing
├── tables.ts                  # Core table definitions
└── types.ts                   # Types
```

### Policy Location Strategy
```
packages/db/src/policies/
├── index.ts                   # Re-export all files in /policies folder, 
├── policies.ts                # Policy definitions
├── policy-builders.ts         # Re-usable policy builder functions
└── policy-helpers.ts          # Re-usable policy helper functions
```

### Repository Class Location Strategy
```
packages/db/src/repository/
├── index.ts                   # Re-export all repository class files
├── types/
├──               # Policy definitions
├── policy-builders.ts         # Re-usable policy builder functions
└── policy-helpers.ts          # Re-usable policy helper functions
```

### Schema Definition Patterns

#### Table Definition Template
```typescript
// packages/db/src/schema/tables.ts
import { pgTable, uuid, text, timestamp, boolean } from 'drizzle-orm/pg-core'
import { createInsertSchema, createSelectSchema } from 'drizzle-zod'
import { z } from 'zod'
import { authUsersTable } from "./auth";
import * as Enums from "./enums";
import * as Policies from "./policies";

export const usersTable = pgTable(
	"users",
	{
		id: uuid("id").primaryKey(),
		firstName: varchar("first_name", { length: 255 }).notNull(),
		middleName: varchar("middle_name", { length: 255 }),
		lastName: varchar("last_name", { length: 255 }).notNull(),
		email: varchar("email", { length: 255 }).notNull(),
		fullName: varchar("full_name", { length: 255 })
			.notNull()
			.generatedAlwaysAs((): SQL => sql`concat_names(${usersTable.firstName}, ${usersTable.middleName}, ${usersTable.lastName})`),
		avatar: text("avatar"),
		isOnboarded: boolean("is_onboarded").notNull().default(true),
		createdAt: timestamp("created_at", { withTimezone: true })
			.notNull()
			.defaultNow(),
		updatedAt: timestamp("updated_at", { withTimezone: true })
			.notNull()
			.defaultNow()
			.$onUpdate(() => new Date()),
	},

	(table) => [
		foreignKey({
			columns: [table.id],
			foreignColumns: [authUsersTable.id],
		}).onDelete("cascade"),
		index("user_name_idx").on(table.fullName),
		Policies.usersSelectPolicy,
		Policies.usersUpdatePolicy,
	],
);

// Generate Zod schemas from Drizzle schema
export const insertUserSchema = createInsertSchema(users, {
  email: z.string().email(),
  name: z.string().min(1).max(100),
})

export const selectUserSchema = createSelectSchema(users)

// Derived schemas for specific use cases
export const updateUserSchema = insertUserSchema.partial().omit({ 
  id: true, 
  createdAt: true 
})

// Type exports
export type User = typeof users.$inferSelect
export type NewUser = typeof users.$inferInsert
export type UpdateUser = z.infer<typeof updateUserSchema>
```

#### Relationship Definitions

```typescript
// packages/db/src/schema/tables.ts
import { relations } from 'drizzle-orm'

export const districtsTable = pgTable(
	"districts",
	{
		id: uuid("id").primaryKey().default(sql`uuid_generate_v7()`),
		name: varchar("name", { length: 255 }).notNull(),
		slug: varchar("slug", { length: 255 }).notNull(),
		type: Enums.districtTypeEnum("type")
			.notNull()
			.default(Enums.DistrictTypeEnum.UNIFIED_DISTRICT),
		website: varchar("website", { length: 255 }).notNull(),
		ncesId: varchar("nces_id", { length: 255 }).notNull(),
		stateId: varchar("state_id", { length: 255 }).notNull(),
		county: varchar("county", { length: 255 }).notNull(),
		addressId: uuid("address_id")
			.notNull()
			.references(() => addressesTable.id),
	},
	(table) => [Policies.districtsSelectPolicy, Policies.districtsUpdatePolicy],
);

export const userDistrictsTable = pgTable(
	"user_districts",
	{
		id: uuid("id").primaryKey().default(sql`uuid_generate_v7()`),
		districtId: uuid("district_id")
			.notNull()
			.references(() => districtsTable.id),
		userId: uuid("user_id")
			.notNull()
			.references(() => usersTable.id),
		createdAt: timestamp("created_at", { withTimezone: true })
			.notNull()
			.defaultNow(),
		updatedAt: timestamp("updated_at", { withTimezone: true })
			.notNull()
			.defaultNow()
			.$onUpdate(() => new Date()),
	},
	(table) => [
		Policies.userDistrictsSelectPolicy,
		Policies.userDistrictsAllPolicy,
	],
);

// Define relationships
export const userDistrictsRelations = relations(
	userDistrictsTable,
	({ one }) => ({
		user: one(usersTable, {
			fields: [userDistrictsTable.userId],
			references: [usersTable.id],
			relationName: "user_district_user",
		}),
		district: one(districtsTable, {
			fields: [userDistrictsTable.districtId],
			references: [districtsTable.id],
		}),
	}),
);

export const usersRelations = relations(usersTable, ({ many }) => ({
	userDistricts: many(userDistrictsTable),
	userRoles: many(userRolesTable),
	availabilities: many(availabilitiesTable),
	caseAssignments: many(caseAssignmentsTable),
	assessments: many(assessmentsTable),
	documents: many(documentsTable),
	events: many(eventsTable),
	eventAttendees: many(eventAttendeesTable),
	invitations: many(invitationsTable),
	tasks: many(tasksTable),
	taskHistory: many(taskHistoryTable),
	userSchools: many(userSchoolsTable),
}));
```

### Database Connection Configuration

#### Connection Setup
```typescript
// packages/db/src/config/client.ts
import { drizzle } from 'drizzle-orm/postgres-js'
import postgres from 'postgres'
import * as schema from '../schema'

// Connection configuration
const connectionString = process.env.DATABASE_URL!

// Create connection pool
const client = postgres(connectionString, {
  max: 10,
  idle_timeout: 20,
  connect_timeout: 10,
})

// Export configured database instance
export const db = drizzle(client, { 
  schema,
  logger: process.env.ENVIRONMENT === 'development',
})

// Export connection for cleanup
export { client }
```

#### Migration Configuration
```typescript
// packages/db/drizzle.config.ts
import type { Config } from 'drizzle-kit'

export default {
  schema: './src/schema/*',
  out: './migrations',
  driver: 'pg',
  dbCredentials: {
    connectionString: process.env.DATABASE_URL!,
  },
} satisfies Config
```

## Database Repository Patterns

### CRUD Operation Templates

#### Basic CRUD Functions
```typescript
// packages/db/src/repository/users.ts
import { db } from '../config/client'
import { users, type User, type NewUser, type UpdateUser } from '../schema/tables'
import { eq, and, desc, count } from 'drizzle-orm'

// Create
export async function createUser(data: NewUser): Promise<User> {
  const [user] =  await db.transaction(async (tx) => {
    await tx.insert(users).values({
      ...data,
      createdAt: new Date(),
      updatedAt: new Date(),
    }).returning().
  });
  
  if (!user) throw new Error('Failed to create user')
  return user
}

// Read (single)
export async function getUserById(id: string): Promise<User | null> {

  const [user] =  await db.transaction(async (tx) => {
    await tx.select()
    .from(users)
    .where(eq(users.id, id))
    .limit(1)
  });
  
  return user || null
}

// Read (multiple with pagination)
export async function getUsers(params: {
  page?: number
  limit?: number
  search?: string
}) {
  const { page = 1, limit = 10, search } = params
  const offset = (page - 1) * limit

  const conditions = [eq(users.isActive, true)]
  if (search) {
    conditions.push(ilike(users.name, `%${search}%`))
  }

  const [usersList, [{ total }]] = await Promise.all([
    db
      .select()
      .from(users)
      .where(and(...conditions))
      .orderBy(desc(users.createdAt))
      .limit(limit)
      .offset(offset),
    
    db
      .select({ total: count() })
      .from(users)
      .where(and(...conditions))
  ])

  return {
    users: usersList,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit),
    },
  }
}

// Update
export async function updateUser(
  id: string, 
  data: UpdateUser
): Promise<User> {
  const [user] = await db
    .update(users)
    .set({
      ...data,
      updatedAt: new Date(),
    })
    .where(eq(users.id, id))
    .returning()
  
  if (!user) throw new Error('User not found')
  return user
}

// Delete (soft delete)
export async function deleteUser(id: string): Promise<void> {
  await db
    .update(users)
    .set({ 
      isDeleted: true,
      updatedAt: new Date(),
    })
    .where(eq(users.id, id))
}
```

#### Complex Queries with Transactions
```typescript
// packages/db/src/repository/organizations.ts
import { db } from '../config/connection'
import { organizations, organizationMembers, users } from '../schema'
import { eq, and } from 'drizzle-orm'

export async function createOrganizationWithOwner(data: {
  name: string
  slug: string
  ownerId: string
}) {
  return await db.transaction(async (tx) => {
    // Create organization
    const [organization] = await tx
      .insert(organizations)
      .values({
        name: data.name,
        slug: data.slug,
        ownerId: data.ownerId,
      })
      .returning()

    if (!organization) {
      throw new Error('Failed to create organization')
    }

    // Add owner as member
    await tx.insert(organizationMembers).values({
      organizationId: organization.id,
      userId: data.ownerId,
      role: 'owner',
    })

    // Return organization with owner details
    const [result] = await tx
      .select({
        organization,
        owner: users,
      })
      .from(organizations)
      .innerJoin(users, eq(organizations.ownerId, users.id))
      .where(eq(organizations.id, organization.id))

    return result
  })
}
```

### Query Optimization Patterns

#### Efficient Joins
```typescript
// packages/db/src/repository/contacts.ts
export async function getContactsWithOrganization(organizationId: string) {
  return await db
    .select({
      id: contacts.id,
      name: contacts.name,
      email: contacts.email,
      status: contacts.status,
      organization: {
        id: organizations.id,
        name: organizations.name,
        slug: organizations.slug,
      },
    })
    .from(contacts)
    .innerJoin(organizations, eq(contacts.organizationId, organizations.id))
    .where(eq(contacts.organizationId, organizationId))
    .orderBy(desc(contacts.createdAt))
}
```

#### Prepared Statements for Frequent Queries
```typescript
// packages/db/src/repository/prepared-queries.ts
import { placeholder } from 'drizzle-orm'

export const getUserByIdPrepared = db
  .select()
  .from(users)
  .where(eq(users.id, placeholder('id')))
  .prepare()

export const getActiveUsersPrepared = db
  .select()
  .from(users)
  .where(eq(users.isActive, true))
  .limit(placeholder('limit'))
  .offset(placeholder('offset'))
  .prepare()
```

## Supabase Integration Patterns

### Auth Integration
```typescript
// packages/supabase/src/auth/server.ts
import { createServerClient } from '@supabase/ssr'
import { db } from '@lilypad/db'
import { users } from '@lilypad/db/schema'
import { eq } from 'drizzle-orm'

export async function getCurrentUser() {
  const supabase = createServerClient(/* config */)
  
  const { data: { user: authUser }, error } = await supabase.auth.getUser()
  if (error || !authUser) return null

  // Get full user data from our database
  const [user] = await db
    .select()
    .from(users)
    .where(eq(users.id, authUser.id))
    .limit(1)

  return user || null
}
```

### Realtime Integration
```typescript
// packages/supabase/src/hooks/use-realtime-subscription.ts
import { useEffect } from 'react'
import { getSupabaseBrowserClient } from '@lilypad/supabase/client'

export function useRealtimeSubscription<T>(
  table: string,
  filter: string,
  onUpdate: (payload: T) => void
) {
  useEffect(() => {
    const supabase = createClient()
    
    const subscription = supabase
      .channel(`realtime:${table}`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table,
        filter,
      }, onUpdate)
      .subscribe()

    return () => {
      subscription.unsubscribe()
    }
  }, [table, filter, onUpdate])
}
```


```typescript
// packages/db/src/repository/error-handler.ts
import { DatabaseError } from 'pg'

export class DatabaseOperationError extends Error {
  constructor(
    message: string,
    public code: string,
    public operation: string
  ) {
    super(message)
    this.name = 'DatabaseOperationError'
  }
}

export function handleDatabaseError(error: unknown, operation: string): never {
  if (error instanceof DatabaseError) {
    switch (error.code) {
      case '23505': // Unique constraint violation
        throw new DatabaseOperationError(
          'Resource already exists',
          'DUPLICATE_RESOURCE',
          operation
        )
      case '23503': // Foreign key constraint violation
        throw new DatabaseOperationError(
          'Referenced resource not found',
          'INVALID_REFERENCE',
          operation
        )
      default:
        throw new DatabaseOperationError(
          'Database operation failed',
          'DATABASE_ERROR',
          operation
        )
    }
  }
  
  throw error
}

// Usage in database functions
export async function createUserSafe(data: NewUser): Promise<User> {
  try {
    return await createUser(data)
  } catch (error) {
    handleDatabaseError(error, 'create_user')
  }
}
```

## Performance Optimization Rules

### Query Optimization
1. **Use indexes** for frequently queried columns
2. **Limit results** with pagination
3. **Select specific columns** instead of `SELECT *`
4. **Use prepared statements** for repeated queries
5. **Batch operations** when possible
6. **Add smart indexes** for all tables where it makes sense and improves database performance

### Connection Management
1. **Use connection pooling** (configured in postgres client)
2. **Close connections** in serverless environments
3. **Monitor connection counts** in production

### Caching Strategy
1. **Cache expensive queries** at the entity layer
2. **Use Supabase cache** for auth-related queries
3. **Implement cache invalidation** on mutations
4. **Consider Redis** for frequently accessed data

## Migration and Schema Evolution

### Migration Best Practices
1. **Always use transactions** for multi-step migrations
2. **Test migrations** on production-like data
3. **Create rollback migrations** for reversible changes
4. **Document breaking changes** in migration comments
