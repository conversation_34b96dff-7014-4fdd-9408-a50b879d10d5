---
description: General rules for building forms with React Hook Form, Zod, and Shadcn UI
globs: apps/**/*.tsx,packages/**/*.tsx
alwaysApply: false
---
# Form Handling - React Hook Form & Zod Rules

## Base Rules

- Use React Hook Form for form validation and submission.
- Use Zod for form validation.
- Use the `zodResolver` function to resolve the Zod schema to the form.
- Use useMutation from TanStack Query along with tRPC endpoints for server-side code handling
- Use Sonner for writing toasters for UI feedback
- Always use `@lilypad/ui` for writing the UI of the form.

## Form Architecture Overview

This project uses:
- **React Hook Form**: Form state management and validation
- **Zod**: Schema validation and type inference
- **@hookform/resolvers/zod**: RHF + Zod integration
- **Shadcn/UI Form**: UI components for forms

## Form Schema Organization

### Schema Location Strategy
```
Form schemas should be placed based on their scope:

entities/[entity]/model/
├── schema.ts         # Entity-specific schemas (reusable)

features/[feature]/model/
├── schemas.ts        # Feature-specific form schemas

shared/types/
├── form-schemas.ts   # Common form validation schemas
```

### Schema Definition Patterns

#### Entity Schema (Reusable)
```typescript
// entities/user/model/schema.ts
import { z } from 'zod'
import { insertUserSchema } from '@lilypad/db/schema/tables'

// Base entity schema from database
export { insertUserSchema, updateUserSchema } from '@lilypad/db/schema/tables'

// Form-specific refinements
export const userProfileFormSchema = insertUserSchema
  .pick({
    name: true,
    email: true,
  })
  .extend({
    name: z.string().min(2, 'Name must be at least 2 characters'),
    email: z.string().email('Invalid email address'),
  })

export const userPreferencesFormSchema = z.object({
  theme: z.enum(['light', 'dark', 'system']).default('system'),
  emailNotifications: z.boolean().default(true),
  marketingEmails: z.boolean().default(false),
})

// Type exports
export type UserProfileForm = z.infer<typeof userProfileFormSchema>
export type UserPreferencesForm = z.infer<typeof userPreferencesFormSchema>
```

#### Feature-Specific Schema
```typescript
// features/auth/model/schemas.ts
import { z } from 'zod'

export const signInFormSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  rememberMe: z.boolean().default(false),
})

export const signUpFormSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Invalid email address'),
  password: z
    .string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/[0-9]/, 'Password must contain at least one number'),
  confirmPassword: z.string(),
  acceptTerms: z.boolean().refine(val => val === true, {
    message: 'You must accept the terms and conditions',
  }),
}).refine(data => data.password === data.confirmPassword, {
  message: 'Passwords do not match',
  path: ['confirmPassword'],
})

export const forgotPasswordFormSchema = z.object({
  email: z.string().email('Invalid email address'),
})

// Type exports
export type SignInFormType = z.infer<typeof signInFormSchema>
export type SignUpFormType = z.infer<typeof signUpFormSchema>
export type ForgotPasswordFormType = z.infer<typeof forgotPasswordFormSchema>
```
