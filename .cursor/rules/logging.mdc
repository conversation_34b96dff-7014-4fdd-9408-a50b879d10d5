---
description: Server side functions logging
globs: 
alwaysApply: false
---
## Logging

Consider logging asynchronous requests using the `@lilypad/shared/logger` @logger.ts package in a structured way to provide context to the logs in both server actions and route handlers.

Using the logger:

```tsx
import { logger } from '@lilypad/shared/logger';

async function getNotes() {
    const ctx = {
        name: 'notes', // use a meaningful name
        userId: user.id, // use the authenticated user's ID
    };

    logger.info(ctx, 'Request started...');

    const { data, error } = await supabase.from('notes').select('*');

    if (error) {
        logger.error({ ...ctx, error }, 'Request failed...');
    // handle error
    } else {
        logger.info(ctx, 'Request succeeded...');
    // use data
    }
}
```
