---
description: General JSX rules
globs: "**/*.{ts,tsx,js,jsx}"
alwaysApply: true
---
# JSX Best Practices
This guide outlines our conventions for writing clean, maintainable JSX in React applications.

## Common Patterns

### Conditional Rendering with `If`

Prefer the `If` component to complex ternary operators in JSX:

```tsx
import { If } from '@lilypad/ui/components';

// Basic usage
<If condition={isLoading}>
  <Spinner />
</If>

// With fallback
<If condition={isLoading} fallback={<Content />}>
  <Spinner />
</If>

// With callback function for condition match
<If condition={user}>
  {(userData) => <UserProfile data={userData} />}
</If>
```

Benefits:
- Improves readability compared to ternary operators
- Type-safe with TypeScript
- Reduces nesting and complexity in JSX

### List Rendering

Consistently use these patterns for list rendering:

```tsx
// Empty state handling, avoid ternaries
{items.length > 0 ? (
  <ul className="list">
    {items.map((item) => (
      <li key={item.id}>{item.name}</li>
    ))}
  </ul>
) : (
  <EmptyState message="No items found" />
)}

// Even better with If component
<If condition={items.length > 0} fallback={
    <EmptyState message="No items found" />
}>
  <ul className="list">
    {items.map((item) => (
      <li key={item.id}>{item.name}</li>
    ))}
  </ul>
</If>
```

## Error and Loading States

Use consistent patterns for handling loading and error states:

```tsx
// Loading state
<If condition={isLoading}>
  <div className="flex justify-center p-8">
    <Spinner />
  </div>
</If>

// Error state that infer the type of the condition. The type of the variable "err" is now inferred
// Always use this pattern when the value of the condition is used within the body
<If condition={error}>
  {(err) => (
    <Alert variant="destructive">
      <AlertCircle className="size-4" />
      <AlertTitle>
        Alert Title
      </AlertTitle>

      <AlertDescription>
        {err.message}
      </AlertDescription>
    </Alert>
  )}
</If>

// Empty state
<If condition={items.length === 0}>
  <div className="flex flex-col items-center justify-center p-8 text-center">
    <EmptyIcon className="size-12 text-muted-foreground" />

    <h3 className="mt-4 text-lg font-medium">
      No data found
    </h3>

    <p className="text-sm text-muted-foreground">
      No data was found.
    </p>
  </div>
</If>
```

# Environment & Configuration Rules

## Environment Variables
- Always use `@t3-oss/env-nextjs` for environment validation
- Extend package-specific key schemas using the `extends` pattern
- Never access `process.env` directly in application code
- Use the centralized `env` object from `@/env`

## Package Key Management
- Each package should export a `keys.ts` file with environment schema
- Use Zod for all environment variable validation
- Group related environment variables by package domain









