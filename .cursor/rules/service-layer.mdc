---
description: Service layer rules
globs: "**/*.{ts,tsx,js,jsx}"
alwaysApply: false
---

# Service Layer Rules (@lilypad/core)

## Service Class Structure
- Services must accept TransactionType in constructor
- Use dependency injection for repository classes
- Implement proper error handling with domain-specific error messages
- Use batch processing for bulk operations (BATCH_SIZE = 50)

## Transaction Management
- Always wrap service operations in database transactions
- Use repository pattern for data access within services
- Handle async operations outside transactions when possible
- Implement proper rollback strategies for failed operations

## Error Handling
- Parse database errors into user-friendly messages
- Use specific error codes for different failure scenarios
- Log errors with proper context for debugging
- Return structured error responses with success/failure indicators