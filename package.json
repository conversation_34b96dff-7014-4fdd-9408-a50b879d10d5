{"name": "lilypad", "private": true, "scripts": {"dev": "turbo run dev", "dev:web": "turbo dev --filter=web", "dev:site": "turbo dev --filter=site", "dev:docs": "turbo dev --filter=docs", "build": "turbo run build", "clean": "git clean -xdf .cache .turbo dist node_modules", "clean:workspaces": "turbo clean", "typecheck": "turbo typecheck", "format": "biome format --write .", "lint": "turbo lint && manypkg check", "update": "pnpm update -recursive", "analyze": "turbo analyze", "test": "turbo test", "seed": "pnpm --filter @lilypad/scripts seed", "preinstall": "pnpm run --filter @tooling/requirements requirements", "supabase:start": "supabase start --ignore-health-check", "supabase:stop": "supabase stop && pnpm supabase:delete", "supabase:reset": "supabase db reset --local", "supabase:delete": "docker volume rm supabase_db_lilypad supabase_config_lilypad supabase_storage_lilypad", "supabase:typegen": "supabase gen types --lang=typescript --local > packages/supabase/src/types/db.ts", "db:gen": "pnpm --filter=@lilypad/db generate", "db:push": "pnpm --filter=@lilypad/db push", "db:studio": "pnpm --filter=@lilypad/db studio", "db:clean": "rm -rf supabase/migrations/meta && find supabase/migrations -type f -name '*_migration.sql' -delete", "db:regen": "pnpm db:clean && pnpm db:gen", "email:preview": "pnpm --filter=@lilypad/email preview", "email:export": "pnpm --filter=@lilypad/email export", "assemble": "pnpm supabase:start && pnpm seed && pnpm dev:web", "renew": "pnpm db:clean && pnpm db:gen && pnpm supabase:reset && pnpm supabase:typegen && pnpm seed && pnpm dev:web", "teardown": "pnpm supabase:stop"}, "devDependencies": {"@biomejs/biome": "2.0.6", "@manypkg/cli": "^0.24.0", "@turbo/gen": "^2.5.0", "supabase": "^2.24.3", "turbo": "^2.5.3", "typescript": "5.8.2"}, "packageManager": "pnpm@10.12.4", "engines": {"node": ">=20"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "nanoid": "^5.1.5"}}