// CSV Parser Web Worker
importScripts('https://unpkg.com/papaparse@5.4.1/papaparse.min.js');

// Message handler
self.addEventListener('message', async (event) => {
  const { type, data } = event.data;

  switch (type) {
    case 'parse':
      await parseCSV(data);
      break;
    default:
      self.postMessage({
        type: 'error',
        error: 'Unknown message type',
      });
  }
});

async function parseCSV({ file, options }) {
  try {
    let totalRows = 0;
    let processedRows = 0;
    const results = [];
    const errors = [];

    await new Promise((resolve, reject) => {
      Papa.parse(file, {
        header: true,
        skipEmptyLines: true,
        transformHeader: (header) => header.trim(),
        chunk: (chunk, parser) => {
          totalRows += chunk.data.length;
          results.push(...chunk.data);

          if (chunk.errors.length > 0) {
            errors.push(...chunk.errors);
          }

          processedRows += chunk.data.length;

          // Send progress update
          if (options.sendProgress !== false) {
            const progress =
              file.size > 0
                ? Math.min(99, (chunk.meta.cursor / file.size) * 100)
                : (processedRows / totalRows) * 100;

            self.postMessage({
              type: 'progress',
              progress: Math.round(progress),
            });
          }
        },
        complete: (finalResults) => {
          if (finalResults.errors.length > 0) {
            errors.push(...finalResults.errors);
          }
          resolve();
        },
        error: (error) => {
          reject(error);
        },
      });
    });

    // Send final results
    self.postMessage({
      type: 'complete',
      data: {
        data: results,
        errors,
        meta: {
          totalRows,
          fields: results[0] ? Object.keys(results[0]) : [],
        },
      },
    });
  } catch (error) {
    self.postMessage({
      type: 'error',
      error: error.message || 'Failed to parse CSV',
    });
  }
}
