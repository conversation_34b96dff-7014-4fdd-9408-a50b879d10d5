'use client';

import { useZodForm } from '@lilypad/shared/hooks/use-zod-form';
import { toast } from '@lilypad/ui/components/sonner';
import { useCallback, useState } from 'react';
import type { SubmitHandler } from 'react-hook-form';
import { createJoinRequestAction } from '../api/create-join-request.action';
import { type JoinRequestSchema, joinRequestSchema } from '../model/schema';

export function useJoinRequest() {
  const [success, setSuccess] = useState<boolean>(false);

  const methods = useZodForm({
    schema: joinRequestSchema,
    mode: 'onSubmit',
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      districtName: '',
      message: '',
    },
  });

  const canSubmit = !methods.formState.isSubmitting;

  const onSubmit: SubmitHandler<JoinRequestSchema> = useCallback(
    async (values) => {
      if (!canSubmit) {
        return;
      }

      const result = await createJoinRequestAction(values);

      if (result?.serverError) {
        toast.error('An error occured during join request', {
          description: 'Please try again.',
        });
      } else {
        setSuccess(true);
      }
    },
    [canSubmit]
  );

  return {
    methods,
    onSubmit,
    canSubmit,
    success,
  };
}
