import { z } from 'zod';

export const joinRequestSchema = z.object({
  firstName: z
    .string({
      required_error: 'First name is required.',
      invalid_type_error: 'First name must be a string.',
    })
    .trim()
    .min(1, 'First name is required.')
    .max(64, 'Maximum 64 characters allowed.'),
  lastName: z
    .string({
      required_error: 'Last name is required.',
      invalid_type_error: 'Last name must be a string.',
    })
    .trim()
    .min(1, 'Last name is required.')
    .max(64, 'Maximum 64 characters allowed.'),
  email: z
    .string({
      required_error: 'Email is required.',
      invalid_type_error: 'Email must be a string.',
    })
    .trim()
    .min(1, 'Email is required.')
    .max(255, 'Maximum 255 characters allowed.')
    .email('Enter a valid email address.'),
  phone: z
    .string({
      required_error: 'Phone is required.',
      invalid_type_error: 'Phone must be a string.',
    })
    .trim()
    .min(1, 'Phone is required.')
    .max(255, 'Maximum 255 characters allowed.'),
  districtName: z
    .string({
      required_error: 'District name is required.',
      invalid_type_error: 'District name must be a string.',
    })
    .trim()
    .min(1, 'District name is required.')
    .max(255, 'Maximum 255 characters allowed.'),
  message: z
    .string({
      required_error: 'Message is required.',
      invalid_type_error: 'Message must be a string.',
    })
    .trim()
    .min(1, 'Message is required.')
    .max(1000, 'Maximum 1000 characters allowed.'),
});

export type JoinRequestSchema = z.infer<typeof joinRequestSchema>;
