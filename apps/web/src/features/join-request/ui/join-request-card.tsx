'use client';
import { useJoinRequest } from '@/features/join-request/model/use-join-request';
import { routes } from '@lilypad/shared/routes';
import { Button } from '@lilypad/ui/components/button';
import {
  type CardProps,
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@lilypad/ui/components/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@lilypad/ui/components/form';
import { Input } from '@lilypad/ui/components/input';
import { InputWithAdornments } from '@lilypad/ui/components/input-with-adornments';
import { Logo } from '@lilypad/ui/components/logo';
import { OptionalBadge } from '@lilypad/ui/components/optional-badge';
import { Textarea } from '@lilypad/ui/components/textarea';
import { cn } from '@lilypad/ui/lib/utils';
import { LandmarkIcon, MailIcon, PhoneIcon } from 'lucide-react';
import Link from 'next/link';
import type * as React from 'react';

export function JoinRequestCard({
  className,
  ...other
}: CardProps): React.JSX.Element {
  const { methods, onSubmit, canSubmit, success } = useJoinRequest();

  if (success) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Join Request Sent</CardTitle>
          <CardDescription>
            Thank you for your interest in joining Lilypad!
          </CardDescription>
        </CardHeader>
        <CardContent className="flex flex-col gap-4 text-sm">
          <p>
            We are so excited to have you join us! We will review your request
            and get back to you within 24 hours.
          </p>
          <p className="flex flex-col gap-0.5 text-muted-foreground">
            <span>If you see a delay in response, please email us at</span>
            <Link
              className="text-primary underline"
              href="mailto:<EMAIL>"
            >
              <EMAIL>
            </Link>
          </p>
        </CardContent>
        <CardFooter>
          <Button asChild className="w-full">
            <Link href={routes.site.Index}>Back to site</Link>
          </Button>
        </CardFooter>
      </Card>
    );
  }

  return (
    <div className="rounded-xl border border-border bg-muted/80 shadow-sm dark:bg-muted/20">
      <Card
        className={cn(
          'border-border border-x-0 border-t-0 border-b-xl shadow-none',
          className
        )}
        {...other}
      >
        <CardHeader>
          <Link href={routes.site.Index}>
            <Logo className="pb-2" />
          </Link>
          <CardTitle className="text-base lg:text-lg">
            Join the Lilypad community
          </CardTitle>
          <CardDescription>
            Please fill in the details to send a join request.
          </CardDescription>
        </CardHeader>
        <CardContent className="flex flex-col gap-4">
          <Form {...methods}>
            <form
              className="flex flex-col gap-4"
              onSubmit={methods.handleSubmit(onSubmit)}
            >
              <div className="flex flex-col gap-4 sm:flex-row">
                <FormField
                  control={methods.control}
                  name="firstName"
                  render={({ field }) => (
                    <FormItem className="flex w-full flex-col">
                      <FormLabel>First Name</FormLabel>
                      <FormControl>
                        <Input
                          autoComplete="given-name"
                          disabled={!canSubmit}
                          maxLength={64}
                          placeholder="Lily"
                          type="text"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={methods.control}
                  name="lastName"
                  render={({ field }) => (
                    <FormItem className="flex w-full flex-col">
                      <FormLabel>Last Name</FormLabel>
                      <FormControl>
                        <Input
                          autoComplete="family-name"
                          disabled={!canSubmit}
                          maxLength={64}
                          placeholder="Padawan"
                          type="text"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <FormField
                control={methods.control}
                name="email"
                render={({ field }) => (
                  <FormItem className="flex w-full flex-col">
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <InputWithAdornments
                        autoComplete="username"
                        disabled={!canSubmit}
                        maxLength={255}
                        placeholder="<EMAIL>"
                        startAdornment={
                          <MailIcon className="size-4 shrink-0" />
                        }
                        type="email"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="flex flex-col gap-4">
                <FormField
                  control={methods.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Phone</FormLabel>
                      <FormControl>
                        <InputWithAdornments
                          autoCapitalize="off"
                          autoComplete="phone"
                          disabled={!canSubmit}
                          maxLength={72}
                          placeholder="************"
                          startAdornment={
                            <PhoneIcon className="size-4 shrink-0" />
                          }
                          type="tel"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={methods.control}
                  name="districtName"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>School or District Name</FormLabel>
                      <FormControl>
                        <InputWithAdornments
                          autoComplete="district-name"
                          disabled={!canSubmit}
                          maxLength={255}
                          placeholder="School or District Name"
                          startAdornment={
                            <LandmarkIcon className="size-4 shrink-0" />
                          }
                          type="text"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={methods.control}
                  name="message"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>
                        Message
                        <OptionalBadge />
                      </FormLabel>
                      <FormControl>
                        <Textarea
                          autoComplete="message"
                          disabled={!canSubmit}
                          maxLength={1000}
                          placeholder="Message"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <Button
                className="w-full"
                disabled={!canSubmit}
                loading={methods.formState.isSubmitting}
                type="submit"
              >
                Send Request
              </Button>
            </form>
          </Form>
        </CardContent>
        <CardFooter className="flex justify-center gap-1 text-muted-foreground text-xs">
          <span>Already have an account?</span>
          <Link
            className="text-foreground hover:underline"
            href={routes.app.auth.SignIn}
          >
            Sign In
          </Link>
        </CardFooter>
      </Card>
      <div className="py-4 text-center text-muted-foreground text-xs">
        By sending a join request, you agree to our <br />
        <Link
          className="underline"
          href={routes.site.TermsOfUse}
          prefetch={false}
        >
          Terms of Use
        </Link>{' '}
        and{' '}
        <Link
          className="underline"
          href={routes.site.PrivacyPolicy}
          prefetch={false}
        >
          Privacy Policy
        </Link>
        .
      </div>
    </div>
  );
}
