'use server';

import { createDatabaseClient } from '@lilypad/db/client';
import { JoinRequestRepository } from '@lilypad/db/repository/join-request';
import { logger } from '@lilypad/shared/logger';
import { rateLimitedActionClient } from '@/shared/safe-action';
import { joinRequestSchema } from '../model/schema';

export const createJoinRequestAction = rateLimitedActionClient
  .metadata({ actionName: 'createJoinRequest' })
  .schema(joinRequestSchema)
  .action(async ({ parsedInput }) => {
    const { firstName, lastName, email, phone, districtName, message } =
      parsedInput;

    const db = await createDatabaseClient({ admin: true });

    const joinRequestRepository = new JoinRequestRepository(db);

    try {
      await joinRequestRepository.createJoinRequest({
        firstName,
        lastName,
        email,
        phone,
        districtName,
        message,
      });

      return {
        success: true,
      };
    } catch (error) {
      logger.error({ error }, 'Error creating join request');
      throw error;
    }
  });
