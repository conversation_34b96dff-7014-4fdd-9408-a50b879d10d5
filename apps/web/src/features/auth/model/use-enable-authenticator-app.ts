'use client';

import { useZodForm } from '@lilypad/shared/hooks/use-zod-form';
import { toast } from '@lilypad/ui/components/sonner';
import { useCallback } from 'react';
import type { SubmitHandler } from 'react-hook-form';

import { useEnhancedModal } from '@/shared/hooks/use-enhanced-modal';
import { enableAuthenticatorApp } from '../api/enable-authenticator-app.action';
import {
  type EnableAuthenticatorAppSchema,
  enableAuthenticatorAppSchema,
} from './schema';

export interface UseEnableAuthenticatorAppProps {
  accountName: string;
  issuer: string;
  secret: string;
  factorId: string;
}

export function useEnableAuthenticatorApp({
  accountName,
  issuer,
  secret,
  factorId,
}: UseEnableAuthenticatorAppProps) {
  const modal = useEnhancedModal();

  const methods = useZodForm({
    schema: enableAuthenticatorAppSchema,
    mode: 'onChange',
    defaultValues: {
      accountName,
      issuer,
      secret,
      factorId,
      totpCode: '',
    },
  });

  const canSubmit =
    !methods.formState.isSubmitting && methods.watch('totpCode').length === 6;

  const onSubmit: SubmitHandler<EnableAuthenticatorAppSchema> = useCallback(
    async (values) => {
      if (!canSubmit) {
        return;
      }

      const result = await enableAuthenticatorApp(values);

      if (!(result?.serverError || result?.validationErrors) && result?.data) {
        toast.success('Authenticator app enabled');
        modal.resolve(result.data.recoveryCodes);
        modal.handleClose();
        return;
      }

      if (result?.validationErrors?._errors?.[0]) {
        const message = result.validationErrors._errors[0];
        methods.setError('totpCode', { message });
      } else {
        toast.error("Couldn't enable authenticator app");
      }
    },
    [canSubmit, methods, modal]
  );

  return {
    modal,
    methods,
    onSubmit,
    canSubmit,
  };
}
