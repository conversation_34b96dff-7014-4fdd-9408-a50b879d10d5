'use client';

import { useZodForm } from '@lilypad/shared/hooks/use-zod-form';
import { routes } from '@lilypad/shared/routes';
import { toast } from '@lilypad/ui/components/sonner';
import { useRouter } from 'next/navigation';
import { useCallback } from 'react';
import { type EmailSchema, emailSchema } from '@/entities/users/model/auth';
import { sendResetPasswordInstructionsAction } from '@/features/auth/api/send-reset-password-instructions.action';

export function useForgotPassword() {
  const router = useRouter();

  const methods = useZodForm({
    schema: emailSchema,
    mode: 'onSubmit',
    defaultValues: {
      email: '',
    },
  });

  const canSubmit = !methods.formState.isSubmitting;

  const onSubmit = useCallback(
    async (values: EmailSchema) => {
      if (!canSubmit) {
        return;
      }

      const result = await sendResetPasswordInstructionsAction(values);

      if (result?.serverError || result?.validationErrors) {
        toast.error('Failed to request password change', {
          description: 'Please check your email address and try again.',
        });
      } else {
        router.replace(
          `${routes.app.auth.forgetPassword.Success}?email=${values.email}`
        );
      }
    },
    [canSubmit, router]
  );

  return {
    methods,
    onSubmit,
    canSubmit,
  };
}
