'use client';

import { useState, useCallback } from 'react';
import type { SubmitHandler } from 'react-hook-form';
import { useZodForm } from '@lilypad/shared/hooks/use-zod-form';

import { signUpAction } from '@/features/auth/api/sign-up.action';
import { continueWithOAuthAction } from '@/features/auth/api/continue-with-oauth.action';
import {
  signUpSchema,
  type SignUpSchema,
} from '@/entities/users/model/auth/sign-up.schema';
import { toast } from '@lilypad/ui/components/sonner';

export function useSignUp() {
  const [errorMessage, setErrorMessage] = useState<string>();

  const methods = useZodForm({
    schema: signUpSchema,
    mode: 'onSubmit',
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      password: '',
    },
  });

  const password = methods.watch('password');
  const canSubmit = !methods.formState.isSubmitting;

  const onSubmit: SubmitHandler<SignUpSchema> = useCallback(
    async (values) => {
      if (!canSubmit) return;

      const result = await signUpAction(values);

      if (result?.serverError || result?.validationErrors) {
        if (result.validationErrors?.email?._errors?.[0]) {
          toast.error(result.validationErrors?.email?._errors?.[0], {
            description: 'Please use a different email address.',
          });
        } else {
          toast.error('An error occured during sign up', {
            description: 'Please try again.',
          });
        }
      }
    },
    [canSubmit]
  );

  const onGoogle = useCallback(async (): Promise<void> => {
    if (!canSubmit) return;

    const result = await continueWithOAuthAction({ provider: 'google' });
    if (result?.serverError || result?.validationErrors) {
      setErrorMessage('An error occured during Google sign up.');
    }
  }, [canSubmit]);

  return {
    methods,
    password,
    onSubmit,
    onGoogle,
    canSubmit,
    errorMessage,
  };
}
