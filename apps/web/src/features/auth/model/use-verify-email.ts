'use client';

import { useState, useCallback } from 'react';
import { toast } from '@lilypad/ui/components/sonner';
import { useZodForm } from '@lilypad/shared/hooks/use-zod-form';

import {
  verifyEmailWithOtpSchema,
  type VerifyEmailWithOtpSchema,
} from '@/entities/users/model/auth/verify-email-with-otp.schema';

import { resendEmailConfirmationAction } from '@/features/auth/api/resend-email-confirmation.action';
import { verifyEmailWithOtpAction } from '../api/verify-email-with-otp.action';

export function useVerifyEmail(email: string) {
  const [isResendingEmailVerification, setIsResendingEmailVerification] =
    useState<boolean>(false);

  const methods = useZodForm({
    schema: verifyEmailWithOtpSchema,
    mode: 'onSubmit',
    defaultValues: {
      otp: '',
      email,
    },
  });

  const canSubmit = !methods.formState.isSubmitting;
  const errorMessage = methods.formState.errors.email?.message;

  const onSubmit = useCallback(
    async (values: VerifyEmailWithOtpSchema) => {
      if (!canSubmit) {
        return;
      }

      const result = await verifyEmailWithOtpAction(values);

      if (result?.serverError || result?.validationErrors) {
        toast.error("Couldn't verify email");
      }
    },
    [canSubmit]
  );

  const handleResendEmailVerification = useCallback(async (): Promise<void> => {
    setIsResendingEmailVerification(true);

    const result = await resendEmailConfirmationAction({ email });

    if (result?.serverError || result?.validationErrors) {
      const errorMessage = result?.validationErrors?.email?._errors?.[0];
      if (errorMessage) {
        toast.error(errorMessage);
      } else {
        toast.error("Couldn't resend verification");
      }
    } else {
      toast.success('Email verification resent');
    }

    setIsResendingEmailVerification(false);
  }, [email]);

  return {
    methods,
    onSubmit,
    canSubmit,
    errorMessage,
    isResendingEmailVerification,
    handleResendEmailVerification,
  };
}
