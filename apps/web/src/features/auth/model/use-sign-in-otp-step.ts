'use client';

import { useZodForm } from '@lilypad/shared/hooks/use-zod-form';
import { toast } from '@lilypad/ui/components/sonner';
import { useRouter } from 'next/navigation';
import { useCallback, useState } from 'react';

import {
  type SignInOtpSchema,
  signInOtpSchema,
} from '@/entities/users/model/auth/sign-in-otp.schema';
import { sendSignInOtpAction } from '@/features/auth/api/send-sign-in-otp.action';
import { verifySignInOtpAction } from '@/features/auth/api/verify-sign-in-otp.action';
import { routes } from '@lilypad/shared/routes';

export function useSignInOtpStep(email: string) {
  const router = useRouter();
  const [isResending, setIsResending] = useState(false);

  const methods = useZodForm({
    schema: signInOtpSchema,
    mode: 'onSubmit',
    defaultValues: { email, otp: '' },
  });

  const canSubmit = !methods.formState.isSubmitting;
  const errorMessage = methods.formState.errors.otp?.message;

  const onSubmit = useCallback(
    async (values: SignInOtpSchema) => {
      if (!canSubmit) return;

      const result = await verifySignInOtpAction(values);

      if (result?.serverError) {
        // Handle different error types
        if (result.serverError.includes('expired')) {
          toast.error('Code expired', {
            description: 'Please request a new sign-in code.',
            action: {
              label: 'Resend Code',
              onClick: () => handleResend(),
            },
          });
        } else if (result.serverError.includes('Invalid code')) {
          toast.error('Invalid code', {
            description: 'Please check your email and try again.',
          });
          // Clear the OTP field for retry
          methods.setValue('otp', '');
        } else {
          toast.error('Verification failed', {
            description: result.serverError,
          });
        }
      }
      // Success handling is done in the server action (redirect)
    },
    [canSubmit, methods]
  );

  const handleResend = useCallback(async () => {
    setIsResending(true);

    const result = await sendSignInOtpAction({ email });

    if (result?.serverError || result?.validationErrors) {
      toast.error('Failed to resend code', {
        description: 'Please try again or contact support.',
      });
    } else {
      toast.success('Code resent', {
        description: `Check your email at ${email}`,
      });
      methods.reset({ email, otp: '' });
    }

    setIsResending(false);
  }, [email, methods]);

  const handleChangeEmail = useCallback(() => {
    router.push(routes.app.auth.SignIn);
  }, [router]);

  return {
    methods,
    onSubmit,
    canSubmit,
    errorMessage,
    handleResend,
    isResending,
    handleChangeEmail,
  };
}
