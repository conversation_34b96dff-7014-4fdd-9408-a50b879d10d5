'use client';

import { useCallback } from 'react';
import { useZodForm } from '@lilypad/shared/hooks/use-zod-form';
import { toast } from '@lilypad/ui/components/sonner';

import { signInWithCredentialsAction } from '@/features/auth/api/sign-in-with-credentials.action';
import { continueWithOAuthAction } from '@/features/auth/api/continue-with-oauth.action';
import {
  passThroughCredentialsSchema,
  type PassThroughCredentialsSchema,
} from '@/entities/users/model';
import { useRouter } from 'next/navigation';
import { routes } from '@lilypad/shared/routes';

export function useSignIn(nextUrl?: string) {
  const router = useRouter();

  const methods = useZodForm({
    schema: passThroughCredentialsSchema,
    mode: 'onSubmit',
    defaultValues: { email: '', password: '' },
  });

  const canSubmit = !methods.formState.isSubmitting;

  const onSubmit = useCallback(
    async (values: PassThroughCredentialsSchema) => {
      if (!canSubmit) return;
      const result = await signInWithCredentialsAction({
        ...values,
        redirectTo: nextUrl,
      });

      if (result?.serverError) {
        const isUnverified = result.serverError.startsWith(
          'Email not confirmed'
        );

        if (isUnverified) {
          toast.error('Email not verified', {
            description: 'Verify your email to continue.',
            action: {
              label: 'Verify Email',
              onClick: () => {
                router.push(
                  `${routes.app.auth.verifyEmail.Index}?email=${encodeURIComponent(values.email)}`
                );
              },
            },
          });
        } else {
          toast.error('Failed to sign in', {
            description: result.serverError,
          });
        }
      }
    },
    [canSubmit, nextUrl, router]
  );

  const onGoogle = useCallback(async () => {
    if (!canSubmit) return;
    const result = await continueWithOAuthAction({ provider: 'google' });
    if (result?.serverError) toast.error("Couldn't continue with Google");
  }, [canSubmit]);

  return {
    methods,
    onSubmit,
    onGoogle,
    canSubmit,
  };
}
