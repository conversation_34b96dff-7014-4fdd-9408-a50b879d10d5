import { z } from 'zod';

export const continueWithOAuthInviteSchema = z.object({
  provider: z.enum(['google']),
  inviteToken: z.string().optional(),
});

export type ContinueWithOAuthInviteSchema = z.infer<
  typeof continueWithOAuthInviteSchema
>;

export type MultiFactorAuthenticationDto = {
  factorId?: string;
};

export const enableAuthenticatorAppSchema = z.object({
  accountName: z
    .string({
      required_error: 'Account name is required.',
      invalid_type_error: 'Account name must be a string.',
    })
    .trim()
    .min(1, 'Account name is required.')
    .max(255, 'Maximum 255 characters allowed.'),
  issuer: z
    .string({
      required_error: 'Issuer is required.',
      invalid_type_error: 'Issuer must be a string.',
    })
    .trim()
    .min(1, 'Issuer is required.')
    .max(255, 'Maximum 255 characters allowed.'),
  secret: z
    .string({
      required_error: 'Secret is required.',
      invalid_type_error: 'Secret must be a string.',
    })
    .trim()
    .min(1, 'Secret is required.'),
  factorId: z
    .string({
      required_error: 'Factor ID is required.',
      invalid_type_error: 'Factor ID must be a string.',
    })
    .trim()
    .min(1, 'Factor ID is required.'),
  totpCode: z
    .string({
      required_error: 'Code is required.',
      invalid_type_error: 'Code consists of 6 digits.',
    })
    .trim()
    .min(6, { message: '' }),
});

export type EnableAuthenticatorAppSchema = z.infer<
  typeof enableAuthenticatorAppSchema
>;

export const submitTotpCodeSchema = z.object({
  token: z
    .string({
      invalid_type_error: 'Token must be a string.',
    })
    .trim()
    .min(1, 'Token is required.')
    .max(255, 'Maximum 255 characters allowed.'),
  expiry: z
    .string({
      invalid_type_error: 'Expiry must be a string.',
    })
    .trim()
    .min(1, 'Expiry is required.')
    .max(255, 'Maximum 255 characters allowed.'),
  totpCode: z
    .string({
      required_error: 'Code is required.',
      invalid_type_error: 'Code consists of 6 digits.',
    })
    .trim()
    .max(6, { message: '' }),
});

export type SubmitTotpCodeSchema = z.infer<typeof submitTotpCodeSchema>;

export const submitRecoveryCodeSchema = z.object({
  token: z
    .string({
      invalid_type_error: 'Token must be a string.',
    })
    .trim()
    .min(1, 'Token is required.')
    .max(255, 'Maximum 255 characters allowed.'),
  expiry: z
    .string({
      invalid_type_error: 'Expiry must be a string.',
    })
    .trim()
    .min(1, 'Expiry is required.')
    .max(255, 'Maximum 255 characters allowed.'),
  recoveryCode: z
    .string({
      required_error: 'Recovery code is required.',
      invalid_type_error: 'Recovery code must be a string.',
    })
    .trim()
    .min(1, 'Recovery code is required.')
    .max(11, 'Maximum 11 characters allowed.'),
});

export type SubmitRecoveryCodeSchema = z.infer<typeof submitRecoveryCodeSchema>;
