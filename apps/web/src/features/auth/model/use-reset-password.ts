'use client';

import { useZodForm } from '@lilypad/shared/hooks/use-zod-form';
import { passwordValidator } from '@lilypad/supabase/auth/password';
import { toast } from '@lilypad/ui/components/sonner';
import { useCallback, useState } from 'react';
import { z } from 'zod';

import { resetPasswordAction } from '../api/reset-password.action';

const resetPasswordSchema = z
  .object({
    password: z
      .string()
      .min(1, 'Password is required')
      .max(72)
      .refine((arg) => passwordValidator.validate(arg).success, {
        message: 'Password does not meet requirements.',
      }),
    confirmPassword: z.string().min(1, 'Please confirm your password'),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  });

type ResetPasswordSchema = z.infer<typeof resetPasswordSchema>;

export function useResetPassword() {
  const [errorMessage, setErrorMessage] = useState<string>();

  const methods = useZodForm({
    schema: resetPasswordSchema,
    mode: 'onChange',
    defaultValues: {
      password: '',
      confirmPassword: '',
    },
  });

  const password = methods.watch('password');
  const canSubmit = !methods.formState.isSubmitting;

  const onSubmit = useCallback(
    async (values: ResetPasswordSchema) => {
      if (!canSubmit) return;

      setErrorMessage(undefined);

      try {
        const result = await resetPasswordAction({
          password: values.password,
        });

        if (result?.serverError) {
          setErrorMessage(result.serverError);
          return;
        }

        toast.success('Password updated successfully', {
          description: 'You can now access your account.',
        });

        // Action will handle redirect
      } catch (error) {
        console.error('Reset password error:', error);
        setErrorMessage('An unexpected error occurred. Please try again.');
      }
    },
    [canSubmit]
  );

  return {
    methods,
    password,
    onSubmit,
    canSubmit,
    errorMessage,
  };
}
