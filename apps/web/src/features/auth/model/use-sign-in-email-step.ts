'use client';

import { useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useZodForm } from '@lilypad/shared/hooks/use-zod-form';
import { toast } from '@lilypad/ui/components/sonner';

import { sendSignInOtpAction } from '@/features/auth/api/send-sign-in-otp.action';
import { continueWithOAuthAction } from '@/features/auth/api/continue-with-oauth.action';
import {
  signInEmailSchema,
  type SignInEmailSchema,
} from '@/entities/users/model/auth/sign-in-email.schema';
import { routes } from '@lilypad/shared/routes';

export function useSignInEmailStep() {
  const router = useRouter();

  const methods = useZodForm({
    schema: signInEmailSchema,
    mode: 'onSubmit',
    defaultValues: { email: '' },
  });

  const canSubmit = !methods.formState.isSubmitting;

  const onSubmit = useCallback(
    async (values: SignInEmailSchema) => {
      if (!canSubmit) return;

      const result = await sendSignInOtpAction(values);

      if (result?.serverError) {
        toast.error('Failed to send passcode!', {
          description: result.serverError,
        });
      } else if (result?.validationErrors?.email?._errors?.[0]) {
        // Handle user not found error
        const errorMessage = result.validationErrors.email._errors[0];
        toast.error('Account not found!', {
          description: errorMessage,
          action: {
            label: 'Sign Up',
            onClick: () => router.push(routes.app.auth.SignUp),
          },
        });
      } else {
        // Success - redirect to verification step
        const params = new URLSearchParams({
          step: 'verify',
          email: values.email,
        });
        router.push(`${routes.app.auth.SignIn}?${params.toString()}`);

        toast.success('Passcode sent!', {
          description: `Check your email at ${values.email}`,
        });
      }
    },
    [canSubmit, router]
  );

  const onGoogle = useCallback(async () => {
    if (!canSubmit) return;
    const result = await continueWithOAuthAction({ provider: 'google' });
    if (result?.serverError) toast.error("Couldn't continue with Google");
  }, [canSubmit]);

  return {
    methods,
    onSubmit,
    onGoogle,
    canSubmit,
  };
}
