'use client';

import { useZodForm } from '@lilypad/shared/hooks/use-zod-form';
import React from 'react';
import { signOutAction } from '@/entities/users/api/auth';
import { submitTotpCode } from '../api/submit-totp-code.action';
import {
  type SubmitTotpCodeSchema,
  submitTotpCodeSchema,
} from '../model/schema';

const errorLabels = {
  REQUEST_EXPIRED: 'The request has expired.',
  INCORRECT_TOTP_CODE: 'The TOTP code is not correct.',
};

export interface UseTotpCodeProps {
  token: string;
  expiry: string;
}

export function useTotpCode({ token, expiry }: UseTotpCodeProps) {
  const [isLoading, setIsLoading] = React.useState<boolean>(false);
  const [errorMessage, setErrorMessage] = React.useState<string>();
  const [isRequestExpired, setIsRequestExpired] =
    React.useState<boolean>(false);

  const methods = useZodForm({
    schema: submitTotpCodeSchema,
    mode: 'onSubmit',
    defaultValues: {
      token,
      expiry,
      totpCode: '',
    },
  });

  const canSubmit = !(isLoading || methods.formState.isSubmitting);

  const onSubmit = async (values: SubmitTotpCodeSchema): Promise<void> => {
    if (!canSubmit) {
      return;
    }
    setIsLoading(true);

    const result = await submitTotpCode(values);

    if (result?.validationErrors?._errors) {
      const errorCode = result.validationErrors._errors[0];
      setIsRequestExpired(errorCode === 'REQUEST_EXPIRED');
      setErrorMessage(errorLabels[errorCode as keyof typeof errorLabels]);
      setIsLoading(false);
    } else if (result?.serverError) {
      setIsRequestExpired(false);
      setErrorMessage(result.serverError);
      setIsLoading(false);
    }
  };

  const onSignInAgain = async (): Promise<void> => {
    await signOutAction({ redirect: true });
  };

  return {
    methods,
    onSubmit,
    onSignInAgain,
    canSubmit,
    isLoading,
    errorMessage,
    isRequestExpired,
  };
}
