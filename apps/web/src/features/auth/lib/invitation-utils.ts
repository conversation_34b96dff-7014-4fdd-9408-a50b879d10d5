/**
 * Utility functions for handling invitation tokens in OAuth flows
 */

export function storeInvitationToken(token: string): void {
  if (typeof window !== 'undefined') {
    sessionStorage.setItem('invite_token', token);
  }
}

export function getStoredInvitationToken(): string | null {
  if (typeof window !== 'undefined') {
    return sessionStorage.getItem('invite_token');
  }
  return null;
}

export function clearStoredInvitationToken(): void {
  if (typeof window !== 'undefined') {
    sessionStorage.removeItem('invite_token');
  }
}
