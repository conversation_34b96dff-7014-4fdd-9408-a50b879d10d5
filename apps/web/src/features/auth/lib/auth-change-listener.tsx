'use client';

import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { useSupabase } from '@lilypad/supabase/hooks';
import { useUser } from '@/shared/contexts/user-context';

export function AuthChangeListener({ children }: React.PropsWithChildren) {
  const router = useRouter();
  const supabase = useSupabase();
  const { clearUser } = useUser();

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const listener = supabase.auth.onAuthStateChange(async (event, session) => {
      if (event === 'SIGNED_OUT' || !session?.user) {
        clearUser(); // Clear client-side user state
        router.push('/');
      }
      // For SIGNED_IN, we rely on server-side rendering to provide fresh user data
      // No need to fetch client-side since the next page load will have current data
    });

    return () => listener.data.subscription.unsubscribe();
  }, [router, supabase.auth, clearUser]);

  return <>{children}</>;
}
