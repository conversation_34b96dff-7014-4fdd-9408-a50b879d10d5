'use client';

import { But<PERSON> } from '@lilypad/ui/components/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  type CardProps,
  CardTitle,
} from '@lilypad/ui/components/card';
import { ScrollArea } from '@lilypad/ui/components/scroll-area';
import { cn } from '@lilypad/ui/lib/utils';
import { ArrowRightIcon } from 'lucide-react';
import Link from 'next/link';
import type { MultiFactorAuthenticationDto } from '../model/schema';

import { routes } from '@lilypad/shared/routes';
import { Logo } from '@lilypad/ui/components/logo';
import { toast } from '@lilypad/ui/components/sonner';
import { skipMfaSetup } from '../api/skip-mfa-setup.action';
import { MultiFactorAuthenticationList } from './multi-factor-authentication-list';

export type MultiFactorAuthenticationCardProps = CardProps &
  MultiFactorAuthenticationDto;

export function MultiFactorAuthenticationCard({
  factorId,
  className,
  ...other
}: MultiFactorAuthenticationCardProps): React.JSX.Element {
  const onSkip = async () => {
    const result = await skipMfaSetup();
    if (result?.serverError || result?.validationErrors) {
      toast.error('Failed to proceed', {
        description: 'Please try again or contact support.',
      });
    }
  };

  return (
    <div className="rounded-xl border border-border bg-muted/80 shadow-sm dark:bg-muted/20">
      <Card
        className={cn(
          'gap-2 border-border border-x-0 border-t-0 border-b-xl pb-0 shadow-none',
          className
        )}
        {...other}
      >
        <CardHeader>
          <Link href={routes.site.Index}>
            <Logo className="pb-2" />
          </Link>
          <CardTitle className="text-base lg:text-lg">
            Multi-factor authentication
          </CardTitle>
          <CardDescription>
            Add an extra layer of security to your account by requiring an
            additional factor to sign in.
          </CardDescription>
        </CardHeader>
        <CardContent className="max-h-72 flex-1 overflow-hidden p-0">
          <ScrollArea className="[&>[data-radix-scroll-area-viewport]>div]:!block h-full">
            <MultiFactorAuthenticationList
              factorId={factorId}
              onRecoveryCodesModalClose={onSkip}
            />
          </ScrollArea>
        </CardContent>
      </Card>
      <div className="flex items-center justify-center space-x-2 py-4 text-center text-muted-foreground text-sm">
        <span>Want to set it up later?</span>
        <Button
          size="sm"
          type="button"
          variant="outline"
          className="h-fit gap-1 py-1 pl-2"
          onClick={onSkip}
        >
          Skip
          <ArrowRightIcon className="size-3 shrink-0" />
        </Button>
      </div>
    </div>
  );
}
