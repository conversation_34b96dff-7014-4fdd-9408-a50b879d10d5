import { Card, CardContent, type CardProps } from '@lilypad/ui/components/card';
import { Skeleton } from '@lilypad/ui/components/skeleton';

export function MultiFactorAuthenticationSkeletonCard(
  props: CardProps
): React.JSX.Element {
  return (
    <Card {...props}>
      <CardContent className="max-h-72 flex-1 overflow-hidden p-0">
        <div className="divide-y">
          <div className="flex flex-row items-center justify-between p-6">
            <div className="flex flex-row items-center gap-4">
              <Skeleton className="size-6 rounded-full" />
              <div className="space-y-1">
                <Skeleton className="h-5 w-16" />
                <Skeleton className="h-4 w-[80px]" />
              </div>
            </div>
            <Skeleton className="h-9 w-[96px]" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
