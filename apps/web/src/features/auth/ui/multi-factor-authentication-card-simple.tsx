import { Card, CardContent, type CardProps } from '@lilypad/ui/components/card';
import { ScrollArea } from '@lilypad/ui/components/scroll-area';
import { cn } from '@lilypad/ui/lib/utils';

import type { MultiFactorAuthenticationDto } from '../model/schema';
import { MultiFactorAuthenticationList } from './multi-factor-authentication-list';

export type MultiFactorAuthenticationCardSimpleProps = CardProps &
  MultiFactorAuthenticationDto;

export function MultiFactorAuthenticationCardSimple({
  factorId,
  className,
  ...other
}: MultiFactorAuthenticationCardSimpleProps): React.JSX.Element {
  return (
    <Card
      className={cn('gap-2 border-border py-0 shadow-none', className)}
      {...other}
    >
      <CardContent className="max-h-72 flex-1 overflow-hidden p-0">
        <ScrollArea className="[&>[data-radix-scroll-area-viewport]>div]:!block h-full">
          <MultiFactorAuthenticationList factorId={factorId} />
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
