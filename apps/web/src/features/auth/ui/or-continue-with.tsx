import type * as React from 'react';

export function OrContinueWith({
  className,
  ...other
}: React.HTMLAttributes<HTMLDivElement>): React.JSX.Element {
  return (
    <div className="my-1 grid grid-cols-[1fr_auto_1fr] items-center gap-3">
      <hr className="border-dashed" />
      <span className="text-muted-foreground text-xs uppercase">Or</span>
      <hr className="border-dashed" />
    </div>
  );
}
