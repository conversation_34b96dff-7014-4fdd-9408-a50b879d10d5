'use client';

import { MailIcon } from 'lucide-react';
import Link from 'next/link';
import type * as React from 'react';

import { routes } from '@lilypad/shared/routes';
import { Button } from '@lilypad/ui/components/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  type CardProps,
  CardTitle,
} from '@lilypad/ui/components/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@lilypad/ui/components/form';
import { InputWithAdornments } from '@lilypad/ui/components/input-with-adornments';
import { cn } from '@lilypad/ui/lib/utils';

import { useForgotPassword } from '@/features/auth/model/use-forgot-password';
import { Logo } from '@lilypad/ui/components/logo';

export function ForgotPasswordCard({
  className,
  ...other
}: CardProps): React.JSX.Element {
  const { methods, onSubmit, canSubmit } = useForgotPassword();

  return (
    <Card className={cn('', className)} {...other}>
      <CardHeader>
        <Link href={routes.site.Index}>
          <Logo className="pb-2" />
        </Link>
        <CardTitle className="text-base lg:text-lg">
          Forgot your password?
        </CardTitle>
        <CardDescription>
          No worries! We'll send you a link with instructions on how to reset
          your password.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...methods}>
          <form
            onSubmit={methods.handleSubmit(onSubmit)}
            className="flex flex-col gap-4"
          >
            <FormField
              control={methods.control}
              name="email"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <InputWithAdornments
                      {...field}
                      type="email"
                      maxLength={255}
                      autoCapitalize="off"
                      autoComplete="email"
                      placeholder="<EMAIL>"
                      startAdornment={<MailIcon className="size-4 shrink-0" />}
                      disabled={!canSubmit}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button
              type="submit"
              variant="default"
              className="w-full"
              disabled={!canSubmit}
              loading={methods.formState.isSubmitting}
            >
              Send Instructions
            </Button>
          </form>
        </Form>
      </CardContent>
      <CardFooter className="flex justify-center gap-1 text-muted-foreground text-xs">
        <span>Remembered your password?</span>
        <Link
          href={routes.app.auth.SignIn}
          className="text-foreground hover:underline"
        >
          Sign In
        </Link>
      </CardFooter>
    </Card>
  );
}
