'use client';

import { useZodForm } from '@lilypad/shared/hooks/use-zod-form';
import { routes } from '@lilypad/shared/routes';
import { Alert, AlertDescription } from '@lilypad/ui/components/alert';
import { Button, buttonVariants } from '@lilypad/ui/components/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  type CardProps,
  CardTitle,
} from '@lilypad/ui/components/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@lilypad/ui/components/form';
import { Input } from '@lilypad/ui/components/input';
import { Logo } from '@lilypad/ui/components/logo';
import { cn } from '@lilypad/ui/lib/utils';
import { AlertCircleIcon, ArrowLeftIcon, ArrowRightIcon } from 'lucide-react';
import Link from 'next/link';
import React from 'react';
import { signOutAction } from '@/entities/users/api/auth';
import { submitRecoveryCode } from '../api/submit-recovery-code.action';
import {
  type SubmitRecoveryCodeSchema,
  submitRecoveryCodeSchema,
} from '../model/schema';

export type RecoveryCodeCardProps = CardProps & {
  token: string;
  expiry: string;
};

export function RecoveryCodeCard({
  token,
  expiry,
  className,
  ...other
}: RecoveryCodeCardProps): React.JSX.Element {
  const [isLoading, setIsLoading] = React.useState<boolean>(false);
  const [errorMessage, setErrorMessage] = React.useState<string>();
  const [isRecoveryCodeExpired, setIsRecoveryCodeExpired] =
    React.useState<boolean>(false);

  const methods = useZodForm({
    schema: submitRecoveryCodeSchema,
    mode: 'onSubmit',
    defaultValues: {
      token,
      expiry,
      recoveryCode: '',
    },
  });

  const canSubmit = !(isLoading || methods.formState.isSubmitting);

  const onSubmit = async (values: SubmitRecoveryCodeSchema): Promise<void> => {
    if (!canSubmit) {
      return;
    }
    setIsLoading(true);

    const result = await submitRecoveryCode(values);

    if (result?.validationErrors?._errors) {
      const errorCode = result.validationErrors._errors[0];
      setIsRecoveryCodeExpired(errorCode === 'RECOVERY_CODE_EXPIRED');
      setIsLoading(false);
    } else if (result?.serverError) {
      setIsRecoveryCodeExpired(false);
      setErrorMessage(result.serverError);
      setIsLoading(false);
    }
  };

  const onSignInAgain = async (): Promise<void> => {
    await signOutAction({ redirect: true });
  };

  return (
    <div className="rounded-xl border border-border bg-muted/80 shadow-sm dark:bg-muted/20">
      <Card
        className={cn(
          'border-border border-x-0 border-t-0 border-b-xl shadow-none',
          className
        )}
        {...other}
      >
        <CardHeader>
          <Link href={routes.site.Index}>
            <Logo className="pb-2" />
          </Link>
          <CardTitle className="text-base lg:text-lg">Recovery code</CardTitle>
          <CardDescription>
            Each recovery code can be used exactly once to grant access without
            your authenticator.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...methods}>
            <form
              className="flex flex-col gap-4"
              onSubmit={methods.handleSubmit(onSubmit)}
            >
              <input
                className="hidden"
                disabled={methods.formState.isSubmitting}
                type="hidden"
                {...methods.register('token')}
              />
              <input
                disabled={methods.formState.isSubmitting}
                type="hidden"
                {...methods.register('expiry')}
              />
              <FormField
                control={methods.control}
                name="recoveryCode"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        {...field}
                        disabled={methods.formState.isSubmitting}
                        maxLength={11}
                        placeholder="XXXXX-XXXXX"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              {errorMessage && (
                <Alert
                  className="flex flex-row items-center gap-2"
                  variant="destructive"
                >
                  <AlertCircleIcon className="size-4.5 shrink-0" />
                  <AlertDescription>
                    {errorMessage}
                    {isRecoveryCodeExpired && (
                      <Button
                        className="ml-0.5 h-fit gap-0.5 px-0.5 py-0 text-foreground"
                        onClick={onSignInAgain}
                        variant="link"
                      >
                        Sign in again
                        <ArrowRightIcon className="size-3 shrink-0" />
                      </Button>
                    )}
                  </AlertDescription>
                </Alert>
              )}
              <Button
                className="w-full"
                disabled={!canSubmit}
                loading={methods.formState.isSubmitting}
                onClick={methods.handleSubmit(onSubmit)}
                type="submit"
                variant="default"
              >
                Submit
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>
      <div className="flex items-center justify-center space-x-4 py-4 text-center text-muted-foreground text-sm">
        <Link
          className={cn(
            buttonVariants({ variant: 'outline', size: 'sm' }),
            'h-fit gap-1 py-1 pl-2 text-muted-foreground hover:text-primary'
          )}
          href={`${routes.app.auth.totp.Verify}?token=${encodeURIComponent(token)}&expiry=${encodeURIComponent(expiry)}`}
        >
          <ArrowLeftIcon className="size-3 shrink-0" />
          Go back
        </Link>
      </div>
    </div>
  );
}
