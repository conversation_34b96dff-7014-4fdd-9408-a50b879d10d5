'use client';

import type * as React from 'react';
import type { CardProps } from '@lilypad/ui/components/card';

import { SignInEmailStep } from './sign-in-email-step';
import { SignInOtpStep } from './sign-in-otp-step';

interface SignInCardProps extends CardProps {
  step: 'email' | 'verify';
  email: string;
}

export function SignInCard({
  step,
  email,
  ...other
}: SignInCardProps): React.JSX.Element {
  switch (step) {
    case 'verify':
      return <SignInOtpStep email={email} {...other} />;
    default:
      return <SignInEmailStep {...other} />;
  }
}
