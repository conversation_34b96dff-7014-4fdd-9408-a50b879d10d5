'use client';

import type * as React from 'react';

import { But<PERSON> } from '@lilypad/ui/components/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
  type CardProps,
} from '@lilypad/ui/components/card';
import {
  FormControl,
  FormField,
  FormItem,
  FormMessage,
  Form,
} from '@lilypad/ui/components/form';
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
  REGEXP_ONLY_DIGITS_AND_CHARS,
} from '@lilypad/ui/components/input-otp';
import { Alert, AlertDescription } from '@lilypad/ui/components/alert';
import { AlertCircleIcon } from 'lucide-react';
import { cn } from '@lilypad/ui/lib/utils';

import { useVerifyEmail } from '@/features/auth/model/use-verify-email';
import { Logo } from '@lilypad/ui/components/logo';
import Link from 'next/link';
import { routes } from '@lilypad/shared/routes';

export type VerifyEmailCardProps = CardProps & {
  email: string;
};

export function VerifyEmailCard({
  email,
  className,
  ...other
}: VerifyEmailCardProps): React.JSX.Element {
  const {
    methods,
    onSubmit,
    canSubmit,
    errorMessage,
    isResendingEmailVerification,
    handleResendEmailVerification,
  } = useVerifyEmail(email);

  return (
    <Card
      className={cn('border-transparent dark:border-border', className)}
      {...other}
    >
      <CardHeader>
        <Link href={routes.site.Index}>
          <Logo className="pb-2" />
        </Link>
        <CardTitle className="text-base lg:text-lg">
          Please check your email
        </CardTitle>
        <CardDescription>
          Your registration has been successful. We have sent you an email with
          a verification link.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...methods}>
          <form
            className="flex flex-col gap-4"
            onSubmit={methods.handleSubmit(onSubmit)}
          >
            <p className="text-muted-foreground text-sm">
              Alternatively you can use the one-time password in the email for
              verification.
            </p>
            <FormField
              control={methods.control}
              name="otp"
              render={({ field }) => (
                <FormItem className="flex w-full flex-col items-center space-y-0">
                  <FormControl>
                    <InputOTP
                      {...field}
                      inputMode="text"
                      maxLength={6}
                      pattern={REGEXP_ONLY_DIGITS_AND_CHARS}
                      disabled={methods.formState.isSubmitting}
                      onComplete={methods.handleSubmit(onSubmit)}
                    >
                      <InputOTPGroup>
                        <InputOTPSlot index={0} />
                        <InputOTPSlot index={1} />
                        <InputOTPSlot index={2} />
                        <InputOTPSlot index={3} />
                        <InputOTPSlot index={4} />
                        <InputOTPSlot index={5} />
                      </InputOTPGroup>
                    </InputOTP>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            {errorMessage && (
              <Alert variant="destructive">
                <div className="flex flex-row items-center gap-2 text-sm">
                  <AlertCircleIcon className="size-[18px] shrink-0" />
                  <AlertDescription>{errorMessage}</AlertDescription>
                </div>
              </Alert>
            )}
            <Button
              type="submit"
              variant="default"
              disabled={!canSubmit}
              loading={methods.formState.isSubmitting}
            >
              Verify
            </Button>
          </form>
        </Form>
      </CardContent>
      <CardFooter className="flex justify-center gap-1 text-muted-foreground text-sm">
        Didn't receive an email?
        <Button
          type="button"
          variant="link"
          className="h-fit px-0.5 py-0 text-foreground underline"
          disabled={
            methods.formState.isSubmitting || isResendingEmailVerification
          }
          onClick={handleResendEmailVerification}
        >
          Resend
        </Button>
      </CardFooter>
    </Card>
  );
}
