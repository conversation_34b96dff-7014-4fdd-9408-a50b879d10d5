'use client';

import { <PERSON>ertCircleIcon, LockIcon, MailIcon } from 'lucide-react';
import Link from 'next/link';
import type * as React from 'react';

import { routes } from '@lilypad/shared/routes';
import { Alert, AlertDescription } from '@lilypad/ui/components/alert';
import { Button } from '@lilypad/ui/components/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  type CardProps,
  CardTitle,
} from '@lilypad/ui/components/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@lilypad/ui/components/form';
import { Input } from '@lilypad/ui/components/input';
import { InputPassword } from '@lilypad/ui/components/input-password';
import { InputWithAdornments } from '@lilypad/ui/components/input-with-adornments';
import { cn } from '@lilypad/ui/lib/utils';

import { useSignUp } from '@/features/auth/model/use-sign-up';
import { PasswordFormMessage } from '@/shared/ui/auth/password-form-message';
import { Logo } from '@lilypad/ui/components/logo';
import { OAuthFooter } from './oauth-footer';

export function SignUpCard({
  className,
  ...other
}: CardProps): React.JSX.Element {
  const { methods, password, onSubmit, onGoogle, canSubmit, errorMessage } =
    useSignUp();

  return (
    <div className="rounded-xl border border-border bg-muted/80 shadow-sm dark:bg-muted/20">
      <Card
        className={cn(
          'border-border border-x-0 border-t-0 border-b-xl shadow-none',
          className
        )}
        {...other}
      >
        <CardHeader>
          <Link href={routes.site.Index}>
            <Logo className="pb-2" />
          </Link>
          <CardTitle className="text-base lg:text-lg">
            Create your account
          </CardTitle>
          <CardDescription>
            Please fill in the details to get started.
          </CardDescription>
        </CardHeader>
        <CardContent className="flex flex-col gap-4">
          <Form {...methods}>
            <form
              className="flex flex-col gap-4"
              onSubmit={methods.handleSubmit(onSubmit)}
            >
              <div className="flex flex-col gap-4 sm:flex-row">
                <FormField
                  control={methods.control}
                  name="firstName"
                  render={({ field }) => (
                    <FormItem className="flex w-full flex-col">
                      <FormLabel>First Name</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          maxLength={64}
                          autoComplete="given-name"
                          placeholder="Lily"
                          disabled={!canSubmit}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={methods.control}
                  name="lastName"
                  render={({ field }) => (
                    <FormItem className="flex w-full flex-col">
                      <FormLabel>Last Name</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          maxLength={64}
                          autoComplete="family-name"
                          placeholder="Padawan"
                          disabled={!canSubmit}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <FormField
                control={methods.control}
                name="email"
                render={({ field }) => (
                  <FormItem className="flex w-full flex-col">
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <InputWithAdornments
                        type="email"
                        maxLength={255}
                        autoComplete="username"
                        placeholder="<EMAIL>"
                        disabled={!canSubmit}
                        startAdornment={
                          <MailIcon className="size-4 shrink-0" />
                        }
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="flex flex-col">
                <FormField
                  control={methods.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Password</FormLabel>
                      <FormControl>
                        <InputPassword
                          maxLength={72}
                          autoCapitalize="off"
                          autoComplete="current-password"
                          placeholder="••••••••"
                          disabled={!canSubmit}
                          startAdornment={
                            <LockIcon className="size-4 shrink-0" />
                          }
                          {...field}
                        />
                      </FormControl>
                      <PasswordFormMessage password={password} />
                    </FormItem>
                  )}
                />
              </div>
              {errorMessage && (
                <Alert variant="destructive">
                  <div className="flex w-full flex-row items-center gap-2 text-sm">
                    <AlertCircleIcon className="size-[18px] shrink-0" />
                    <AlertDescription>{errorMessage}</AlertDescription>
                  </div>
                </Alert>
              )}
              <Button
                type="submit"
                className="w-full"
                disabled={!canSubmit}
                loading={methods.formState.isSubmitting}
              >
                Continue
              </Button>
            </form>
          </Form>

          <OAuthFooter disabled={!canSubmit} onGoogle={onGoogle} />
        </CardContent>
        <CardFooter className="flex justify-center gap-1 text-muted-foreground text-xs">
          <span>Already have an account?</span>
          <Link
            href={routes.app.auth.SignIn}
            className="text-foreground hover:underline"
          >
            Sign In
          </Link>
        </CardFooter>
      </Card>
      <div className="py-4 text-center text-muted-foreground text-xs">
        By creating an account, you agree to our <br />
        <Link
          prefetch={false}
          href={routes.site.TermsOfUse}
          className="underline"
        >
          Terms of Use
        </Link>{' '}
        and{' '}
        <Link
          prefetch={false}
          href={routes.site.PrivacyPolicy}
          className="underline"
        >
          Privacy Policy
        </Link>
        .
      </div>
    </div>
  );
}
