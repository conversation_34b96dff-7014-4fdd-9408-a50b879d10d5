import React from 'react';

import { <PERSON><PERSON> } from '@lilypad/ui/components/button';

import { OrContinueWith } from './or-continue-with';
import { GoogleLogo } from '@lilypad/ui/logos/google';

interface OAuthFooterProps {
  disabled: boolean;
  onGoogle: () => void;
}

export const OAuthFooter = ({ disabled, onGoogle }: OAuthFooterProps) => {
  return (
    <React.Fragment>
      <OrContinueWith />

      <Button
        type="button"
        variant="outline"
        className="flex w-full flex-row items-center gap-2"
        disabled={disabled}
        onClick={onGoogle}
      >
        <GoogleLogo width="20" height="20" />
        Continue with Google
      </Button>
    </React.Fragment>
  );
};
