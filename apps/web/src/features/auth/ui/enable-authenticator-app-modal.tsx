'use client';

import NiceModal, { type NiceModalHocProps } from '@ebay/nice-modal-react';
import { CopyIcon, InfoIcon } from 'lucide-react';
import Link from 'next/link';

import { Alert, AlertDescription } from '@lilypad/ui/components/alert';
import { Button } from '@lilypad/ui/components/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@lilypad/ui/components/dialog';
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from '@lilypad/ui/components/drawer';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@lilypad/ui/components/form';
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
  REGEXP_ONLY_DIGITS,
} from '@lilypad/ui/components/input-otp';
import { useMediaQuery } from '@lilypad/ui/hooks/use-media-query';
import { MediaQueries } from '@lilypad/ui/lib/media-queries';
import { cn } from '@lilypad/ui/lib/utils';

import { useCopy } from '@lilypad/ui/hooks/use-copy';
import { useEnableAuthenticatorApp } from '../model/use-enable-authenticator-app';

const title = 'Authenticator app';
const description = 'Add an authenticator app by filling out the form below.';

export type EnableAuthenticatorAppModalProps = NiceModalHocProps & {
  accountName: string;
  issuer: string;
  secret: string;
  factorId: string;
  qrCode: string;
};

export const EnableAuthenticatorAppModal =
  NiceModal.create<EnableAuthenticatorAppModalProps>(
    ({ accountName, issuer, secret, factorId, qrCode }) => {
      const mdUp = useMediaQuery(MediaQueries.MdUp, { ssr: false });
      const { modal, methods, onSubmit, canSubmit } = useEnableAuthenticatorApp(
        {
          accountName,
          issuer,
          secret,
          factorId,
        }
      );
      const [_, copyToClipboard] = useCopy();

      const handleCopySecret = async (): Promise<void> => {
        await copyToClipboard(secret);
      };

      const renderForm = (
        <div
          className={cn(
            'min-h-0 flex-1 overflow-y-auto',
            mdUp ? 'px-6 py-4' : 'p-4'
          )}
        >
          <form className="space-y-6" onSubmit={methods.handleSubmit(onSubmit)}>
            <div className="space-y-4">
              <p className="text-muted-foreground text-sm leading-relaxed">
                Use an authenticator app like{' '}
                <Link
                  href="https://play.google.com/store/apps/details?id=com.google.android.apps.authenticator2"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="font-semibold text-foreground hover:underline"
                >
                  Google Authenticator
                </Link>
                ,{' '}
                <Link
                  href="https://www.microsoft.com/en-us/security/mobile-authenticator-app"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="font-semibold text-foreground hover:underline"
                >
                  Microsoft Authenticator
                </Link>
                ,{' '}
                <Link
                  href="https://authy.com/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="font-semibold text-foreground hover:underline"
                >
                  Authy
                </Link>
                , or{' '}
                <Link
                  href="https://1password.com/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="font-semibold text-foreground hover:underline"
                >
                  1Password
                </Link>{' '}
                on your smartphone to scan this QR code. It will generate a 6
                digit code for you to enter below.
              </p>

              <div className="flex flex-col items-center space-y-4">
                <div className="mx-auto size-48 rounded-lg border bg-white p-2">
                  <img alt="QR code" className="size-full" src={qrCode} />
                </div>
                <div className="flex flex-row items-center justify-center gap-2 rounded-md bg-muted px-3 py-2 font-mono text-xs">
                  <span className="select-all">{secret}</span>
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="size-6 hover:bg-background"
                    onClick={handleCopySecret}
                  >
                    <CopyIcon className="size-3 shrink-0" />
                  </Button>
                </div>
              </div>

              <FormField
                control={methods.control}
                name="totpCode"
                render={({ field }) => (
                  <FormItem className="flex w-full flex-col items-center space-y-4">
                    <FormLabel className="font-medium text-base">
                      Enter the 6-digit code from the app
                    </FormLabel>
                    <FormControl>
                      <InputOTP
                        {...field}
                        inputMode="numeric"
                        maxLength={6}
                        pattern={REGEXP_ONLY_DIGITS}
                        disabled={methods.formState.isSubmitting}
                        onComplete={methods.handleSubmit(onSubmit)}
                      >
                        <InputOTPGroup className="w-full justify-between [&>*]:size-10">
                          <InputOTPSlot index={0} />
                          <InputOTPSlot index={1} />
                          <InputOTPSlot index={2} />
                          <InputOTPSlot index={3} />
                          <InputOTPSlot index={4} />
                          <InputOTPSlot index={5} />
                        </InputOTPGroup>
                      </InputOTP>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Alert variant="info" className="flex flex-row items-start gap-3">
                <InfoIcon className="mt-0.5 size-4 shrink-0" />
                <AlertDescription className="flex-1">
                  If your app asks for an issuer use "
                  <strong className="text-foreground">{issuer}</strong>" and for
                  an account name use "
                  <strong className="text-foreground">{accountName}</strong>
                  ".
                </AlertDescription>
              </Alert>
            </div>
          </form>
        </div>
      );
      const renderButtons = (
        <div
          className={cn(
            'flex flex-shrink-0 gap-2 bg-secondary p-4 md:justify-between md:rounded-b-md'
          )}
        >
          <Button
            type="button"
            variant="outline"
            size="sm"
            className="w-1/2 md:w-auto"
            onClick={modal.handleClose}
          >
            Cancel
          </Button>
          <Button
            type="button"
            variant="default"
            size="sm"
            className="w-1/2 md:w-auto"
            disabled={!canSubmit}
            loading={methods.formState.isSubmitting}
            onClick={methods.handleSubmit(onSubmit)}
          >
            Enable
          </Button>
        </div>
      );
      return (
        <Form {...methods}>
          {mdUp ? (
            <Dialog open={modal.visible} onOpenChange={modal.handleOpenChange}>
              <DialogContent
                className="flex max-w-lg flex-col gap-0 p-0"
                onAnimationEndCapture={modal.handleAnimationEndCapture}
              >
                <DialogHeader className="flex-shrink-0 border-b px-6 py-5">
                  <DialogTitle>{title}</DialogTitle>
                  <DialogDescription className="sr-only">
                    {description}
                  </DialogDescription>
                </DialogHeader>
                {renderForm}
                {renderButtons}
              </DialogContent>
            </Dialog>
          ) : (
            <Drawer open={modal.visible} onOpenChange={modal.handleOpenChange}>
              <DrawerContent className="flex flex-col">
                <DrawerHeader className="flex-shrink-0 border-b text-left">
                  <DrawerTitle>{title}</DrawerTitle>
                  <DrawerDescription className="sr-only">
                    {description}
                  </DrawerDescription>
                </DrawerHeader>
                {renderForm}
                {renderButtons}
              </DrawerContent>
            </Drawer>
          )}
        </Form>
      );
    }
  );
