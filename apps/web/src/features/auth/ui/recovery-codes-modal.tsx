'use client';

import NiceModal, { type NiceModalHocProps } from '@ebay/nice-modal-react';
import saveAs from 'file-saver';

import { APP_NAME } from '@lilypad/shared';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@lilypad/ui/components/alert-dialog';
import { Button } from '@lilypad/ui/components/button';
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from '@lilypad/ui/components/drawer';
import { useMediaQuery } from '@lilypad/ui/hooks/use-media-query';
import { MediaQueries } from '@lilypad/ui/lib/media-queries';

import { useEnhancedModal } from '@/shared/hooks/use-enhanced-modal';
import { useCopy } from '@lilypad/ui/hooks/use-copy';

const title = 'Recovery codes';
const description =
  'Each recovery code can be used exactly once to grant access without your authenticator.';

function splitIntoChunks(str: string, sep: string, chunkSize: number): string {
  return str.match(new RegExp(`.{1,${chunkSize}}`, 'g'))?.join(sep) || '';
}

export type RecoveryCodesModalProps = NiceModalHocProps & {
  recoveryCodes: string[];
  onClose?: VoidFunction;
};

export const RecoveryCodesModal = NiceModal.create<RecoveryCodesModalProps>(
  ({ recoveryCodes, onClose }) => {
    const modal = useEnhancedModal();
    const mdUp = useMediaQuery(MediaQueries.MdUp, { ssr: false });
    const [_, copyToClipboard] = useCopy();

    const handleCopyRecoveryCodes = async (): Promise<void> => {
      await copyToClipboard(
        recoveryCodes
          .map((recoveryCode) => splitIntoChunks(recoveryCode, '-', 5))
          .join('\n')
      );
    };

    const handleDownloadRecoveryCodes = () => {
      const filename = `${APP_NAME}-recovery-codes.txt`;
      const content = recoveryCodes
        .map((recoveryCode) => splitIntoChunks(recoveryCode, '-', 5))
        .join('\n');
      const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
      saveAs(blob, filename);
    };

    const handleClose = () => {
      if (onClose) {
        onClose();
      }
      modal.handleClose();
    };

    const renderContent = (
      <div className="min-h-0 flex-1 overflow-y-auto p-4 md:px-6 md:py-4">
        <div className="space-y-4">
          <p className="text-muted-foreground text-sm leading-relaxed">
            Store these recovery codes in a safe place. Each code can only be
            used once to access your account if you lose your authenticator
            device.
          </p>
          <div className="rounded-lg border bg-muted/50 p-4">
            <div className="grid grid-cols-1 gap-2 text-center font-medium font-mono text-sm sm:grid-cols-2">
              {recoveryCodes.map((recoveryCode) => (
                <div
                  key={recoveryCode}
                  className="select-all rounded border bg-background px-3 py-2"
                >
                  {splitIntoChunks(recoveryCode, '-', 5)}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );

    const renderButtons = (
      <div className="flex flex-shrink-0 gap-2 bg-secondary p-4 md:justify-between md:rounded-b-md">
        <Button
          type="button"
          variant="outline"
          size="sm"
          className="w-1/3 md:w-auto"
          onClick={handleClose}
        >
          Close
        </Button>
        <div className="flex flex-1 gap-2 md:flex-initial">
          <Button
            type="button"
            variant="outline"
            size="sm"
            className="flex-1 md:w-auto"
            onClick={handleCopyRecoveryCodes}
          >
            Copy
          </Button>
          <Button
            type="button"
            variant="default"
            size="sm"
            className="flex-1 md:w-auto"
            onClick={handleDownloadRecoveryCodes}
          >
            Download
          </Button>
        </div>
      </div>
    );

    return (
      <>
        {mdUp ? (
          <AlertDialog
            open={modal.visible}
            onOpenChange={modal.handleOpenChange}
          >
            <AlertDialogContent
              className="flex max-w-2xl flex-col gap-0 p-0"
              onAnimationEndCapture={modal.handleAnimationEndCapture}
            >
              <AlertDialogHeader className="flex-shrink-0 border-b px-6 py-5">
                <AlertDialogTitle>{title}</AlertDialogTitle>
                <AlertDialogDescription className="sr-only">
                  {description}
                </AlertDialogDescription>
              </AlertDialogHeader>
              {renderContent}
              {renderButtons}
            </AlertDialogContent>
          </AlertDialog>
        ) : (
          <Drawer open={modal.visible} onOpenChange={modal.handleOpenChange}>
            <DrawerContent className="flex flex-col">
              <DrawerHeader className="flex-shrink-0 border-b text-left">
                <DrawerTitle>{title}</DrawerTitle>
                <DrawerDescription className="sr-only">
                  {description}
                </DrawerDescription>
              </DrawerHeader>
              {renderContent}
              {renderButtons}
            </DrawerContent>
          </Drawer>
        )}
      </>
    );
  }
);
