'use client';

import { MailIcon } from 'lucide-react';
import Link from 'next/link';
import type * as React from 'react';

import { routes } from '@lilypad/shared/routes';
import { Button } from '@lilypad/ui/components/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  type CardProps,
  CardTitle,
} from '@lilypad/ui/components/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@lilypad/ui/components/form';
import { InputWithAdornments } from '@lilypad/ui/components/input-with-adornments';
import { Logo } from '@lilypad/ui/components/logo';
import { cn } from '@lilypad/ui/lib/utils';

import { useSignInEmailStep } from '@/features/auth/model/use-sign-in-email-step';
import { OAuthFooter } from './oauth-footer';

export function SignInEmailStep({
  className,
  ...other
}: CardProps): React.JSX.Element {
  const { methods, onSubmit, onGoogle, canSubmit } = useSignInEmailStep();

  return (
    <div className="rounded-xl border border-border bg-muted/80 shadow-sm dark:bg-muted/20">
      <Card
        className={cn(
          'border-border border-x-0 border-t-0 border-b-xl shadow-none',
          className
        )}
        {...other}
      >
        <CardHeader>
          <Link href={routes.site.Index}>
            <Logo className="pb-2" />
          </Link>
          <CardTitle className="text-base lg:text-lg">
            Sign in to your account
          </CardTitle>
          <CardDescription>Enter your email to continue</CardDescription>
        </CardHeader>

        <CardContent className="flex flex-col gap-4">
          <Form {...methods}>
            <form
              className="flex flex-col gap-4"
              onSubmit={methods.handleSubmit(onSubmit)}
            >
              <FormField
                control={methods.control}
                name="email"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <InputWithAdornments
                        {...field}
                        type="email"
                        autoCapitalize="off"
                        autoComplete="username"
                        placeholder="<EMAIL>"
                        startAdornment={
                          <MailIcon className="size-4 shrink-0" />
                        }
                        disabled={!canSubmit}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Button
                type="submit"
                className="w-full"
                disabled={!canSubmit}
                loading={methods.formState.isSubmitting}
              >
                Continue
              </Button>
            </form>
          </Form>

          <OAuthFooter disabled={!canSubmit} onGoogle={onGoogle} />
        </CardContent>

        <CardFooter className="flex justify-center gap-1 text-muted-foreground text-xs">
          <span>Don't have an account?</span>
          <Link
            href={routes.app.joinRequest.Index}
            className="text-foreground hover:underline"
          >
            Request to join
          </Link>
        </CardFooter>
      </Card>
      <div className="py-4 text-center text-muted-foreground text-xs">
        By signing in, you agree to our <br />
        <Link
          prefetch={false}
          href={routes.site.TermsOfUse}
          className="underline"
        >
          Terms of Use
        </Link>{' '}
        and{' '}
        <Link
          prefetch={false}
          href={routes.site.PrivacyPolicy}
          className="underline"
        >
          Privacy Policy
        </Link>
        .
      </div>
    </div>
  );
}
