'use client';

import NiceModal, { type NiceModalHocProps } from '@ebay/nice-modal-react';

import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@lilypad/ui/components/alert-dialog';
import { Button } from '@lilypad/ui/components/button';
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
} from '@lilypad/ui/components/drawer';
import { toast } from '@lilypad/ui/components/sonner';
import { useMediaQuery } from '@lilypad/ui/hooks/use-media-query';
import { MediaQueries } from '@lilypad/ui/lib/media-queries';

import { useEnhancedModal } from '@/shared/hooks/use-enhanced-modal';
import { disableAuthenticatorApp } from '../api/disable-authenticator-app.action';

const title = 'Disable authenticator app?';
const description =
  'Are you sure you want to continue? This will remove the authenticator app from your account. You can add it back later.';

export type DisableAuthenticatorAppModalProps = NiceModalHocProps & {
  factorId: string;
};

export const DisableAuthenticatorAppModal =
  NiceModal.create<DisableAuthenticatorAppModalProps>(({ factorId }) => {
    const modal = useEnhancedModal();
    const mdUp = useMediaQuery(MediaQueries.MdUp, { ssr: false });

    const handleSubmit = async () => {
      const result = await disableAuthenticatorApp({ factorId });
      if (result?.serverError || result?.validationErrors) {
        toast.error("Couldn't disable authenticator app");
      } else {
        toast.success('Authenticator app disabled');
        modal.handleClose();
      }
    };

    const renderButtons = (
      <>
        <Button
          onClick={modal.handleClose}
          type="button"
          variant="outline"
        >
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}>
          type="button"
          variant="destructive"
          Yes, disable
        </Button>
      </>
    );
    return (
      <>
        {mdUp ? (
          <AlertDialog
            onOpenChange={modal.handleOpenChange}
            open={modal.visible}
          >
            <AlertDialogContent
              className="max-w-sm"
              onAnimationEndCapture={modal.handleAnimationEndCapture}
            >
              <AlertDialogHeader>
                <AlertDialogTitle>{title}</AlertDialogTitle>
                <AlertDialogDescription>{description}</AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>{renderButtons}</AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        ) : (
          <Drawer
            onOpenChange={modal.handleOpenChange}
            open={modal.visible}
          >
            <DrawerContent>
              <DrawerHeader className="text-left">
                <DrawerTitle>{title}</DrawerTitle>
                <DrawerDescription>{description}</DrawerDescription>
              </DrawerHeader>
              <DrawerFooter className="flex-col-reverse pt-4">
                {renderButtons}
              </DrawerFooter>
            </DrawerContent>
          </Drawer>
        )}
      </>
    );
  });
