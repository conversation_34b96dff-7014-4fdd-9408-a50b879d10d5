'use client';

import {
  AlertCircleIcon,
  ArrowRightIcon,
  LockIcon,
  LogOutIcon,
} from 'lucide-react';
import Link from 'next/link';
import type * as React from 'react';

import { routes } from '@lilypad/shared/routes';
import { Alert, AlertDescription } from '@lilypad/ui/components/alert';
import { Button, buttonVariants } from '@lilypad/ui/components/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  type CardProps,
  CardTitle,
} from '@lilypad/ui/components/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@lilypad/ui/components/form';
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
  REGEXP_ONLY_DIGITS,
} from '@lilypad/ui/components/input-otp';
import { Logo } from '@lilypad/ui/components/logo';
import { cn } from '@lilypad/ui/lib/utils';

import { useTotpCode } from '../model/use-totp-code';

export type TotpCodeCardProps = CardProps & {
  token: string;
  expiry: string;
};

export function TotpCodeCard({
  token,
  expiry,
  className,
  ...other
}: TotpCodeCardProps): React.JSX.Element {
  const {
    methods,
    onSubmit,
    onSignInAgain,
    canSubmit,
    isRequestExpired,
    errorMessage,
  } = useTotpCode({
    token,
    expiry,
  });

  return (
    <div className="rounded-xl border border-border bg-muted/80 shadow-sm dark:bg-muted/20">
      <Card
        className={cn(
          'border-border border-x-0 border-t-0 border-b-xl shadow-none',
          className
        )}
        {...other}
      >
        <CardHeader>
          <Link href={routes.site.Index}>
            <Logo className="pb-2" />
          </Link>
          <CardTitle className="text-base lg:text-lg">
            Authenticator code
          </CardTitle>
          <CardDescription>
            Please enter the 6-digit code from your authenticator app.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...methods}>
            <form
              className="flex flex-col gap-4"
              onSubmit={methods.handleSubmit(onSubmit)}
            >
              <input
                type="hidden"
                className="hidden"
                disabled={methods.formState.isSubmitting}
                {...methods.register('token')}
              />
              <FormField
                control={methods.control}
                name="totpCode"
                render={({ field }) => (
                  <FormItem className="flex w-full flex-col items-center space-y-0">
                    <FormControl>
                      <InputOTP
                        {...field}
                        inputMode="numeric"
                        maxLength={6}
                        pattern={REGEXP_ONLY_DIGITS}
                        disabled={methods.formState.isSubmitting}
                        onComplete={methods.handleSubmit(onSubmit)}
                      >
                        <InputOTPGroup className="w-full [&>*]:size-12">
                          <InputOTPSlot index={0} />
                          <InputOTPSlot index={1} />
                          <InputOTPSlot index={2} />
                          <InputOTPSlot index={3} />
                          <InputOTPSlot index={4} />
                          <InputOTPSlot index={5} />
                        </InputOTPGroup>
                      </InputOTP>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              {errorMessage && (
                <Alert
                  variant="destructive"
                  className="flex flex-row items-center gap-2"
                >
                  <AlertCircleIcon className="size-4.5 shrink-0" />
                  <AlertDescription>
                    {errorMessage}
                    {isRequestExpired && (
                      <Button
                        variant="link"
                        className="ml-0.5 h-fit gap-0.5 px-0.5 py-0 text-foreground"
                        onClick={onSignInAgain}
                      >
                        Sign in again
                        <ArrowRightIcon className="size-3 shrink-0" />
                      </Button>
                    )}
                  </AlertDescription>
                </Alert>
              )}
              <Button
                type="submit"
                variant="default"
                className="w-full"
                disabled={!canSubmit}
                loading={methods.formState.isSubmitting}
                onClick={methods.handleSubmit(onSubmit)}
              >
                Submit
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>
      <div className="flex items-center justify-center space-x-4 py-4 text-center text-muted-foreground text-sm">
        <Button
          size="sm"
          variant="outline"
          className="h-fit gap-1 py-1 pl-2 text-muted-foreground hover:text-primary"
        >
          <LogOutIcon className="size-3 shrink-0" />
          Sign out
        </Button>
        <Link
          href={`${routes.app.auth.RecoveryCode}?token=${encodeURIComponent(token)}&expiry=${encodeURIComponent(expiry)}`}
          className={cn(
            buttonVariants({ variant: 'outline', size: 'sm' }),
            'h-fit gap-1 py-1 pl-2 text-muted-foreground hover:text-primary'
          )}
        >
          <LockIcon className="size-3 shrink-0" />
          Lost access
        </Link>
      </div>
    </div>
  );
}
