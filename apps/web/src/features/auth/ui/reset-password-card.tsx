'use client';

import { <PERSON><PERSON><PERSON><PERSON>cleIcon, LockIcon } from 'lucide-react';
import Link from 'next/link';
import type * as React from 'react';

import { routes } from '@lilypad/shared/routes';
import { Alert, AlertDescription } from '@lilypad/ui/components/alert';
import { But<PERSON> } from '@lilypad/ui/components/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  type CardProps,
  CardTitle,
} from '@lilypad/ui/components/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@lilypad/ui/components/form';
import { InputPassword } from '@lilypad/ui/components/input-password';
import { cn } from '@lilypad/ui/lib/utils';

import { useResetPassword } from '@/features/auth/model/use-reset-password';
import { PasswordFormMessage } from '@/shared/ui/auth/password-form-message';
import { Logo } from '@lilypad/ui/components/logo';

export function ResetPasswordCard({
  className,
  ...other
}: CardProps): React.JSX.Element {
  const { methods, password, onSubmit, canSubmit, errorMessage } =
    useResetPassword();

  return (
    <Card className={cn('', className)} {...other}>
      <CardHeader>
        <Link href={routes.site.Index}>
          <Logo className="pb-2" />
        </Link>
        <CardTitle className="text-base lg:text-lg">
          Reset Your Password
        </CardTitle>
        <CardDescription>
          For security purposes, you need to create a new password before
          continuing.
        </CardDescription>
      </CardHeader>

      <CardContent className="flex flex-col gap-4">
        <Form {...methods}>
          <form
            className="flex flex-col gap-4"
            onSubmit={methods.handleSubmit(onSubmit)}
          >
            <div className="flex flex-col">
              <FormField
                control={methods.control}
                name="password"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>New Password</FormLabel>
                    <FormControl>
                      <InputPassword
                        maxLength={72}
                        autoCapitalize="off"
                        autoComplete="new-password"
                        placeholder="Create a secure password"
                        disabled={!canSubmit}
                        startAdornment={
                          <LockIcon className="size-4 shrink-0" />
                        }
                        {...field}
                      />
                    </FormControl>
                    <PasswordFormMessage password={password} />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={methods.control}
              name="confirmPassword"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Confirm New Password</FormLabel>
                  <FormControl>
                    <InputPassword
                      maxLength={72}
                      autoCapitalize="off"
                      autoComplete="new-password"
                      placeholder="Confirm your password"
                      disabled={!canSubmit}
                      startAdornment={<LockIcon className="size-4 shrink-0" />}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {errorMessage && (
              <Alert variant="destructive">
                <div className="flex w-full flex-row items-center gap-2 text-sm">
                  <AlertCircleIcon className="size-[18px] shrink-0" />
                  <AlertDescription>{errorMessage}</AlertDescription>
                </div>
              </Alert>
            )}

            <Button
              type="submit"
              className="w-full"
              disabled={!canSubmit}
              loading={methods.formState.isSubmitting}
            >
              Update Password
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
