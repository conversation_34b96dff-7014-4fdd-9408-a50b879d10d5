'use client';

import Link from 'next/link';
import type * as React from 'react';

import { routes } from '@lilypad/shared/routes';
import { Button } from '@lilypad/ui/components/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  type CardProps,
  CardTitle,
} from '@lilypad/ui/components/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@lilypad/ui/components/form';
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
  REGEXP_ONLY_DIGITS_AND_CHARS,
} from '@lilypad/ui/components/input-otp';
import { Logo } from '@lilypad/ui/components/logo';
import { cn } from '@lilypad/ui/lib/utils';

import { useSignInOtpStep } from '@/features/auth/model/use-sign-in-otp-step';

interface SignInOtpStepProps extends CardProps {
  email: string;
}

export function SignInOtpStep({
  email,
  className,
  ...other
}: SignInOtpStepProps): React.JSX.Element {
  const {
    methods,
    onSubmit,
    canSubmit,
    errorMessage,
    handleResend,
    isResending,
    handleChangeEmail,
  } = useSignInOtpStep(email);

  return (
    <div className="rounded-xl border border-border bg-muted/80 shadow-sm dark:bg-muted/20">
      <Card
        className={cn(
          'border-border border-x-0 border-t-0 border-b-xl shadow-none',
          className
        )}
        {...other}
      >
        <CardHeader>
          <Link href={routes.site.Index}>
            <Logo className="pb-2" />
          </Link>
          <CardTitle className="flex flex-row justify-between text-base lg:text-lg">
            Check your email
            <Button
              variant="outline"
              size="sm"
              className="h-fit py-1 pl-2 text-muted-foreground"
              onClick={handleChangeEmail}
            >
              Change email
            </Button>
          </CardTitle>
          <CardDescription>
            We sent a one-time passcode to your email
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...methods}>
            <form
              className="flex flex-col gap-6"
              onSubmit={methods.handleSubmit(onSubmit)}
            >
              <p className="text-center text-muted-foreground text-xs">
                Enter the 6-digit code from your email to sign in
              </p>
              <FormField
                control={methods.control}
                name="otp"
                render={({ field }) => (
                  <FormItem className="flex w-full flex-col items-center space-y-0">
                    <FormControl>
                      <InputOTP
                        {...field}
                        inputMode="text"
                        maxLength={6}
                        pattern={REGEXP_ONLY_DIGITS_AND_CHARS}
                        disabled={methods.formState.isSubmitting}
                        onComplete={methods.handleSubmit(onSubmit)}
                      >
                        <InputOTPGroup>
                          <InputOTPSlot index={0} />
                          <InputOTPSlot index={1} />
                          <InputOTPSlot index={2} />
                          <InputOTPSlot index={3} />
                          <InputOTPSlot index={4} />
                          <InputOTPSlot index={5} />
                        </InputOTPGroup>
                      </InputOTP>
                    </FormControl>
                    <FormMessage className="text-xs" />
                  </FormItem>
                )}
              />
              <Button
                type="submit"
                variant="default"
                disabled={!canSubmit}
                loading={methods.formState.isSubmitting}
              >
                Verify
              </Button>
            </form>
          </Form>
        </CardContent>
        <CardFooter className="flex justify-center space-x-2 text-muted-foreground text-xs">
          <span>Didn't receive the code?</span>
          <Button
            type="button"
            variant="outline"
            size="sm"
            className="h-fit py-1 pl-2 text-xs"
            disabled={methods.formState.isSubmitting || isResending}
            onClick={handleResend}
          >
            Resend
          </Button>
        </CardFooter>
      </Card>
      <div className="py-4 text-center text-muted-foreground text-xs">
        By signing in, you agree to our <br />
        <Link
          prefetch={false}
          href={routes.site.TermsOfUse}
          className="underline"
        >
          Terms of Use
        </Link>{' '}
        and{' '}
        <Link
          prefetch={false}
          href={routes.site.PrivacyPolicy}
          className="underline"
        >
          Privacy Policy
        </Link>
        .
      </div>
    </div>
  );
}
