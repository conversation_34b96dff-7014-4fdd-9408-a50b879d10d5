'use server';

import { redirect } from 'next/navigation';
import { z } from 'zod';

import { logger } from '@lilypad/shared/logger';
import { routes } from '@lilypad/shared/routes';
import { passwordValidator } from '@lilypad/supabase/auth/password';
import { getSupabaseServerClient } from '@lilypad/supabase/server';

import { rateLimitedActionClient } from '@/shared/safe-action';
import { isRedirectError } from 'next/dist/client/components/redirect-error';

const resetPasswordSchema = z.object({
  password: z
    .string()
    .min(1, 'Password is required')
    .max(72)
    .refine((arg) => passwordValidator.validate(arg).success, {
      message: 'Password does not meet requirements.',
    }),
});

export const resetPasswordAction = rateLimitedActionClient
  .metadata({ actionName: 'resetPassword' })
  .schema(resetPasswordSchema)
  .action(async ({ parsedInput }) => {
    const supabase = await getSupabaseServerClient();

    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError || !user) {
      logger.error(
        { error: userError },
        'Error getting current user for password reset'
      );
      throw new Error('Authentication required');
    }

    const requiresPasswordReset = user.app_metadata?.requiresPasswordReset;

    if (!requiresPasswordReset) {
      logger.warn(
        { userId: user.id },
        'User tried to reset password but flag not set'
      );
      redirect(routes.app.dashboard.Index);
    }

    try {
      const { error: passwordUpdateError } = await supabase.auth.updateUser({
        password: parsedInput.password,
      });

      if (passwordUpdateError) {
        logger.error(
          { error: passwordUpdateError, userId: user.id },
          'Error updating user password'
        );
        throw passwordUpdateError;
      }

      const supabaseAdmin = await getSupabaseServerClient({
        admin: true,
        script: false,
      });

      const { error: metadataUpdateError } =
        await supabaseAdmin.auth.admin.updateUserById(user.id, {
          app_metadata: {
            ...user.app_metadata,
            requiresPasswordReset: false,
          },
        });

      if (metadataUpdateError) {
        logger.error(
          { error: metadataUpdateError, userId: user.id },
          'Error updating user password'
        );
        throw metadataUpdateError;
      }

      logger.info(
        { userId: user.id },
        'Successfully reset user password and cleared flag'
      );

      redirect(routes.app.dashboard.Index);
    } catch (error) {
      if (isRedirectError(error)) {
        redirect(routes.app.dashboard.Index);
      }
      logger.error(
        { error, userId: user.id },
        'Error in password reset process'
      );
      throw new Error('Failed to update password. Please try again.');
    }
  });
