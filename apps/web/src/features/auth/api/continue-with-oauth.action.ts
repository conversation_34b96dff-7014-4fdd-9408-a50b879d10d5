'use server';

import { env } from '@/env';
import { actionClient } from '@/shared/safe-action';
import { baseUrl, routes } from '@lilypad/shared/routes';
import { getSupabaseServerClient } from '@lilypad/supabase/server';
import { redirect } from 'next/navigation';
import { continueWithOAuthInviteSchema } from '../model/schema';

export const continueWithOAuthAction = actionClient
  .metadata({ actionName: 'continueWithOAuth' })
  .schema(continueWithOAuthInviteSchema)
  .action(async ({ parsedInput }) => {
    const supabase = await getSupabaseServerClient();

    const options =
      new URL(baseUrl.App).protocol === 'https:'
        ? {}
        : {
            queryParams: {
              access_type: 'offline',
              prompt: 'consent',
            },
          };

    const callbackUrl = `${env.NEXT_PUBLIC_APP_URL}/${routes.app.auth.Callback}`;

    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: parsedInput.provider,
      options: {
        redirectTo: callbackUrl,
        ...options,
      },
    });

    if (error) {
      throw error;
    }
    redirect(data.url);
  });
