'use server';

import { redirect } from 'next/navigation';

import { dbAdmin, eq } from '@lilypad/db/client';
import { usersTable } from '@lilypad/db/schema';
import { NotFoundError } from '@lilypad/shared/errors';
import { logger } from '@lilypad/shared/logger';
import { routes } from '@lilypad/shared/routes';
import { getSupabaseServerClient } from '@lilypad/supabase/server';

import { signInOtpSchema } from '@/entities/users/model/auth/sign-in-otp.schema';
import { rateLimitedActionClient } from '@/shared/safe-action';

export const verifySignInOtpAction = rateLimitedActionClient
  .metadata({ actionName: 'verifySignInOtp' })
  .schema(signInOtpSchema)
  .action(async ({ parsedInput: { email, otp } }) => {
    const normalizedEmail = email.toLowerCase();

    const supabase = await getSupabaseServerClient();

    const { error, data: auth } = await supabase.auth.verifyOtp({
      email: normalizedEmail,
      token: otp,
      type: 'magiclink',
    });

    if (error || !auth.user) {
      logger.error(
        { error, email: normalizedEmail },
        'Error verifying sign-in OTP'
      );

      if (error?.code === 'otp_expired') {
        throw new Error(
          'Your sign-in code has expired. Please request a new one.'
        );
      }

      if (error?.code === 'invalid_otp') {
        throw new Error('Invalid code. Please check your email and try again.');
      }

      throw error || new Error('Failed to verify sign-in code');
    }

    const userId = auth.user.id;
    logger.info(
      { email: normalizedEmail, userId },
      'Sign-in OTP verified successfully'
    );

    // Verify user exists in our database
    const [user] = await dbAdmin
      .select({
        id: usersTable.id,
        isOnboarded: usersTable.isOnboarded,
      })
      .from(usersTable)
      .where(eq(usersTable.id, userId))
      .limit(1);

    if (!user) {
      logger.error(
        { userId, email: normalizedEmail },
        'User authenticated but not found in database'
      );
      throw new NotFoundError('Account not found. Please contact support.');
    }

    const mfaSetupSkipped = auth.user.app_metadata?.mfaSetupSkipped === true;

    if (!mfaSetupSkipped) {
      return redirect(routes.app.auth.totp.Index);
    }

    // Redirect based on onboarding status
    if (!user.isOnboarded) {
      return redirect(routes.app.onboarding.Index);
    }

    return redirect(routes.app.dashboard.Index);
  });
