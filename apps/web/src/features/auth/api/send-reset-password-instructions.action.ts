'use server';

import { getSupabaseServerClient } from '@lilypad/supabase/server';
import { logger } from '@lilypad/shared/logger';
import { routes } from '@lilypad/shared/routes';

import { rateLimitedActionClient } from '@/shared/safe-action';
import { type EmailSchema, emailSchema } from '@/entities/users/model/auth';

export const sendResetPasswordInstructionsAction = rateLimitedActionClient
  .metadata({ actionName: 'sendResetPasswordInstructions' })
  .schema(emailSchema)
  .action(async ({ parsedInput }: { parsedInput: EmailSchema }) => {
    const supabase = await getSupabaseServerClient();
    const email = parsedInput.email.toLowerCase();

    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: routes.app.auth.resetPassword.Request,
    });

    if (error) {
      logger.error({ error }, 'Error sending reset password instructions');
      throw new Error(error.message);
    }

    logger.info('Reset password instructions sent successfully!');
  });
