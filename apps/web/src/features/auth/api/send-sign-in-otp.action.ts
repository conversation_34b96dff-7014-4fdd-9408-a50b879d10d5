'use server';

import { returnValidationErrors } from 'next-safe-action';
import { logger } from '@lilypad/shared/logger';
import { getSupabaseServerClient } from '@lilypad/supabase/server';
import { dbAdmin, eq } from '@lilypad/db/client';
import { usersTable } from '@lilypad/db/schema';

import { rateLimitedActionClient } from '@/shared/safe-action';
import { signInEmailSchema } from '@/entities/users/model/auth/sign-in-email.schema';

export const sendSignInOtpAction = rateLimitedActionClient
  .metadata({ actionName: 'sendSignInOtp' })
  .schema(signInEmailSchema)
  .action(async ({ parsedInput: { email } }) => {
    const normalizedEmail = email.toLowerCase();

    // Check if user exists in our database
    const [existingUser] = await dbAdmin
      .select({ id: usersTable.id, email: usersTable.email })
      .from(usersTable)
      .where(eq(usersTable.email, normalizedEmail))
      .limit(1);

    if (!existingUser) {
      logger.warn(
        { email: normalizedEmail },
        'Sign-in OTP requested for non-existent user'
      );

      return returnValidationErrors(signInEmailSchema, {
        email: {
          _errors: ['No account found with this email address'],
        },
      });
    }

    // Send OTP via Supabase (without auto-creation)
    const supabase = await getSupabaseServerClient();
    const { error } = await supabase.auth.signInWithOtp({
      email: normalizedEmail,
      options: {
        shouldCreateUser: false,
      },
    });

    if (error) {
      logger.error(
        { error, email: normalizedEmail },
        'Error sending sign-in OTP'
      );

      if (error.message.includes('User not found')) {
        return returnValidationErrors(signInEmailSchema, {
          email: {
            _errors: ['No account found with this email address'],
          },
        });
      }

      throw error;
    }

    logger.info(
      { email: normalizedEmail, userId: existingUser.id },
      'Sign-in OTP sent successfully'
    );

    return { success: true };
  });
