'use server';

import { getSupabaseServerClient } from '@lilypad/supabase/server';
import { logger } from '@lilypad/shared/logger';
import { redirect } from 'next/navigation';

import { rateLimitedActionClient } from '@/shared/safe-action';
import { passThroughCredentialsSchema } from '@/entities/users/model';
import { routes } from '@lilypad/shared/routes';

export const signInWithCredentialsAction = rateLimitedActionClient
  .metadata({ actionName: 'signInWithCredentials' })
  .schema(passThroughCredentialsSchema)
  .action(async ({ parsedInput }) => {
    const supabase = await getSupabaseServerClient();

    const { data, error } = await supabase.auth.signInWithPassword({
      email: parsedInput.email,
      password: parsedInput.password,
    });

    if (error) {
      logger.error({ error }, 'Error signing in with credentials');
      throw error;
    }

    logger.info(
      { userId: data.user?.id },
      'Successfully signed in with credentials'
    );

    if (parsedInput.redirectTo) {
      redirect(parsedInput.redirectTo);
    } else {
      redirect(routes.app.dashboard.Index);
    }
  });
