'use server';

import { logger } from '@lilypad/shared/logger';
import { getSupabaseServerClient } from '@lilypad/supabase/server';
import { returnValidationErrors } from 'next-safe-action';

import { rateLimitedActionClient } from '@/shared/safe-action';
import { emailSchema } from '@/entities/users/model';

export const resendEmailConfirmationAction = rateLimitedActionClient
  .metadata({ actionName: 'resendEmailConfirmation' })
  .schema(emailSchema)
  .action(async ({ parsedInput }) => {
    const email = parsedInput.email.toLowerCase();
    const supabase = await getSupabaseServerClient();

    const { error } = await supabase.auth.resend({
      email,
      type: 'signup',
    });

    if (error) {
      logger.error({ error, email }, 'Error resending email confirmation link');

      if (error?.code === 'over_email_send_rate_limit') {
        return returnValidationErrors(emailSchema, {
          email: {
            _errors: ['Resent too many times. Please try again later.'],
          },
        });
      }

      throw error;
    }

    logger.info({ email }, 'Successfully resent email confirmation link');
  });
