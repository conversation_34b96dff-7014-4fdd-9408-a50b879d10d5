'use server';

import { isBefore, isValid } from 'date-fns';
import { returnValidationErrors } from 'next-safe-action';
import { redirect } from 'next/navigation';
import { authenticator } from 'otplib';

import { authUsersTable } from '@lilypad/db/auth-schema';
import { dbAdmin, eq } from '@lilypad/db/client';
import { logger } from '@lilypad/shared/logger';
import { routes } from '@lilypad/shared/routes';
import {
  symmetricDecrypt,
  symmetricEncrypt,
} from '@lilypad/supabase/auth/encryption';
import { getSupabaseServerClient } from '@lilypad/supabase/server';

import { env } from '@/env';
import { rateLimitedActionClient } from '@/shared/safe-action';
import { submitRecoveryCodeSchema } from '../model/schema';

export const submitRecoveryCode = rateLimitedActionClient
  .metadata({ actionName: 'submitRecoveryCode' })
  .schema(submitRecoveryCodeSchema)
  .action(async ({ parsedInput }) => {
    const supabase = await getSupabaseServerClient();
    const key = env.SUPABASE_RECOVERY_CODE_SECRET;
    const userId = symmetricDecrypt(parsedInput.token, key);
    const expiry = new Date(symmetricDecrypt(parsedInput.expiry, key));

    if (!isValid(expiry) || isBefore(expiry, new Date())) {
      return returnValidationErrors(submitRecoveryCodeSchema, {
        _errors: ['RECOVERY_CODE_EXPIRED'],
      });
    }

    const [user] = await dbAdmin
      .select({ rawAppMetaData: authUsersTable.rawAppMetaData })
      .from(authUsersTable)
      .where(eq(authUsersTable.id, userId))
      .limit(1);

    if (!user) {
      logger.error({ userId }, '❌ ERROR USER NOT FOUND');
      throw new Error('Invalid credentials');
    }

    if (
      !(user.rawAppMetaData?.mfaRecoveryCodes && user.rawAppMetaData?.mfaSecret)
    ) {
      return returnValidationErrors(submitRecoveryCodeSchema, {
        _errors: ['Missing recovery codes.'],
      });
    }

    const recoveryCodes = JSON.parse(
      symmetricDecrypt(String(user.rawAppMetaData.mfaRecoveryCodes), key)
    ) as (string | null)[];

    // Check if user-supplied code matches one
    const index = recoveryCodes.indexOf(
      parsedInput.recoveryCode.replaceAll('-', '')
    );
    if (index === -1) {
      logger.error({ userId }, '❌ ERROR INCORRECT RECOVERY CODE');
      return returnValidationErrors(submitRecoveryCodeSchema, {
        _errors: ['The recovery code is incorrect.'],
      });
    }

    // Delete verified recovery code and re-encrypt remaining
    recoveryCodes[index] = null;
    const activeRecoveryCodes = recoveryCodes.filter(
      (code): code is string => code !== null
    );
    await dbAdmin
      .update(authUsersTable)
      .set({
        rawAppMetaData: {
          ...user.rawAppMetaData,
          mfaRecoveryCodes: symmetricEncrypt(
            JSON.stringify(activeRecoveryCodes),
            key
          ),
        },
      })
      .where(eq(authUsersTable.id, userId));

    const secret = symmetricDecrypt(String(user.rawAppMetaData.mfaSecret), key);
    const totpCode = authenticator.generate(secret);

    logger.info({ totpCode }, '✅ TOTP CODE GENERATED');

    const { data: factors, error: factorsError } =
      await supabase.auth.mfa.listFactors();
    if (factorsError) {
      logger.error({ factorsError }, '❌ ERROR LISTING FACTORS');
      throw factorsError;
    }

    const { error } = await supabase.auth.mfa.challengeAndVerify({
      factorId: factors.totp[0].id,
      code: totpCode,
    });

    if (error) {
      logger.error({ error }, '❌ ERROR VERIFYING TOTP');
      throw error;
    }

    redirect(routes.app.dashboard.Index);
  });
