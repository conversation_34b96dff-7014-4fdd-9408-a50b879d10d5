'use server';

import { dbAdmin, eq } from '@lilypad/db/client';
import { usersTable } from '@lilypad/db/schema';
import { EmailService } from '@lilypad/email/service';
import { NotFoundError } from '@lilypad/shared/errors';
import { logger } from '@lilypad/shared/logger';
import { routes } from '@lilypad/shared/routes';
import { getSupabaseServerClient } from '@lilypad/supabase/server';
import { redirect } from 'next/navigation';
import { verifyEmailWithOtpSchema } from '@/entities/users/model/auth/verify-email-with-otp.schema';
import { rateLimitedActionClient } from '@/shared/safe-action';

export const verifyEmailWithOtpAction = rateLimitedActionClient
  .metadata({ actionName: 'verifyEmailWithOtp' })
  .schema(verifyEmailWithOtpSchema)
  .action(async ({ parsedInput: { otp, email } }) => {
    const supabase = await getSupabaseServerClient();

    const { error, data: auth } = await supabase.auth.verifyOtp({
      email,
      type: 'email',
      token: otp,
    });

    if (error || !auth.user) {
      logger.error({ error, email }, 'Error in verifying OTP');

      if (error?.code === 'otp_expired') {
        redirect(`${routes.app.auth.verifyEmail.Expired}?email=${email}`);
      }

      throw error;
    }

    const userId = auth.user.id;

    logger.info({ email, userId }, 'OTP verified successfully');

    const [user] = await dbAdmin
      .select({
        firstName: usersTable.firstName,
        isOnboarded: usersTable.isOnboarded,
      })
      .from(usersTable)
      .where(eq(usersTable.id, userId));

    if (!user) {
      logger.error({ email, userId }, 'User not found');
      throw new NotFoundError('User not found');
    }

    try {
      await EmailService.sendWelcomeEmail({
        recipient: email,
        name: user.firstName,
        getStartedLink: routes.app.Index,
      });
    } catch (emailError) {
      logger.error({ emailError }, 'Error sending welcome email');
    }

    if (!user.isOnboarded) {
      return redirect(routes.app.onboarding.Index);
    }

    return redirect(routes.app.Index);
  });
