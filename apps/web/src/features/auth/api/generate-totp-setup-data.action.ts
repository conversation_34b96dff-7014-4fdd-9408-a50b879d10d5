'use server';

import { APP_NAME } from '@lilypad/shared';
import { logger } from '@lilypad/shared/logger';

import { authActionClient } from '@/shared/safe-action';
import { getSupabaseServerClient } from '@lilypad/supabase/server';

export const generateTotpSetupData = authActionClient
  .metadata({ actionName: 'generateTotpSetupData' })
  .action(async ({ ctx: { user } }) => {
    const supabase = await getSupabaseServerClient();

    const { data: factors, error } = await supabase.auth.mfa.listFactors();
    if (error) {
      logger.error({ error }, '❌ ERROR LISTING FACTORS');
      throw error;
    }

    const existingFactor = factors.all?.find(
      (factor) => factor.factor_type === 'totp'
    );

    if (existingFactor && existingFactor?.status === 'verified') {
      throw new Error('TOTP already enabled');
    }

    if (existingFactor && existingFactor?.status === 'unverified') {
      const { error: unenrollError } = await supabase.auth.mfa.unenroll({
        factorId: existingFactor.id,
      });
      if (unenrollError) {
        logger.error({ unenrollError }, '❌ ERROR UNENROLLING TOTP');
        throw unenrollError;
      }
    }

    const { data, error: enrollError } = await supabase.auth.mfa.enroll({
      friendlyName: user?.email ?? '',
      factorType: 'totp',
      issuer: APP_NAME,
    });

    if (enrollError) {
      logger.error({ enrollError }, '❌ ERROR ENROLLING TOTP');
      throw enrollError;
    }

    return {
      accountName: user?.email ?? '',
      issuer: APP_NAME,
      secret: data.totp.secret,
      factorId: data.id,
      qrCode: data.totp.qr_code,
    };
  });
