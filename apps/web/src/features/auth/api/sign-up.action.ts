'use server';

import { redirect } from 'next/navigation';
import { returnValidationErrors } from 'next-safe-action';

import { routes } from '@lilypad/shared/routes';
import { logger } from '@lilypad/shared/logger';
import { getSupabaseServerClient } from '@lilypad/supabase/server';
import { dbAdmin } from '@lilypad/db/client';
import { usersTable } from '@lilypad/db/schema';

import { rateLimitedActionClient } from '@/shared/safe-action';
import { signUpSchema } from '@/entities/users/model/auth/sign-up.schema';

export const signUpAction = rateLimitedActionClient
  .metadata({ actionName: 'signUp' })
  .schema(signUpSchema)
  .action(
    async ({ parsedInput: { firstName, lastName, password, ...data } }) => {
      const email = data.email.toLowerCase();

      const supabase = await getSupabaseServerClient();

      const { error, data: response } = await supabase.auth.signUp({
        email,
        password,
      });

      if (error || !response.user) {
        logger.error({ error, email }, 'Error signing up');

        if (error?.code === 'user_already_exists') {
          return returnValidationErrors(signUpSchema, {
            email: {
              _errors: ['Email is already in use'],
            },
          });
        }
        throw error;
      }

      const userId = response.user.id;

      await dbAdmin.insert(usersTable).values({
        id: userId,
        firstName,
        lastName,
        email,
      });

      logger.info({ userId }, 'User successfully signed up');

      return redirect(
        `${routes.app.auth.verifyEmail.Index}?email=${encodeURIComponent(email)}`
      );
    }
  );
