'use server';

import { authUsersTable } from '@lilypad/db/auth-schema';
import { dbAdmin, eq } from '@lilypad/db/client';
import { logger } from '@lilypad/shared/logger';
import { symmetricEncrypt } from '@lilypad/supabase/auth/encryption';
import { returnValidationErrors } from 'next-safe-action';
import { revalidateTag } from 'next/cache';
import crypto from 'node:crypto';

import { env } from '@/env';
import { caching, UserCacheKey } from '@/shared/caching';
import { authActionClient } from '@/shared/safe-action';
import { getSupabaseServerClient } from '@lilypad/supabase/server';
import { enableAuthenticatorAppSchema } from '../model/schema';

export const enableAuthenticatorApp = authActionClient
  .metadata({ actionName: 'enableAuthenticatorApp' })
  .schema(enableAuthenticatorAppSchema)
  .action(async ({ parsedInput, ctx: { user } }) => {
    const userId = user?.id as string;
    const supabase = await getSupabaseServerClient();

    const { error } = await supabase.auth.mfa.challengeAndVerify({
      factorId: parsedInput.factorId,
      code: parsedInput.totpCode,
    });

    if (error) {
      logger.error({ error }, '❌ ERROR VERIFYING TOTP');
      if (error.code === 'mfa_verification_failed') {
        return returnValidationErrors(enableAuthenticatorAppSchema, {
          _errors: ['The TOTP code is not correct'],
        });
      }
      throw error;
    }

    const recoveryCodes = Array.from(new Array(10), () =>
      crypto.randomBytes(5).toString('hex')
    );

    const key = env.SUPABASE_RECOVERY_CODE_SECRET;

    await dbAdmin.transaction(async (tx) => {
      const [authUser] = await tx
        .select({ rawAppMetaData: authUsersTable.rawAppMetaData })
        .from(authUsersTable)
        .where(eq(authUsersTable.id, userId))
        .limit(1);

      await tx
        .update(authUsersTable)
        .set({
          rawAppMetaData: {
            ...authUser?.rawAppMetaData,
            mfaSecret: symmetricEncrypt(parsedInput.secret, key),
            mfaRecoveryCodes: symmetricEncrypt(
              JSON.stringify(recoveryCodes),
              key
            ),
          },
        })
        .where(eq(authUsersTable.id, userId));
    });

    revalidateTag(
      caching.createUserTag(UserCacheKey.MultiFactorAuthentication, userId)
    );

    return { recoveryCodes };
  });
