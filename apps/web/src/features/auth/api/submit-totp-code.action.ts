'use server';

import { isBefore, isValid } from 'date-fns';
import { returnValidationErrors } from 'next-safe-action';
import { redirect } from 'next/navigation';

import { dbAdmin, eq } from '@lilypad/db/client';
import { usersTable } from '@lilypad/db/schema';
import { logger } from '@lilypad/shared/logger';
import { routes } from '@lilypad/shared/routes';
import { symmetricDecrypt } from '@lilypad/supabase/auth/encryption';
import { getSupabaseServerClient } from '@lilypad/supabase/server';

import { env } from '@/env';
import { rateLimitedActionClient } from '@/shared/safe-action';
import { submitTotpCodeSchema } from '../model/schema';

export const submitTotpCode = rateLimitedActionClient
  .metadata({ actionName: 'submitTotpCode' })
  .schema(submitTotpCodeSchema)
  .action(async ({ parsedInput }) => {
    const supabase = await getSupabaseServerClient();
    const redirectTo = routes.app.dashboard.Index;

    const key = env.SUPABASE_RECOVERY_CODE_SECRET;
    const userId = symmetricDecrypt(parsedInput.token, key);
    const expiry = new Date(symmetricDecrypt(parsedInput.expiry, key));

    if (!isValid(expiry) || isBefore(expiry, new Date())) {
      return returnValidationErrors(submitTotpCodeSchema, {
        _errors: ['REQUEST_EXPIRED'],
      });
    }

    const [user] = await dbAdmin
      .select({ id: usersTable.id })
      .from(usersTable)
      .where(eq(usersTable.id, userId))
      .limit(1);

    if (!user) {
      logger.error({ userId }, '❌ ERROR USER NOT FOUND');
      throw new Error('Invalid credentials');
    }

    const { data: factors, error } = await supabase.auth.mfa.listFactors();
    const factorId = factors?.all?.find(
      (factor) => factor.factor_type === 'totp'
    )?.id;
    if (error || !factorId) {
      redirect(redirectTo);
    }

    const { error: verificationError } =
      await supabase.auth.mfa.challengeAndVerify({
        code: parsedInput.totpCode,
        factorId,
      });

    if (verificationError) {
      logger.error({ verificationError }, '❌ ERROR VERIFYING TOTP');
      if (verificationError.code === 'mfa_verification_failed') {
        return returnValidationErrors(submitTotpCodeSchema, {
          _errors: ['INCORRECT_TOTP_CODE'],
        });
      }
      throw verificationError;
    }

    logger.info('✅ TOTP VERIFIED');

    redirect(redirectTo);
  });
