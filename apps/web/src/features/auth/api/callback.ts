import type { EmailOtpType } from '@lilypad/supabase';
import type { NextRequest } from 'next/server';

import { getURLFromRedirectError } from 'next/dist/client/components/redirect';
import { isRedirectError } from 'next/dist/client/components/redirect-error';
import { redirect } from 'next/navigation';

import { logger } from '@lilypad/shared/logger';
import { routes } from '@lilypad/shared/routes';
import { getSupabaseServerClient } from '@lilypad/supabase/server';

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url);
  const searchParams = requestUrl.searchParams;

  const authCode = searchParams.get('code');
  const tokenHash = searchParams.get('tokenHash');
  const type = searchParams.get('type') as EmailOtpType;
  const nextUrl = searchParams.get('next') ?? routes.app.Index;

  try {
    if (authCode) {
      const supabase = await getSupabaseServerClient();
      const { error, data } =
        await supabase.auth.exchangeCodeForSession(authCode);

      if (error) {
        logger.error({ error }, 'Error exchanging code for session:');
        throw error;
      }

      redirect(nextUrl);
    }

    if (tokenHash && type) {
      logger.info({ type, tokenHash }, 'Attempting to verify token hash...');

      const supabase = await getSupabaseServerClient();

      const { error } = await supabase.auth.verifyOtp({
        type,
        token_hash: tokenHash,
      });

      if (error) {
        logger.error({ error }, 'Error verifying OTP:');
        throw error;
      }
      redirect(nextUrl);
    }

    redirect(nextUrl);
  } catch (error) {
    if (isRedirectError(error)) {
      redirect(getURLFromRedirectError(error));
    }

    logger.error({ error }, 'Auth verification error:');
    const message = getAuthErrorMessage((error as Error).message);
    const redirectUrl = `${routes.app.auth.Error}?error=${message}`;
    redirect(redirectUrl);
  }
}

function isVerifierError(error: string) {
  return error.includes('both auth code and code verifier should be non-empty');
}

function getAuthErrorMessage(error: string) {
  return isVerifierError(error)
    ? "It looks like you're trying to sign in using a different browser than the one you used to request the sign in link. Please try again using the same browser."
    : 'We could not authenticate you. Please try again.';
}
