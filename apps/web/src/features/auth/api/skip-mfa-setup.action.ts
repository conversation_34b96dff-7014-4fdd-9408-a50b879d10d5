'use server';

import { eq } from 'drizzle-orm';
import { redirect } from 'next/navigation';

import { authActionClient } from '@/shared/safe-action';
import { authUsersTable } from '@lilypad/db/auth-schema';
import { dbAdmin } from '@lilypad/db/client';
import { routes } from '@lilypad/shared/routes';

export const skipMfaSetup = authActionClient
  .metadata({ actionName: 'skipMfaSetup' })
  .action(async ({ ctx: { user } }) => {
    if (!user?.id) {
      throw new Error('skipMfaSetup called without an authenticated user');
    }

    const userId = user.id;

    await dbAdmin.transaction(async (tx) => {
      const [authUser] = await tx
        .select({ rawAppMetaData: authUsersTable.rawAppMetaData })
        .from(authUsersTable)
        .where(eq(authUsersTable.id, userId))
        .limit(1);

      await tx
        .update(authUsersTable)
        .set({
          rawAppMetaData: {
            ...authUser?.rawAppMetaData,
            mfaSetupSkipped: true,
          },
        })
        .where(eq(authUsersTable.id, userId));
    });

    redirect(routes.app.dashboard.Index);
  });
