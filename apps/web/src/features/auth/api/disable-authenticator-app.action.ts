'use server';

import { authUsersTable } from '@lilypad/db/auth-schema';
import { dbAdmin, eq } from '@lilypad/db/client';
import { logger } from '@lilypad/shared/logger';
import { revalidateTag } from 'next/cache';
import { z } from 'zod';

import { caching, UserCacheKey } from '@/shared/caching';
import { authActionClient } from '@/shared/safe-action';
import { getSupabaseServerClient } from '@lilypad/supabase/server';

export const disableAuthenticatorApp = authActionClient
  .schema(
    z.object({
      factorId: z.string().min(1, 'Factor ID is required'),
    })
  )
  .metadata({ actionName: 'disableAuthenticatorApp' })
  .action(async ({ parsedInput, ctx: { user } }) => {
    const userId = user?.id as string;
    const supabase = await getSupabaseServerClient();

    const { error } = await supabase.auth.mfa.unenroll({
      factorId: parsedInput.factorId,
    });
    if (error) {
      logger.error({ error }, '❌ ERROR UNENROLLING TOTP');
      throw error;
    }
    await dbAdmin.transaction(async (tx) => {
      const [authUser] = await tx
        .select({ rawAppMetaData: authUsersTable.rawAppMetaData })
        .from(authUsersTable)
        .where(eq(authUsersTable.id, userId))
        .limit(1);

      await tx
        .update(authUsersTable)
        .set({
          rawAppMetaData: {
            ...authUser?.rawAppMetaData,
            mfaSecret: undefined,
            mfaRecoveryCodes: undefined,
          },
        })
        .where(eq(authUsersTable.id, userId));
    });
    revalidateTag(
      caching.createUserTag(UserCacheKey.MultiFactorAuthentication, userId)
    );
  });
