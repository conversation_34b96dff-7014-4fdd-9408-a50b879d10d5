'use client';

import { FeedbackTypeEnum } from '@lilypad/db/enums';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@lilypad/ui/components/dialog';
import { If } from '@lilypad/ui/components/if';
import { useSidebar } from '@lilypad/ui/components/sidebar';
import {
  AnimatedTabs,
  AnimatedTabsContent,
  AnimatedTabsContentWrapper,
  AnimatedTabsList,
  AnimatedTabsTrigger,
} from '@lilypad/ui/components/tabs.animated';
import { Bug, MessageSquare, SparklesIcon } from 'lucide-react';
import React, {
  type PropsWithChildren,
  useCallback,
  useMemo,
  useState,
} from 'react';

import {
  FeedbackProvider,
  useFeedbackContext,
} from '../model/feedback-context';
import { FeedbackDialogFooter } from './components/feedback-dialog-footer';
import { BugFeedbackForm } from './forms/bug-feedback-form';
import { FeatureRequestForm } from './forms/feature-request-form';
import { GeneralFeedbackForm } from './forms/general-feedback-form';

const tabs = [
  {
    label: 'General',
    value: FeedbackTypeEnum.GENERAL,
    icon: <MessageSquare className="size-4" />,
  },
  {
    label: 'Bug',
    value: FeedbackTypeEnum.BUG,
    icon: <Bug className="size-4" />,
  },
  {
    label: 'Feature',
    value: FeedbackTypeEnum.FEATURE_REQUEST,
    icon: <SparklesIcon className="size-4" />,
  },
];

const FeedbackDialogContent = React.memo(
  function FeedbackDialogContentComponent({
    setFeedbackOpen,
  }: {
    setFeedbackOpen: (open: boolean) => void;
  }) {
    const [activeTab, setActiveTab] = useState(FeedbackTypeEnum.GENERAL);
    const { submitActiveForm, clearActiveForm, isExecuting, activeFormLabel } =
      useFeedbackContext();

    const tabTriggers = useMemo(
      () =>
        tabs.map((tab) => (
          <AnimatedTabsTrigger key={tab.value} value={tab.value}>
            {tab.icon}
            <span>{tab.label}</span>
          </AnimatedTabsTrigger>
        )),
      []
    );

    const onCancel = useCallback(() => {
      setFeedbackOpen(false);
    }, [setFeedbackOpen]);

    return (
      <>
        <AnimatedTabs
          className="flex w-full flex-1 flex-col overflow-y-auto"
          defaultValue={FeedbackTypeEnum.GENERAL}
          onValueChange={(value) => setActiveTab(value as FeedbackTypeEnum)}
          tabs={tabs}
          value={activeTab}
        >
          <DialogHeader className="flex-shrink-0 px-6 pt-6">
            <DialogTitle>Share your feedback</DialogTitle>
            <DialogDescription className="text-xs">
              We'd love to hear from you! Choose a category below and share your
              thoughts.
            </DialogDescription>
            <AnimatedTabsList className="my-2">{tabTriggers}</AnimatedTabsList>
          </DialogHeader>

          <AnimatedTabsContentWrapper className="flex-1 overflow-y-auto px-6 pt-4">
            <AnimatedTabsContent value={FeedbackTypeEnum.BUG}>
              <BugFeedbackForm isActive={activeTab === FeedbackTypeEnum.BUG} />
            </AnimatedTabsContent>

            <AnimatedTabsContent value={FeedbackTypeEnum.FEATURE_REQUEST}>
              <FeatureRequestForm
                isActive={activeTab === FeedbackTypeEnum.FEATURE_REQUEST}
              />
            </AnimatedTabsContent>

            <AnimatedTabsContent value={FeedbackTypeEnum.GENERAL}>
              <GeneralFeedbackForm
                isActive={activeTab === FeedbackTypeEnum.GENERAL}
              />
            </AnimatedTabsContent>
          </AnimatedTabsContentWrapper>
        </AnimatedTabs>

        <FeedbackDialogFooter
          isExecuting={isExecuting}
          onCancel={onCancel}
          onClear={clearActiveForm}
          onSubmit={submitActiveForm}
          submitLabel={activeFormLabel}
        />
      </>
    );
  }
);

interface FeedbackDialogProps extends PropsWithChildren {}

export const FeedbackDialog = React.memo(function FeedbackDialogComponent({
  children,
}: FeedbackDialogProps) {
  const { feedbackOpen, setFeedbackOpen } = useSidebar();

  const handleOpenChange = useCallback(
    (open: boolean) => {
      setFeedbackOpen(open);
    },
    [setFeedbackOpen]
  );

  return (
    <Dialog onOpenChange={handleOpenChange} open={feedbackOpen}>
      <If condition={children}>
        <DialogTrigger asChild>{children}</DialogTrigger>
      </If>
      <DialogContent
        className="flex max-h-[85dvh] min-h-[85dvh] max-w-2xl flex-col p-0"
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
        overlayClassName="backdrop-blur-sm"
      >
        <FeedbackProvider>
          <FeedbackDialogContent setFeedbackOpen={setFeedbackOpen} />
        </FeedbackProvider>
      </DialogContent>
    </Dialog>
  );
});
