'use client';

import { FeedbackTypeEnum } from '@lilypad/db/enums';
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@lilypad/ui/components/form';
import { Textarea } from '@lilypad/ui/components/textarea';
import React, { useMemo } from 'react';

import { featureRequestSchema } from '../../model/schemas';
import { FeedbackFormWrapper } from '../components/feedback-form-wrapper';

interface FeatureRequestFormProps {
  onSuccess?: () => void;
  isActive?: boolean;
}

export const FeatureRequestForm = React.memo(
  function FeatureRequestFormComponent({
    onSuccess,
    isActive,
  }: FeatureRequestFormProps) {
    const defaultValues = useMemo(
      () => ({
        type: FeedbackTypeEnum.FEATURE_REQUEST,
        description: '',
      }),
      []
    );

    return (
      <FeedbackFormWrapper
        action="submitFeatureRequest"
        defaultValues={defaultValues}
        errorMessage="Failed to submit feature request"
        isActive={isActive}
        onSuccess={onSuccess}
        schema={featureRequestSchema}
        submitLabel="Submit Feature Request"
        successMessage="Feature request submitted successfully!"
      >
        {({ form, isExecuting }) => (
          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Description</FormLabel>
                <FormDescription className="text-xs">
                  Please describe the feature in detail. Explain how it would
                  help you and what problem it solves.
                </FormDescription>
                <FormControl>
                  <Textarea
                    className="min-h-[120px] resize-none"
                    {...field}
                    disabled={isExecuting}
                  />
                </FormControl>
                <FormMessage className="text-xs" />
              </FormItem>
            )}
          />
        )}
      </FeedbackFormWrapper>
    );
  }
);
