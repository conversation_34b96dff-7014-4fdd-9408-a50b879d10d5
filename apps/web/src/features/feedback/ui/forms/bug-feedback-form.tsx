'use client';

import {
  FeedbackIssueTypeEnum,
  FeedbackIssueTypeEnumMap,
  FeedbackTypeEnum,
} from '@lilypad/db/enums';
import {
  Choicebox,
  ChoiceboxItem,
  ChoiceboxItemHeader,
  ChoiceboxItemTitle,
} from '@lilypad/ui/components/choicebox';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@lilypad/ui/components/form';
import { Textarea } from '@lilypad/ui/components/textarea';
import React, { useMemo } from 'react';

import { bugFeedbackSchema } from '../../model/schemas';
import { FeedbackFormWrapper } from '../components/feedback-form-wrapper';

interface BugFeedbackFormProps {
  onSuccess?: () => void;
  isActive?: boolean;
}

export const BugFeedbackForm = React.memo(function BugFeedbackFormComponent({
  onSuccess,
  isActive,
}: BugFeedbackFormProps) {
  const defaultValues = useMemo(
    () => ({
      type: FeedbackTypeEnum.BUG,
      description: '',
      issueType: FeedbackIssueTypeEnum.OTHER,
    }),
    []
  );

  return (
    <FeedbackFormWrapper
      action="submitBugFeedback"
      defaultValues={defaultValues}
      errorMessage="Failed to submit bug report!"
      isActive={isActive}
      onSuccess={onSuccess}
      schema={bugFeedbackSchema}
      submitLabel="Submit Bug Report"
      successMessage="Bug report submitted successfully!"
    >
      {({ form, isExecuting }) => (
        <>
          <FormField
            control={form.control}
            name="issueType"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Issue Type</FormLabel>
                <FormControl>
                  <Choicebox
                    className="grid grid-cols-4 gap-3"
                    disabled={isExecuting}
                    onValueChange={field.onChange}
                    value={field.value}
                  >
                    {Object.entries(FeedbackIssueTypeEnumMap).map(
                      ([value, label]) => (
                        <ChoiceboxItem
                          className="items-center p-2 text-center "
                          key={value}
                          value={value}
                        >
                          <ChoiceboxItemHeader className="gap-0">
                            <ChoiceboxItemTitle className="mx-auto font-normal text-xs">
                              {label}
                            </ChoiceboxItemTitle>
                          </ChoiceboxItemHeader>
                        </ChoiceboxItem>
                      )
                    )}
                  </Choicebox>
                </FormControl>
                <FormMessage className="text-xs" />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Description</FormLabel>
                <FormControl>
                  <Textarea
                    className="min-h-[120px] resize-none"
                    placeholder="Please describe the issue in detail. Include steps to reproduce if possible."
                    {...field}
                    disabled={isExecuting}
                  />
                </FormControl>
                <FormMessage className="text-xs" />
              </FormItem>
            )}
          />
        </>
      )}
    </FeedbackFormWrapper>
  );
});
