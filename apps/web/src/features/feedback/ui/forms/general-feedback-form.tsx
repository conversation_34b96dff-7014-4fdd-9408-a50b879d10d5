'use client';

import { FeedbackTypeEnum } from '@lilypad/db/enums';
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@lilypad/ui/components/form';
import { Textarea } from '@lilypad/ui/components/textarea';
import React, { useMemo } from 'react';

import { generalFeedbackSchema } from '../../model/schemas';
import { FeedbackFormWrapper } from '../components/feedback-form-wrapper';
import { FeedbackRating } from '../components/feedback-rating';

interface GeneralFeedbackFormProps {
  onSuccess?: () => void;
  isActive?: boolean;
}

export const GeneralFeedbackForm = React.memo(
  function GeneralFeedbackFormComponent({
    onSuccess,
    isActive,
  }: GeneralFeedbackFormProps) {
    const defaultValues = useMemo(
      () => ({
        type: FeedbackTypeEnum.GENERAL,
        description: '',
        rating: undefined,
      }),
      []
    );

    return (
      <FeedbackFormWrapper
        action="submitOtherFeedback"
        defaultValues={defaultValues}
        errorMessage="Failed to submit feedback!"
        isActive={isActive}
        onSuccess={onSuccess}
        schema={generalFeedbackSchema}
        submitLabel="Submit Feedback"
        successMessage="Feedback submitted successfully!"
      >
        {({ form, isExecuting }) => (
          <>
            <FormField
              control={form.control}
              name="rating"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    How would you rate your overall experience?
                  </FormLabel>
                  <FormControl>
                    <FeedbackRating
                      disabled={isExecuting}
                      onChange={field.onChange}
                      value={field.value}
                    />
                  </FormControl>
                  <FormMessage className="text-xs" />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormDescription className="text-xs">
                    Share your thoughts, suggestions, or questions. We value all
                    feedback!
                  </FormDescription>
                  <FormControl>
                    <Textarea
                      className="min-h-[120px] resize-none"
                      {...field}
                      disabled={isExecuting}
                    />
                  </FormControl>
                  <FormMessage className="text-xs" />
                </FormItem>
              )}
            />
          </>
        )}
      </FeedbackFormWrapper>
    );
  }
);
