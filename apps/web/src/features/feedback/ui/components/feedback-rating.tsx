'use client';

import { cn } from '@lilypad/ui/lib/utils';
import { Star } from 'lucide-react';
import React from 'react';

interface FeedbackRatingProps {
  value?: number;
  onChange?: (rating: number) => void;
  disabled?: boolean;
  className?: string;
}

export function FeedbackRating({
  value = 0,
  onChange,
  disabled,
  className,
}: FeedbackRatingProps) {
  const [hoverValue, setHoverValue] = React.useState(0);

  const handleClick = (rating: number) => {
    if (!disabled && onChange) {
      onChange(rating);
    }
  };

  const handleMouseEnter = (rating: number) => {
    if (!disabled) {
      setHoverValue(rating);
    }
  };

  const handleMouseLeave = () => {
    setHoverValue(0);
  };

  return (
    // biome-ignore lint/a11y/noStaticElementInteractions: Fix later
    // biome-ignore lint/nursery/noNoninteractiveElementInteractions: Fix later
    <div
      className={cn('flex gap-1', className)}
      onMouseLeave={handleMouseLeave}
    >
      {[1, 2, 3, 4, 5].map((rating) => {
        const filled = rating <= (hoverValue || value);

        return (
          <button
            aria-label={`Rate ${rating} stars`}
            className={cn(
              'transform cursor-pointer transition-all duration-200',
              'hover:scale-110 active:scale-95',
              disabled && 'cursor-not-allowed opacity-50'
            )}
            disabled={disabled}
            key={rating}
            onClick={() => handleClick(rating)}
            onMouseEnter={() => handleMouseEnter(rating)}
            type="button"
          >
            <Star
              className={cn(
                'size-6 transition-colors',
                filled
                  ? 'fill-primary text-primary'
                  : 'fill-transparent text-muted-foreground hover:text-primary'
              )}
            />
          </button>
        );
      })}
    </div>
  );
}
