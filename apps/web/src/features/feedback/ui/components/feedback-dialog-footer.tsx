'use client';

import { But<PERSON> } from '@lilypad/ui/components/button';
import { DialogFooter } from '@lilypad/ui/components/dialog';
import React from 'react';

interface FeedbackDialogFooterProps {
  isExecuting: boolean;
  onClear: () => void;
  onSubmit: () => void;
  onCancel: () => void;
  submitLabel?: string;
  clearLabel?: string;
}

export const FeedbackDialogFooter = React.memo(
  function FeedbackDialogFooterComponent({
    isExecuting,
    onClear,
    onSubmit,
    onCancel,
    submitLabel = 'Submit',
    clearLabel = 'Clear',
  }: FeedbackDialogFooterProps) {
    return (
      <DialogFooter className="flex w-full gap-3 rounded-b-lg bg-muted px-6 py-4 sm:justify-between">
        <Button onClick={onCancel} size="sm" variant="cancel">
          Cancel
        </Button>
        <div className="flex gap-3">
          <Button
            disabled={isExecuting}
            onClick={onClear}
            size="sm"
            type="button"
            variant="outline"
          >
            {clearLabel}
          </Button>
          <Button
            disabled={isExecuting}
            onClick={onSubmit}
            size="sm"
            type="button"
          >
            {isExecuting ? 'Submitting...' : submitLabel}
          </Button>
        </div>
      </DialogFooter>
    );
  }
);
