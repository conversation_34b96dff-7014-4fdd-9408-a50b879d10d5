'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useTRPC } from '@lilypad/api/client';
import { Form, FormDescription, FormLabel } from '@lilypad/ui/components/form';
import { OptionalBadge } from '@lilypad/ui/components/optional-badge';
import { useSidebar } from '@lilypad/ui/components/sidebar';
import { toast } from '@lilypad/ui/components/sonner';
import { useMutation } from '@tanstack/react-query';
import React, { useCallback, useEffect, useId, useMemo } from 'react';
import { type UseFormReturn, useForm } from 'react-hook-form';
import type { z } from 'zod';
import { useUser } from '@/shared/contexts/user-context';
import { uploadMultipleFeedbackFiles } from '../../api/upload-feedback-files';
import { useFeedbackContext } from '../../model/feedback-context';
import { useFileUpload } from '../../model/use-file-upload';
import { FeedbackFileUpload } from './feedback-file-upload';

type FeedbackAction =
  | 'submitBugFeedback'
  | 'submitFeatureRequest'
  | 'submitOtherFeedback';

// biome-ignore lint/suspicious/noExplicitAny: Generic type
interface FeedbackFormWrapperProps<TSchema extends z.ZodType<any, any>> {
  schema: TSchema;
  defaultValues: z.infer<TSchema>;
  action: FeedbackAction;
  children: (props: {
    form: UseFormReturn<z.infer<TSchema>>;
    isExecuting: boolean;
  }) => React.ReactNode;
  onSuccess?: () => void;
  successMessage?: string;
  errorMessage?: string;
  submitLabel?: string;
  showFileUpload?: boolean;
  fileUploadLabel?: string;
  fileUploadDescription?: string;
  isActive?: boolean;
}

// Helper function to validate user authentication
const validateUserAuth = (userId?: string): boolean => {
  if (!userId) {
    toast.error(
      'You must be logged in to submit feedback. Please sign in and try again.'
    );
    return false;
  }
  return true;
};

// Helper function to handle file upload errors
const handleFileUploadErrors = (
  uploadResults: Array<{ error?: string; url: string }>
): string[] | null => {
  const errors = uploadResults.filter((r) => r.error);

  if (errors.length > 0) {
    const errorDetails = errors
      .map((e) => e.error)
      .filter(Boolean)
      .join(', ');

    toast.error(
      `Failed to upload ${errors.length} file(s)${
        errorDetails ? `: ${errorDetails}` : ''
      }`
    );
    return null;
  }

  return uploadResults.map((r) => r.url);
};

// Helper function to handle file upload process
const processFileUploads = async (
  uploadedFiles: File[],
  userId: string,
  setUploadProgress: (progress: number) => void
): Promise<string[]> => {
  if (uploadedFiles.length === 0) {
    return [];
  }

  try {
    const uploadResults = await uploadMultipleFeedbackFiles(
      uploadedFiles,
      userId,
      setUploadProgress
    );

    const fileUrls = handleFileUploadErrors(uploadResults);
    if (!fileUrls) {
      throw new Error('File upload validation failed');
    }

    return fileUrls;
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : 'Unknown error';
    toast.error(`File upload failed: ${errorMessage}`);
    throw error;
  }
};

export const FeedbackFormWrapper = React.memo(
  function FeedbackFormWrapperComponent<
    // biome-ignore lint/suspicious/noExplicitAny: Generic type
    TSchema extends z.ZodType<any, any>,
  >({
    schema,
    defaultValues,
    action,
    children,
    onSuccess,
    successMessage = 'Feedback submitted successfully',
    errorMessage = 'Failed to submit feedback',
    submitLabel = 'Submit',
    showFileUpload = true,
    fileUploadLabel = 'Attachments',
    fileUploadDescription,
    isActive = false,
  }: FeedbackFormWrapperProps<TSchema>) {
    const formId = useId();
    const { setFeedbackOpen } = useSidebar();
    const { registerForm, unregisterForm, setActiveForm } =
      useFeedbackContext();
    const {
      uploadedFiles,
      setUploadProgress,
      clearFiles,
      addFiles,
      removeFile,
    } = useFileUpload();
    const { user } = useUser();
    const trpc = useTRPC();

    const form = useForm<z.infer<TSchema>>({
      resolver: zodResolver(schema),
      defaultValues,
    });

    const handleSuccess = useCallback(() => {
      toast.success(successMessage);
      form.reset();
      clearFiles();
      onSuccess?.();
      setFeedbackOpen(false);
    }, [successMessage, form, clearFiles, onSuccess, setFeedbackOpen]);

    const handleError = useCallback(
      (error: unknown) => {
        const message = error instanceof Error ? error.message : errorMessage;
        toast.error(message);
      },
      [errorMessage]
    );

    // Get the appropriate mutation based on action
    const mutation = useMemo(() => {
      switch (action) {
        case 'submitBugFeedback':
          return trpc.feedback.submitBugFeedback.mutationOptions({
            onSuccess: handleSuccess,
            onError: handleError,
          });
        case 'submitFeatureRequest':
          return trpc.feedback.submitFeatureRequest.mutationOptions({
            onSuccess: handleSuccess,
            onError: handleError,
          });
        case 'submitOtherFeedback':
          return trpc.feedback.submitOtherFeedback.mutationOptions({
            onSuccess: handleSuccess,
            onError: handleError,
          });
        default:
          throw new Error(`Invalid action: ${action}`);
      }
    }, [action, trpc, handleSuccess, handleError]);

    const { mutate, isPending: isExecuting } = useMutation(mutation);

    const handleClear = useCallback(() => {
      form.reset();
      clearFiles();
    }, [form, clearFiles]);

    const onSubmit = useCallback(
      async (data: z.infer<TSchema>) => {
        if (!validateUserAuth(user?.id)) {
          return;
        }

        try {
          const fileUrls = await processFileUploads(
            uploadedFiles,
            // biome-ignore lint/style/noNonNullAssertion: User is validated
            user!.id,
            setUploadProgress
          );

          mutate({ ...data, fileUrls });
        } catch (_error) {
          // Error handling is done in processFileUploads
          return;
        }
      },
      [user?.id, uploadedFiles, setUploadProgress, mutate, user]
    );

    const handleSubmit = useCallback(() => {
      form.handleSubmit(onSubmit)();
    }, [form, onSubmit]);

    // Register form handlers
    useEffect(() => {
      registerForm(formId, {
        submit: handleSubmit,
        clear: handleClear,
        isExecuting,
        submitLabel,
      });

      return () => {
        unregisterForm(formId);
      };
    }, [
      formId,
      registerForm,
      unregisterForm,
      handleSubmit,
      handleClear,
      isExecuting,
      submitLabel,
    ]);

    // Set as active form when active
    useEffect(() => {
      if (isActive) {
        setActiveForm(formId);
      }
    }, [isActive, formId, setActiveForm]);

    const memoizedChildren = useMemo(
      () => children({ form, isExecuting }),
      [children, form, isExecuting]
    );

    return (
      <Form {...form}>
        <form className="space-y-6 px-1" onSubmit={form.handleSubmit(onSubmit)}>
          {memoizedChildren}

          {showFileUpload && (
            <div className="space-y-2">
              <FormLabel>
                {fileUploadLabel} <OptionalBadge />
              </FormLabel>
              {fileUploadDescription && (
                <FormDescription className="text-xs">
                  {fileUploadDescription}
                </FormDescription>
              )}
              <FeedbackFileUpload
                addFiles={addFiles}
                disabled={isExecuting}
                removeFile={removeFile}
                uploadedFiles={uploadedFiles}
              />
            </div>
          )}
        </form>
      </Form>
    );
  }
);
