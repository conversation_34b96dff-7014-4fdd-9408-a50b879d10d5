'use client';

import { Button } from '@lilypad/ui/components/button';
import { cn } from '@lilypad/ui/lib/utils';
import { FileText, Image, Upload, X } from 'lucide-react';
import type React from 'react';
import { useCallback, useRef } from 'react';

interface FeedbackFileUploadProps {
  className?: string;
  disabled?: boolean;
  uploadedFiles: File[];
  addFiles: (files: File[]) => void;
  removeFile: (index: number) => void;
}

export function FeedbackFileUpload({
  className,
  disabled,
  uploadedFiles,
  addFiles,
  removeFile,
}: FeedbackFileUploadProps) {
  const inputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const files = Array.from(e.target.files || []);
      if (files.length > 0) {
        addFiles(files);
      }
    },
    [addFiles]
  );

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      const files = Array.from(e.dataTransfer.files);
      if (files.length > 0) {
        addFiles(files);
      }
    },
    [addFiles]
  );

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
  }, []);

  const getFileIcon = useCallback((file: File) => {
    if (file.type.startsWith('image/')) {
      return <Image className="size-4" />;
    }
    return <FileText className="size-4" />;
  }, []);

  const formatFileSize = useCallback((bytes: number) => {
    if (bytes === 0) {
      return '0 Bytes';
    }
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`;
  }, []);

  return (
    <div className={cn('space-y-4', className)}>
      {/** biome-ignore lint/a11y/noStaticElementInteractions: Fix later */}
      {/** biome-ignore lint/nursery/noNoninteractiveElementInteractions: Fix later */}
      {/** biome-ignore assist/source/useSortedAttributes: Fix later */}
      <div
        className={cn(
          'cursor-pointer space-y-1 rounded-lg border border-dashed bg-muted/50 p-6 text-center transition-colors',
          'hover:border-primary hover:bg-muted/50',
          disabled && 'cursor-not-allowed opacity-50',
          uploadedFiles.length >= 5 && 'pointer-events-none opacity-50'
        )}
        onClick={() =>
          !disabled && uploadedFiles.length < 5 && inputRef.current?.click()
        }
        onDrop={!disabled && uploadedFiles.length < 5 ? handleDrop : undefined}
        onDragOver={
          !disabled && uploadedFiles.length < 5 ? handleDragOver : undefined
        }
      >
        <div className="mx-auto w-fit rounded-lg bg-muted-foreground/20 p-2">
          <Upload className="mx-auto size-4 text-muted-foreground" />
        </div>
        <p className="text-muted-foreground text-xs">
          Drag and drop or click to upload
        </p>
        <p className="text-muted-foreground text-xs">
          Max 5 files, up to 5MB each.
        </p>
        <p className="text-muted-foreground text-xs">
          Images, PDFs, and text files allowed.
        </p>
        <input
          accept="image/*,.pdf,.txt"
          className="hidden"
          disabled={disabled || uploadedFiles.length >= 5}
          multiple
          onChange={handleFileSelect}
          ref={inputRef}
          type="file"
        />
      </div>

      {uploadedFiles.length > 0 && (
        <div className="space-y-2">
          <p className="font-medium text-xs">
            Attached files{' '}
            <span className="rounded-md bg-muted px-2 py-1 text-xxs">
              {uploadedFiles.length} of 5
            </span>
          </p>
          {uploadedFiles.map((file, index) => (
            <div
              className="flex items-center justify-between rounded-md border bg-muted px-2 py-1"
              key={file.name}
            >
              <div className="flex items-center gap-2">
                {getFileIcon(file)}
                <div className="flex space-x-2">
                  <p className="truncate font-medium text-xs">{file.name}</p>
                  <p className="text-muted-foreground text-xs">
                    {formatFileSize(file.size)}
                  </p>
                </div>
              </div>
              <Button
                disabled={disabled}
                onClick={() => removeFile(index)}
                size="sm"
                type="button"
                variant="ghost"
              >
                <X className="size-4" />
              </Button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
