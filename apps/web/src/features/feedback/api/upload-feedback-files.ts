import { getSupabaseBrowserClient } from '@lilypad/supabase/client';

const FEEDBACK_BUCKET = 'feedback';
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const ALLOWED_FILE_TYPES = [
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/webp',
  'application/pdf',
  'text/plain',
];

export interface UploadResult {
  url: string;
  path: string;
  error?: string;
}

export async function uploadFeedbackFile(
  file: File,
  userId: string
): Promise<UploadResult> {
  // Validate file size
  if (file.size > MAX_FILE_SIZE) {
    return {
      url: '',
      path: '',
      error: 'File size exceeds 5MB limit',
    };
  }

  // Validate file type
  if (!ALLOWED_FILE_TYPES.includes(file.type)) {
    return {
      url: '',
      path: '',
      error:
        'File type not allowed. Please upload images, PDFs, or text files.',
    };
  }

  const supabase = getSupabaseBrowserClient();

  const timestamp = Date.now();
  const fileExt = file.name.split('.').pop();
  const fileName = `${userId}_${timestamp}-${Math.random().toString(36).substring(7)}.${fileExt}`;

  try {
    const { data, error } = await supabase.storage
      .from(FEEDBACK_BUCKET)
      .upload(fileName, file, {
        cacheControl: '3600',
        upsert: false,
      });

    if (error) {
      console.error('Upload error:', error);
      return {
        url: '',
        path: '',
        error: 'Failed to upload file',
      };
    }

    // Get public URL
    const {
      data: { publicUrl },
    } = supabase.storage.from(FEEDBACK_BUCKET).getPublicUrl(data.path);

    return {
      url: publicUrl,
      path: data.path,
    };
  } catch (error) {
    console.error('Upload exception:', error);
    return {
      url: '',
      path: '',
      error: 'An unexpected error occurred during upload',
    };
  }
}

export async function deleteFeedbackFile(filePath: string): Promise<boolean> {
  const supabase = getSupabaseBrowserClient();

  try {
    const { error } = await supabase.storage
      .from(FEEDBACK_BUCKET)
      .remove([filePath]);

    if (error) {
      console.error('Delete error:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Delete exception:', error);
    return false;
  }
}

export async function uploadMultipleFeedbackFiles(
  files: File[],
  userId: string,
  onProgress?: (progress: number) => void
): Promise<UploadResult[]> {
  const results: UploadResult[] = [];

  for (let i = 0; i < files.length; i++) {
    // biome-ignore lint/nursery/noAwaitInLoop: Not a big deal
    const result = await uploadFeedbackFile(files[i], userId);
    results.push(result);

    if (onProgress) {
      onProgress(((i + 1) / files.length) * 100);
    }
  }

  return results;
}
