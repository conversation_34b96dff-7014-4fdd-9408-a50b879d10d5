'use client';

import type React from 'react';
import {
  createContext,
  useCallback,
  useContext,
  useRef,
  useState,
} from 'react';

interface FeedbackContextValue {
  registerForm: (id: string, handlers: FormHandlers) => void;
  unregisterForm: (id: string) => void;
  submitActiveForm: () => void;
  clearActiveForm: () => void;
  setActiveForm: (id: string) => void;
  isExecuting: boolean;
  activeFormLabel: string;
}

interface FormHandlers {
  submit: () => void;
  clear: () => void;
  isExecuting: boolean;
  submitLabel: string;
}

const FeedbackContext = createContext<FeedbackContextValue | null>(null);

export function FeedbackProvider({ children }: { children: React.ReactNode }) {
  const formsRef = useRef<Map<string, FormHandlers>>(new Map());
  const [activeFormId, setActiveFormId] = useState<string>('');
  const [, forceUpdate] = useState({});

  const registerForm = useCallback((id: string, handlers: FormHandlers) => {
    formsRef.current.set(id, handlers);
    forceUpdate({});
  }, []);

  const unregisterForm = useCallback((id: string) => {
    formsRef.current.delete(id);
    forceUpdate({});
  }, []);

  const activeForm = formsRef.current.get(activeFormId);

  const submitActiveForm = useCallback(() => {
    activeForm?.submit();
  }, [activeForm]);

  const clearActiveForm = useCallback(() => {
    activeForm?.clear();
  }, [activeForm]);

  return (
    <FeedbackContext.Provider
      value={{
        registerForm,
        unregisterForm,
        submitActiveForm,
        clearActiveForm,
        setActiveForm: setActiveFormId,
        isExecuting: activeForm?.isExecuting ?? false,
        activeFormLabel: activeForm?.submitLabel ?? 'Submit',
      }}
    >
      {children}
    </FeedbackContext.Provider>
  );
}

export const useFeedbackContext = () => {
  const context = useContext(FeedbackContext);
  if (!context) {
    throw new Error('useFeedbackContext must be used within FeedbackProvider');
  }
  return context;
};
