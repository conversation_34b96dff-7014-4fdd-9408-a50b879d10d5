import { z } from 'zod';

import { FeedbackIssueTypeEnum, FeedbackTypeEnum } from '@lilypad/db/enums';

const baseFeedbackSchema = z.object({
  description: z
    .string()
    .min(10, 'Description must be at least 10 characters')
    .max(1000, 'Description must be less than 1000 characters'),
  rating: z.number().min(1).max(5).optional(),
});

export const bugFeedbackSchema = baseFeedbackSchema.extend({
  type: z.literal(FeedbackTypeEnum.BUG),
  issueType: z.nativeEnum(FeedbackIssueTypeEnum, {
    errorMap: () => ({ message: 'Please select an issue type' }),
  }),
});

export const featureRequestSchema = baseFeedbackSchema.extend({
  type: z.literal(FeedbackTypeEnum.FEATURE_REQUEST),
});

export const generalFeedbackSchema = baseFeedbackSchema.extend({
  type: z.literal(FeedbackTypeEnum.GENERAL),
});

export const feedbackSchema = z.discriminatedUnion('type', [
  bugFeedbackSchema,
  featureRequestSchema,
  generalFeedbackSchema,
]);

export const feedbackFileSchema = z.object({
  file: z.instanceof(File),
  url: z.string().url(),
});

export const feedbackFilesSchema = z
  .array(feedbackFileSchema)
  .max(5, 'Maximum 5 files allowed');

export type BugFeedbackForm = z.infer<typeof bugFeedbackSchema>;
export type FeatureRequestForm = z.infer<typeof featureRequestSchema>;
export type GeneralFeedbackForm = z.infer<typeof generalFeedbackSchema>;
export type FeedbackForm = z.infer<typeof feedbackSchema>;
export type FeedbackFile = z.infer<typeof feedbackFileSchema>;
