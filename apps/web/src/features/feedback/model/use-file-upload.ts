import { useCallback, useState } from 'react';

export function useFileUpload() {
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [uploadProgress, setUploadProgress] = useState(0);

  const addFiles = useCallback((files: File[]) => {
    setUploadedFiles((prev) => [...prev, ...files].slice(0, 5)); // Max 5 files
  }, []);

  const removeFile = useCallback((index: number) => {
    setUploadedFiles((prev) => prev.filter((_, i) => i !== index));
  }, []);

  const clearFiles = useCallback(() => {
    setUploadedFiles([]);
    setUploadProgress(0);
  }, []);

  return {
    uploadedFiles,
    uploadProgress,
    addFiles,
    removeFile,
    clearFiles,
    setUploadProgress,
  };
}
