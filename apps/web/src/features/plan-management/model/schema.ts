import { PlanStatusEnum, PlanTypeEnum } from '@lilypad/db/schema/enums';
import { z } from 'zod';

export const createPlanSchema = z.object({
  studentId: z.string().uuid('Invalid student ID'),
  caseId: z.string().uuid('Please select a case'),
  type: z.nativeEnum(PlanTypeEnum, {
    errorMap: () => ({ message: 'Please select a plan type' }),
  }),
  status: z.nativeEnum(PlanStatusEnum, {
    errorMap: () => ({ message: 'Please select a plan status' }),
  }),
  expirationDate: z.date({
    required_error: 'Expiration date is required',
    invalid_type_error: 'Please select a valid date',
  }),
});

export type CreatePlanFormData = z.infer<typeof createPlanSchema>;

export const PlanTypeEnumMap = {
  [PlanTypeEnum.IEP]: 'IEP',
  [PlanTypeEnum.PLAN_504]: '504 Plan',
  [PlanTypeEnum.BIP]: 'Behavior Intervention Plan (BIP)',
  [PlanTypeEnum.SST]: 'Student Study Team (SST)',
} as const;

export const PlanStatusEnumMap = {
  [PlanStatusEnum.PENDING]: 'Pending',
  [PlanStatusEnum.ACTIVE]: 'Active',
  [PlanStatusEnum.CANCELLED]: 'Cancelled',
} as const;
