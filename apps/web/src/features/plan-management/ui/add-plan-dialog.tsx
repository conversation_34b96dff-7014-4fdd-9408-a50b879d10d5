'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import {
  CasePriorityEnum,
  CaseStatusEnum,
  CaseTypeEnum,
  IepStatusEnum,
  PlanStatusEnum,
} from '@lilypad/db/schema/enums';
import { Button } from '@lilypad/ui/components/button';
import { Calendar } from '@lilypad/ui/components/calendar';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@lilypad/ui/components/dialog';
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from '@lilypad/ui/components/drawer';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@lilypad/ui/components/form';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@lilypad/ui/components/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@lilypad/ui/components/select';
import { useMediaQuery } from '@lilypad/ui/hooks/use-media-query';
import { MediaQueries } from '@lilypad/ui/lib/media-queries';
import { cn } from '@lilypad/ui/lib/utils';
import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import {
  type CreatePlanFormData,
  PlanStatusEnumMap,
  PlanTypeEnumMap,
  createPlanSchema,
} from '../model/schema';

// Mock case data - will be replaced with actual data fetching
const mockCases = [
  {
    id: '550e8400-e29b-41d4-a716-446655440001',
    status: CaseStatusEnum.EVALUATION_IN_PROGRESS,
    priority: CasePriorityEnum.HIGH,
    caseType: CaseTypeEnum.INITIAL_EVALUATION,
    isActive: true,
    iepStatus: IepStatusEnum.ACTIVE,
    iepStartDate: new Date('2024-01-15'),
    iepEndDate: new Date('2024-12-31'),
    referralDate: new Date('2023-12-01'),
    evaluationDueDate: new Date('2024-08-30'),
    meetingDate: new Date('2024-07-15'),
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-06-01'),
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440002',
    status: CaseStatusEnum.READY_FOR_EVALUATION,
    priority: CasePriorityEnum.MEDIUM,
    caseType: CaseTypeEnum.TRIENNIAL_EVALUATION,
    isActive: true,
    iepStatus: IepStatusEnum.ACTIVE,
    iepStartDate: new Date('2024-02-01'),
    iepEndDate: new Date('2025-01-31'),
    referralDate: new Date('2024-01-15'),
    evaluationDueDate: new Date('2024-09-15'),
    meetingDate: null,
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-05-15'),
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440003',
    status: CaseStatusEnum.AWAITING_MEETING,
    priority: CasePriorityEnum.LOW,
    caseType: CaseTypeEnum.REEVALUATION,
    isActive: false,
    iepStatus: IepStatusEnum.INACTIVE,
    iepStartDate: new Date('2023-09-01'),
    iepEndDate: new Date('2024-08-31'),
    referralDate: new Date('2023-08-15'),
    evaluationDueDate: new Date('2024-02-15'),
    meetingDate: new Date('2024-08-01'),
    createdAt: new Date('2023-08-15'),
    updatedAt: new Date('2024-07-15'),
  },
];

const title = 'Add New Plan';
const description =
  'Add a new evaluation plan for this student. Select an existing case and configure the plan details below.';

interface AddPlanDialogProps {
  studentId: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function AddPlanDialog({
  studentId,
  open,
  onOpenChange,
}: AddPlanDialogProps) {
  const mdUp = useMediaQuery(MediaQueries.MdUp, { ssr: false });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<CreatePlanFormData>({
    resolver: zodResolver(createPlanSchema),
    defaultValues: {
      studentId,
      caseId: '',
      type: undefined,
      status: PlanStatusEnum.PENDING,
      expirationDate: new Date(
        new Date().setFullYear(new Date().getFullYear() + 1)
      ), // Default to 1 year from now
    },
  });

  const onSubmit = async (data: CreatePlanFormData) => {
    setIsSubmitting(true);
    try {
      console.log('Plan data:', data);
      // TODO: Implement plan creation logic
      onOpenChange(false);
      form.reset();
      return await Promise.resolve(true);
    } finally {
      setIsSubmitting(false);
    }
  };

  const getCaseDisplayText = (caseItem: (typeof mockCases)[0]) => {
    const caseTypeText =
      Object.entries({
        [CaseTypeEnum.INITIAL_EVALUATION]: 'Initial Eval',
        [CaseTypeEnum.TRIENNIAL_EVALUATION]: 'Triennial Eval',
        [CaseTypeEnum.REEVALUATION]: 'Reevaluation',
        [CaseTypeEnum.INDEPENDENT_EVALUATION]: 'Independent Eval',
        [CaseTypeEnum.CHANGE_OF_PLACEMENT]: 'Change of Placement',
        [CaseTypeEnum.DISCIPLINE_EVALUATION]: 'Discipline Eval',
        [CaseTypeEnum.TRANSITION_EVALUATION]: 'Transition Eval',
      }).find(([key]) => key === caseItem.caseType)?.[1] || caseItem.caseType;

    const statusText =
      Object.entries({
        [CaseStatusEnum.READY_FOR_EVALUATION]: 'Ready for Eval',
        [CaseStatusEnum.EVALUATION_IN_PROGRESS]: 'Eval in Progress',
        [CaseStatusEnum.REPORT_IN_PROGRESS]: 'Report in Progress',
        [CaseStatusEnum.AWAITING_MEETING]: 'Awaiting Meeting',
        [CaseStatusEnum.MEETING_COMPLETE]: 'Meeting Complete',
      }).find(([key]) => key === caseItem.status)?.[1] || caseItem.status;

    return `${caseTypeText} - ${statusText} (${caseItem.isActive ? 'Active' : 'Inactive'})`;
  };

  const renderForm = (
    <div
      className={cn(
        'min-h-0 flex-1 overflow-y-auto',
        mdUp ? 'px-6 py-4' : 'p-4'
      )}
    >
      <form className="space-y-6 pb-4" onSubmit={form.handleSubmit(onSubmit)}>
        <FormField
          control={form.control}
          name="caseId"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="font-medium text-sm">
                Associated Case
              </FormLabel>
              <Select defaultValue={field.value} onValueChange={field.onChange}>
                <FormControl>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select a case" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {mockCases.map((caseItem) => (
                    <SelectItem key={caseItem.id} value={caseItem.id}>
                      {getCaseDisplayText(caseItem)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="type"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="font-medium text-sm">Plan Type</FormLabel>
                <Select
                  defaultValue={field.value}
                  onValueChange={field.onChange}
                >
                  <FormControl>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select plan type" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {Object.entries(PlanTypeEnumMap).map(([value, label]) => (
                      <SelectItem key={value} value={value}>
                        {label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="status"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="font-medium text-sm">
                  Plan Status
                </FormLabel>
                <Select
                  defaultValue={field.value}
                  onValueChange={field.onChange}
                >
                  <FormControl>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {Object.entries(PlanStatusEnumMap).map(([value, label]) => (
                      <SelectItem key={value} value={value}>
                        {label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="expirationDate"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="font-medium text-sm">
                Expiration Date
              </FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      className={cn(
                        'w-full justify-start text-left font-normal',
                        !field.value && 'text-muted-foreground'
                      )}
                      variant="outline"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {field.value ? (
                        format(field.value, 'PPP')
                      ) : (
                        <span>Pick an expiration date</span>
                      )}
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent align="start" className="w-auto p-0">
                  <Calendar
                    disabled={(date) => date < new Date()}
                    mode="single"
                    onSelect={field.onChange}
                    selected={field.value}
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />
      </form>
    </div>
  );

  const renderButtons = (
    <div
      className={cn(
        'flex flex-shrink-0 justify-between gap-2 bg-secondary p-4',
        mdUp && 'rounded-b-md'
      )}
    >
      <Button
        className="w-1/2 md:w-auto"
        disabled={isSubmitting}
        onClick={() => onOpenChange(false)}
        size="sm"
        type="button"
        variant="outline"
      >
        Cancel
      </Button>
      <Button
        className="w-1/2 md:w-auto"
        disabled={!form.formState.isValid || isSubmitting}
        loading={isSubmitting}
        onClick={form.handleSubmit(onSubmit)}
        size="sm"
        type="button"
        variant="default"
      >
        Add Plan
      </Button>
    </div>
  );

  return (
    <Form {...form}>
      {mdUp ? (
        <Dialog onOpenChange={onOpenChange} open={open}>
          <DialogContent className="flex max-w-2xl flex-col gap-0 p-0">
            <DialogHeader className="flex-shrink-0 border-b px-6 py-5">
              <DialogTitle className="font-semibold text-lg">
                {title}
              </DialogTitle>
              <DialogDescription className="text-muted-foreground text-sm">
                {description}
              </DialogDescription>
            </DialogHeader>
            {renderForm}
            {renderButtons}
          </DialogContent>
        </Dialog>
      ) : (
        <Drawer onOpenChange={onOpenChange} open={open}>
          <DrawerContent className="flex flex-col">
            <DrawerHeader className="flex-shrink-0 border-b text-left">
              <DrawerTitle className="font-semibold text-lg">
                {title}
              </DrawerTitle>
              <DrawerDescription className="text-muted-foreground text-sm">
                {description}
              </DrawerDescription>
            </DrawerHeader>
            {renderForm}
            {renderButtons}
          </DrawerContent>
        </Drawer>
      )}
    </Form>
  );
}
