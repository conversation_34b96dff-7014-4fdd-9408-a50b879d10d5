'use client';

import { useModal } from '@ebay/nice-modal-react';
import { zodResolver } from '@hookform/resolvers/zod';
import type { TRPCClientError } from '@lilypad/api/client';
import { useTRPC } from '@lilypad/api/client';
import { rejectTaskInputSchema } from '@lilypad/api/schemas/tasks';
import { toast } from '@lilypad/ui/components/sonner';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useCallback, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import type { z } from 'zod';

type RejectTaskFormData = z.infer<typeof rejectTaskInputSchema>;

interface UseRejectTaskOptions {
  taskId: string;
  onSuccess?: () => void;
  onError?: (error: TRPCClientError) => void;
}

export function useRejectTask(options: UseRejectTaskOptions) {
  const trpc = useTRPC();
  const queryClient = useQueryClient();
  const modal = useModal();

  // Form setup with React Hook Form and Zod validation
  const methods = useForm<RejectTaskFormData>({
    resolver: zodResolver(rejectTaskInputSchema),
    defaultValues: {
      taskId: options.taskId,
      reason: '',
    },
    mode: 'onChange',
  });

  // Watch the reason field for UI updates
  const rejectionReason = methods.watch('reason');

  // Error handling
  const handleError = useCallback(
    (error: TRPCClientError) => {
      const message = error.message || 'An unexpected error occurred';

      // Handle specific error cases
      switch (error.data?.code) {
        case 'NOT_FOUND':
          toast.error('Task Not Found', {
            description:
              'The task you are trying to reject could not be found.',
          });
          break;
        case 'FORBIDDEN':
          toast.error('Permission Denied', {
            description: 'You do not have permission to reject this task.',
          });
          break;
        case 'CONFLICT':
          toast.error('Task Already Processed', {
            description:
              'This task has already been completed or rejected by someone else.',
          });
          break;
        default:
          toast.error('Rejection Failed', {
            description: `Failed to reject task: ${message}`,
          });
      }

      options.onError?.(error);
    },
    [options]
  );

  // Success handling
  const handleSuccess = useCallback(async () => {
    // Invalidate related queries
    await queryClient.invalidateQueries({
      queryKey: [['tasks']],
      exact: false,
    });

    // Show success message
    toast.success('Task Rejected', {
      description: 'The task has been successfully rejected and reassigned.',
    });

    // Reset form
    methods.reset();

    options.onSuccess?.();
  }, [queryClient, methods, options]);

  // Task rejection mutation
  const rejectTaskMutation = useMutation({
    mutationFn: async (data: RejectTaskFormData) => {
      return await trpc.tasks.rejectTask.mutate(data);
    },
    onSuccess: handleSuccess,
    onError: handleError,
  });

  // Form submission handler
  const onSubmit = useCallback(
    async (data: RejectTaskFormData) => {
      // Additional client-side validation
      if (!data.reason.trim()) {
        methods.setError('reason', {
          type: 'manual',
          message: 'Rejection reason is required',
        });
        return;
      }

      if (data.reason.trim().length < 10) {
        methods.setError('reason', {
          type: 'manual',
          message:
            'Please provide a more detailed reason (at least 10 characters)',
        });
        return;
      }

      if (data.reason.trim().length > 1000) {
        methods.setError('reason', {
          type: 'manual',
          message: 'Reason is too long (maximum 1000 characters)',
        });
        return;
      }

      // Submit the form
      await rejectTaskMutation.mutateAsync(data);
    },
    [methods, rejectTaskMutation]
  );

  // Computed states
  const canSubmit = useMemo(() => {
    return (
      methods.formState.isValid &&
      rejectionReason.trim().length >= 10 &&
      rejectionReason.trim().length <= 1000 &&
      !rejectTaskMutation.isPending
    );
  }, [
    methods.formState.isValid,
    rejectionReason,
    rejectTaskMutation.isPending,
  ]);

  const isSubmitting = rejectTaskMutation.isPending;

  // Form validation helpers
  const validationState = useMemo(() => {
    const reason = rejectionReason.trim();

    return {
      hasReason: reason.length > 0,
      isReasonTooShort: reason.length > 0 && reason.length < 10,
      isReasonTooLong: reason.length > 1000,
      isReasonValid: reason.length >= 10 && reason.length <= 1000,
      characterCount: reason.length,
      remainingCharacters: 1000 - reason.length,
    };
  }, [rejectionReason]);

  // Utility functions for UI feedback
  const getReasonValidationMessage = useCallback(() => {
    const { characterCount, isReasonTooShort, isReasonTooLong } =
      validationState;

    if (isReasonTooShort) {
      return `Please provide more detail (${characterCount}/10 minimum characters)`;
    }

    if (isReasonTooLong) {
      return `Reason is too long (${characterCount}/1000 maximum characters)`;
    }

    return `${characterCount}/1000 characters`;
  }, [validationState]);

  const getReasonValidationVariant = useCallback(() => {
    const { isReasonTooShort, isReasonTooLong, isReasonValid } =
      validationState;

    if (isReasonTooShort || isReasonTooLong) {
      return 'destructive';
    }

    if (isReasonValid) {
      return 'success';
    }

    return 'default';
  }, [validationState]);

  return {
    // Modal control
    modal,

    // Form methods
    methods,
    onSubmit,

    // Form state
    rejectionReason,
    canSubmit,
    isSubmitting,
    validationState,

    // Mutation state
    isPending: rejectTaskMutation.isPending,
    isError: rejectTaskMutation.isError,
    error: rejectTaskMutation.error,

    // Utility functions
    getReasonValidationMessage,
    getReasonValidationVariant,

    // Actions
    rejectTask: rejectTaskMutation.mutate,
    rejectTaskAsync: rejectTaskMutation.mutateAsync,
    resetForm: methods.reset,

    // Quick validation checks
    isReasonValid: validationState.isReasonValid,
    hasMinimumReason: validationState.characterCount >= 10,
    isWithinLimit: validationState.characterCount <= 1000,
  };
}

// Simplified hook for just rejecting a task without form management
export function useRejectTaskAction(
  options: {
    onSuccess?: () => void;
    onError?: (error: TRPCClientError) => void;
  } = {}
) {
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  const handleError = useCallback(
    (error: TRPCClientError) => {
      const message = error.message || 'An unexpected error occurred';
      toast.error('Rejection Failed', {
        description: `Failed to reject task: ${message}`,
      });
      options.onError?.(error);
    },
    [options]
  );

  const handleSuccess = useCallback(async () => {
    await queryClient.invalidateQueries({
      queryKey: [['tasks']],
      exact: false,
    });

    toast.success('Task Rejected', {
      description: 'The task has been successfully rejected.',
    });

    options.onSuccess?.();
  }, [queryClient, options]);

  const mutation = useMutation({
    mutationFn: async (data: RejectTaskFormData) => {
      return await trpc.tasks.rejectTask.mutate(data);
    },
    onSuccess: handleSuccess,
    onError: handleError,
  });

  return {
    rejectTask: mutation.mutate,
    rejectTaskAsync: mutation.mutateAsync,
    isPending: mutation.isPending,
    isError: mutation.isError,
    error: mutation.error,
  };
}
