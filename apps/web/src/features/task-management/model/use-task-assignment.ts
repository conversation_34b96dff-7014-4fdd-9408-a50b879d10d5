'use client';

import NiceModal from '@ebay/nice-modal-react';
import { toast } from '@lilypad/ui/components/sonner';
import { useCallback } from 'react';
import type { TaskTableRow } from '@/entities/tasks/model/schema';
import { TaskAssignmentDialog } from '../ui/task-assignment-dialog';
import { useTaskActions } from './use-task-actions';

export function useTaskAssignment() {
  const { reassignTask, isReassigning } = useTaskActions({
    onSuccess: (action, taskId) => {},
  });

  // Open assignment dialog for new assignment
  const openAssignmentDialog = useCallback((task: TaskTableRow) => {
    NiceModal.show(TaskAssignmentDialog, {
      task,
      isReassignment: false,
    });
  }, []);

  // Open reassignment dialog for existing assignment
  const openReassignmentDialog = useCallback((task: TaskTableRow) => {
    NiceModal.show(TaskAssignmentDialog, {
      task,
      isReassignment: true,
    });
  }, []);

  // Quick assignment without dialog (for programmatic use)
  const assignTask = useCallback(
    async (taskId: string, assigneeId: string, notes?: string) => {
      try {
        await reassignTask(taskId, assigneeId, notes);
        toast.success('Task Assigned', {
          description: 'Task has been successfully assigned.',
        });
      } catch (error) {
        console.error('Assign task error:', error);
        toast.error('Failed to assign task',{
          description: 'Task assignment failed. Please try again.',
        });
      }
    },
    [reassignTask]
  );

// Unassign task (set to null/unassigned)
const unassignTask = useCallback(
    async (taskId: string, notes?: string) => {
      try {
        // _taskIdould need_notesecific unassign endpoint or null assignee handling
        // For now, we'll handle it as a reassignment to a special "unassigned" state
        toast.info('Unassign Feature', {
          description: 'Task unassignment will be implemented when the backend supports it.',
        });
      } catch (error) {
        console.error('Failed to unassign task:', error);
        toast.error('Unassign task failed', {
          description: 'Task unassignment failed. Please try again.',
        });
      }
    },
    []
  )

// Bulk assignment for multiple tasks
const bulkAssignTasks = useCallback(
  async (taskIds: string[], assigneeId: string, notes?: string) => {
    const results = await Promise.allSettled(
      taskIds.map((taskId) => assignTask(taskId, assigneeId, notes))
    );

    const successful = results.filter((r) => r.status === 'fulfilled').length;
    const failed = results.filter((r) => r.status === 'rejected').length;

    if (successful > 0) {
      toast.success('Bulk Assignment Completed', {
        description: `${successful} task(s) assigned successfully${failed > 0 ? `, ${failed} failed` : ''}.`,
      });
    }

    if (failed > 0 && successful === 0) {
      toast.error('Bulk Assignment Failed', {
        description: `Failed to assign ${failed} task(s).`,
      });
    }

    return results;
  },
  [assignTask]
);

// Get assignment status and actions for a task
const getAssignmentInfo = useCallback((task: TaskTableRow) => {
  const isAssigned = Boolean(task.assignedToId && task.assignedToName);
  const canReassign = isAssigned;
  const canAssign = !isAssigned;

  return {
    isAssigned,
    canReassign,
    canAssign,
    assigneeName: task.assignedToName,
    assigneeId: task.assignedToId,
  };
}, []);

return {
    // Dialog actions
    openAssignmentDialog,
    openReassignmentDialog,
    
    // Direct assignment actions
    assignTask,
    unassignTask,
    bulkAssignTasks,
    
    // Utilities
    getAssignmentInfo,
    
    // State
    isAssigning: isReassigning,
    
    // Quick action helpers
    quickAssign: (task: TaskTableRow) => {
      if (task.assignedToId) {
        openReassignmentDialog(task);
      } else {
        openAssignmentDialog(task);
      }
    },
    
    // Check if user can perform assignment actions
    canAssignTask: (task: TaskTableRow) => {
      // Add business logic here - e.g., check user permissions, task status, etc.
      const { isAssigned } = getAssignmentInfo(task);
      
      // Example business rules:
      // - Can assign unassigned tasks
      // - Can reassign assigned tasks
      // - Cannot assign completed/cancelled tasks
      const completedStatuses = ['COMPLETED', 'CANCELLED'];
      const isCompleted = completedStatuses.includes(task.status);
      
      return !isCompleted;
    },
    
    // Assignment action helpers
    getAssignmentActions: (task: TaskTableRow) => {
      const { isAssigned, canReassign, canAssign } = getAssignmentInfo(task);
      const canPerformActions = canAssignTask(task);
      
      return {
        canAssign: canAssign && canPerformActions,
        canReassign: canReassign && canPerformActions,
        canUnassign: isAssigned && canPerformActions,
        primaryAction: isAssigned ? 'reassign' : 'assign',
        primaryActionLabel: isAssigned ? 'Reassign' : 'Assign',
      };
    },
  };
}

// Simplified hook for just assignment dialogs
export function useTaskAssignmentDialogs() {
  const { openAssignmentDialog, openReassignmentDialog, quickAssign } =
    useTaskAssignment();

  return {
    openAssignmentDialog,
    openReassignmentDialog,
    quickAssign,
  };
}

// Hook for bulk assignment operations
export function useBulkTaskAssignment() {
  const { bulkAssignTasks, isAssigning } = useTaskAssignment();

  return {
    bulkAssignTasks,
    isAssigning,
  };
}
