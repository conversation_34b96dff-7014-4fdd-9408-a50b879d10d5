'use client';

import NiceModal from '@ebay/nice-modal-react';
import { TaskStatusEnum } from '@lilypad/db/enums';
import { toast } from '@lilypad/ui/components/sonner';
import { useCallback } from 'react';
import type { TaskTableRow } from '@/entities/tasks/model/schema';
import { TaskCompletionDialog } from '../ui/task-completion-dialog';
import { useTaskActions } from './use-task-actions';

export function useTaskCompletion() {
  const { completeTask, isCompleting, updateStatus } = useTaskActions({
    // onSuccess: (action, taskId) => {},
  });

  // Open completion dialog
  const openCompletionDialog = useCallback((task: TaskTableRow) => {
    NiceModal.show(TaskCompletionDialog, {
      task,
    });
  }, []);

  // Quick completion without dialog (for simple tasks)
  const quickComplete = useCallback(
    async (taskId: string, notes?: string) => {
      try {
        await completeTask(taskId, notes);
        toast.success('Task Completed', {
          description: 'Task has been successfully completed.',
        });
      } catch (error) {
        console.log('Failed to complete task:', error);
        toast.error('Failed to complete task', {
          description: 'Failed to complete task. Please try again.',
        });
      }
    },
    [completeTask]
  );

// Bulk completion for multiple tasks
const bulkCompleteTasks = useCallback(
  async (taskIds: string[], notes?: string) => {
    const results = await Promise.allSettled(
      taskIds.map((taskId) => quickComplete(taskId, notes))
    );

    const successful = results.filter((r) => r.status === 'fulfilled').length;
    const failed = results.filter((r) => r.status === 'rejected').length;

    if (successful > 0) {
      toast.success('Bulk Completion Completed', {
        description: `${successful} task(s) completed successfully${failed > 0 ? `, ${failed} failed` : ''}.`,
      });
    }

    if (failed > 0 && successful === 0) {
      toast.error('Bulk Completion Failed', {
        description: `Failed to complete ${failed} task(s).`,
      });
    }

    return results;
  },
  [quickComplete]
);

// Mark task as in progress (if it's pending)
const startTask = useCallback(
    async (taskId: string, notes?: string) => {
      try {
        await updateStatus(taskId, TaskStatusEnum.IN_PROGRESS, notes);
        toast.success('Task Started', {
          description: 'Task has been moved to in progress.',
        });
      } catch (error) {
        console.log('Failed to start task:', error);
        toast.error('Failed to start task', { description: 'Failed to start task. Please try again.'});
      }
},
    [updateStatus]
  );

// Get completion info and validation for a task
const getCompletionInfo = useCallback((task: TaskTableRow) => {
  const canComplete =
    task.status !== TaskStatusEnum.COMPLETED &&
    task.status !== TaskStatusEnum.CANCELLED;
  const canStart = task.status === TaskStatusEnum.PENDING;
  const isCompleted = task.status === TaskStatusEnum.COMPLETED;
  const isInProgress = task.status === TaskStatusEnum.IN_PROGRESS;
  const isOverdue = task.dueDate ? new Date(task.dueDate) < new Date() : false;

  return {
    canComplete,
    canStart,
    isCompleted,
    isInProgress,
    isOverdue,
    completedAt: task.completedAt,
    dueDate: task.dueDate,
    status: task.status,
  };
}, []);

// Get completion actions available for a task
const getCompletionActions = useCallback(
  (task: TaskTableRow) => {
    const { canComplete, canStart, isCompleted, isInProgress } =
      getCompletionInfo(task);

    return {
      canComplete,
      canStart,
      canQuickComplete: canComplete && isInProgress, // Can quick complete if already in progress
      needsDialog: canComplete && !isInProgress, // Needs dialog if not in progress yet
      primaryAction: canStart ? 'start' : canComplete ? 'complete' : null,
      primaryActionLabel: canStart
        ? 'Start Task'
        : canComplete
          ? 'Complete Task'
          : 'Completed',
      isCompleted,
    };
  },
  [getCompletionInfo]
);

// Check if user can perform completion actions based on business rules
const canPerformCompletionActions = useCallback(
  (task: TaskTableRow) => {
    // Add business logic here - e.g., check user permissions, task dependencies, etc.
    const { canComplete, canStart } = getCompletionInfo(task);

    // Example business rules:
    // - User must be assigned to the task or have admin permissions
    // - Task must not have blocking dependencies
    // - Task must be in a completable state

    // For now, just check basic status rules
    return canComplete || canStart;
  },
  [getCompletionInfo]
);

// Get timestamp information for completed tasks
const getTimestampInfo = useCallback((task: TaskTableRow) => {
  const now = new Date();
  const created = new Date(task.createdAt);
  const completed = task.completedAt ? new Date(task.completedAt) : null;
  const due = task.dueDate ? new Date(task.dueDate) : null;

  const timeToComplete = completed
    ? Math.round(
        (completed.getTime() - created.getTime()) / (1000 * 60 * 60 * 24)
      )
    : null;

  const daysOverdue =
    due && completed && completed > due
      ? Math.round(
          (completed.getTime() - due.getTime()) / (1000 * 60 * 60 * 24)
        )
      : null;

  const currentOverdue =
    due && !completed && now > due
      ? Math.round((now.getTime() - due.getTime()) / (1000 * 60 * 60 * 24))
      : null;

  return {
    created,
    completed,
    due,
    timeToComplete,
    daysOverdue,
    currentOverdue,
    isOverdue: Boolean(currentOverdue && currentOverdue > 0),
    wasCompletedLate: Boolean(daysOverdue && daysOverdue > 0),
  };
}, []);

return {
    // Dialog actions
    openCompletionDialog,
    
    // Direct completion actions
    quickComplete,
    bulkCompleteTasks,
    startTask,
    
    // Utilities
    getCompletionInfo,
    getCompletionActions,
    getTimestampInfo,
    canPerformCompletionActions,
    
    // State
    isCompleting,
    
    // Quick action helper
    performPrimaryAction: (task: TaskTableRow) => {
      const { primaryAction } = getCompletionActions(task);
      
      switch (primaryAction) {
        case 'start':
          return startTask(task.id);
        case 'complete':
          // Use dialog for completion to allow notes
          return openCompletionDialog(task);
        default:
          return Promise.resolve();
      }
    },
    
    // Task status helpers
    isTaskCompleted: (task: TaskTableRow) => task.status === TaskStatusEnum.COMPLETED,
    isTaskInProgress: (task: TaskTableRow) => task.status === TaskStatusEnum.IN_PROGRESS,
    isTaskPending: (task: TaskTableRow) => task.status === TaskStatusEnum.PENDING,
    isTaskOverdue: (task: TaskTableRow) => {
      if (!task.dueDate || task.status === TaskStatusEnum.COMPLETED) {
        return false;
      }
      return new Date(task.dueDate) < new Date();
    },
    
    // Completion statistics helpers
    getCompletionRate: (tasks: TaskTableRow[]) => {
      const completed = tasks.filter(t => t.status === TaskStatusEnum.COMPLETED).length;
      return tasks.length > 0 ? (completed / tasks.length) * 100 : 0;
    },
    
    getAverageCompletionTime: (tasks: TaskTableRow[]) => {
      const completedTasks = tasks.filter(t => t.completedAt);
      if (completedTasks.length === 0) { return 0; }
      
      const totalTime = completedTasks.reduce((acc, task) => {
        const created = new Date(task.createdAt);
        const completed = new Date(task.completedAt!);
        return acc + (completed.getTime() - created.getTime());
      }, 0);
      
      // Return average in days
      return Math.round(totalTime / (completedTasks.length * 1000 * 60 * 60 * 24));
    },
    
    getOverdueTasks: (tasks: TaskTableRow[]) => {
      return tasks.filter(task => {
        if (!task.dueDate || task.status === TaskStatusEnum.COMPLETED) {
          return false;
        }
        return new Date(task.dueDate) < new Date();
      });
    },
  };
}

// Simplified hook for just completion dialogs
export function useTaskCompletionDialogs() {
  const { openCompletionDialog, performPrimaryAction } = useTaskCompletion();

  return {
    openCompletionDialog,
    performPrimaryAction,
  };
}

// Hook for completion statistics and analytics
export function useTaskCompletionStats() {
  const {
    getCompletionRate,
    getAverageCompletionTime,
    getOverdueTasks,
    getTimestampInfo,
  } = useTaskCompletion();

  return {
    getCompletionRate,
    getAverageCompletionTime,
    getOverdueTasks,
    getTimestampInfo,
  };
}
