'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import type { TRPCClientError } from '@lilypad/api/client';
import { useTRPC } from '@lilypad/api/client';
import type {
  CompleteTaskInput,
  ReassignTaskInput,
  UpdateTaskDueDateInput,
  UpdateTaskPriorityInput,
  UpdateTaskStatusInput,
} from '@lilypad/api/schemas/tasks';
import { TaskPriorityEnum, TaskStatusEnum } from '@lilypad/db/enums';
import { toast } from '@lilypad/ui/components/sonner';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useCallback, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import type { TaskTableRow } from '@/entities/tasks/model/schema';

// Form schemas for UI components
const statusChangeFormSchema = z.object({
  status: z.nativeEnum(TaskStatusEnum),
  notes: z.string().optional(),
});

const priorityChangeFormSchema = z.object({
  priority: z.nativeEnum(TaskPriorityEnum),
  notes: z.string().optional(),
});

const dueDateChangeFormSchema = z.object({
  dueDate: z.date().nullable(),
  notes: z.string().optional(),
});

const reassignmentFormSchema = z.object({
  newAssigneeId: z.string().uuid('Please select a valid assignee'),
  notes: z.string().optional(),
});

const completionFormSchema = z.object({
  notes: z.string().optional(),
});

type StatusChangeForm = z.infer<typeof statusChangeFormSchema>;
type PriorityChangeForm = z.infer<typeof priorityChangeFormSchema>;
type DueDateChangeForm = z.infer<typeof dueDateChangeFormSchema>;
type ReassignmentForm = z.infer<typeof reassignmentFormSchema>;
type CompletionForm = z.infer<typeof completionFormSchema>;

interface UseTaskActionsOptions {
  onSuccess?: (action: string, taskId: string) => void;
  onError?: (error: TRPCClientError, action: string) => void;
}

export function useTaskActions(options: UseTaskActionsOptions = {}) {
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  // Generic error handler
  const handleError = useCallback(
    (error: TRPCClientError, action: string) => {
      const message = error.message || 'An unexpected error occurred';

      // Handle specific error cases
      switch (error.data?.code) {
        case 'NOT_FOUND':
          toast.error('Task Not Found', {
            description:
              'The task you are trying to update could not be found.',
          });
          break;
        case 'FORBIDDEN':
          toast.error('Permission Denied', {
            description: `You do not have permission to ${action.toLowerCase()} this task.`,
          });
          break;
        case 'CONFLICT':
          toast.error('Conflict Error', {
            description:
              'This task has been modified by someone else. Please refresh and try again.',
          });
          break;
        case 'BAD_REQUEST':
          toast.error('Invalid Request', {
            description: message,
          });
          break;
        default:
          toast.error(`${action} Failed`, {
            description: `Failed to ${action.toLowerCase()}: ${message}`,
          });
      }

      options.onError?.(error, action);
    },
    [options]
  );

  // Generic success handler
  const handleSuccess = useCallback(
    async (action: string, taskId: string) => {
      // Invalidate related queries
      await queryClient.invalidateQueries({
        queryKey: [['tasks']],
        exact: false,
      });

      toast.success(`${action} Successful`, {
        description: `Task has been successfully ${action.toLowerCase()}.`,
      });

      options.onSuccess?.(action, taskId);
    },
    [queryClient, options]
  );

  // Optimistic update helper
  const createOptimisticUpdate = useCallback(
    <T extends Record<string, any>>(taskId: string, updates: Partial<T>) => {
      return {
        onMutate: async () => {
          // Cancel outgoing refetches
          await queryClient.cancelQueries({ queryKey: [['tasks']] });

          // Snapshot previous values
          const previousTasks = queryClient.getQueryData([['tasks']]);

          // Optimistically update task data
          queryClient.setQueriesData(
            { queryKey: [['tasks']] },
            (oldData: any) => {
              if (!oldData) {
                return oldData;
              }

              // Handle different data structures (paginated vs array)
              if (oldData.data && Array.isArray(oldData.data)) {
                return {
                  ...oldData,
                  data: oldData.data.map((task: TaskTableRow) =>
                    task.id === taskId ? { ...task, ...updates } : task
                  ),
                };
              }

              if (Array.isArray(oldData)) {
                return oldData.map((task: TaskTableRow) =>
                  task.id === taskId ? { ...task, ...updates } : task
                );
              }

              return oldData;
            }
          );

          return { previousTasks };
        },
        onError: (_error: any, _variables: any, context: any) => {
          // Rollback optimistic update on error
          queryClient.setQueryData([['tasks']], context?.previousTasks);
        },
        onSettled: () => {
          // Always refetch after error or success
          void queryClient.invalidateQueries({ queryKey: [['tasks']] });
        },
      };
    },
    [queryClient]
  );

  // Status Update Mutation
  const updateStatusMutation = useMutation({
    mutationFn: async (data: UpdateTaskStatusInput) => {
      return await trpc.tasks.updateTaskStatus.mutate(data);
    },
    onSuccess: () => handleSuccess('Status Updated', ''),
    onError: (error) => handleError(error, 'Update Status'),
  });

  // Priority Update Mutation
  const updatePriorityMutation = useMutation({
    mutationFn: async (data: UpdateTaskPriorityInput) => {
      return await trpc.tasks.updateTaskPriority.mutate(data);
    },
    onSuccess: () => handleSuccess('Priority Updated', ''),
    onError: (error) => handleError(error, 'Update Priority'),
  });

  // Due Date Update Mutation
  const updateDueDateMutation = useMutation({
    mutationFn: async (data: UpdateTaskDueDateInput) => {
      return await trpc.tasks.updateTaskDueDate.mutate(data);
    },
    onSuccess: () => handleSuccess('Due Date Updated', ''),
    onError: (error) => handleError(error, 'Update Due Date'),
  });

  // Reassignment Mutation
  const reassignTaskMutation = useMutation({
    mutationFn: async (data: ReassignTaskInput) => {
      return await trpc.tasks.reassignTask.mutate(data);
    },
    onSuccess: () => handleSuccess('Task Reassigned', ''),
    onError: (error) => handleError(error, 'Reassign Task'),
  });

  // Completion Mutation
  const completeTaskMutation = useMutation({
    mutationFn: async (data: CompleteTaskInput) => {
      return await trpc.tasks.completeTask.mutate(data);
    },
    onSuccess: () => handleSuccess('Task Completed', ''),
    onError: (error) => handleError(error, 'Complete Task'),
  });

  // Form handlers for different actions
  const createStatusChangeForm = useCallback(
    (_taskId: string, currentStatus?: TaskStatusEnum) => {
      return useForm<StatusChangeForm>({
        resolver: zodResolver(statusChangeFormSchema),
        defaultValues: {
          status: currentStatus || TaskStatusEnum.PENDING,
          notes: '',
          _taskId,
        },
      });
    },
    []
  );

  const createPriorityChangeForm = useCallback(
    (_taskId: string, currentPriority?: TaskPriorityEnum) => {
      return useForm<PriorityChangeForm>({
        resolver: zodResolver(priorityChangeFormSchema),
        defaultValues: {
          priority: currentPriority || TaskPriorityEnum.MEDIUM,
          notes: '',
          _taskId,
        },
      });
    },
    []
  );

  const createDueDateChangeForm = useCallback(
    (_taskId: string, currentDueDate?: Date | null) => {
      return useForm<DueDateChangeForm>({
        resolver: zodResolver(dueDateChangeFormSchema),
        defaultValues: {
          dueDate: currentDueDate || null,
          notes: '',
          _taskId,
        },
      });
    },
    []
  );

  const createReassignmentForm = useCallback((_taskId: string) => {
    return useForm<ReassignmentForm>({
      resolver: zodResolver(reassignmentFormSchema),
      defaultValues: {
        newAssigneeId: '',
        notes: '',
        _taskId,
      },
    });
  }, []);

  const createCompletionForm = useCallback((_taskId: string) => {
    return useForm<CompletionForm>({
      resolver: zodResolver(completionFormSchema),
      defaultValues: {
        notes: '',
      },
      _taskId,
    });
  }, []);

  // Quick action methods (without forms)
  const updateStatus = useCallback(
    async (taskId: string, status: TaskStatusEnum, notes?: string) => {
      const optimisticUpdates = createOptimisticUpdate(taskId, {
        status,
        updatedAt: new Date().toISOString(),
      });

      return await updateStatusMutation.mutateAsync(
        { taskId, status, notes },
        optimisticUpdates
      );
    },
    [updateStatusMutation, createOptimisticUpdate]
  );

  const updatePriority = useCallback(
    async (taskId: string, priority: TaskPriorityEnum, notes?: string) => {
      const optimisticUpdates = createOptimisticUpdate(taskId, {
        priority,
        updatedAt: new Date().toISOString(),
      });

      return await updatePriorityMutation.mutateAsync(
        { taskId, priority, notes },
        optimisticUpdates
      );
    },
    [updatePriorityMutation, createOptimisticUpdate]
  );

  const updateDueDate = useCallback(
    async (taskId: string, dueDate: Date | null, notes?: string) => {
      const optimisticUpdates = createOptimisticUpdate(taskId, {
        dueDate: dueDate?.toISOString() || null,
        updatedAt: new Date().toISOString(),
      });

      return await updateDueDateMutation.mutateAsync(
        { taskId, dueDate, notes },
        optimisticUpdates
      );
    },
    [updateDueDateMutation, createOptimisticUpdate]
  );

  const reassignTask = useCallback(
    async (taskId: string, newAssigneeId: string, notes?: string) => {
      const optimisticUpdates = createOptimisticUpdate(taskId, {
        assignedToId: newAssigneeId,
        updatedAt: new Date().toISOString(),
      });

      return await reassignTaskMutation.mutateAsync(
        { taskId, newAssigneeId, notes },
        optimisticUpdates
      );
    },
    [reassignTaskMutation, createOptimisticUpdate]
  );

  const completeTask = useCallback(
    async (taskId: string, notes?: string) => {
      const optimisticUpdates = createOptimisticUpdate(taskId, {
        status: TaskStatusEnum.COMPLETED,
        completedAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });

      return await completeTaskMutation.mutateAsync(
        { taskId, notes },
        optimisticUpdates
      );
    },
    [completeTaskMutation, createOptimisticUpdate]
  );

  // Batch operations
  const batchUpdateStatus = useCallback(
    async (taskIds: string[], status: TaskStatusEnum, notes?: string) => {
      const results = await Promise.allSettled(
        taskIds.map((taskId) => updateStatus(taskId, status, notes))
      );

      const successful = results.filter((r) => r.status === 'fulfilled').length;
      const failed = results.filter((r) => r.status === 'rejected').length;

      if (successful > 0) {
        toast.success('Batch Update Completed', {
          description: `${successful} task(s) updated successfully${failed > 0 ? `, ${failed} failed` : ''}.`,
        });
      }

      if (failed > 0 && successful === 0) {
        toast.error('Batch Update Failed', {
          description: `Failed to update ${failed} task(s).`,
        });
      }

      return results;
    },
    [updateStatus]
  );

  // Status helpers
  const isPending = useMemo(
    () =>
      updateStatusMutation.isPending ||
      updatePriorityMutation.isPending ||
      updateDueDateMutation.isPending ||
      reassignTaskMutation.isPending ||
      completeTaskMutation.isPending,
    [
      updateStatusMutation.isPending,
      updatePriorityMutation.isPending,
      updateDueDateMutation.isPending,
      reassignTaskMutation.isPending,
      completeTaskMutation.isPending,
    ]
  );

  const hasErrors = useMemo(
    () =>
      updateStatusMutation.isError ||
      updatePriorityMutation.isError ||
      updateDueDateMutation.isError ||
      reassignTaskMutation.isError ||
      completeTaskMutation.isError,
    [
      updateStatusMutation.isError,
      updatePriorityMutation.isError,
      updateDueDateMutation.isError,
      reassignTaskMutation.isError,
      completeTaskMutation.isError,
    ]
  );

  return {
    // Quick actions
    updateStatus,
    updatePriority,
    updateDueDate,
    reassignTask,
    completeTask,

    // Batch operations
    batchUpdateStatus,

    // Form creators
    createStatusChangeForm,
    createPriorityChangeForm,
    createDueDateChangeForm,
    createReassignmentForm,
    createCompletionForm,

    // Mutation objects (for advanced usage)
    mutations: {
      updateStatus: updateStatusMutation,
      updatePriority: updatePriorityMutation,
      updateDueDate: updateDueDateMutation,
      reassignTask: reassignTaskMutation,
      completeTask: completeTaskMutation,
    },

    // Status flags
    isPending,
    hasErrors,

    // Individual loading states
    isUpdatingStatus: updateStatusMutation.isPending,
    isUpdatingPriority: updatePriorityMutation.isPending,
    isUpdatingDueDate: updateDueDateMutation.isPending,
    isReassigning: reassignTaskMutation.isPending,
    isCompleting: completeTaskMutation.isPending,

    // Error states
    statusError: updateStatusMutation.error,
    priorityError: updatePriorityMutation.error,
    dueDateError: updateDueDateMutation.error,
    reassignError: reassignTaskMutation.error,
    completeError: completeTaskMutation.error,

    // Reset functions
    resetStatusMutation: updateStatusMutation.reset,
    resetPriorityMutation: updatePriorityMutation.reset,
    resetDueDateMutation: updateDueDateMutation.reset,
    resetReassignMutation: reassignTaskMutation.reset,
    resetCompleteMutation: completeTaskMutation.reset,

    // Utility functions
    canPerformAction: (taskStatus: TaskStatusEnum) => {
      // Define business rules for when actions can be performed
      switch (taskStatus) {
        case TaskStatusEnum.COMPLETED:
          return { canUpdate: false, canReassign: false, canComplete: false };
        case TaskStatusEnum.CANCELLED:
          return { canUpdate: false, canReassign: false, canComplete: false };
        case TaskStatusEnum.REJECTED:
          return { canUpdate: false, canReassign: true, canComplete: false };
        case TaskStatusEnum.BLOCKED:
          return { canUpdate: true, canReassign: true, canComplete: false };
        default:
          return { canUpdate: true, canReassign: true, canComplete: true };
      }
    },

    getStatusColor: (status: TaskStatusEnum) => {
      switch (status) {
        case TaskStatusEnum.PENDING:
          return 'bg-yellow-100 text-yellow-800 border-yellow-200';
        case TaskStatusEnum.IN_PROGRESS:
          return 'bg-blue-100 text-blue-800 border-blue-200';
        case TaskStatusEnum.COMPLETED:
          return 'bg-green-100 text-green-800 border-green-200';
        case TaskStatusEnum.CANCELLED:
          return 'bg-gray-100 text-gray-800 border-gray-200';
        case TaskStatusEnum.BLOCKED:
          return 'bg-red-100 text-red-800 border-red-200';
        case TaskStatusEnum.REJECTED:
          return 'bg-orange-100 text-orange-800 border-orange-200';
        default:
          return 'bg-gray-100 text-gray-800 border-gray-200';
      }
    },

    getPriorityColor: (priority: TaskPriorityEnum) => {
      switch (priority) {
        case TaskPriorityEnum.LOW:
          return 'bg-green-100 text-green-800 border-green-200';
        case TaskPriorityEnum.MEDIUM:
          return 'bg-blue-100 text-blue-800 border-blue-200';
        case TaskPriorityEnum.HIGH:
          return 'bg-orange-100 text-orange-800 border-orange-200';
        case TaskPriorityEnum.URGENT:
          return 'bg-red-100 text-red-800 border-red-200';
        default:
          return 'bg-gray-100 text-gray-800 border-gray-200';
      }
    },
  };
}

// Simplified hooks for specific actions (when you only need one type of action)
export function useTaskStatusUpdate(options: UseTaskActionsOptions = {}) {
  const { updateStatus, isUpdatingStatus, statusError, resetStatusMutation } =
    useTaskActions(options);

  return {
    updateStatus,
    isUpdating: isUpdatingStatus,
    error: statusError,
    reset: resetStatusMutation,
  };
}

export function useTaskCompletion(options: UseTaskActionsOptions = {}) {
  const { completeTask, isCompleting, completeError, resetCompleteMutation } =
    useTaskActions(options);

  return {
    completeTask,
    isCompleting,
    error: completeError,
    reset: resetCompleteMutation,
  };
}

export function useTaskReassignment(options: UseTaskActionsOptions = {}) {
  const { reassignTask, isReassigning, reassignError, resetReassignMutation } =
    useTaskActions(options);

  return {
    reassignTask,
    isReassigning,
    error: reassignError,
    reset: resetReassignMutation,
  };
}
