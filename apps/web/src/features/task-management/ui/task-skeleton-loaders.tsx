import { <PERSON>, CardContent, CardHeader } from '@lilypad/ui/components/card';
import { Separator } from '@lilypad/ui/components/separator';
import { Skeleton } from '@lilypad/ui/components/skeleton';

/**
 * Basic task card skeleton
 */
export function TaskCardSkeleton() {
  return (
    <Card className="w-full">
      <CardHeader className="space-y-2 pb-2">
        <div className="flex items-start justify-between gap-2">
          <div className="flex-1 space-y-2">
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-3 w-1/2" />
          </div>
          <Skeleton className="h-6 w-16 rounded-full" />
        </div>
      </CardHeader>
      <CardContent className="space-y-3 pt-0">
        <div className="space-y-2">
          <Skeleton className="h-3 w-full" />
          <Skeleton className="h-3 w-2/3" />
        </div>

        <div className="flex items-center justify-between">
          <div className="flex gap-2">
            <Skeleton className="h-5 w-12 rounded-full" />
            <Skeleton className="h-5 w-16 rounded-full" />
          </div>
          <Skeleton className="h-6 w-6 rounded-full" />
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * Detailed task card skeleton with more elements
 */
export function DetailedTaskCardSkeleton() {
  return (
    <Card className="w-full">
      <CardHeader className="space-y-3 pb-3">
        <div className="flex items-start justify-between gap-2">
          <div className="flex-1 space-y-2">
            <Skeleton className="h-5 w-4/5" />
            <div className="flex items-center gap-2">
              <Skeleton className="h-4 w-4 rounded-full" />
              <Skeleton className="h-3 w-24" />
            </div>
          </div>
          <div className="flex gap-2">
            <Skeleton className="h-6 w-20 rounded-full" />
            <Skeleton className="h-6 w-6" />
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4 pt-0">
        <div className="space-y-2">
          <Skeleton className="h-3 w-full" />
          <Skeleton className="h-3 w-4/5" />
          <Skeleton className="h-3 w-3/5" />
        </div>

        <Separator />

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Skeleton className="h-6 w-6 rounded-full" />
            <Skeleton className="h-3 w-20" />
          </div>
          <Skeleton className="h-3 w-16" />
        </div>

        <div className="flex gap-2">
          <Skeleton className="h-5 w-14 rounded-full" />
          <Skeleton className="h-5 w-12 rounded-full" />
          <Skeleton className="h-5 w-18 rounded-full" />
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * Task list skeleton for table-like layouts
 */
export function TaskListRowSkeleton() {
  return (
    <div className="flex items-center gap-4 border-b p-4">
      <Skeleton className="h-4 w-4" />

      <div className="flex-1 space-y-1">
        <Skeleton className="h-4 w-3/4" />
        <Skeleton className="h-3 w-1/2" />
      </div>

      <div className="flex items-center gap-4">
        <Skeleton className="h-6 w-20 rounded-full" />
        <Skeleton className="h-6 w-16 rounded-full" />
        <Skeleton className="h-6 w-6 rounded-full" />
        <Skeleton className="h-3 w-16" />
        <Skeleton className="h-4 w-4" />
      </div>
    </div>
  );
}

/**
 * Complete task list skeleton
 */
export function TaskListSkeleton({
  count = 6,
  variant = 'card'
}: {
  count?: number;
  variant?: 'card' | 'row' | 'detailed';
}) {
  const componentsMap = {
    row: TaskListRowSkeleton,
    detailed: DetailedTaskCardSkeleton,
    card: TaskCardSkeleton,
  };
  const SkeletonComponent = componentsMap[variant];

  return (
    <div className={`space-y-${variant === 'row' ? '0' : '4'}`}>
      {Array.from({ length: count }, (_, i) => (
        <SkeletonComponent key={i} />
      ))}
    </div>
  );
}

/**
 * Kanban column skeleton
 */
export function KanbanColumnSkeleton({ cardCount = 3 }: { cardCount?: number }) {
  return (
    <div className="flex h-full w-80 flex-col rounded-lg border bg-muted/50">
      <div className="flex items-center justify-between border-b p-4">
        <div className="flex items-center gap-2">
          <Skeleton className="h-5 w-24" />
          <Skeleton className="h-5 w-8 rounded-full" />
        </div>
        <Skeleton className="h-4 w-4" />
      </div>

      <div className="flex-1 space-y-3 overflow-hidden p-3">
        {Array.from({ length: cardCount }, (_, i) => (
          <div className="slide-in-from-top animate-in duration-200" key={i} style={{ animationDelay: `${i * 100}ms` }}>
            <TaskCardSkeleton />
          </div>
        ))}
      </div>

      <div className="border-t p-3">
        <Skeleton className="h-8 w-full" />
      </div>
    </div>
  );
}

/**
 * Complete kanban board skeleton
 */
export function TaskKanbanSkeleton({
  columnCount = 4,
  cardsPerColumn = [3, 4, 2, 5]
}: {
  columnCount?: number;
  cardsPerColumn?: number[];
}) {
  const cardCounts = cardsPerColumn.length >= columnCount
    ? cardsPerColumn.slice(0, columnCount)
    : [...cardsPerColumn, ...new Array(columnCount - cardsPerColumn.length).fill(3)];

  return (
    <div className="flex h-full gap-4 overflow-x-auto pb-4">
      {Array.from({ length: columnCount }, (_, i) => (
        <div
          className="flex-shrink-0"
          key={i}
        >
          <KanbanColumnSkeleton cardCount={cardCounts[i]} />
        </div>
      ))}
    </div>
  );
}

/**
 * Task detail sheet skeleton
 */
export function TaskDetailSkeleton() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="space-y-4">
        <div className="flex items-start justify-between gap-4">
          <div className="flex-1 space-y-2">
            <Skeleton className="h-6 w-4/5" />
            <div className="flex gap-2">
              <Skeleton className="h-5 w-20 rounded-full" />
              <Skeleton className="h-5 w-16 rounded-full" />
            </div>
          </div>
          <Skeleton className="h-8 w-8" />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-1">
            <Skeleton className="h-3 w-16" />
            <Skeleton className="h-4 w-24" />
          </div>
          <div className="space-y-1">
            <Skeleton className="h-3 w-20" />
            <Skeleton className="h-4 w-28" />
          </div>
        </div>
      </div>

      <Separator />

      {/* Description */}
      <div className="space-y-3">
        <Skeleton className="h-4 w-20" />
        <div className="space-y-2">
          <Skeleton className="h-3 w-full" />
          <Skeleton className="h-3 w-4/5" />
          <Skeleton className="h-3 w-3/5" />
        </div>
      </div>

      <Separator />

      {/* Metadata */}
      <div className="grid grid-cols-2 gap-6">
        <div className="space-y-3">
          <Skeleton className="h-4 w-16" />
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Skeleton className="h-6 w-6 rounded-full" />
              <Skeleton className="h-3 w-24" />
            </div>
            <div className="flex items-center gap-2">
              <Skeleton className="h-4 w-4" />
              <Skeleton className="h-3 w-20" />
            </div>
          </div>
        </div>

        <div className="space-y-3">
          <Skeleton className="h-4 w-20" />
          <div className="space-y-2">
            <Skeleton className="h-3 w-28" />
            <Skeleton className="h-3 w-24" />
          </div>
        </div>
      </div>

      <Separator />

      {/* Dependencies */}
      <div className="space-y-3">
        <Skeleton className="h-4 w-24" />
        <div className="space-y-2">
          {Array.from({ length: 2 }, (_, i) => (
            <div className="flex items-center gap-3 rounded-md border p-2" key={i} >
              <Skeleton className="h-4 w-4" />
              <Skeleton className="h-3 w-32" />
              <Skeleton className="ml-auto h-5 w-16 rounded-full" />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

/**
 * Task filters skeleton
 */
export function TaskFiltersSkeleton() {
  return (
    <div className="flex items-center gap-3 border-b p-4">
      <Skeleton className="h-8 w-40" />
      <div className="flex gap-2">
        <Skeleton className="h-8 w-24" />
        <Skeleton className="h-8 w-20" />
        <Skeleton className="h-8 w-28" />
      </div>
      <div className="ml-auto flex gap-2">
        <Skeleton className="h-8 w-8" />
        <Skeleton className="h-8 w-8" />
      </div>
    </div>
  );
}

/**
 * Task stats skeleton
 */
export function TaskStatsSkeleton() {
  return (
    <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
      {Array.from({ length: 4 }, (_, i) => (
        <Card key={i}>
          <CardContent className="space-y-2 p-4">
            <Skeleton className="h-3 w-20" />
            <Skeleton className="h-6 w-12" />
            <Skeleton className="h-2 w-full" />
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

/**
 * Task management container skeleton (complete loading state)
 */
export function TaskManagementSkeleton({ view = 'list' }: { view?: 'list' | 'kanban' }) {
  return (
    <div className="space-y-6">
      <TaskStatsSkeleton />
      <TaskFiltersSkeleton />

      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Skeleton className="h-6 w-32" />
          <div className="flex gap-2">
            <Skeleton className="h-8 w-8" />
            <Skeleton className="h-8 w-8" />
          </div>
        </div>

        {view === 'kanban' ? (
          <TaskKanbanSkeleton />
        ) : (
          <TaskListSkeleton count={4} variant="detailed" />
        )}
      </div>
    </div>
  );
}

export function TaskDetailSheetLoadingSkeleton() {
  return (
    <div className="fixed inset-y-0 right-0 z-50 w-full border-l bg-background sm:max-w-lg">
      <div className="flex h-full flex-col">
        <div className="border-b p-6">
          <div className="space-y-2">
            <div className="h-6 w-3/4 animate-pulse rounded bg-muted" />
            <div className="h-4 w-1/2 animate-pulse rounded bg-muted" />
          </div>
        </div>
        <div className="flex-1 p-6">
          <TaskDetailSkeleton />
        </div>
      </div>
    </div>
  );
}
