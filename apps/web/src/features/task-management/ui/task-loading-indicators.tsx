'use client';

import { Badge } from '@lilypad/ui/components/badge';
import { Button } from '@lilypad/ui/components/button';
import { Progress } from '@lilypad/ui/components/progress';
import { Skeleton } from '@lilypad/ui/components/skeleton';
import { cn } from '@lilypad/ui/lib/utils';
import {
  Calendar,
  CheckCircle,
  Clock,
  Loader2,
  Pause,
  Play,
  RefreshCw,
  Target,
  User,
  XCircle
} from 'lucide-react';
import { useEffect, useState } from 'react';

export interface LoadingIndicatorProps {
  isLoading?: boolean;
  loadingText?: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'spinner' | 'pulse' | 'progress' | 'skeleton';
}

/**
 * Basic loading indicator for general use
 */
export function LoadingIndicator({
  isLoading = false,
  loadingText = 'Loading...',
  className,
  size = 'md',
  variant = 'spinner'
}: LoadingIndicatorProps) {
  if (!isLoading) { return null; }

  const sizeClasses = {
    sm: 'size-3',
    md: 'size-4',
    lg: 'size-6'
  };

  if (variant === 'skeleton') {
    return <Skeleton className={cn('h-4 w-20', className)} />;
  }

  if (variant === 'pulse') {
    return (
      <div className={cn('flex items-center gap-2', className)}>
        <div className={cn('animate-pulse rounded-full bg-primary', sizeClasses[size])} />
        <span className="text-muted-foreground text-sm">{loadingText}</span>
      </div>
    );
  }

  if (variant === 'progress') {
    return (
      <div className={cn('space-y-2', className)}>
        <div className="text-muted-foreground text-sm">{loadingText}</div>
        <Progress value={undefined} className="h-1" />
      </div>
    );
  }

  return (
    <div className={cn('flex items-center gap-2', className)}>
      <Loader2 className={cn('animate-spin', sizeClasses[size])} />
      <span className="text-muted-foreground text-sm">{loadingText}</span>
    </div>
  );
}

/**
 * Task status change loading indicator
 */
export interface TaskStatusChangeIndicatorProps {
  isChanging: boolean;
  currentStatus: string;
  targetStatus?: string;
  className?: string;
}

export function TaskStatusChangeIndicator({
  isChanging,
  currentStatus,
  targetStatus,
  className
}: TaskStatusChangeIndicatorProps) {
  if (!isChanging) { return null; }

  const _getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return <Clock className="size-3" />;
      case 'in_progress':
        return <Play className="size-3" />;
      case 'blocked':
        return <Pause className="size-3" />;
      case 'completed':
        return <CheckCircle className="size-3" />;
      case 'cancelled':
      case 'rejected':
        return <XCircle className="size-3" />;
      default:
        return <RefreshCw className="size-3" />;
    }
  };

  return (
    <div className={cn('flex items-center gap-2 text-sm', className)}>
      <Loader2 className="size-3 animate-spin text-primary" />
      <span className="text-muted-foreground">
        Updating status
        {targetStatus && (
          <>
            {' '}to{' '}
            <Badge variant="outline" className="text-xs">
              {targetStatus.replace('_', ' ')}
            </Badge>
          </>
        )}
      </span>
    </div>
  );
}

/**
 * Task assignment loading indicator
 */
export interface TaskAssignmentIndicatorProps {
  isAssigning: boolean;
  action: 'assign' | 'reassign' | 'unassign';
  assigneeName?: string;
  className?: string;
}

export function TaskAssignmentIndicator({
  isAssigning,
  action,
  assigneeName,
  className
}: TaskAssignmentIndicatorProps) {
  if (!isAssigning) { return null; }

  const actionText = {
    assign: 'Assigning task',
    reassign: 'Reassigning task',
    unassign: 'Unassigning task'
  };

  return (
    <div className={cn('flex items-center gap-2 text-sm', className)}>
      <Loader2 className="size-3 animate-spin text-primary" />
      <User className="size-3 text-muted-foreground" />
      <span className="text-muted-foreground">
        {actionText[action]}
        {assigneeName && (
          <>
            {' '}to{' '}
            <span className="font-medium">{assigneeName}</span>
          </>
        )}
      </span>
    </div>
  );
}

/**
 * Task completion loading indicator
 */
export function TaskCompletionIndicator({
  isCompleting,
  className
}: {
  isCompleting: boolean;
  className?: string;
}) {
  if (!isCompleting) { return null; }

  return (
    <div className={cn('flex items-center gap-2 text-sm', className)}>
      <Loader2 className="size-3 animate-spin text-green-600" />
      <CheckCircle className="size-3 text-green-600" />
      <span className="text-muted-foreground">Completing task...</span>
    </div>
  );
}

/**
 * Task rejection loading indicator
 */
export function TaskRejectionIndicator({
  isRejecting,
  className
}: {
  isRejecting: boolean;
  className?: string;
}) {
  if (!isRejecting) { return null; }

  return (
    <div className={cn('flex items-center gap-2 text-sm', className)}>
      <Loader2 className="size-3 animate-spin text-red-600" />
      <XCircle className="size-3 text-red-600" />
      <span className="text-muted-foreground">Rejecting task...</span>
    </div>
  );
}

/**
 * Bulk operation loading indicator
 */
export interface BulkOperationIndicatorProps {
  isProcessing: boolean;
  operation: string;
  total: number;
  completed: number;
  failed: number;
  className?: string;
}

export function BulkOperationIndicator({
  isProcessing,
  operation,
  total,
  completed,
  failed,
  className
}: BulkOperationIndicatorProps) {
  if (!isProcessing && completed === 0) { return null; }

  const progress = total > 0 ? ((completed + failed) / total) * 100 : 0;
  const isComplete = completed + failed >= total;

  return (
    <div className={cn('space-y-2 rounded-md border bg-muted/50 p-3', className)}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {isProcessing && !isComplete && (
            <Loader2 className="size-4 animate-spin text-primary" />
          )}
          {isComplete && (
            <CheckCircle className="size-4 text-green-600" />
          )}
          <span className="font-medium text-sm">
            {isComplete ? `${operation} Complete` : `${operation} in Progress`}
          </span>
        </div>
        <Badge variant="outline" className="text-xs">
          {completed + failed}/{total}
        </Badge>
      </div>

      <Progress value={progress} className="h-2" />

      <div className="flex justify-between text-muted-foreground text-xs">
        <span>
          {completed} completed
          {failed > 0 && `, ${failed} failed`}
        </span>
        <span>{Math.round(progress)}%</span>
      </div>
    </div>
  );
}

/**
 * Task action button with loading state
 */
export interface TaskActionButtonProps {
  isLoading: boolean;
  loadingText?: string;
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  disabled?: boolean;
  className?: string;
}

export function TaskActionButton({
  isLoading,
  loadingText,
  children,
  onClick,
  variant = 'default',
  size = 'default',
  disabled,
  className
}: TaskActionButtonProps) {
  return (
    <Button
      onClick={onClick}
      variant={variant}
      size={size}
      disabled={disabled || isLoading}
      className={cn('flex items-center gap-2', className)}
    >
      {isLoading && <Loader2 className="size-3 animate-spin" />}
      {isLoading ? loadingText : children}
    </Button>
  );
}

/**
 * Auto-updating progress indicator for long-running operations
 */
export function AutoProgressIndicator({
  isActive,
  duration = 30_000, // 30 seconds
  onComplete,
  className
}: {
  isActive: boolean;
  duration?: number;
  onComplete?: () => void;
  className?: string;
}) {
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    if (!isActive) {
      setProgress(0);
      return;
    }

    const interval = setInterval(() => {
      setProgress(prev => {
        const newProgress = prev + (100 / (duration / 100));
        if (newProgress >= 100) {
          onComplete?.();
          return 100;
        }
        return newProgress;
      });
    }, 100);

    return () => clearInterval(interval);
  }, [isActive, duration, onComplete]);

  if (!isActive) { return null; }

  return (
    <div className={cn('space-y-2', className)}>
      <div className="flex items-center justify-between text-sm">
        <span className="text-muted-foreground">Processing...</span>
        <span className="text-muted-foreground">{Math.round(progress)}%</span>
      </div>
      <Progress value={progress} className="h-1" />
    </div>
  );
}

/**
 * Task field update loading indicator
 */
export function TaskFieldUpdateIndicator({
  isUpdating,
  field,
  className
}: {
  isUpdating: boolean;
  field: string;
  className?: string;
}) {
  if (!isUpdating) { return null; }

  const getFieldIcon = (fieldName: string) => {
    switch (fieldName.toLowerCase()) {
      case 'priority':
        return <Target className="size-3" />;
      case 'duedate':
      case 'due_date':
        return <Calendar className="size-3" />;
      case 'assignee':
      case 'assigned_to':
        return <User className="size-3" />;
      default:
        return <RefreshCw className="size-3" />;
    }
  };

  return (
    <div className={cn('flex items-center gap-2 text-sm', className)}>
      <Loader2 className="size-3 animate-spin text-primary" />
      {getFieldIcon(field)}
      <span className="text-muted-foreground">
        Updating {field.replace('_', ' ')}...
      </span>
    </div>
  );
}

/**
 * Inline loading state for task cards
 */
export function TaskCardLoadingOverlay({
  isLoading,
  operation,
  className
}: {
  isLoading: boolean;
  operation?: string;
  className?: string;
}) {
  if (!isLoading) { return null; }

  return (
    <div className={cn(
      'absolute inset-0 flex items-center justify-center rounded-md bg-background/80 backdrop-blur-sm',
      className
    )}>
      <div className="flex items-center gap-2 text-sm">
        <Loader2 className="size-4 animate-spin text-primary" />
        <span className="text-muted-foreground">
          {operation || 'Updating...'}
        </span>
      </div>
    </div>
  );
}

/**
 * Loading state for dropdowns and selects
 */
export function SelectLoadingIndicator({
  isLoading,
  className
}: {
  isLoading: boolean;
  className?: string;
}) {
  if (!isLoading) { return null; }

  return (
    <div className={cn('flex items-center gap-2 px-3 py-2', className)}>
      <Loader2 className="size-3 animate-spin" />
      <span className="text-muted-foreground text-sm">Loading options...</span>
    </div>
  );
}

/**
 * Toast-style loading notification
 */
export function TaskLoadingToast({
  isVisible,
  operation,
  onDismiss,
  className
}: {
  isVisible: boolean;
  operation: string;
  onDismiss?: () => void;
  className?: string;
}) {
  if (!isVisible) { return null; }

  return (
    <div className={cn(
      'fixed right-4 bottom-4 z-50 flex items-center gap-2 rounded-lg border bg-background p-3 shadow-lg',
      className
    )}>
      <Loader2 className="size-4 animate-spin text-primary" />
      <span className="text-sm">{operation}</span>
      {onDismiss && (
        <Button
          variant="ghost"
          size="sm"
          onClick={onDismiss}
          className="h-auto p-1"
        >
          <XCircle className="size-3" />
        </Button>
      )}
    </div>
  );
} 