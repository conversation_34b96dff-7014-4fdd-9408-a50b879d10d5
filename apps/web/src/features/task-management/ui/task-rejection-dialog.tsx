'use client';

import NiceModal, { type NiceModalHocProps } from '@ebay/nice-modal-react';
import { Alert, AlertDescription } from '@lilypad/ui/components/alert';
import { Button } from '@lilypad/ui/components/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@lilypad/ui/components/dialog';
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from '@lilypad/ui/components/drawer';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@lilypad/ui/components/form';
import { Textarea } from '@lilypad/ui/components/textarea';
import { useMediaQuery } from '@lilypad/ui/hooks/use-media-query';
import { MediaQueries } from '@lilypad/ui/lib/media-queries';
import { cn } from '@lilypad/ui/lib/utils';
import { Alert<PERSON>rian<PERSON>, <PERSON>, } from 'lucide-react';
import { useState } from 'react';
import type { TaskTableRow } from '@/entities/tasks/model/schema';
import { useRejectTask } from '../model/use-reject-task';

const title = 'Reject Task';
const description = 'Provide a reason for rejecting this task. This action cannot be undone.';

export type TaskRejectionDialogProps = NiceModalHocProps & {
  task: TaskTableRow;
};

enum RejectionStep {
  REASON_INPUT = 'reason_input',
  CONFIRMATION = 'confirmation',
}

export const TaskRejectionDialog = NiceModal.create<TaskRejectionDialogProps>(
  ({ task }) => {
    const mdUp = useMediaQuery(MediaQueries.MdUp, { ssr: false });
    const [currentStep, setCurrentStep] = useState<RejectionStep>(RejectionStep.REASON_INPUT);

    const {
      modal,
      methods,
      onSubmit,
      canSubmit,
      isSubmitting,
      rejectionReason
    } = useRejectTask({
      taskId: task.id,
      onSuccess: () => {
        modal.handleClose();
      },
    });

    const handleNextStep = () => {
      if (currentStep === RejectionStep.REASON_INPUT && rejectionReason.trim()) {
        setCurrentStep(RejectionStep.CONFIRMATION);
      }
    };

    const handleBackStep = () => {
      if (currentStep === RejectionStep.CONFIRMATION) {
        setCurrentStep(RejectionStep.REASON_INPUT);
      }
    };

    const renderReasonInput = (
      <div className="space-y-6">
        <div className="space-y-4">
          <Alert variant="warning" className="flex flex-row items-start gap-3">
            <AlertTriangle className="mt-0.5 size-4 shrink-0 text-amber-600" />
            <AlertDescription className="flex-1">
              <strong className="text-foreground">Task Information</strong>
              <div className="mt-2 space-y-1 text-sm">
                <p><strong>Type:</strong> {task.taskType}</p>
                <p><strong>Status:</strong> {task.status}</p>
                <p><strong>Priority:</strong> {task.priority}</p>
                {task.dueDate && (
                  <p><strong>Due Date:</strong> {new Date(task.dueDate).toLocaleDateString()}</p>
                )}
              </div>
            </AlertDescription>
          </Alert>

          <FormField
            control={methods.control}
            name="reason"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="font-medium text-base">
                  Reason for Rejection
                  <span className="ml-1 text-destructive">*</span>
                </FormLabel>
                <FormControl>
                  <Textarea
                    {...field}
                    placeholder="Please provide a detailed reason for rejecting this task..."
                    className="min-h-[120px] resize-none"
                    disabled={isSubmitting}
                  />
                </FormControl>
                <FormMessage />
                <p className="text-muted-foreground text-xs">
                  This reason will be sent to the task assigner and logged in the task history.
                </p>
              </FormItem>
            )}
          />
        </div>
      </div>
    );

    const renderConfirmation = (
      <div className="space-y-6">
        <Alert variant="destructive" className="flex flex-row items-start gap-3">
          <Ban className="mt-0.5 size-4 shrink-0" />
          <AlertDescription className="flex-1">
            <strong className="text-foreground">Confirm Task Rejection</strong>
            <p className="mt-2">
              Are you sure you want to reject this task? This action will:
            </p>
            <ul className="mt-2 list-inside list-disc space-y-1 text-sm">
              <li>Mark the task as rejected and remove it from your task list</li>
              <li>Notify the task assigner with your rejection reason</li>
              <li>Create a new task for reassignment to another team member</li>
              <li>Log this action in the task history</li>
            </ul>
            <p className="mt-3 font-medium text-destructive text-sm">
              This action cannot be undone.
            </p>
          </AlertDescription>
        </Alert>

        <div className="rounded-lg border bg-muted/50 p-4">
          <h4 className="mb-2 font-medium text-sm">Rejection Reason:</h4>
          <p className="whitespace-pre-wrap text-muted-foreground text-sm">
            {rejectionReason}
          </p>
        </div>
      </div>
    );

    const renderForm = (
      <div
        className={cn(
          'min-h-0 flex-1 overflow-y-auto',
          mdUp ? 'px-6 py-4' : 'p-4'
        )}
      >
        <Form {...methods}>
          <form className="space-y-6" onSubmit={methods.handleSubmit(onSubmit)}>
            {currentStep === RejectionStep.REASON_INPUT && renderReasonInput}
            {currentStep === RejectionStep.CONFIRMATION && renderConfirmation}
          </form>
        </Form>
      </div>
    );

    const renderButtons = (
      <div
        className={cn(
          'flex flex-shrink-0 gap-2 bg-secondary p-4 md:justify-between md:rounded-b-md'
        )}
      >
        {currentStep === RejectionStep.REASON_INPUT ? (
          <>
            <Button
              type="button"
              variant="outline"
              size="sm"
              className="w-1/2 md:w-auto"
              onClick={modal.handleClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="button"
              size="sm"
              className="w-1/2 md:w-auto"
              onClick={handleNextStep}
              disabled={!rejectionReason.trim() || isSubmitting}
            >
              Next: Review
            </Button>
          </>
        ) : (
          <>
            <Button
              type="button"
              variant="outline"
              size="sm"
              className="w-1/3 md:w-auto"
              onClick={handleBackStep}
              disabled={isSubmitting}
            >
              Back
            </Button>
            <div className="flex w-2/3 gap-2 md:w-auto">
              <Button
                type="button"
                variant="outline"
                size="sm"
                className="flex-1 md:w-auto"
                onClick={modal.handleClose}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="destructive"
                size="sm"
                className="flex-1 md:w-auto"
                onClick={methods.handleSubmit(onSubmit)}
                disabled={!canSubmit || isSubmitting}
              >
                {isSubmitting ? 'Rejecting...' : 'Confirm Rejection'}
              </Button>
            </div>
          </>
        )}
      </div>
    );

    if (mdUp) {
      return (
        <Dialog {...modal.register}>
          <DialogContent className="flex max-h-[90vh] max-w-2xl flex-col p-0">
            <DialogHeader className="flex-shrink-0 space-y-2 p-6 pb-4">
              <DialogTitle className="flex items-center gap-2">
                <Ban className="size-5 text-destructive" />
                {title}
              </DialogTitle>
              <DialogDescription>{description}</DialogDescription>
            </DialogHeader>
            {renderForm}
            {renderButtons}
          </DialogContent>
        </Dialog>
      );
    }

    return (
      <Drawer {...modal.register}>
        <DrawerContent className="flex max-h-[95vh] flex-col">
          <DrawerHeader className="flex-shrink-0 space-y-2 pb-4">
            <DrawerTitle className="flex items-center gap-2">
              <Ban className="size-5 text-destructive" />
              {title}
            </DrawerTitle>
            <DrawerDescription>{description}</DrawerDescription>
          </DrawerHeader>
          {renderForm}
          {renderButtons}
        </DrawerContent>
      </Drawer>
    );
  }
); 