'use client';

import { useTRPC } from '@lilypad/api/client';
import { Alert, AlertDescription } from '@lilypad/ui/components/alert';
import { Avatar, AvatarFallback, AvatarImage } from '@lilypad/ui/components/avatar';
import { Badge } from '@lilypad/ui/components/badge';
import { Button } from '@lilypad/ui/components/button';
import { Separator } from '@lilypad/ui/components/separator';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from '@lilypad/ui/components/sheet';
import { Skeleton } from '@lilypad/ui/components/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@lilypad/ui/components/tabs';
import { useSuspenseQuery } from '@tanstack/react-query';
import {
  Calendar,
  Clock,
  User,
  AlertCircle,
  CheckCircle,
  ArrowRight,
  Calendar as CalendarIcon,
  UserCheck,
  Play,
} from 'lucide-react';
import { Suspense, useState } from 'react';
import type { TaskTableRow } from '@/entities/tasks/model/schema';
import { useTaskActions } from '../model/use-task-actions';
import { useTaskAssignment } from '../model/use-task-assignment';
import { useTaskCompletion } from '../model/use-task-completion';
import { TaskErrorWrapper } from './task-error-boundary';
import { TaskNotFoundFallback, TaskPermissionDeniedFallback } from './task-error-fallbacks';
import { TaskDetailSkeleton } from './task-skeleton-loaders';

interface TaskDetailSheetProps {
  taskId: string;
  isOpen: boolean;
  onClose: () => void;
}

export function TaskDetailSheet({ taskId, isOpen, onClose }: TaskDetailSheetProps) {
  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent className="flex w-full flex-col overflow-hidden sm:max-w-lg">
        <TaskErrorWrapper resetKeys={[taskId]}>
          <Suspense fallback={<TaskDetailSheetSkeleton />}>
            <TaskDetailContent taskId={taskId} onClose={onClose} />
          </Suspense>
        </TaskErrorWrapper>
      </SheetContent>
    </Sheet>
  );
}

function TaskDetailContent({ taskId, onClose }: { taskId: string; onClose: () => void }) {
  const trpc = useTRPC();
  const [activeTab, setActiveTab] = useState('details');

  // Fetch task details with error handling
  const { data: task, isError, error } = useSuspenseQuery(
    trpc.tasks.getTaskById.queryOptions(taskId, {
      staleTime: 30 * 1000, // 30 seconds
    })
  );

  // Handle loading and error states
  if (isError) {
    if (error?.data?.code === 'NOT_FOUND') {
      return <TaskNotFoundFallback taskId={taskId} onGoBack={onClose} />;
    }
    if (error?.data?.code === 'FORBIDDEN') {
      return <TaskPermissionDeniedFallback onAction={onClose} />;
    }
    throw error; // Let error boundary handle other errors
  }

  if (!task) {
    return <TaskNotFoundFallback taskId={taskId} onGoBack={onClose} />;
  }

  return (
    <>
      <TaskDetailHeader task={task} onClose={onClose} />

      <div className="flex-1 overflow-hidden">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="flex h-full flex-col">
          <TabsList className="mb-4 grid w-full grid-cols-4">
            <TabsTrigger value="details">Details</TabsTrigger>
            <TabsTrigger value="dependencies">Dependencies</TabsTrigger>
            <TabsTrigger value="history">History</TabsTrigger>
            <TabsTrigger value="comments">Comments</TabsTrigger>
          </TabsList>

          <div className="flex-1 overflow-auto">
            <TabsContent value="details" className="mt-0">
              <Suspense fallback={<TaskDetailsSkeleton />}>
                <TaskDetailsTab task={task} />
              </Suspense>
            </TabsContent>

            <TabsContent value="dependencies" className="mt-0">
              <Suspense fallback={<TaskDependenciesSkeleton />}>
                <TaskDependenciesTab taskId={taskId} />
              </Suspense>
            </TabsContent>

            <TabsContent value="history" className="mt-0">
              <Suspense fallback={<TaskHistorySkeleton />}>
                <TaskHistoryTab taskId={taskId} />
              </Suspense>
            </TabsContent>

            <TabsContent value="comments" className="mt-0">
              <Suspense fallback={<TaskCommentsSkeleton />}>
                <TaskCommentsTab taskId={taskId} />
              </Suspense>
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </>
  );
}

function TaskDetailHeader({ task, onClose }: { task: TaskTableRow; onClose: () => void }) {
  const { getCompletionActions, performPrimaryAction } = useTaskCompletion();
  const { getAssignmentActions, quickAssign } = useTaskAssignment();
  const { isCompleting, isReassigning } = useTaskActions();

  const completionActions = getCompletionActions(task);
  const assignmentActions = getAssignmentActions(task);

  const isLoading = isCompleting || isReassigning;

  return (
    <>
      <SheetHeader className="space-y-4">
        <div className="flex items-start justify-between gap-4">
          <div className="min-w-0 flex-1">
            <SheetTitle className="font-semibold text-lg leading-tight">
              {task.title}
            </SheetTitle>
            <SheetDescription className="mt-1">
              {task.studentName && (
                <span>Student: {task.studentName}</span>
              )}
              {task.schoolName && task.studentName && (
                <span> • </span>
              )}
              {task.schoolName && (
                <span>School: {task.schoolName}</span>
              )}
            </SheetDescription>
          </div>

          <div className="flex gap-2">
            {completionActions.canComplete && (
              <Button
                size="sm"
                variant={completionActions.isCompleted ? "secondary" : "default"}
                onClick={() => performPrimaryAction(task)}
                disabled={isLoading}
                className="flex items-center gap-1.5"
              >
                {completionActions.primaryAction === 'start' ? (
                  <Play className="size-3" />
                ) : (
                  <CheckCircle className="size-3" />
                )}
                {isCompleting ? 'Processing...' : completionActions.primaryActionLabel}
              </Button>
            )}

            <Button
              size="sm"
              variant="outline"
              onClick={() => quickAssign(task)}
              disabled={isLoading}
              className="flex items-center gap-1.5"
            >
              <UserCheck className="size-3" />
              {isReassigning ? 'Assigning...' : assignmentActions.primaryActionLabel}
            </Button>
          </div>
        </div>

        <div className="flex flex-wrap gap-2">
          <Badge variant={getStatusVariant(task.status)}>
            {task.status.replace('_', ' ')}
          </Badge>
          <Badge variant={getPriorityVariant(task.priority)}>
            {task.priority}
          </Badge>
          {task.taskType && (
            <Badge variant="outline">
              {task.taskType.replace('_', ' ')}
            </Badge>
          )}
        </div>
      </SheetHeader>

      <Separator />
    </>
  );
}

function TaskDetailsTab({ task }: { task: TaskTableRow }) {
  return (
    <div className="space-y-6">
      {/* Description */}
      {task.description && (
        <div className="space-y-2">
          <h4 className="font-medium text-sm">Description</h4>
          <p className="whitespace-pre-wrap text-muted-foreground text-sm">
            {task.description}
          </p>
        </div>
      )}

      {/* Assignment Information */}
      <div className="space-y-3">
        <h4 className="font-medium text-sm">Assignment</h4>
        <div className="space-y-2">
          {task.assignedToName ? (
            <div className="flex items-center gap-2">
              <Avatar className="size-6">
                <AvatarImage src={task.assignedToAvatar} />
                <AvatarFallback className="text-xs">
                  {getInitials(task.assignedToName)}
                </AvatarFallback>
              </Avatar>
              <span className="text-sm">{task.assignedToName}</span>
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <User className="size-4 text-muted-foreground" />
              <span className="text-muted-foreground text-sm">Unassigned</span>
            </div>
          )}

          {task.assignedByName && (
            <div className="text-muted-foreground text-xs">
              Assigned by {task.assignedByName}
            </div>
          )}
        </div>
      </div>

      {/* Dates */}
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <h4 className="font-medium text-sm">Created</h4>
          <div className="flex items-center gap-1 text-muted-foreground text-sm">
            <Calendar className="size-3" />
            {new Date(task.createdAt).toLocaleDateString()}
          </div>
        </div>

        {task.dueDate && (
          <div className="space-y-2">
            <h4 className="font-medium text-sm">Due Date</h4>
            <div className="flex items-center gap-1 text-muted-foreground text-sm">
              <Clock className="size-3" />
              {new Date(task.dueDate).toLocaleDateString()}
              {new Date(task.dueDate) < new Date() && task.status !== 'COMPLETED' && (
                <Badge variant="destructive" className="ml-1 text-xs">
                  Overdue
                </Badge>
              )}
            </div>
          </div>
        )}
      </div>

      {task.completedAt && (
        <div className="space-y-2">
          <h4 className="font-medium text-sm">Completed</h4>
          <div className="flex items-center gap-1 text-muted-foreground text-sm">
            <CheckCircle className="size-3" />
            {new Date(task.completedAt).toLocaleDateString()}
          </div>
        </div>
      )}

      {/* Notes */}
      {task.notes && (
        <div className="space-y-2">
          <h4 className="font-medium text-sm">Notes</h4>
          <div className="rounded-md border bg-muted/50 p-3 text-sm">
            {task.notes}
          </div>
        </div>
      )}
    </div>
  );
}

function TaskDependenciesTab({ taskId }: { taskId: string }) {
  const trpc = useTRPC();

  const { data: dependencies, isLoading, error } = useSuspenseQuery(
    trpc.tasks.getTaskDependencies.queryOptions(taskId)
  );

  if (isLoading) {
    return <TaskDependenciesSkeleton />;
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="size-4" />
        <AlertDescription>
          Failed to load task dependencies. Please try again.
        </AlertDescription>
      </Alert>
    );
  }

  if (!dependencies || (dependencies.predecessors.length === 0 && dependencies.successors.length === 0)) {
    return (
      <div className="py-8 text-center">
        <ArrowRight className="mx-auto mb-2 size-8 text-muted-foreground" />
        <p className="text-muted-foreground text-sm">No dependencies found</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {dependencies.predecessors.length > 0 && (
        <div>
          <h4 className="mb-3 font-medium text-sm">Blocking Tasks</h4>
          <div className="space-y-2">
            {dependencies.predecessors.map((dep) => (
              <DependencyCard key={dep.id} dependency={dep} type="predecessor" />
            ))}
          </div>
        </div>
      )}

      {dependencies.successors.length > 0 && (
        <div>
          <h4 className="mb-3 font-medium text-sm">Dependent Tasks</h4>
          <div className="space-y-2">
            {dependencies.successors.map((dep) => (
              <DependencyCard key={dep.id} dependency={dep} type="successor" />
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

function TaskHistoryTab({ taskId }: { taskId: string }) {
  const trpc = useTRPC();

  const { data: history, isLoading, error } = useSuspenseQuery(
    trpc.tasks.getTaskHistory.queryOptions(taskId)
  );

  if (isLoading) {
    return <TaskHistorySkeleton />;
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="size-4" />
        <AlertDescription>
          Failed to load task history. Please try again.
        </AlertDescription>
      </Alert>
    );
  }

  if (!history || history.length === 0) {
    return (
      <div className="py-8 text-center">
        <Clock className="mx-auto mb-2 size-8 text-muted-foreground" />
        <p className="text-muted-foreground text-sm">No history available</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {history.map((entry, index) => (
        <div key={entry.id} className="flex gap-3">
          <div className="flex flex-col items-center">
            <div className="flex size-8 items-center justify-center rounded-full bg-muted">
              <CalendarIcon className="size-3" />
            </div>
            {index < history.length - 1 && (
              <div className="mt-2 h-4 w-px bg-muted" />
            )}
          </div>

          <div className="min-w-0 flex-1">
            <div className="text-sm">
              <span className="font-medium">{entry.userName}</span>
              <span className="text-muted-foreground"> {entry.action}</span>
            </div>
            <div className="text-muted-foreground text-xs">
              {new Date(entry.createdAt).toLocaleString()}
            </div>
            {entry.notes && (
              <div className="mt-1 text-muted-foreground text-xs">
                {entry.notes}
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  );
}

function TaskCommentsTab({ taskId }: { taskId: string }) {
  // Placeholder for future comments functionality
  return (
    <div className="py-8 text-center">
      <p className="text-muted-foreground text-sm">Comments feature coming soon</p>
    </div>
  );
}

// Skeleton components for loading states
function TaskDetailSheetSkeleton() {
  return (
    <div className="flex h-full flex-col">
      <div className="space-y-4 border-b pb-4">
        <div className="flex items-start justify-between gap-4">
          <div className="flex-1 space-y-2">
            <Skeleton className="h-6 w-4/5" />
            <Skeleton className="h-4 w-3/5" />
          </div>
          <div className="flex gap-2">
            <Skeleton className="h-8 w-20" />
            <Skeleton className="h-8 w-16" />
          </div>
        </div>
        <div className="flex gap-2">
          <Skeleton className="h-5 w-16" />
          <Skeleton className="h-5 w-12" />
          <Skeleton className="h-5 w-20" />
        </div>
      </div>

      <div className="space-y-4 pt-4">
        <div className="flex space-x-1">
          {Array.from({ length: 4 }, (_, i) => (
            <Skeleton key={i} className="h-8 w-20" />
          ))}
        </div>
        <TaskDetailSkeleton />
      </div>
    </div>
  );
}

function TaskDetailsSkeleton() {
  return <TaskDetailSkeleton />;
}

function TaskDependenciesSkeleton() {
  return (
    <div className="space-y-4">
      {Array.from({ length: 3 }, (_, i) => (
        <div key={i} className="flex items-center gap-3 rounded-md border p-2">
          <Skeleton className="h-4 w-4" />
          <Skeleton className="h-3 w-32" />
          <Skeleton className="ml-auto h-5 w-16 rounded-full" />
        </div>
      ))}
    </div>
  );
}

function TaskHistorySkeleton() {
  return (
    <div className="space-y-4">
      {Array.from({ length: 5 }, (_, i) => (
        <div key={i} className="flex gap-3">
          <Skeleton className="size-8 rounded-full" />
          <div className="flex-1 space-y-1">
            <Skeleton className="h-3 w-2/3" />
            <Skeleton className="h-3 w-1/3" />
          </div>
        </div>
      ))}
    </div>
  );
}

function TaskCommentsSkeleton() {
  return (
    <div className="space-y-4">
      {Array.from({ length: 3 }, (_, i) => (
        <div key={i} className="space-y-2 rounded-md border p-3">
          <div className="flex items-center gap-2">
            <Skeleton className="size-6 rounded-full" />
            <Skeleton className="h-3 w-20" />
            <Skeleton className="ml-auto h-3 w-16" />
          </div>
          <Skeleton className="h-3 w-full" />
          <Skeleton className="h-3 w-3/4" />
        </div>
      ))}
    </div>
  );
}

// Helper functions
function DependencyCard({ dependency, type }: {
  dependency: any;
  type: 'predecessor' | 'successor';
}) {
  return (
    <div className="flex items-center gap-3 rounded-md border p-2">
      <ArrowRight className={`size-4 ${type === 'predecessor' ? 'rotate-180' : ''}`} />
      <div className="flex-1">
        <div className="font-medium text-sm">{dependency.title}</div>
        <div className="text-muted-foreground text-xs">{dependency.assignedToName}</div>
      </div>
      <Badge variant={getStatusVariant(dependency.status)} className="text-xs">
        {dependency.status}
      </Badge>
    </div>
  );
}

function getStatusVariant(status: string) {
  switch (status) {
    case 'COMPLETED':
      return 'default';
    case 'IN_PROGRESS':
      return 'secondary';
    case 'PENDING':
      return 'outline';
    case 'CANCELLED':
    case 'REJECTED':
      return 'destructive';
    default:
      return 'outline';
  }
}

function getPriorityVariant(priority: string) {
  switch (priority) {
    case 'HIGH':
    case 'URGENT':
      return 'destructive';
    case 'MEDIUM':
      return 'secondary';
    case 'LOW':
      return 'outline';
    default:
      return 'outline';
  }
}

function getInitials(name: string) {
  return name
    .split(' ')
    .map(n => n[0])
    .join('')
    .toUpperCase();
} 