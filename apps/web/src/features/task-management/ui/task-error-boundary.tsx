'use client';

import { Alert, AlertDescription, AlertTitle } from '@lilypad/ui/components/alert';
import { Button } from '@lilypad/ui/components/button';
import { Card, CardContent } from '@lilypad/ui/components/card';
import { toast } from '@lilypad/ui/components/sonner';
import { AlertCircle, RefreshCw } from 'lucide-react';
import type { ErrorInfo, ReactNode } from 'react';
import { Component } from 'react';

interface TaskErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  resetKeys?: string[];
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface TaskErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

export class TaskErrorBoundary extends Component<
  TaskErrorBoundaryProps,
  TaskErrorBoundaryState
> {
  constructor(props: TaskErrorBoundaryProps) {
    super(props);

    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): TaskErrorBoundaryState {
    return {
      hasError: true,
      error,
      errorInfo: null,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {

    // Capture error for monitoring
    if (typeof window !== 'undefined') {
      this.props.onError?.(error, errorInfo);

      toast.error('Task Error', {
        description: 'An error occurred while processing tasks. Please try again.',
      });

      this.setState({
        hasError: true,
        error,
        errorInfo,
      });
    }
  }

  componentDidUpdate(prevProps: TaskErrorBoundaryProps) {
    const { resetKeys } = this.props;
    const { hasError } = this.state;

    if (hasError && resetKeys && resetKeys !== prevProps.resetKeys) {
      // Reset error state when resetKeys change
      this.setState({
        hasError: false,
        error: null,
        errorInfo: null,
      });
    }
  }

  render() {
    const { hasError, error } = this.state;
    const { children, fallback } = this.props;

    if (hasError) {
      if (fallback) {
        return fallback;
      }

      return (
        <Card className="m-4 border-destructive">
          <CardContent className="p-6">
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Task Error</AlertTitle>
              <AlertDescription>
                An error occurred while processing tasks. Please try refreshing the page.
              </AlertDescription>
            </Alert>

            <div className="mt-4 space-y-4">
              <Button
                onClick={() => {
                  this.setState({
                    hasError: false,
                    error: null,
                    errorInfo: null,
                  });
                }}
                variant="outline"
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Try Again
              </Button>

              {error && (
                <details className="mt-4">
                  <summary className="cursor-pointer font-medium text-sm">
                    Error Details
                  </summary>
                  <pre className="mt-2 whitespace-pre-wrap text-muted-foreground text-sm">
                    {error.message}
                    {error.stack}
                  </pre>
                </details>
              )}
            </div>
          </CardContent>
        </Card>
      );
    }

    return children;
  }
}

export function TaskErrorWrapper({
  children,
  ...props
}: TaskErrorBoundaryProps) {
  return <TaskErrorBoundary {...props}>{children}</TaskErrorBoundary>;
} 