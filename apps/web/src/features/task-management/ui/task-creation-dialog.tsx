'use client';

import NiceModal, { type NiceModalHocProps } from '@ebay/nice-modal-react';
import { zodResolver } from '@hookform/resolvers/zod';
import { TaskPriorityEnum, TaskTypeEnum } from '@lilypad/db/enums';
import { Alert, AlertDescription } from '@lilypad/ui/components/alert';
import { Button } from '@lilypad/ui/components/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@lilypad/ui/components/dialog';
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from '@lilypad/ui/components/drawer';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@lilypad/ui/components/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@lilypad/ui/components/select';
import { Textarea } from '@lilypad/ui/components/textarea';
import { useMediaQuery } from '@lilypad/ui/hooks/use-media-query';
import { MediaQueries } from '@lilypad/ui/lib/media-queries';
import { Plus } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { useUser } from '@/shared/contexts/user-context';

const title = 'Create New Task';
const description = 'Create a new task and assign it to a team member.';

const taskCreationSchema = z.object({
  taskType: z.nativeEnum(TaskTypeEnum),
  priority: z.nativeEnum(TaskPriorityEnum),
  assignedToId: z.string().uuid('Please select an assignee'),
  studentId: z.string().uuid().optional(),
  caseId: z.string().uuid().optional(),
  dueDate: z.date().optional(),
  notes: z.string().optional(),
});

type TaskCreationFormData = z.infer<typeof taskCreationSchema>;

export type TaskCreationDialogProps = NiceModalHocProps & {
  onTaskCreated?: (taskId: string) => void;
};

export const TaskCreationDialog = NiceModal.create<TaskCreationDialogProps>(
  ({ onTaskCreated }) => {
    const modal = NiceModal.useModal();
    const { user } = useUser();
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitError, setSubmitError] = useState<string | null>(null);
    const isDesktop = useMediaQuery(MediaQueries.MdUp);

    const form = useForm<TaskCreationFormData>({
      resolver: zodResolver(taskCreationSchema),
      defaultValues: {
        taskType: TaskTypeEnum.COMPLETE_REFERRAL_FORM,
        priority: TaskPriorityEnum.MEDIUM,
        assignedToId: '',
        studentId: '',
        caseId: '',
        notes: '',
      },
    });

    const handleSubmit = async (data: TaskCreationFormData) => {
      setIsSubmitting(true);
      setSubmitError(null);

      try {

        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));

        onTaskCreated?.('new-task-id');
        modal.remove();
      } catch (_err) {
        setSubmitError('Failed to create task. Please try again.');
      } finally {
        setIsSubmitting(false);
      }
    };

const handleClose = () => {
  if (!isSubmitting) {
    modal.remove();
  }
};

const formContent = (
  <Form {...form}>
    <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
      {submitError && (
        <Alert variant="destructive">
          <AlertDescription>{submitError}</AlertDescription>
        </Alert>
      )}

      <FormField
        control={form.control}
        name="taskType"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Task Type</FormLabel>
            <Select onValueChange={field.onChange} defaultValue={field.value}>
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="Select task type" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                <SelectItem value={TaskTypeEnum.ASSIGN_PSYCHOLOGIST}>
                  Assign Psychologist
                </SelectItem>
                <SelectItem value={TaskTypeEnum.COMPLETE_REFERRAL_FORM}>
                  Complete Referral Form
                </SelectItem>
                <SelectItem value={TaskTypeEnum.SCHEDULE_STUDENT_EVALUATIONS}>
                  Schedule Student Evaluations
                </SelectItem>
                <SelectItem value={TaskTypeEnum.PREPARE_RATING_SCALES}>
                  Prepare Rating Scales
                </SelectItem>
                <SelectItem value={TaskTypeEnum.GENERATE_REPORT_DRAFT}>
                  Generate Report Draft
                </SelectItem>
                <SelectItem value={TaskTypeEnum.FINALIZE_EVALUATION_REPORT}>
                  Finalize Evaluation Report
                </SelectItem>
                <SelectItem value={TaskTypeEnum.SCHEDULE_IEP_MEETING}>
                  Schedule IEP Meeting
                </SelectItem>
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="priority"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Priority</FormLabel>
            <Select onValueChange={field.onChange} defaultValue={field.value}>
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                <SelectItem value={TaskPriorityEnum.LOW}>Low</SelectItem>
                <SelectItem value={TaskPriorityEnum.MEDIUM}>Medium</SelectItem>
                <SelectItem value={TaskPriorityEnum.HIGH}>High</SelectItem>
                <SelectItem value={TaskPriorityEnum.URGENT}>Urgent</SelectItem>
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="assignedToId"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Assign To</FormLabel>
            <Select onValueChange={field.onChange} defaultValue={field.value}>
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="Select assignee" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {/* TODO: Load users from API */}
                <SelectItem value="placeholder">Select a team member</SelectItem>
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="notes"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Notes (Optional)</FormLabel>
            <FormControl>
              <Textarea
                placeholder="Add any additional notes or context..."
                rows={3}
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <div className="flex gap-2 pt-4">
        <Button
          type="button"
          variant="outline"
          onClick={handleClose}
          disabled={isSubmitting}
          className="flex-1"
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={isSubmitting}
          className="flex-1"
        >
          {isSubmitting ? 'Creating...' : 'Create Task'}
        </Button>
      </div>
    </form>
  </Form>
);

if (isDesktop) {
  return (
    <Dialog open={modal.visible} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            {title}
          </DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>
        {formContent}
      </DialogContent>
    </Dialog>
  );
}

return (
  <Drawer open={modal.visible} onOpenChange={handleClose}>
    <DrawerContent>
      <DrawerHeader className="text-left">
        <DrawerTitle className="flex items-center gap-2">
          <Plus className="h-5 w-5" />
          {title}
        </DrawerTitle>
        <DrawerDescription>{description}</DrawerDescription>
      </DrawerHeader>
      <div className="px-4 pb-4">{formContent}</div>
    </DrawerContent>
  </Drawer>
);
  }
); 