'use client';

import { Alert, AlertDescription, AlertTitle } from '@lilypad/ui/components/alert';
import { Badge } from '@lilypad/ui/components/badge';
import { Button } from '@lilypad/ui/components/button';
import { Card, CardContent, CardHeader, } from '@lilypad/ui/components/card';
import { Skeleton } from '@lilypad/ui/components/skeleton';
import {
  AlertCircle,
  Ban,
  Clock,
  RefreshCw,
  Shield,
  TriangleAlertIcon,
  WifiOff,
  XCircle
} from 'lucide-react';
import type { ReactNode } from 'react';

export interface TaskErrorFallbackProps {
  title?: string;
  message?: string;
  actionLabel?: string;
  onAction?: () => void;
  showRetry?: boolean;
  children?: ReactNode;
}

/**
 * Generic task error fallback component
 */
export function TaskErrorFallback({
  title = 'Task Error',
  message = 'An error occurred while loading tasks.',
  actionLabel = 'Try Again',
  onAction,
  showRetry = true,
  children,
}: TaskErrorFallbackProps) {
  return (
    <Card className="border-dashed shadow-none">
      <CardContent className="flex flex-col items-center justify-center gap-4 pt-6">
        <div className="flex items-center justify-center rounded-md border bg-background p-2 shadow-sm">
          <TriangleAlertIcon className="size-4 shrink-0 text-destructive" />
        </div>

        <div className="text-center">
          <h3 className="font-medium text-sm">{title}</h3>
          <p className="mt-1 text-muted-foreground text-xs">{message}</p>
        </div>

        {children}

        {showRetry && onAction && (
          <Button
            onClick={onAction}
            size="sm"
            type="button"
            variant="outline"
            className="flex items-center gap-2"
          >
            <RefreshCw className="size-3" />
            {actionLabel}
          </Button>
        )}
      </CardContent>
    </Card>
  );
}

/**
 * Permission denied error fallback
 */
export function TaskPermissionDeniedFallback({ onAction }: { onAction?: () => void }) {
  return (
    <TaskErrorFallback
      title="Access Denied"
      message="You do not have permission to view these tasks."
      actionLabel="Refresh"
      onAction={onAction}
      showRetry={false}
    >
      <div className="flex items-center justify-center rounded-md border bg-background p-2 shadow-sm">
        <Shield className="size-4 shrink-0 text-destructive" />
      </div>
    </TaskErrorFallback>
  );
}

/**
 * Network error fallback
 */
export function TaskNetworkErrorFallback({ onRetry }: { onRetry?: () => void }) {
  return (
    <TaskErrorFallback
      title="Connection Error"
      message="Unable to connect to the server. Please check your internet connection."
      actionLabel="Retry"
      onAction={onRetry}
    >
      <div className="flex items-center justify-center rounded-md border bg-background p-2 shadow-sm">
        <WifiOff className="size-4 shrink-0 text-destructive" />
      </div>
    </TaskErrorFallback>
  );
}

/**
 * Task not found error fallback
 */
export function TaskNotFoundFallback({
  taskId,
  onGoBack
}: {
  taskId?: string;
  onGoBack?: () => void;
}) {
  return (
    <TaskErrorFallback
      title="Task Not Found"
      message={taskId
        ? `Task ${taskId} could not be found. It may have been deleted.`
        : 'The requested task could not be found.'
      }
      actionLabel="Go Back"
      onAction={onGoBack}
      showRetry={false}
    >
      <div className="flex items-center justify-center rounded-md border bg-background p-2 shadow-sm">
        <XCircle className="size-4 shrink-0 text-destructive" />
      </div>
    </TaskErrorFallback>
  );
}

/**
 * Rate limit error fallback
 */
export function TaskRateLimitFallback({
  retryAfter,
  onRetry
}: {
  retryAfter?: number;
  onRetry?: () => void;
}) {
  const waitTime = retryAfter ? Math.ceil(retryAfter / 1000) : 60;

  return (
    <TaskErrorFallback
      title="Rate Limit Exceeded"
      message={`Too many requests. Please wait ${waitTime} seconds before trying again.`}
      actionLabel="Retry"
      onAction={onRetry}
    >
      <div className="flex items-center justify-center rounded-md border bg-background p-2 shadow-sm">
        <Clock className="size-4 shrink-0 text-warning" />
      </div>
      <Badge variant="secondary" className="text-xs">
        Wait {waitTime}s
      </Badge>
    </TaskErrorFallback>
  );
}

/**
 * Task list empty state (not technically an error, but a fallback state)
 */
export function TaskListEmptyFallback({
  title = "No Tasks Found",
  message = "No tasks match your current filters.",
  actionLabel = "Clear Filters",
  onAction
}: TaskErrorFallbackProps) {
  return (
    <Card className="border-dashed shadow-none">
      <CardContent className="flex flex-col items-center justify-center gap-4 py-8">
        <div className="flex items-center justify-center rounded-md border bg-muted p-3 shadow-sm">
          <Ban className="size-6 shrink-0 text-muted-foreground" />
        </div>

        <div className="text-center">
          <h3 className="font-medium text-base">{title}</h3>
          <p className="mt-1 text-muted-foreground text-sm">{message}</p>
        </div>

        {onAction && (
          <Button
            onClick={onAction}
            size="sm"
            type="button"
            variant="outline"
          >
            {actionLabel}
          </Button>
        )}
      </CardContent>
    </Card>
  );
}

/**
 * Task loading error with details
 */
export function TaskLoadingErrorFallback({
  error,
  onRetry,
  onReport
}: {
  error?: Error;
  onRetry?: () => void;
  onReport?: () => void;
}) {
  const isNetworkError = error?.message?.includes('fetch') ||
    error?.message?.includes('network');

  return (
    <Alert variant="destructive">
      <AlertCircle className="size-4" />
      <AlertTitle>Failed to Load Tasks</AlertTitle>
      <AlertDescription>
        <p className="mb-3">
          {isNetworkError
            ? 'Network connection failed. Please check your internet connection.'
            : error?.message || 'An unexpected error occurred while loading tasks.'
          }
        </p>

        <div className="flex gap-2">
          {onRetry && (
            <Button
              onClick={onRetry}
              size="sm"
              variant="outline"
              className="flex items-center gap-1"
            >
              <RefreshCw className="size-3" />
              Retry
            </Button>
          )}

          {onReport && !isNetworkError && (
            <Button
              onClick={onReport}
              size="sm"
              variant="ghost"
              className="text-xs"
            >
              Report Issue
            </Button>
          )}
        </div>
      </AlertDescription>
    </Alert>
  );
}

/**
 * Skeleton loader for task cards
 */
export function TaskCardSkeleton() {
  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex items-start justify-between gap-2">
          <div className="flex-1 space-y-2">
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-3 w-1/2" />
          </div>
          <Skeleton className="h-6 w-16" />
        </div>
      </CardHeader>
      <CardContent className="pt-2">
        <div className="space-y-2">
          <Skeleton className="h-3 w-full" />
          <Skeleton className="h-3 w-2/3" />
          <div className="mt-3 flex gap-2">
            <Skeleton className="h-5 w-12" />
            <Skeleton className="h-5 w-16" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * Skeleton loader for task list
 */
export function TaskListSkeleton({ count = 6 }: { count?: number }) {
  return (
    <div className="space-y-4">
      {Array.from({ length: count }, (_, i) => (
        <TaskCardSkeleton key={i} />
      ))}
    </div>
  );
}

/**
 * Skeleton loader for kanban columns
 */
export function TaskKanbanSkeleton() {
  return (
    <div className="grid gap-4 md:grid-cols-3 lg:grid-cols-4">
      {Array.from({ length: 4 }, (_, i) => (
        <Card key={i}>
          <CardHeader className="pb-3">
            <Skeleton className="h-5 w-24" />
          </CardHeader>
          <CardContent className="space-y-3">
            {Array.from({ length: 3 }, (_, j) => (
              <TaskCardSkeleton key={j} />
            ))}
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

/**
 * Error boundary fallback specifically for task components
 */
export function TaskComponentErrorFallback({
  error,
  resetError,
  componentName = 'Task Component'
}: {
  error?: Error;
  resetError?: () => void;
  componentName?: string;
}) {
  return (
    <div className="rounded-lg border border-destructive/20 bg-destructive/5 p-4">
      <div className="flex items-start gap-3">
        <AlertCircle className="mt-0.5 size-5 shrink-0 text-destructive" />
        <div className="flex-1 space-y-2">
          <h3 className="font-medium text-destructive">
            {componentName} Error
          </h3>
          <p className="text-muted-foreground text-sm">
            {error?.message || 'An unexpected error occurred in this component.'}
          </p>
          {resetError && (
            <Button
              onClick={resetError}
              size="sm"
              variant="outline"
              className="mt-2"
            >
              Reset Component
            </Button>
          )}
        </div>
      </div>
    </div>
  );
} 