'use client';

import NiceModal, { type NiceModalHocProps } from '@ebay/nice-modal-react';
import { zodResolver } from '@hookform/resolvers/zod';
import { TaskStatusEnum } from '@lilypad/db/enums';
import { Alert, AlertDescription } from '@lilypad/ui/components/alert';
import { Button } from '@lilypad/ui/components/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@lilypad/ui/components/dialog';
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from '@lilypad/ui/components/drawer';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@lilypad/ui/components/form';
import { Textarea } from '@lilypad/ui/components/textarea';
import { useMediaQuery } from '@lilypad/ui/hooks/use-media-query';
import { MediaQueries } from '@lilypad/ui/lib/media-queries';
import { cn } from '@lilypad/ui/lib/utils';
import { CheckCircle, Clock, Info } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import type { TaskTableRow } from '@/entities/tasks/model/schema';
import { useTaskActions } from '../model/use-task-actions';

const title = 'Complete Task';
const description = 'Mark this task as completed and add any final notes.';

export type TaskCompletionDialogProps = NiceModalHocProps & {
  task: TaskTableRow;
};

// Form schema for task completion
const completionFormSchema = z.object({
  notes: z.string().optional(),
  confirmCompletion: z.boolean().refine(val => val === true, {
    message: 'You must confirm task completion',
  }),
});

type CompletionFormData = z.infer<typeof completionFormSchema>;

enum CompletionStep {
  NOTES_INPUT = 'notes_input',
  CONFIRMATION = 'confirmation',
}

export const TaskCompletionDialog = NiceModal.create<TaskCompletionDialogProps>(
  ({ task }) => {
    const modal = NiceModal.useModal();
    const mdUp = useMediaQuery(MediaQueries.MdUp, { ssr: false });
    const [currentStep, setCurrentStep] = useState<CompletionStep>(CompletionStep.NOTES_INPUT);

    const { completeTask, isCompleting } = useTaskActions({
      onSuccess: () => {
        modal.handleClose();
      },
    });

    const form = useForm<CompletionFormData>({
      resolver: zodResolver(completionFormSchema),
      defaultValues: {
        notes: '',
        confirmCompletion: false,
      },
    });

    const completionNotes = form.watch('notes');

    const handleNextStep = () => {
      if (currentStep === CompletionStep.NOTES_INPUT) {
        setCurrentStep(CompletionStep.CONFIRMATION);
      }
    };

    const handleBackStep = () => {
      if (currentStep === CompletionStep.CONFIRMATION) {
        setCurrentStep(CompletionStep.NOTES_INPUT);
      }
    };

    const handleSubmit = async (data: CompletionFormData) => {
      try {
        await completeTask(task.id, data.notes);
      } catch (error) {
      }
    };

    const canProceed = currentStep === CompletionStep.NOTES_INPUT;
    const _canProceed = currentStep === CompletionStep.CONFIRMATION;

    const renderStepContent = () => {
      switch (currentStep) {
        case CompletionStep.NOTES_INPUT:
          return (
            <div className="space-y-6">
              {/* Task Information */}
              <div className="rounded-lg border bg-muted/50 p-4">
                <div className="space-y-2">
                  <div className="font-medium text-sm">Task Details</div>
                  <div className="text-muted-foreground text-sm">
                    <div className="font-medium">{task.title}</div>
                    <div className="text-xs">
                      Student: {task.studentName} • School: {task.schoolName}
                    </div>
                    <div className="text-xs">
                      Assigned to: {task.assignedToName}
                    </div>
                  </div>
                </div>
              </div>

              {/* Current Status Alert */}
              {task.status !== TaskStatusEnum.IN_PROGRESS && (
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    This task is currently <strong>{task.status.toLowerCase()}</strong>.
                    Completing it will change the status to completed.
                  </AlertDescription>
                </Alert>
              )}

              {/* Due Date Info */}
              {task.dueDate && (
                <Alert>
                  <Clock className="h-4 w-4" />
                  <AlertDescription>
                    Due date: {new Date(task.dueDate).toLocaleDateString()}
                    {new Date(task.dueDate) < new Date() && (
                      <span className="text-destructive"> (Overdue)</span>
                    )}
                  </AlertDescription>
                </Alert>
              )}

              {/* Completion Notes */}
              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Completion Notes (Optional)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Add any notes about the task completion, outcomes, or follow-up actions..."
                        rows={4}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          );

        case CompletionStep.CONFIRMATION:
          return (
            <div className="space-y-6">
              {/* Confirmation Summary */}
              <div className="rounded-lg border border-green-200 bg-green-50 p-4">
                <div className="mb-2 flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <div className="font-medium text-green-800">Ready to Complete</div>
                </div>
                <div className="text-green-700 text-sm">
                  This task will be marked as completed with a timestamp of{' '}
                  <strong>{new Date().toLocaleString()}</strong>.
                </div>
              </div>

              {/* Task Summary */}
              <div className="space-y-3">
                <div>
                  <div className="font-medium text-muted-foreground text-sm">Task</div>
                  <div>{task.title}</div>
                </div>
                <div>
                  <div className="font-medium text-muted-foreground text-sm">Student</div>
                  <div>{task.studentName}</div>
                </div>
                <div>
                  <div className="font-medium text-muted-foreground text-sm">Assigned To</div>
                  <div>{task.assignedToName}</div>
                </div>
                {completionNotes && (
                  <div>
                    <div className="font-medium text-muted-foreground text-sm">Completion Notes</div>
                    <div className="rounded border bg-muted/50 p-3 text-sm">
                      {completionNotes}
                    </div>
                  </div>
                )}
              </div>

              {/* Confirmation Checkbox */}
              <FormField
                control={form.control}
                name="confirmCompletion"
                render={({ field }) => (
                  <FormItem>
                    <div className="flex items-center space-x-2">
                      <FormControl>
                        <input
                          type="checkbox"
                          id="confirm"
                          checked={field.value}
                          onChange={field.onChange}
                          className="rounded border border-input"
                        />
                      </FormControl>
                      <label
                        htmlFor="confirm"
                        className="font-medium text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        I confirm that this task has been completed successfully
                      </label>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Warning */}
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  This action cannot be undone. The task will be marked as completed and
                  moved to the completed tasks list.
                </AlertDescription>
              </Alert>
            </div>
          );

        default:
          return null;
      }
    };

    const renderButtons = () => (
      <div className={cn(
        'flex flex-shrink-0 gap-2 bg-secondary p-4 md:justify-between md:rounded-b-md'
      )}>
        {currentStep === CompletionStep.NOTES_INPUT ? (
          <>
            <Button
              type="button"
              variant="outline"
              size="sm"
              className="w-1/2 md:w-auto"
              onClick={modal.handleClose}
              disabled={isCompleting}
            >
              Cancel
            </Button>
            <Button
              type="button"
              size="sm"
              className="w-1/2 md:w-auto"
              onClick={handleNextStep}
              disabled={isCompleting}
            >
              Next: Review
            </Button>
          </>
        ) : (
          <>
            <Button
              type="button"
              variant="outline"
              size="sm"
              className="w-1/3 md:w-auto"
              onClick={handleBackStep}
              disabled={isCompleting}
            >
              Back
            </Button>
            <div className="flex w-2/3 gap-2 md:w-auto">
              <Button
                type="button"
                variant="outline"
                size="sm"
                className="flex-1 md:w-auto"
                onClick={modal.handleClose}
                disabled={isCompleting}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                size="sm"
                className="flex-1 md:w-auto"
                onClick={form.handleSubmit(handleSubmit)}
                disabled={!(canSubmit && form.watch('confirmCompletion')) || isCompleting}
              >
                {isCompleting ? 'Completing...' : 'Complete Task'}
              </Button>
            </div>
          </>
        )}
      </div>
    );

    const renderContent = () => (
      <Form {...form}>
        <div className="flex flex-col gap-4">
          {renderStepContent()}
        </div>
      </Form>
    );

    // Render as drawer on mobile, dialog on desktop
    if (mdUp) {
      return (
        <Dialog open={modal.visible} onOpenChange={modal.handleClose}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                {title}
              </DialogTitle>
              <DialogDescription>{description}</DialogDescription>
            </DialogHeader>
            <div className="max-h-[70vh] overflow-y-auto">
              {renderContent()}
            </div>
            {renderButtons()}
          </DialogContent>
        </Dialog>
      );
    }

    return (
      <Drawer open={modal.visible} onOpenChange={modal.handleClose}>
        <DrawerContent className="max-h-[95vh]">
          <DrawerHeader className="text-left">
            <DrawerTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              {title}
            </DrawerTitle>
            <DrawerDescription>{description}</DrawerDescription>
          </DrawerHeader>
          <div className="max-h-[60vh] overflow-y-auto px-4">
            {renderContent()}
          </div>
          {renderButtons()}
        </DrawerContent>
      </Drawer>
    );
  }
); 