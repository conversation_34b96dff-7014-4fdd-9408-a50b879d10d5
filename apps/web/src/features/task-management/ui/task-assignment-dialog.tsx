'use client';

import NiceModal, { type NiceModalHocProps } from '@ebay/nice-modal-react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useTRPC } from '@lilypad/api/client';
import { Alert, AlertDescription } from '@lilypad/ui/components/alert';
import { Avatar, AvatarFallback, AvatarImage } from '@lilypad/ui/components/avatar';
import { Button } from '@lilypad/ui/components/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@lilypad/ui/components/command';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@lilypad/ui/components/dialog';
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from '@lilypad/ui/components/drawer';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@lilypad/ui/components/form';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@lilypad/ui/components/popover';
import { Textarea } from '@lilypad/ui/components/textarea';
import { useMediaQuery } from '@lilypad/ui/hooks/use-media-query';
import { MediaQueries } from '@lilypad/ui/lib/media-queries';
import { cn } from '@lilypad/ui/lib/utils';
import { Check, ChevronDown, UserCheck } from 'lucide-react';
import { useState } from 'react';

import { useForm } from 'react-hook-form';
import { z } from 'zod';
import type { TaskTableRow } from '@/entities/tasks/model/schema';
import { useTaskActions } from '../model/use-task-actions';

const title = 'Assign Task';
const description = 'Select a team member to assign this task to.';

export type TaskAssignmentDialogProps = NiceModalHocProps & {
  task: TaskTableRow;
  isReassignment?: boolean;
};

// Form schema for task assignment
const assignmentFormSchema = z.object({
  assigneeId: z.string().min(1, 'Please select an assignee'),
  notes: z.string().optional(),
});

type AssignmentFormData = z.infer<typeof assignmentFormSchema>;

// Mock user data - in real app this would come from API
const mockUsers = [
  {
    id: '1',
    fullName: 'Dr. Sarah Johnson',
    email: '<EMAIL>',
    role: 'School Psychologist',
    avatar: null,
  },
  {
    id: '2',
    fullName: 'Michael Chen',
    email: '<EMAIL>',
    role: 'Clinical Director',
    avatar: null,
  },
  {
    id: '3',
    fullName: 'Lisa Rodriguez',
    email: '<EMAIL>',
    role: 'Case Manager',
    avatar: null,
  },
  {
    id: '4',
    fullName: 'David Thompson',
    email: '<EMAIL>',
    role: 'Assistant',
    avatar: null,
  },
];

export const TaskAssignmentDialog = NiceModal.create<TaskAssignmentDialogProps>(
  ({ task, isReassignment = false }) => {
    const modal = NiceModal.useModal();
    const mdUp = useMediaQuery(MediaQueries.MdUp, { ssr: false });
    const trpc = useTRPC();
    
    const [isAssigneeOpen, setIsAssigneeOpen] = useState(false);

    const { reassignTask, isReassigning } = useTaskActions({
      onSuccess: () => {
        modal.handleClose();
      },
    });

    // In a real app, this would fetch actual users from the API
    // const { data: users } = useSuspenseQuery(
    //   trpc.users.getAvailableAssignees.queryOptions()
    // );
    const users = mockUsers;

    const form = useForm<AssignmentFormData>({
      resolver: zodResolver(assignmentFormSchema),
      defaultValues: {
        assigneeId: task.assignedToId || '',
        notes: '',
      },
    });

    const selectedUserId = form.watch('assigneeId');
    const selectedUser = users.find(user => user.id === selectedUserId);

    const handleSubmit = async (data: AssignmentFormData) => {
      try {
        await reassignTask(task.id, data.assigneeId, data.notes);
      } catch (error) {
        console.log('Submit error:', error);
    };

      const canSubmit = selectedUserId && selectedUserId !== task.assignedToId;

      const getInitials = (name: string) => {
        return name
          .split(' ')
          .map(n => n[0])
          .join('')
          .toUpperCase();
      };

      const renderContent = () => (
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Task Information */}
            <div className="rounded-lg border bg-muted/50 p-4">
              <div className="space-y-2">
                <div className="font-medium text-sm">Task Details</div>
                <div className="text-muted-foreground text-sm">
                  <div className="font-medium">{task.title}</div>
                  <div className="text-xs">
                    Student: {task.studentName} • School: {task.schoolName}
                  </div>
                </div>
              </div>
            </div>

            {/* Current Assignment Info */}
            {task.assignedToName && isReassignment && (
              <Alert>
                <UserCheck className="h-4 w-4" />
                <AlertDescription>
                  Currently assigned to <strong>{task.assignedToName}</strong>
                </AlertDescription>
              </Alert>
            )}

            {/* Assignee Selection */}
            <FormField
              control={form.control}
              name="assigneeId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {isReassignment ? 'Reassign to' : 'Assign to'}
                  </FormLabel>
                  <div className="space-y-2">
                    <Popover open={isAssigneeOpen} onOpenChange={setIsAssigneeOpen}>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            role="combobox"
                            className={cn(
                              'w-full justify-between',
                              !field.value && 'text-muted-foreground'
                            )}
                          >
                            {selectedUser ? (
                              <div className="flex items-center gap-2">
                                <Avatar className="h-6 w-6">
                                  <AvatarImage src={selectedUser.avatar || undefined} />
                                  <AvatarFallback className="text-xs">
                                    {getInitials(selectedUser.fullName)}
                                  </AvatarFallback>
                                </Avatar>
                                <div className="flex flex-col items-start">
                                  <span className="text-sm">{selectedUser.fullName}</span>
                                  <span className="text-muted-foreground text-xs">
                                    {selectedUser.role}
                                  </span>
                                </div>
                              </div>
                            ) : (
                              'Select assignee...'
                            )}
                            <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-[400px] p-0">
                        <Command>
                          <CommandInput placeholder="Search team members..." />
                          <CommandEmpty>No team members found.</CommandEmpty>
                          <CommandList>
                            <CommandGroup>
                              {users.map((user) => (
                                <CommandItem
                                  key={user.id}
                                  value={user.fullName}
                                  onSelect={() => {
                                    field.onChange(user.id);
                                    setIsAssigneeOpen(false);
                                  }}
                                  className="flex items-center gap-2 p-2"
                                >
                                  <Avatar className="h-8 w-8">
                                    <AvatarImage src={user.avatar || undefined} />
                                    <AvatarFallback className="text-xs">
                                      {getInitials(user.fullName)}
                                    </AvatarFallback>
                                  </Avatar>
                                  <div className="flex flex-1 flex-col">
                                    <span className="font-medium text-sm">
                                      {user.fullName}
                                    </span>
                                    <span className="text-muted-foreground text-xs">
                                      {user.role} • {user.email}
                                    </span>
                                  </div>
                                  <Check
                                    className={cn(
                                      'ml-auto h-4 w-4',
                                      field.value === user.id ? 'opacity-100' : 'opacity-0'
                                    )}
                                  />
                                </CommandItem>
                              ))}
                            </CommandGroup>
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Notes */}
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes (optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Add any notes about this assignment..."
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Action Buttons */}
            <div className="flex gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={modal.handleClose}
                className="flex-1"
                disabled={isReassigning}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="flex-1"
                disabled={!canSubmit || isReassigning}
              >
                {isReassigning ? 'Assigning...' : isReassignment ? 'Reassign Task' : 'Assign Task'}
              </Button>
            </div>
          </form>
        </Form>
      );

      // Render as drawer on mobile, dialog on desktop
      if (mdUp) {
        return (
          <Dialog open={modal.visible} onOpenChange={modal.handleClose}>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>{isReassignment ? 'Reassign Task' : title}</DialogTitle>
                <DialogDescription>{description}</DialogDescription>
              </DialogHeader>
              {renderContent()}
            </DialogContent>
          </Dialog>
        );
      }

      return (
        <Drawer open={modal.visible} onOpenChange={modal.handleClose}>
          <DrawerContent>
            <DrawerHeader className="text-left">
              <DrawerTitle>{isReassignment ? 'Reassign Task' : title}</DrawerTitle>
              <DrawerDescription>{description}</DrawerDescription>
            </DrawerHeader>
            <div className="px-4 pb-4">{renderContent()}</div>
          </DrawerContent>
        </Drawer>
      );
    }
  }
); 