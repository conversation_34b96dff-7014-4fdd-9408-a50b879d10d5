import { useCallback, useEffect, useState } from 'react';

/**
 * Breakpoint definitions for task components
 */
export const TASK_BREAKPOINTS = {
  xs: 0,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
} as const;

export type Breakpoint = keyof typeof TASK_BREAKPOINTS;

/**
 * Hook to detect current screen size and breakpoint
 */
export function useTaskResponsive() {
  const [windowSize, setWindowSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 1024,
    height: typeof window !== 'undefined' ? window.innerHeight : 768,
  });

  const [currentBreakpoint, setCurrentBreakpoint] = useState<Breakpoint>('lg');

  const updateSize = useCallback(() => {
    const width = window.innerWidth;
    const height = window.innerHeight;

    setWindowSize({ width, height });

    // Determine current breakpoint
    let breakpoint: Breakpoint = 'xs';
    Object.entries(TASK_BREAKPOINTS).forEach(([bp, size]) => {
      if (width >= size) {
        breakpoint = bp as Breakpoint;
      }
    });
    setCurrentBreakpoint(breakpoint);
  }, []);

  useEffect(() => {
    if (typeof window === 'undefined') {
      return;
    }

    updateSize();
    window.addEventListener('resize', updateSize);
    return () => window.removeEventListener('resize', updateSize);
  }, [updateSize]);

  const isBreakpoint = useCallback(
    (bp: Breakpoint) => {
      return windowSize.width >= TASK_BREAKPOINTS[bp];
    },
    [windowSize.width]
  );

  const isSmallScreen = isBreakpoint('md') === false;
  const isMediumScreen = isBreakpoint('md') && !isBreakpoint('lg');
  const isLargeScreen = isBreakpoint('lg');

  return {
    windowSize,
    currentBreakpoint,
    isBreakpoint,
    isSmallScreen,
    isMediumScreen,
    isLargeScreen,
  };
}

/**
 * Responsive configuration for task components
 */
export const TASK_RESPONSIVE_CONFIG = {
  // Task cards per row at different breakpoints
  cardsPerRow: {
    xs: 1,
    sm: 1,
    md: 2,
    lg: 3,
    xl: 4,
    '2xl': 5,
  },

  // Task list item heights at different breakpoints
  itemHeight: {
    xs: 120, // Taller on mobile for better touch targets
    sm: 100,
    md: 80,
    lg: 72,
    xl: 64,
    '2xl': 64,
  },

  // Container padding at different breakpoints
  containerPadding: {
    xs: 'p-2',
    sm: 'p-4',
    md: 'p-4',
    lg: 'p-6',
    xl: 'p-6',
    '2xl': 'p-8',
  },

  // Modal widths at different breakpoints
  modalWidth: {
    xs: 'w-full max-w-sm',
    sm: 'w-full max-w-md',
    md: 'w-full max-w-lg',
    lg: 'w-full max-w-xl',
    xl: 'w-full max-w-2xl',
    '2xl': 'w-full max-w-3xl',
  },

  // Sheet widths at different breakpoints
  sheetWidth: {
    xs: 'w-full',
    sm: 'w-full',
    md: 'w-96',
    lg: 'w-[400px]',
    xl: 'w-[480px]',
    '2xl': 'w-[520px]',
  },
};

/**
 * Get responsive classes for task components
 */
export function getTaskResponsiveClasses(
  config: Record<Breakpoint, string>,
  currentBreakpoint: Breakpoint
): string {
  const breakpointOrder: Breakpoint[] = ['xs', 'sm', 'md', 'lg', 'xl', '2xl'];
  const currentIndex = breakpointOrder.indexOf(currentBreakpoint);

  // Find the appropriate class by working backwards from current breakpoint
  for (let i = currentIndex; i >= 0; i--) {
    const bp = breakpointOrder[i];
    if (config[bp]) {
      return config[bp];
    }
  }

  return config.xs || '';
}

/**
 * Hook for responsive task list layout
 */
export function useTaskListLayout() {
  const { currentBreakpoint, isSmallScreen, isMediumScreen, isLargeScreen } =
    useTaskResponsive();

  const getLayout = useCallback(() => {
    if (isSmallScreen) {
      return {
        view: 'list' as const,
        itemsPerPage: 10,
        showFilters: false,
        compactMode: true,
        stackedLayout: true,
      };
    }

    if (isMediumScreen) {
      return {
        view: 'list' as const,
        itemsPerPage: 15,
        showFilters: true,
        compactMode: false,
        stackedLayout: false,
      };
    }

    return {
      view: 'list' as const,
      itemsPerPage: 20,
      showFilters: true,
      compactMode: false,
      stackedLayout: false,
    };
  }, [isSmallScreen, isMediumScreen]);

  const layout = getLayout();

  const containerClasses = getTaskResponsiveClasses(
    TASK_RESPONSIVE_CONFIG.containerPadding,
    currentBreakpoint
  );

  return {
    ...layout,
    currentBreakpoint,
    containerClasses,
  };
}

/**
 * Hook for responsive kanban board layout
 */
export function useKanbanLayout() {
  const { currentBreakpoint, isSmallScreen, windowSize } = useTaskResponsive();

  const getKanbanConfig = useCallback(() => {
    const columnWidth = isSmallScreen ? windowSize.width - 32 : 320; // 32px for padding
    const maxColumns = isSmallScreen ? 1 : Math.floor(windowSize.width / 350);

    return {
      columnWidth,
      maxColumns,
      horizontalScroll: isSmallScreen,
      stackedColumns: isSmallScreen,
      cardSpacing: isSmallScreen ? 8 : 12,
    };
  }, [isSmallScreen, windowSize]);

  return {
    ...getKanbanConfig(),
    currentBreakpoint,
  };
}

/**
 * Responsive task modal configuration
 */
export function useTaskModalLayout() {
  const { currentBreakpoint, isSmallScreen } = useTaskResponsive();

  const getModalConfig = useCallback(() => {
    if (isSmallScreen) {
      return {
        type: 'drawer' as const,
        position: 'bottom' as const,
        height: '90vh',
        width: '100vw',
        showHandle: true,
      };
    }

    return {
      type: 'modal' as const,
      position: 'center' as const,
      height: 'auto',
      width: getTaskResponsiveClasses(
        TASK_RESPONSIVE_CONFIG.modalWidth,
        currentBreakpoint
      ),
      showHandle: false,
    };
  }, [currentBreakpoint, isSmallScreen]);

  return getModalConfig();
}

/**
 * Responsive task form layout
 */
export function useTaskFormLayout() {
  const { currentBreakpoint, isSmallScreen } = useTaskResponsive();

  const getFormConfig = useCallback(() => {
    return {
      columnsPerRow: isSmallScreen ? 1 : 2,
      fieldSpacing: isSmallScreen ? 'space-y-4' : 'space-y-3',
      buttonLayout: isSmallScreen ? 'stacked' : 'inline',
      showLabelsInline: !isSmallScreen,
    };
  }, [isSmallScreen]);

  return {
    ...getFormConfig(),
    currentBreakpoint,
  };
}

/**
 * Touch-friendly configurations for mobile devices
 */
export function useTouchOptimization() {
  const { isSmallScreen } = useTaskResponsive();
  const [isTouchDevice, setIsTouchDevice] = useState(false);

  useEffect(() => {
    const checkTouch = () => {
      setIsTouchDevice(
        'ontouchstart' in window || navigator.maxTouchPoints > 0
      );
    };

    checkTouch();
  }, []);

  const getTouchConfig = useCallback(() => {
    if (!(isTouchDevice || isSmallScreen)) {
      return {
        minTouchTarget: 'min-h-[32px]',
        tapSpacing: 'space-y-1',
        buttonSize: 'h-8',
        showHoverStates: true,
      };
    }

    return {
      minTouchTarget: 'min-h-[44px]', // iOS minimum touch target
      tapSpacing: 'space-y-3',
      buttonSize: 'h-12',
      showHoverStates: false,
    };
  }, [isTouchDevice, isSmallScreen]);

  return {
    isTouchDevice,
    isSmallScreen,
    ...getTouchConfig(),
  };
}
