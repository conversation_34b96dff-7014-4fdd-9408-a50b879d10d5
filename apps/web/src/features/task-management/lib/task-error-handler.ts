import type { TRPCClientError } from '@lilypad/api/client';
import { toast } from '@lilypad/ui/components/sonner';

export interface TaskErrorContext {
  taskId?: string;
  action?: string;
  userId?: string;
}

export interface TaskErrorOptions {
  showToast?: boolean;
  context?: TaskErrorContext;
  onRetry?: () => void;
  customMessage?: string;
}

/**
 * Centralized error handler for task-related TRPC errors
 * Provides user-friendly messages and consistent error handling
 */
export class TaskErrorHandler {
  static handle(
    error: TRPCClientError,
    options: TaskErrorOptions = {}
  ): {
    title: string;
    message: string;
    code: string;
    severity: 'error' | 'warning' | 'info';
    retryable: boolean;
  } {
    const { showToast = true, context, onRetry, customMessage } = options;

    const errorCode = error.data?.code || 'UNKNOWN_ERROR';
    const action = context?.action || 'perform action';

    let title: string;
    let message: string;
    let severity: 'error' | 'warning' | 'info' = 'error';
    let retryable = true;

    switch (errorCode) {
      case 'UNAUTHORIZED':
        title = 'Authentication Required';
        message = 'Please sign in to continue accessing tasks.';
        severity = 'warning';
        retryable = false;
        break;

      case 'FORBIDDEN':
        title = 'Permission Denied';
        message = `You do not have permission to ${action.toLowerCase()}.`;
        severity = 'warning';
        retryable = false;
        break;

      case 'NOT_FOUND':
        title = 'Task Not Found';
        message = context?.taskId
          ? `Task ${context.taskId} could not be found. It may have been deleted.`
          : 'The requested task could not be found.';
        severity = 'warning';
        retryable = false;
        break;

      case 'CONFLICT':
        title = 'Conflict Error';
        message =
          'This task has been modified by someone else. Please refresh and try again.';
        severity = 'warning';
        retryable = true;
        break;

      case 'BAD_REQUEST':
        title = 'Invalid Request';
        message =
          customMessage ||
          error.message ||
          'The request contains invalid data.';
        severity = 'error';
        retryable = false;
        break;

      case 'TIMEOUT':
        title = 'Request Timeout';
        message = 'The request took too long to complete. Please try again.';
        severity = 'warning';
        retryable = true;
        break;

      case 'TOO_MANY_REQUESTS':
        title = 'Rate Limit Exceeded';
        message =
          'Too many requests. Please wait a moment before trying again.';
        severity = 'warning';
        retryable = true;
        break;

      case 'INTERNAL_SERVER_ERROR':
        title = 'Server Error';
        message = 'An internal server error occurred. Please try again later.';
        severity = 'error';
        retryable = true;
        break;

      case 'SERVICE_UNAVAILABLE':
        title = 'Service Unavailable';
        message =
          'The task service is temporarily unavailable. Please try again later.';
        severity = 'error';
        retryable = true;
        break;

      case 'PARSE_ERROR':
        title = 'Data Format Error';
        message = 'Invalid data format received. Please refresh the page.';
        severity = 'error';
        retryable = true;
        break;

      default:
        title = `${action} Failed`;
        message =
          customMessage || error.message || 'An unexpected error occurred.';
        severity = 'error';
        retryable = true;
    }

    // Show toast notification if requested
    if (showToast) {
      const toastAction =
        retryable && onRetry
          ? {
              label: 'Retry',
              onClick: onRetry,
            }
          : undefined;

      switch (severity) {
        case 'error':
          toast.error(title, {
            description: message,
            action: toastAction,
          });
          break;
        case 'warning':
          toast.error(title, {
            description: message,
            action: toastAction,
          });
          break;
        case 'info':
          toast.info(title, {
            description: message,
            action: toastAction,
          });
          break;
      }
    }

    return {
      title,
      message,
      code: errorCode,
      severity,
      retryable,
    };
  }

  /**
   * Handle task-specific operations with context
   */
  static handleTaskOperation(
    error: TRPCClientError,
    operation:
      | 'create'
      | 'update'
      | 'delete'
      | 'assign'
      | 'complete'
      | 'reject'
      | 'view',
    options: Omit<TaskErrorOptions, 'action'> = {}
  ) {
    const actionMap = {
      create: 'Create Task',
      update: 'Update Task',
      delete: 'Delete Task',
      assign: 'Assign Task',
      complete: 'Complete Task',
      reject: 'Reject Task',
      view: 'View Task',
    };

    return TaskErrorHandler.handle(error, {
      ...options,
      context: {
        ...options.context,
        action: actionMap[operation],
      },
    });
  }

  /**
   * Handle bulk operations
   */
  static handleBulkOperation(
    errors: TRPCClientError[],
    operation: string,
    options: TaskErrorOptions = {}
  ) {
    if (errors.length === 0) {
      return;
    }

    const { showToast = true } = options;
    const successCount = errors.filter((e) => !e).length;
    const failureCount = errors.length - successCount;

    if (failureCount === 0) {
      if (showToast) {
        toast.success(`${operation} Completed`, {
          description: `All ${errors.length} tasks processed successfully.`,
        });
      }
      return;
    }

    // Group errors by type
    const errorGroups = errors.reduce(
      (acc, error) => {
        if (!error) {
          return acc;
        }

        const code = error.data?.code || 'UNKNOWN_ERROR';
        if (!acc[code]) {
          acc[code] = [];
        }
        acc[code].push(error);
        return acc;
      },
      {} as Record<string, TRPCClientError[]>
    );

    // Show summary toast
    if (showToast) {
      if (successCount > 0) {
        toast.error(`${operation} Partially Failed`, {
          description: `${successCount} succeeded, ${failureCount} failed. See details below.`,
        });
      } else {
        toast.error(`${operation} Failed`, {
          description: `All ${failureCount} tasks failed to process.`,
        });
      }

      // Show individual error details
      Object.entries(errorGroups).forEach(([_code, errs]) => {
        const sampleError = errs[0];
        const count = errs.length;

        TaskErrorHandler.handle(sampleError, {
          showToast: true,
          customMessage:
            count > 1
              ? `${count} tasks failed: ${sampleError.message}`
              : undefined,
        });
      });
    }

    return {
      successCount,
      failureCount,
      errorGroups,
    };
  }

  /**
   * Handle network errors specifically
   */
  static handleNetworkError(
    error: Error | TRPCClientError,
    options: TaskErrorOptions = {}
  ) {
    const { showToast = true, onRetry } = options;

    const isNetworkError =
      error.message?.includes('fetch') ||
      error.message?.includes('network') ||
      error.message?.includes('Failed to load') ||
      error.message?.includes('ERR_NETWORK');

    if (isNetworkError) {
      if (showToast) {
        toast.error('Connection Error', {
          description:
            'Unable to connect to the server. Please check your internet connection.',
          action: onRetry
            ? {
                label: 'Retry',
                onClick: onRetry,
              }
            : undefined,
        });
      }

      return {
        title: 'Connection Error',
        message: 'Network connection failed',
        code: 'NETWORK_ERROR',
        severity: 'error' as const,
        retryable: true,
      };
    }

    // If it's a TRPCClientError, handle normally
    if ('data' in error) {
      return TaskErrorHandler.handle(error as TRPCClientError, options);
    }

    // Generic error fallback
    if (showToast) {
      toast.error('Unexpected Error', {
        description: error.message || 'An unexpected error occurred.',
        action: onRetry
          ? {
              label: 'Retry',
              onClick: onRetry,
            }
          : undefined,
      });
    }

    return {
      title: 'Unexpected Error',
      message: error.message || 'An unexpected error occurred',
      code: 'GENERIC_ERROR',
      severity: 'error' as const,
      retryable: true,
    };
  }
}

/**
 * Hook for handling task errors in React components
 */
export function useTaskErrorHandler() {
  const handleError = (
    error: TRPCClientError,
    operation?: string,
    options?: TaskErrorOptions
  ) => {
    return TaskErrorHandler.handle(error, {
      ...options,
      context: {
        ...options?.context,
        action: operation,
      },
    });
  };

  const handleTaskOperation = (
    error: TRPCClientError,
    operation:
      | 'create'
      | 'update'
      | 'delete'
      | 'assign'
      | 'complete'
      | 'reject'
      | 'view',
    options?: Omit<TaskErrorOptions, 'action'>
  ) => {
    return TaskErrorHandler.handleTaskOperation(error, operation, options);
  };

  const handleNetworkError = (
    error: Error | TRPCClientError,
    options?: TaskErrorOptions
  ) => {
    return TaskErrorHandler.handleNetworkError(error, options);
  };

  return {
    handleError,
    handleTaskOperation,
    handleNetworkError,
  };
}
