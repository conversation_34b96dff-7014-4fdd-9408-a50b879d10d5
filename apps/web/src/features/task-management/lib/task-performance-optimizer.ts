import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import type { TaskTableRow } from '@/entities/tasks/model/schema';

/**
 * Configuration for task list virtualization
 */
export interface VirtualizationConfig {
  itemHeight: number;
  containerHeight: number;
  overscan: number;
  threshold: number; // Number of items above which to enable virtualization
}

/**
 * Default virtualization configuration
 */
export const DEFAULT_VIRTUALIZATION_CONFIG: VirtualizationConfig = {
  itemHeight: 80, // Height of each task card/row in pixels
  containerHeight: 600, // Height of the container
  overscan: 5, // Number of extra items to render outside viewport
  threshold: 50, // Enable virtualization when more than 50 items
};

/**
 * Hook for virtualizing large task lists
 */
export function useTaskListVirtualization(
  items: TaskTableRow[],
  config: Partial<VirtualizationConfig> = {}
) {
  const fullConfig = { ...DEFAULT_VIRTUALIZATION_CONFIG, ...config };
  const scrollElementRef = useRef<HTMLDivElement>(null);
  const { itemHeight, containerHeight, overscan, threshold } = fullConfig;

  // Determine if virtualization should be enabled
  const shouldVirtualize = items.length > threshold;

  // Calculate visible range
  const calculateVisibleRange = useCallback(
    (scrollTop: number) => {
      const visibleCount = Math.ceil(containerHeight / itemHeight);
      const startIndex = Math.floor(scrollTop / itemHeight);
      const endIndex = Math.min(
        startIndex + visibleCount + overscan,
        items.length
      );

      return {
        startIndex: Math.max(0, startIndex - overscan),
        endIndex,
        visibleCount,
      };
    },
    [itemHeight, containerHeight, overscan, items.length]
  );

  // Get visible items based on scroll position
  const getVisibleItems = useCallback(
    (scrollTop: number) => {
      if (!shouldVirtualize) {
        return {
          items,
          startIndex: 0,
          endIndex: items.length,
          totalHeight: items.length * itemHeight,
        };
      }

      const { startIndex, endIndex } = calculateVisibleRange(scrollTop);
      const visibleItems = items.slice(startIndex, endIndex);
      const totalHeight = items.length * itemHeight;

      return {
        items: visibleItems,
        startIndex,
        endIndex,
        totalHeight,
        offsetY: startIndex * itemHeight,
      };
    },
    [items, itemHeight, shouldVirtualize, calculateVisibleRange]
  );

  return {
    shouldVirtualize,
    getVisibleItems,
    scrollElementRef,
    config: fullConfig,
  };
}

/**
 * Hook for optimized task filtering and sorting
 */
export function useOptimizedTaskFiltering(
  tasks: TaskTableRow[],
  filters: {
    search?: string;
    status?: string;
    priority?: string;
    assignee?: string;
  },
  sortConfig?: {
    field: keyof TaskTableRow;
    direction: 'asc' | 'desc';
  }
) {
  // Memoize filtering function
  const filterTasks = useMemo(() => {
    return TaskPerformanceOptimizer.memoize(
      (
        taskList: TaskTableRow[],
        filterConfig: typeof filters,
        sort?: typeof sortConfig
      ) => {
        let filtered = taskList;

        // Apply filters
        if (filterConfig.search) {
          const searchLower = filterConfig.search.toLowerCase();
          filtered = filtered.filter(
            (task) =>
              task.title.toLowerCase().includes(searchLower) ||
              task.description?.toLowerCase().includes(searchLower) ||
              task.studentName?.toLowerCase().includes(searchLower)
          );
        }

        if (filterConfig.status) {
          filtered = filtered.filter(
            (task) => task.status === filterConfig.status
          );
        }

        if (filterConfig.priority) {
          filtered = filtered.filter(
            (task) => task.priority === filterConfig.priority
          );
        }

        if (filterConfig.assignee) {
          filtered = filtered.filter((task) =>
            task.assignedToName
              ?.toLowerCase()
              .includes(filterConfig.assignee?.toLowerCase())
          );
        }

        // Apply sorting
        if (sort) {
          filtered = [...filtered].sort((a, b) => {
            const aValue = a[sort.field];
            const bValue = b[sort.field];

            if (aValue == null && bValue == null) {
              return 0;
            }
            if (aValue == null) {
              return 1;
            }
            if (bValue == null) {
              return -1;
            }

            let comparison = 0;
            if (aValue < bValue) {
              comparison = -1;
            }
            if (aValue > bValue) {
              comparison = 1;
            }

            return sort.direction === 'desc' ? -comparison : comparison;
          });
        }

        return filtered;
      },
      (taskList, filterConfig, sort) =>
        `${taskList.length}-${JSON.stringify(filterConfig)}-${JSON.stringify(sort)}`
    );
  }, []);

  return useMemo(
    () => filterTasks(tasks, filters, sortConfig),
    [tasks, filters, sortConfig, filterTasks]
  );
}

/**
 * Hook for optimized task search with debouncing
 */
export function useOptimizedTaskSearch(
  tasks: TaskTableRow[],
  searchTerm: string,
  delay = 300
) {
  const debouncedSearch = useMemo(
    () =>
      TaskPerformanceOptimizer.debounce(
        (term: string, taskList: TaskTableRow[]) => {
          if (!term.trim()) {
            return taskList;
          }

          const searchLower = term.toLowerCase();
          return taskList.filter(
            (task) =>
              task.title.toLowerCase().includes(searchLower) ||
              task.description?.toLowerCase().includes(searchLower) ||
              task.studentName?.toLowerCase().includes(searchLower) ||
              task.assignedToName?.toLowerCase().includes(searchLower)
          );
        },
        delay
      ),
    [delay]
  );

  const [filteredTasks, _setFilteredTasks] = useState<TaskTableRow[]>(tasks);

  useEffect(() => {
    debouncedSearch(searchTerm, tasks);
  }, [searchTerm, tasks, debouncedSearch]);

  return filteredTasks;
}
