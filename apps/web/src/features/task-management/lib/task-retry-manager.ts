import type { TRPCClientError } from '@lilypad/api/client';
import { toast } from '@lilypad/ui/components/sonner';

export interface RetryConfig {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
  retryableErrors: string[];
  onRetry?: (attempt: number, error: Error) => void;
  onMaxAttemptsReached?: (error: Error) => void;
}

export interface RetryableOperation<T> {
  operation: () => Promise<T>;
  operationName: string;
  config?: Partial<RetryConfig>;
}

const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxAttempts: 3,
  baseDelay: 1000, // 1 second
  maxDelay: 10_000, // 10 seconds
  backoffMultiplier: 2,
  retryableErrors: [
    'TIMEOUT',
    'INTERNAL_SERVER_ERROR',
    'SERVICE_UNAVAILABLE',
    'TOO_MANY_REQUESTS',
    'CONFLICT',
    'NETWORK_ERROR',
    'PARSE_ERROR',
  ],
};

// Module-level state for retry tracking
const retryAttempts = new Map<string, number>();
const activeRetries = new Set<string>();

async function handleRetryAttempt(
  operationName: string,
  attempt: number,
  config: RetryConfig,
  error: Error
): Promise<void> {
  const delay = Math.min(
    config.baseDelay * config.backoffMultiplier ** (attempt - 1),
    config.maxDelay
  );

  const jitteredDelay = delay + Math.random() * 1000;

  config.onRetry?.(attempt, error);

  if (attempt < config.maxAttempts) {
    toast.info(`Retrying ${operationName}`, {
      description: `Attempt ${attempt + 1} of ${config.maxAttempts} in ${Math.round(jitteredDelay / 1000)}s...`,
    });
  }

  await delayMs(jitteredDelay);
}

/**
 * Check if an error is retryable based on config
 */
function isRetryableError(
  error: TRPCClientError | Error,
  config: RetryConfig
): boolean {
  if ('data' in error) {
    const errorCode = error.data?.code;
    return config.retryableErrors.includes(errorCode || '');
  }

  const isNetworkError =
    error.message?.includes('fetch') ||
    error.message?.includes('network') ||
    error.message?.includes('Failed to load') ||
    error.message?.includes('ERR_NETWORK');

  return isNetworkError || config.retryableErrors.includes('NETWORK_ERROR');
}

/**
 * Check if we should throttle retries for an operation type
 */
function shouldThrottle(operationName: string): boolean {
  const attempts = retryAttempts.get(operationName) || 0;
  const activeCount = Array.from(activeRetries).filter((id) =>
    id.startsWith(operationName)
  ).length;

  return attempts > 10 || activeCount > 3;
}

/**
 * Update retry tracking for an operation
 */
function updateRetryTracking(operationName: string, attempts: number) {
  const current = retryAttempts.get(operationName) || 0;
  retryAttempts.set(operationName, current + attempts);

  setTimeout(
    () => {
      const remaining =
        (retryAttempts.get(operationName) || attempts) - attempts;
      if (remaining <= 0) {
        retryAttempts.delete(operationName);
      } else {
        retryAttempts.set(operationName, remaining);
      }
    },
    5 * 60 * 1000
  );
}

/**
 * Clear retry tracking for successful operations
 */
function clearRetryTracking(operationName: string) {
  retryAttempts.delete(operationName);
}

/**
 * Delay utility
 */
function delayMs(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

function showBatchSummary<T>(
  operationName: string,
  totalOperations: number,
  results: Array<{ id: string; result?: T; error?: Error }>
): void {
  const successCount = results.filter((r) => r.result).length;
  const failureCount = results.filter((r) => r.error).length;

  if (failureCount === 0) {
    toast.success(`${operationName} Completed`, {
      description: `All ${totalOperations} operations completed successfully.`,
    });
  } else if (successCount > 0) {
    toast.warning(`${operationName} Partially Completed`, {
      description: `${successCount} succeeded, ${failureCount} failed.`,
    });
  } else {
    toast.error(`${operationName} Failed`, {
      description: `All ${totalOperations} operations failed.`,
    });
  }
}

/**
 * Execute an operation with retry logic using recursive approach to avoid await in loop
 */
export async function executeWithRetry<T>(
  retryableOp: RetryableOperation<T>
): Promise<T> {
  const config = { ...DEFAULT_RETRY_CONFIG, ...retryableOp.config };
  const operationId = `${retryableOp.operationName}-${Date.now()}`;

  return executeAttempt(retryableOp, config, operationId, 1);
}

async function executeAttempt<T>(
  retryableOp: RetryableOperation<T>,
  config: RetryConfig,
  operationId: string,
  attempt: number
): Promise<T> {
  try {
    if (shouldThrottle(retryableOp.operationName)) {
      throw new Error(
        'Too many retry attempts. Please wait before trying again.'
      );
    }

    activeRetries.add(operationId);
    const result = await retryableOp.operation();

    clearRetryTracking(retryableOp.operationName);
    activeRetries.delete(operationId);

    return result;
  } catch (error) {
    const lastError = error as Error;
    activeRetries.delete(operationId);

    const isRetryable = isRetryableError(error as TRPCClientError, config);

    if (!isRetryable || attempt === config.maxAttempts) {
      updateRetryTracking(retryableOp.operationName, attempt);

      if (attempt === config.maxAttempts) {
        config.onMaxAttemptsReached?.(lastError);

        toast.error(`${retryableOp.operationName} Failed`, {
          description: `Failed after ${config.maxAttempts} attempts. Please try again later.`,
        });
      }

      throw lastError;
    }

    await handleRetryAttempt(
      retryableOp.operationName,
      attempt,
      config,
      lastError
    );

    // Recursive call for next attempt
    return executeAttempt(retryableOp, config, operationId, attempt + 1);
  }
}

/**
 * Create a retryable version of a task mutation
 */
export function createRetryableMutation<TInput, TOutput>(
  mutationFn: (input: TInput) => Promise<TOutput>,
  operationName: string,
  config?: Partial<RetryConfig>
) {
  return async (input: TInput): Promise<TOutput> => {
    return executeWithRetry({
      operation: () => mutationFn(input),
      operationName,
      config,
    });
  };
}

/**
 * Batch retry operations with individual tracking
 */
export async function executeBatchWithRetry<T>(
  operations: Array<{
    operation: () => Promise<T>;
    id: string;
  }>,
  operationName: string,
  config?: Partial<RetryConfig>
): Promise<Array<{ id: string; result?: T; error?: Error }>> {
  const results: Array<{ id: string; result?: T; error?: Error }> = [];

  const promises = operations.map(async (op) => {
    try {
      const result = await executeWithRetry({
        operation: op.operation,
        operationName: `${operationName}-${op.id}`,
        config,
      });
      return { id: op.id, result };
    } catch (error) {
      return { id: op.id, error: error as Error };
    }
  });

  const settledResults = await Promise.all(promises);

  for (const [_index, settledResult] of settledResults.entries()) {
    results.push(settledResult);
  }

  showBatchSummary(operationName, operations.length, results);
  return results;
}

/**
 * Get retry statistics for monitoring
 */
export function getRetryStats() {
  return {
    activeRetries: activeRetries.size,
    operationCounts: Object.fromEntries(retryAttempts),
  };
}

/**
 * Reset all retry tracking (useful for testing)
 */
export function resetRetryTracking() {
  retryAttempts.clear();
  activeRetries.clear();
}

/**
 * Hook for using retry functionality in React components
 */
export function useTaskRetry() {
  const executeWithRetryHook = async <T>(
    operation: () => Promise<T>,
    operationName: string,
    config?: Partial<RetryConfig>
  ): Promise<T> => {
    return executeWithRetry({
      operation,
      operationName,
      config,
    });
  };

  const createRetryableMutationHook = <TInput, TOutput>(
    mutationFn: (input: TInput) => Promise<TOutput>,
    operationName: string,
    config?: Partial<RetryConfig>
  ) => {
    return createRetryableMutation(mutationFn, operationName, config);
  };

  const executeBatchWithRetryHook = async <T>(
    operations: Array<{
      operation: () => Promise<T>;
      id: string;
    }>,
    operationName: string,
    config?: Partial<RetryConfig>
  ) => {
    return executeBatchWithRetry(operations, operationName, config);
  };

  return {
    executeWithRetry: executeWithRetryHook,
    createRetryableMutation: createRetryableMutationHook,
    executeBatchWithRetry: executeBatchWithRetryHook,
    getRetryStats,
  };
}
