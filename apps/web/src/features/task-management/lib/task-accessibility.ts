import { type KeyboardEvent, useCallback, useEffect, useRef } from 'react';
import type { TaskTableRow } from '@/entities/tasks/model/schema';

/**
 * ARIA labels and descriptions for task components
 */
export const TASK_ARIA_LABELS = {
  taskCard: 'Task card',
  taskList: 'Task list',
  taskKanban: 'Task kanban board',
  statusDropdown: 'Change task status',
  prioritySelector: 'Change task priority',
  assigneeSelector: 'Assign task to user',
  dueDatePicker: 'Set task due date',
  taskActions: 'Task actions menu',
  taskDetails: 'Task details',
  taskComments: 'Task comments',
  taskHistory: 'Task history',
  taskDependencies: 'Task dependencies',
  createTask: 'Create new task',
  editTask: 'Edit task',
  deleteTask: 'Delete task',
  completeTask: 'Complete task',
  rejectTask: 'Reject task',
  reassignTask: 'Reassign task',
} as const;

/**
 * Generate ARIA attributes for task components
 */
export function getTaskAriaAttributes(task: TaskTableRow) {
  const statusText = task.status.replace('_', ' ').toLowerCase();
  const priorityText = task.priority.toLowerCase();
  const isOverdue =
    task.dueDate &&
    new Date(task.dueDate) < new Date() &&
    task.status !== 'COMPLETED';

  return {
    'aria-label': `Task: ${task.title}. Status: ${statusText}. Priority: ${priorityText}${
      isOverdue ? '. Overdue' : ''
    }${task.assignedToName ? `. Assigned to ${task.assignedToName}` : '. Unassigned'}`,
    'aria-describedby': `task-${task.id}-description`,
    'aria-expanded': false,
    role: 'button',
    tabIndex: 0,
  };
}

/**
 * Generate ARIA attributes for task status changes
 */
export function getStatusChangeAriaAttributes(
  currentStatus: string,
  _availableStatuses: string[]
) {
  return {
    'aria-label': `Current status: ${currentStatus.replace('_', ' ')}. Press Enter or Space to open status menu`,
    'aria-haspopup': 'listbox' as const,
    'aria-expanded': false,
    role: 'combobox',
    'aria-describedby': 'status-help-text',
  };
}

/**
 * Keyboard navigation hooks and utilities
 */
export function useTaskKeyboardNavigation(
  tasks: TaskTableRow[],
  onTaskSelect: (task: TaskTableRow) => void,
  onTaskAction?: (task: TaskTableRow, action: string) => void
) {
  const currentIndexRef = useRef(0);
  const listRef = useRef<HTMLDivElement>(null);

  // Focus management
  const focusTask = useCallback((index: number) => {
    if (!listRef.current) {
      return;
    }

    const taskElements = listRef.current.querySelectorAll('[data-task-id]');
    const element = taskElements[index] as HTMLElement;

    if (element) {
      element.focus();
      currentIndexRef.current = index;
    }
  }, []);

  // Keyboard event handler
  const handleKeyDown = useCallback(
    (event: KeyboardEvent<HTMLDivElement>) => {
      const { key, shiftKey, ctrlKey, metaKey } = event;
      const currentIndex = currentIndexRef.current;

      switch (key) {
        case 'ArrowDown': {
          event.preventDefault();
          const nextIndex = Math.min(currentIndex + 1, tasks.length - 1);
          focusTask(nextIndex);
          break;
        }
        case 'ArrowUp': {
          event.preventDefault();
          const prevIndex = Math.max(currentIndex - 1, 0);
          focusTask(prevIndex);
          break;
        }

        case 'Home':
          event.preventDefault();
          focusTask(0);
          break;

        case 'End':
          event.preventDefault();
          focusTask(tasks.length - 1);
          break;

        case 'Enter':
        case ' ':
          event.preventDefault();
          if (tasks[currentIndex]) {
            onTaskSelect(tasks[currentIndex]);
          }
          break;

        case 'c':
          if (ctrlKey || metaKey) {
            event.preventDefault();
            if (tasks[currentIndex] && onTaskAction) {
              onTaskAction(tasks[currentIndex], 'complete');
            }
          }
          break;

        case 'r':
          if (ctrlKey || metaKey) {
            event.preventDefault();
            if (tasks[currentIndex] && onTaskAction) {
              onTaskAction(tasks[currentIndex], 'reject');
            }
          }
          break;

        case 'a':
          if (ctrlKey || metaKey) {
            event.preventDefault();
            if (tasks[currentIndex] && onTaskAction) {
              onTaskAction(tasks[currentIndex], 'assign');
            }
          }
          break;

        case 'e':
          if (ctrlKey || metaKey) {
            event.preventDefault();
            if (tasks[currentIndex] && onTaskAction) {
              onTaskAction(tasks[currentIndex], 'edit');
            }
          }
          break;

        case 'Escape':
          event.preventDefault();
          if (listRef.current) {
            listRef.current.blur();
          }
          break;
        default:
          break;
      }
    },
    [tasks, focusTask, onTaskSelect, onTaskAction]
  );

  return {
    listRef,
    currentIndex: currentIndexRef.current,
    handleKeyDown,
    focusTask,
  };
}

/**
 * Kanban keyboard navigation
 */
export function useKanbanKeyboardNavigation(
  columns: string[],
  tasks: Record<string, TaskTableRow[]>,
  onTaskMove: (taskId: string, fromColumn: string, toColumn: string) => void
) {
  const currentPositionRef = useRef({ columnIndex: 0, taskIndex: 0 });
  const kanbanRef = useRef<HTMLDivElement>(null);
  const focusTask = useCallback(
    (columnIndex: number, taskIndex: number) => {
      if (!kanbanRef.current) {
        return;
      }
      const column = columns[columnIndex];
      if (!column || !tasks[column]) {
        return;
      }
      (&&)

      const selector = `[data-column="${column}"] [data-task-index="${taskIndex}"]`;
      const element = kanbanRef.current.querySelector(selector) as HTMLElement;

      if (element) {
        element.focus();
        currentPositionRef.current = { columnIndex, taskIndex };
      }
    },
    [columns, tasks]
  );

  const handleKeyDown = useCallback(
    (event: KeyboardEvent<HTMLDivElement>) => {
      const { key, shiftKey, ctrlKey } = event;
      const { columnIndex, taskIndex } = currentPositionRef.current;
      const currentColumn = columns[columnIndex];
      const currentTasks = tasks[currentColumn] || [];

      switch (key) {
        case 'ArrowRight': {
          event.preventDefault();
          if (shiftKey && ctrlKey) {
            // Move task to next column
            const nextColumnIndex = Math.min(
              columnIndex + 1,
              columns.length - 1
            );
            if (nextColumnIndex !== columnIndex && currentTasks[taskIndex]) {
              onTaskMove(
                currentTasks[taskIndex].id,
                currentColumn,
                columns[nextColumnIndex]
              );
            }
          } else {
            // Navigate to next column
            const nextColumnIndex = Math.min(
              columnIndex + 1,
              columns.length - 1
            );
            const nextColumnTasks = tasks[columns[nextColumnIndex]] || [];
            const newTaskIndex = Math.min(
              taskIndex,
              nextColumnTasks.length - 1
            );
            focusTask(nextColumnIndex, Math.max(0, newTaskIndex));
          }
          break;
        }
        case 'ArrowLeft': {
          event.preventDefault();
          if (shiftKey && ctrlKey) {
            // Move task to previous column
            const prevColumnIndex = Math.max(columnIndex - 1, 0);
            if (prevColumnIndex !== columnIndex && currentTasks[taskIndex]) {
              onTaskMove(
                currentTasks[taskIndex].id,
                currentColumn,
                columns[prevColumnIndex]
              );
            }
          } else {
            // Navigate to previous column
            const prevColumnIndex = Math.max(columnIndex - 1, 0);
            const prevColumnTasks = tasks[columns[prevColumnIndex]] || [];
            const newTaskIndex = Math.min(
              taskIndex,
              prevColumnTasks.length - 1
            );
            focusTask(prevColumnIndex, Math.max(0, newTaskIndex));
          }
          break;
        }
        case 'ArrowDown': {
          event.preventDefault();
          const nextTaskIndex = Math.min(
            taskIndex + 1,
            currentTasks.length - 1
          );
          focusTask(columnIndex, nextTaskIndex);
          break;
        }
        case 'ArrowUp': {
          event.preventDefault();
          const prevTaskIndex = Math.max(taskIndex - 1, 0);
          focusTask(columnIndex, prevTaskIndex);
          break;
        }
        case 'Home': {
          event.preventDefault();
          if (ctrlKey) {
            focusTask(0, 0);
          } else {
            focusTask(columnIndex, 0);
          }
          break;
        }
        case 'End': {
          event.preventDefault();
          if (ctrlKey) {
            const lastColumnIndex = columns.length - 1;
            const lastColumnTasks = tasks[columns[lastColumnIndex]] || [];
            focusTask(lastColumnIndex, Math.max(0, lastColumnTasks.length - 1));
          } else {
            focusTask(columnIndex, Math.max(0, currentTasks.length - 1));
          }
          break;
        }
        default: {
          break;
        }
      }
    },
    [columns, tasks, onTaskMove, focusTask]
  );

  return {
    kanbanRef,
    currentPosition: currentPositionRef.current,
    handleKeyDown,
    focusTask,
  };
}

/**
 * Accessibility validation utilities
 */
export function validateTaskAccessibility(element: HTMLElement): string[] {
  const issues: string[] = [];
  // Check for required ARIA attributes
  if (
    !element.getAttribute('aria-label') &&
    !element.getAttribute('aria-labelledby')
  ) {
    (||ssues.push('Missing aria-label or aria-)labelledby');
  }

  // Check for keyboard accessibility
  const isInteractive = element.matches(
    'button, a, input, select, textarea, [tabindex]'
  );
  if (
    isInteractive &&
    !element.getAttribute('tabindex') &&
    element.tabIndex < 0
  ) {
    issues.push('Interactive element is not keyboard accessible');
  }

  // Check for focus indicators
  const computedStyle = window.getComputedStyle(element, ':focus');
  if (
    isInteractive &&
    computedStyle.outline === 'none' &&
    !computedStyle.boxShadow
  ) {
    issues.push('Missing focus indicator');
  }

  // Check for sufficient color contrast (simplified check)
  const _color = window.getComputedStyle(element);
  const backgroundColor = style.backgroundColor;
  const color = style.color;

  if (
    backgroundColor === 'rgba(0, 0, 0, 0)' ||
    backgroundColor === 'transparent'
  ) {
    // Check parent background if transparent
    const parent = element.parentElement;
    if (parent) {
      const parentStyle = window.getComputedStyle(parent);
      if (parentStyle.backgroundColor === 'rgba(0, 0, 0, 0)') {
        issues.push(
          'Possible color contrast issue - unable to determine background'
        );
      }
    }
  }

  return issues;
}

/**
 * Hook for accessibility enhancements
 */
export function useTaskAccessibility() {
  useEffect(() => {
    TaskScreenReaderAnnouncer.initialize();
  }, []);

  const announceAction = useCallback((task: TaskTableRow, action: string) => {
    TaskScreenReaderAnnouncer.announceTaskAction(task, action);
  }, []);

  const announceStatusChange = useCallback(
    (task: TaskTableRow, oldStatus: string, newStatus: string) => {
      TaskScreenReaderAnnouncer.announceStatusChange(
        task,
        oldStatus,
        newStatus
      );
    },
    []
  );

  return {
    announceAction,
    announceStatusChange,
    pushFocus: TaskFocusManager.pushFocus,
    popFocus: TaskFocusManager.popFocus,
    trapFocus: TaskFocusManager.trapFocus,
  };
}
