'use client';

import { useCallback, useEffect, useRef } from 'react';

/**
 * Focus management hook for task modals and dialogs
 */
export function useTaskModalFocus(isOpen: boolean) {
  const modalRef = useRef<HTMLDivElement>(null);
  const previousFocusRef = useRef<HTMLElement | null>(null);
  const firstFocusableRef = useRef<HTMLElement | null>(null);
  const lastFocusableRef = useRef<HTMLElement | null>(null);

  // Get focusable elements within the modal
  const getFocusableElements = useCallback(() => {
    if (!modalRef.current) {
      return [];
    }

    const focusableSelectors = [
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      'a[href]',
      '[tabindex]:not([tabindex="-1"])',
      '[contenteditable="true"]',
    ].join(', ');

    return Array.from(
      modalRef.current.querySelectorAll(focusableSelectors)
    ) as HTMLElement[];
  }, []);

  // Update focusable element references
  const updateFocusableElements = useCallback(() => {
    const elements = getFocusableElements();
    firstFocusableRef.current = elements[0] || null;
    lastFocusableRef.current = elements.at(-1) || null;
  }, [getFocusableElements]);

  // Handle tab key for focus trapping
  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      if (event.key !== 'Tab') {
        return;
      }

      const elements = getFocusableElements();
      if (elements.length === 0) {
        return;
      }

      const firstElement = elements[0];
      const lastElement = elements.at(-1);

      if (event.shiftKey) {
        // Shift + Tab
        if (document.activeElement === firstElement) {
          event.preventDefault();
          lastElement.focus();
        }
      } else if (document.activeElement === lastElement) {
        // Tab
        event.preventDefault();
        firstElement.focus();
      }
    },
    [getFocusableElements]
  );

  // Set up focus management when modal opens
  useEffect(() => {
    if (isOpen && modalRef.current) {
      // Store the previously focused element
      previousFocusRef.current = document.activeElement as HTMLElement;

      // Update focusable elements
      updateFocusableElements();

      // Focus the first focusable element
      const firstElement = firstFocusableRef.current;
      if (firstElement) {
        // Use setTimeout to ensure the modal is fully rendered
        setTimeout(() => {
          firstElement.focus();
        }, 100);
      }

      // Add keyboard listener
      document.addEventListener('keydown', handleKeyDown);

      // Cleanup function
      return () => {
        document.removeEventListener('keydown', handleKeyDown);

        // Restore focus to the previously focused element
        if (
          previousFocusRef.current &&
          document.body.contains(previousFocusRef.current)
        ) {
          previousFocusRef.current.focus();
        }
      };
    }
  }, [isOpen, handleKeyDown, updateFocusableElements]);

  // Update focusable elements when modal content changes
  useEffect(() => {
    if (isOpen) {
      updateFocusableElements();
    }
  }, [isOpen, updateFocusableElements]);

  return {
    modalRef,
    firstFocusableRef,
    lastFocusableRef,
    updateFocusableElements,
  };
}

/**
 * Focus management hook for task sheets (sliding panels)
 */
export function useTaskSheetFocus(isOpen: boolean) {
  const sheetRef = useRef<HTMLDivElement>(null);
  const _previousFocusRef = useRef<HTMLElement | null>(null);
  const triggerElementRef = useRef<HTMLElement | null>(null);

  // Handle escape key to close sheet
  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        // The parent component should handle the actual closing
        // We just need to ensure proper focus management
        const closeButton = sheetRef.current?.querySelector(
          '[data-sheet-close]'
        ) as HTMLElement;
        if (closeButton) {
          closeButton.click();
        }
      }
    },
    [isOpen]
  );

  // Set up focus management
  useEffect(() => {
    if (isOpen && sheetRef.current) {
      // Store the trigger element
      triggerElementRef.current = document.activeElement as HTMLElement;

      // Find the first focusable element in the sheet
      const focusableElements = sheetRef.current.querySelectorAll(
        'button:not([disabled]), input:not([disabled]), select:not([disabled]), textarea:not([disabled]), a[href], [tabindex]:not([tabindex="-1"])'
      ) as NodeListOf<HTMLElement>;

      if (focusableElements.length > 0) {
        // Focus the first element after animation
        setTimeout(() => {
          focusableElements[0].focus();
        }, 200);
      }

      // Add keyboard listener
      document.addEventListener('keydown', handleKeyDown);

      return () => {
        document.removeEventListener('keydown', handleKeyDown);

        // Restore focus to trigger element
        if (
          triggerElementRef.current &&
          document.body.contains(triggerElementRef.current)
        ) {
          triggerElementRef.current.focus();
        }
      };
    }
  }, [isOpen, handleKeyDown]);

  return {
    sheetRef,
    triggerElementRef,
  };
}

/**
 * Focus management for dropdown menus and select components
 */
export function useTaskDropdownFocus() {
  const dropdownRef = useRef<HTMLDivElement>(null);
  const triggerRef = useRef<HTMLButtonElement>(null);

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (!dropdownRef.current) {
      return;
    }

    const items = dropdownRef.current.querySelectorAll(
      '[role="option"], [role="menuitem"]'
    ) as NodeListOf<HTMLElement>;
    const currentIndex = Array.from(items).indexOf(document.activeElement);

    switch (event.key) {
      case 'ArrowDown':
        {
          event.preventDefault();
          const nextIndex =
            currentIndex < items.length - 1 ? currentIndex + 1 : 0;
          items[nextIndex]?.focus();
          break;
        }
          case 'ArrowUp': {
        event.preventDefault()
          const prevIndex =
            currentIndex > 0 ? currentIndex - 1 : items.length - 1;
            items[prevIndex]?.focus();
          break;
        }
      case 'Home':
            {
        event.preventDefault();
        items[0]?.focus();
        break;
        }
        case 'End':
          {
        event.preventDefault();
        items.at(-1)?.focus();
        break;
        }
      case 'Escape': {
        event.preventDefault();
        triggerRef.current?.focus();
        // The parent should handle closing
        break;
      }
      case 'Enter':
        {
        event.preventDefault();
        (document.activeElement as HTMLElement)?.click();
        break;
        }
      default:
        break;
    }
  }, []);

  const openDropdown = useCallback(() => {
    setTimeout(() => {
      const firstItem = dropdownRef.current?.querySelector(
        '[role="option"], [role="menuitem"]'
      ) as HTMLElement;
      firstItem?.focus();
    }, 100);
  }, []);

  return {
    dropdownRef,
    triggerRef,
    handleKeyDown,
    openDropdown,
  };
}

/**
 * Focus management for task forms
 */
export function useTaskFormFocus() {
  const formRef = useRef<HTMLFormElement>(null);
  const firstErrorRef = useRef<HTMLElement | null>(null);

  // Focus first error fie{ ld on v }alidation failure
  const focusFirstError = useCallback(() => {
    if (!formRef.current) {
      return;
    }

    const errorElements = formRef.current.querySelectorAll(
      '[aria-invalid="true"], .error, [data-error]'
    ) as NodeListOf<HTMLElement>;
    if (errorElements.length > 0) {
      errorElements[0].focus();
      firstErrorRef.current = errorElements[0];
    }
  }, []);

  // Set up initial focus { on firs }t input
  const focusFirstInput = useCallback(() => {
    if (!formRef.current) {
      return;
    }

    const firstInput = formRef.current.querySelector(
      'input:not([type="hidden"]), select, textarea'
    ) as HTMLElement;
    if (firstInput) {
      firstInput.focus();
    }
  }, []);

  return {
    formRef,
    firstErrorRef,
    focusFirstError,
    focusFirstInput,
  };
}

/**
 * Focus management for task tables and lists
 */
export function useTaskListFocus() {
  const listRef = useRef<HTMLDivElement>(null);
  const currentIndexRef = useRef(0);
  {
  }
  const focusTask = useCallback((index: number) => {
    if (!listRef.current) {
      return;
    }

    const taskElements = listRef.current.querySelectorAll(
      '[data-task-item]'
    ) as NodeListOf<HTMLElement>;
    if (taskElements[index]) {
      taskElements[index].focus();
      currentIndexRef.current = index;
    }
  }, []);
  
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (!listRef.current) {
      return;
    }

    const taskElements = listRef.current.querySelectorAll('[data-task-item]') as NodeListOf<HTMLElement>;
    const currentIndex = currentIndexRef.current;

    switch (event.key) {
      case 'ArrowDown':
        {
        event.preventDefault();
        const nextIndex = Math.min(currentIndex + 1, taskElements.length - 1);
        focusTask(nextIndex);
        break;
        }
      case 'ArrowUp':
        {
        event.preventDefault();
        const prevIndex = Math.max(currentIndex - 1, 0);
        focusTask(prevIndex);
        break;
        }
      case 'Home':
        {
        event.preventDefault();
        focusTask(0);
        break;
        }
      case 'End':
        {
        event.preventDefault();
        focusTask(taskElements.length - 1);
        break;
        }
      case 'PageDown':
        {
        event.preventDefault();
        const pageDownIndex = Math.min(currentIndex + 10, taskElements.length - 1);
        focusTask(pageDownIndex);
        break;
        }
      case 'PageUp':
        {
        event.preventDefault();
        const pageUpIndex = Math.max(currentIndex - 10, 0);
        focusTask(pageUpIndex);
        break;
        }
      default:
        break;
    }
  }, [focusTask]);

return {
    listRef,
    currentIndex: currentIndexRef.current,
    focusTask,
    handleKeyDown,
  };
}

/**
 * Hook to manage focus restoration after navigation
 */
export function useFocusRestoration() {
  const previousFocusRef = useRef<HTMLElement | null>(null);

  const saveFocus = useCallback(() => {
    previousFocusRef.current = document.activeElement as HTMLElement;
  }, []);

  const restoreFocus = useCallback(() => {
    if (
      previousFocusRef.current &&
      document.body.contains(previousFocusRef.current)
    ) {
      previousFocusRef.current.focus();
    }
  }, []);

  return {
    saveFocus,
    restoreFocus,
  };
}

/**
 * Hook for skip links functionality
 */
export function useSkipLinks() {
  const skipLinksRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
    // Show skip links on first Tab press
    if (event.key === 'Tab' && !event.shiftKey) {
      skipLinksRef.current?.style.display = 'block';
    }
  };

  const handleFocusOut = () => {
    if (skipLinksRef.current && !skipLinksRef.current.contains(document.activeElement)) {
      skipLinksRef.current.style.display = 'none';
    }
  };

  document.addEventListener('keydown', handleKeyDown);
  document.addEventListener('focusout', handleFocusOut);

  return () => {
    document.removeEventListener('keydown', handleKeyDown);
    document.removeEventListener('focusout', handleFocusOut);
  };
}, []);

return {
  skipLinksRef,
};
}
