import {
  CasePriorityEnum,
  CaseTypeEnum,
  IepStatusEnum,
} from '@lilypad/db/schema/enums';
import { z } from 'zod';

export const createCaseSchema = z.object({
  studentId: z.string().uuid(),
  caseType: z.nativeEnum(CaseTypeEnum),
  priority: z.nativeEnum(CasePriorityEnum).default(CasePriorityEnum.MEDIUM),
  iepStatus: z.nativeEnum(IepStatusEnum),
  iepStartDate: z.date(),
  iepEndDate: z.date(),
  referralDate: z.date().optional(),
});

export type CreateCaseFormData = z.infer<typeof createCaseSchema>;
