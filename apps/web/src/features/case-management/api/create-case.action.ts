'use server';

import { authActionClient } from '@/shared/safe-action';
import { createDatabaseClient } from '@lilypad/db';
import { CaseRepository } from '@lilypad/db/repository/cases';
import { CaseStatusEnum } from '@lilypad/db/schema/enums';
import type { NewCase } from '@lilypad/db/schema/types';
import { revalidatePath } from 'next/cache';
import { createCaseSchema } from '../model/schema';

export const createCaseAction = authActionClient
  .metadata({ actionName: 'create-case' })
  .schema(createCaseSchema)
  .action(async ({ parsedInput }) => {
    // Calculate evaluation due date (60 days from referral date)
    const evaluationDueDate = parsedInput.referralDate
      ? new Date(parsedInput.referralDate.getTime() + 60 * 24 * 60 * 60 * 1000)
      : undefined;

    const newCase: NewCase = {
      studentId: parsedInput.studentId,
      caseType: parsedInput.caseType,
      priority: parsedInput.priority,
      iepStatus: parsedInput.iepStatus,
      iepStartDate: parsedInput.iepStartDate,
      iepEndDate: parsedInput.iepEndDate,
      referralDate: parsedInput.referralDate || null,
      evaluationDueDate: evaluationDueDate || null,
      status: CaseStatusEnum.READY_FOR_EVALUATION,
      isActive: true,
      meetingDate: null,
      isDeleted: false,
      deletedAt: null,
      deletedBy: null,
    };

    const db = await createDatabaseClient();
    const caseRepository = new CaseRepository(db);
    await caseRepository.create(newCase);

    revalidatePath(`/students/${parsedInput.studentId}/cases`);
    revalidatePath(`/students/${parsedInput.studentId}`);

    return { success: true };
  });
