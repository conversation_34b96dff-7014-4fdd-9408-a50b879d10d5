'use client';

import { createCaseAction } from '@/features/case-management/api/create-case.action';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  CasePriorityEnum,
  CasePriorityEnumMap,
  CaseTypeEnumMap,
  IepStatusEnum,
} from '@lilypad/db/schema/enums';
import { Button } from '@lilypad/ui/components/button';
import { Calendar } from '@lilypad/ui/components/calendar';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@lilypad/ui/components/dialog';
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from '@lilypad/ui/components/drawer';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@lilypad/ui/components/form';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@lilypad/ui/components/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@lilypad/ui/components/select';
import { useMediaQuery } from '@lilypad/ui/hooks/use-media-query';
import { MediaQueries } from '@lilypad/ui/lib/media-queries';
import { cn } from '@lilypad/ui/lib/utils';
import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { type CreateCaseFormData, createCaseSchema } from '../model/schema';

const title = 'Add New Case';
const description =
  'Add a new case for this student. Fill in the required information below.';

interface AddCaseDialogProps {
  studentId: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function AddCaseDialog({
  studentId,
  open,
  onOpenChange,
}: AddCaseDialogProps) {
  const mdUp = useMediaQuery(MediaQueries.MdUp, { ssr: false });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm({
    resolver: zodResolver(createCaseSchema),
    defaultValues: {
      studentId,
      caseType: undefined,
      priority: CasePriorityEnum.MEDIUM,
      iepStatus: IepStatusEnum.ACTIVE,
      iepStartDate: new Date(),
      iepEndDate: new Date(),
      referralDate: new Date(),
    },
  });

  // const { form, handleSubmitWithAction } = useHookFormAction({
  // 	action: createCaseAction,
  // 	schema: createCaseAction.schema,
  // 	defaultValues: {
  // 		studentId,
  // 		caseType: undefined,
  // 		priority: "MEDIUM",
  // 		iepStatus: IepStatusEnum.ACTIVE,
  // 		iepStartDate: new Date(),
  // 		iepEndDate: new Date(),
  // 		referralDate: new Date(),
  // 	},
  // 	onSuccess: () => {
  // 		toast({
  // 			title: "Success",
  // 			description: "Case created successfully.",
  // 		});
  // 		onOpenChange(false);
  // 		form.reset();
  // 	},
  // 	onError: (error) => {
  // 		toast({
  // 			title: "Error",
  // 			description: error.message || "Failed to create case.",
  // 			variant: "destructive",
  // 		});
  // 	},
  // });

  const onSubmit = async (data: CreateCaseFormData) => {
    setIsSubmitting(true);
    try {
      console.log(data);
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderForm = (
    <div
      className={cn(
        'min-h-0 flex-1 overflow-y-auto',
        mdUp ? 'px-6 py-4' : 'p-4'
      )}
    >
      <form className="space-y-6 pb-4" onSubmit={form.handleSubmit(onSubmit)}>
        <FormField
          control={form.control}
          name="caseType"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="font-medium text-sm">Case Type</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select a case type" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {Object.entries(CaseTypeEnumMap).map(([value, label]) => (
                    <SelectItem key={value} value={value}>
                      {label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="priority"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="font-medium text-sm">Priority</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select priority" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {Object.entries(CasePriorityEnumMap).map(
                      ([value, label]) => (
                        <SelectItem key={value} value={value}>
                          {label}
                        </SelectItem>
                      )
                    )}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="iepStatus"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="font-medium text-sm">
                  IEP Status
                </FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select IEP status" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value={IepStatusEnum.ACTIVE}>Active</SelectItem>
                    <SelectItem value={IepStatusEnum.INACTIVE}>
                      Inactive
                    </SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="iepStartDate"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="font-medium text-sm">
                  IEP Start Date
                </FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant="outline"
                        className={cn(
                          'w-full justify-start text-left font-normal',
                          !field.value && 'text-muted-foreground'
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {field.value ? (
                          format(field.value, 'PPP')
                        ) : (
                          <span>Pick a date</span>
                        )}
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={field.onChange}
                      disabled={(date) =>
                        date > new Date() || date < new Date('1900-01-01')
                      }
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="iepEndDate"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="font-medium text-sm">
                  IEP End Date
                </FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant="outline"
                        className={cn(
                          'w-full justify-start text-left font-normal',
                          !field.value && 'text-muted-foreground'
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {field.value ? (
                          format(field.value, 'PPP')
                        ) : (
                          <span>Pick a date</span>
                        )}
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={field.onChange}
                      disabled={(date) => date < new Date('1900-01-01')}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="referralDate"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="font-medium text-sm">
                Referral Date
              </FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant="outline"
                      className={cn(
                        'w-full justify-start text-left font-normal',
                        !field.value && 'text-muted-foreground'
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {field.value ? (
                        format(field.value, 'PPP')
                      ) : (
                        <span>Pick a date</span>
                      )}
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={field.value}
                    onSelect={field.onChange}
                    disabled={(date) =>
                      date > new Date() || date < new Date('1900-01-01')
                    }
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />
      </form>
    </div>
  );

  const renderButtons = (
    <div
      className={cn(
        'flex flex-shrink-0 justify-between gap-2 bg-secondary p-4',
        mdUp && 'rounded-b-md'
      )}
    >
      <Button
        type="button"
        variant="outline"
        size="sm"
        className="w-1/2 md:w-auto"
        onClick={() => onOpenChange(false)}
        disabled={isSubmitting}
      >
        Cancel
      </Button>
      <Button
        type="button"
        variant="default"
        size="sm"
        className="w-1/2 md:w-auto"
        disabled={!form.formState.isValid || isSubmitting}
        loading={isSubmitting}
        onClick={form.handleSubmit(onSubmit)}
      >
        Add Case
      </Button>
    </div>
  );

  return (
    <Form {...form}>
      {mdUp ? (
        <Dialog open={open} onOpenChange={onOpenChange}>
          <DialogContent className="flex max-w-2xl flex-col gap-0 p-0">
            <DialogHeader className="flex-shrink-0 border-b px-6 py-5">
              <DialogTitle className="font-semibold text-lg">
                {title}
              </DialogTitle>
              <DialogDescription className="text-muted-foreground text-sm">
                {description}
              </DialogDescription>
            </DialogHeader>
            {renderForm}
            {renderButtons}
          </DialogContent>
        </Dialog>
      ) : (
        <Drawer open={open} onOpenChange={onOpenChange}>
          <DrawerContent className="flex flex-col">
            <DrawerHeader className="flex-shrink-0 border-b text-left">
              <DrawerTitle className="font-semibold text-lg">
                {title}
              </DrawerTitle>
              <DrawerDescription className="text-muted-foreground text-sm">
                {description}
              </DrawerDescription>
            </DrawerHeader>
            {renderForm}
            {renderButtons}
          </DrawerContent>
        </Drawer>
      )}
    </Form>
  );
}
