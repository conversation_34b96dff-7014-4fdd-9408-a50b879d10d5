'use client';

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@lilypad/ui/components/form';
import { Switch } from '@lilypad/ui/components/switch';
import { cn } from '@lilypad/ui/lib/utils';
import { MailIcon, MonitorIcon } from 'lucide-react';
import type { UseFormReturn } from 'react-hook-form';
import type { NotificationPreferencesFormData } from '../model/schema';

const PREFERENCE_CONFIG = [
  {
    id: 'isInAppNotificationsEnabled',
    label: 'In-app notifications',
    description: 'Receive notifications within the application',
    icon: MonitorIcon,
  },
  {
    id: 'isEmailEnabled',
    label: 'Email notifications',
    description: 'Get updates via email',
    icon: MailIcon,
  },
] as const;

export type NotificationPreferencesListProps = {
  methods: UseFormReturn<NotificationPreferencesFormData>;
};

export function NotificationPreferencesList({
  methods,
}: NotificationPreferencesListProps) {
  const isAllNotificationsEnabled = methods.watch('isAllNotificationsEnabled');

  return (
    <ul className="m-0 list-none divide-y">
      <li className="flex w-full flex-col space-y-4">
        <div className="flex items-center justify-between">
          <FormField
            control={methods.control}
            name="isAllNotificationsEnabled"
            render={({ field }) => (
              <FormItem className="flex w-full flex-row items-center justify-between space-y-0">
                <div className="space-y-0.5">
                  <FormLabel className="font-medium text-sm">
                    All Notifications
                  </FormLabel>
                  <FormDescription className="text-muted-foreground text-xs">
                    Enable or disable all notification types
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    aria-label="Toggle all notifications"
                    checked={field.value}
                    disabled={methods.formState.isSubmitting}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
                <FormMessage className="text-xs" />
              </FormItem>
            )}
          />
        </div>

        <div className="space-y-3">
          {PREFERENCE_CONFIG.map((preference) => {
            const Icon = preference.icon;
            const fieldName = preference.id;

            return (
              <FormField
                control={methods.control}
                key={preference.id}
                name={fieldName}
                render={({ field }) => (
                  <FormItem
                    className={cn(
                      'flex items-center justify-between rounded-lg p-2 transition-colors',
                      'hover:bg-accent/50',
                      !isAllNotificationsEnabled && 'opacity-50'
                    )}
                  >
                    <div className="flex flex-1 items-center gap-3">
                      <div className="flex size-8 items-center justify-center rounded-md bg-muted">
                        <Icon className="size-4 text-muted-foreground" />
                      </div>
                      <div className="flex-1 space-y-0.5">
                        <FormLabel className="font-medium text-sm">
                          {preference.label}
                        </FormLabel>
                        <FormDescription className="text-muted-foreground text-xs leading-tight">
                          {preference.description}
                        </FormDescription>
                      </div>
                    </div>
                    <FormControl>
                      <Switch
                        aria-label={`Toggle ${preference.label}`}
                        checked={field.value && isAllNotificationsEnabled}
                        disabled={
                          !isAllNotificationsEnabled ||
                          methods.formState.isSubmitting
                        }
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <FormMessage className="text-xs" />
                  </FormItem>
                )}
              />
            );
          })}
        </div>
      </li>
    </ul>
  );
}
