'use client';

import { If } from '@lilypad/ui/components/if';
import { cn } from '@lilypad/ui/lib/utils';
import { useNotificationsContext } from '@/shared/contexts/notifications-context';

const format = (count: number) => (count > 99 ? '99+' : count);

export function NotificationCountBadge({ children }: React.PropsWithChildren) {
  const { notificationsCount } = useNotificationsContext();
  return (
    <span className="relative">
      <If condition={!!notificationsCount}>
        <span
          className={cn(
            '-top-2 -right-2 absolute size-4 animate-ping rounded-full bg-red-500 opacity-75'
          )}
        />
        <span
          className={cn(
            '-top-2 -right-2 absolute z-10 inline-flex animate-pulse items-center justify-center rounded-full bg-red-500 p-px text-[9px] text-white',
            notificationsCount > 99 ? '-top-3 -right-3 size-5' : 'size-4'
          )}
        >
          {format(notificationsCount)}
        </span>
      </If>
      {children}
    </span>
  );
}
