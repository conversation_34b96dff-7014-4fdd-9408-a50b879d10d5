import type { Notification } from '@/entities/notifications/model/types';
import { NotificationCard } from './notification-card';

interface NotificationItemProps {
  notification: Notification;
  compact?: boolean;
  onAction?: (actionId: string, notification: Notification) => void;
}

export function NotificationItem({
  notification,
  compact,
  onAction,
}: NotificationItemProps) {
  if (!notification?.id) {
    console.error('Invalid notification data:', notification);
    return null;
  }

  try {
    return (
      <NotificationCard
        compact={compact}
        notification={notification}
        onAction={onAction}
      />
    );
  } catch (error) {
    console.error('Error rendering notification:', error, notification);
    return (
      <div className="p-4 text-muted-foreground text-sm">
        Unable to display notification
      </div>
    );
  }
}
