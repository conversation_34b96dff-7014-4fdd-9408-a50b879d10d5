'use client';

import type { UserNotificationPreference } from '@lilypad/db/schema/types';
import { Button } from '@lilypad/ui/components/button';
import { Card, CardContent, type CardProps } from '@lilypad/ui/components/card';
import { Form } from '@lilypad/ui/components/form';
import { ScrollArea } from '@lilypad/ui/components/scroll-area';
import { useNotificationPreferences } from '../model/use-notification-preferences';
import { NotificationPreferencesList } from './notification-preferences-list';

export type NotificationPreferencesCardProps = CardProps & {
  notificationPreferences: UserNotificationPreference;
};

export function NotificationPreferencesCard({
  notificationPreferences,
  ...other
}: NotificationPreferencesCardProps): React.JSX.Element {
  const { methods, canSubmit, onSubmit } = useNotificationPreferences({
    notificationPreferences,
  });
  return (
    <div className="rounded-xl border border-border bg-muted/80 shadow-sm dark:bg-muted/20">
      <Form {...methods}>
        <form onSubmit={methods.handleSubmit(onSubmit)}>
          <Card
            className="gap-2 border-border border-x-0 border-t-0 border-b-xl shadow-none"
            {...other}
          >
            <CardContent className="max-h-72 flex-1 overflow-hidden">
              <ScrollArea className="[&>[data-radix-scroll-area-viewport]>div]:!block h-full">
                <NotificationPreferencesList methods={methods} />
              </ScrollArea>
            </CardContent>
          </Card>
          <div className="flex items-center justify-end space-x-2 p-3 text-center text-muted-foreground text-sm">
            <Button disabled={!canSubmit} size="sm" type="submit">
              Save
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
