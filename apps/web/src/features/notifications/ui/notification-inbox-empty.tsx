import { ArchiveIcon, BellOffIcon } from 'lucide-react';
import type React from 'react';

const config = {
  inbox: {
    icon: BellOffIcon,
    title: 'No notifications',
    description: "You're all caught up! No new notifications right now.",
  },
  archived: {
    icon: ArchiveIcon,
    title: 'No archived notifications',
    description: 'Dismissed notifications will appear here.',
  },
};

interface NotificationInboxEmptyProps {
  type: 'inbox' | 'archived';
}

export const NotificationInboxEmpty: React.FC<NotificationInboxEmptyProps> = ({
  type,
}) => {
  const { icon: Icon, title, description } = config[type];

  return (
    <div className="flex h-full min-h-96 flex-col items-center justify-center p-8 text-center">
      <div className="mb-4 rounded-full bg-muted p-3">
        <Icon className="size-6 text-muted-foreground" />
      </div>
      <h3 className="mb-1 font-medium text-sm">{title}</h3>
      <p className="max-w-[200px] text-muted-foreground text-xs">
        {description}
      </p>
    </div>
  );
};
