import { Card, CardContent } from '@lilypad/ui/components/card';
import { Skeleton } from '@lilypad/ui/components/skeleton';

export function NotificationPreferencesSkeleton() {
  return (
    <Card>
      <CardContent className="max-h-72 flex-1 overflow-hidden">
        <ul className="m-0 list-none divide-y">
          <li className="flex w-full flex-col space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-3 w-48" />
              </div>
              <Skeleton className="h-6 w-11 rounded-full" />
            </div>

            <div className="space-y-3">
              {Array.from({ length: 3 }).map((index) => (
                <div
                  className="flex items-center justify-between p-2"
                  key={`skeleton-${index}`}
                >
                  <div className="flex flex-1 items-center gap-3">
                    <Skeleton className="size-8 rounded-md" />
                    <div className="flex-1 space-y-0.5">
                      <Skeleton className="h-4 w-28" />
                      <Skeleton className="h-3 w-40" />
                    </div>
                  </div>
                  <Skeleton className="h-6 w-11 rounded-full" />
                </div>
              ))}
            </div>
          </li>
        </ul>
      </CardContent>
    </Card>
  );
}
