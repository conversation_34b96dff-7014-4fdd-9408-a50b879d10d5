'use client';

import { But<PERSON> } from '@lilypad/ui/components/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@lilypad/ui/components/dropdown-menu';
import { If } from '@lilypad/ui/components/if';
import {
  ArchiveIcon,
  ArchiveXIcon,
  CheckIcon,
  MoreVerticalIcon,
  Trash2Icon,
} from 'lucide-react';
import { memo, useCallback } from 'react';
import { useNotificationsContext } from '@/shared/contexts/notifications-context';
import {
  archiveAllNotificationsAction,
  archiveReadNotificationsAction,
  unarchiveAllNotificationsAction,
} from '../../api/archive-notifications.action';
import { deleteArchivedNotificationsAction } from '../../api/delete-archived-notifications.action';
import { markAllNotificationsAsReadAction } from '../../api/mark-notifications-as-read.action';
import type { NotificationStatus } from './types';

interface NotificationActionsMenuProps {
  activeStatus: NotificationStatus;
  unreadCount: number;
  allCount: number;
  readCount: number;
  archivedCount: number;
  onDeleteArchived?: () => void;
}

export const NotificationActionsMenu = memo(
  function NotificationActionsMenuComponent({
    activeStatus,
    unreadCount,
    allCount,
    readCount,
    archivedCount,
    onDeleteArchived,
  }: NotificationActionsMenuProps) {
    const { notifications, setNotifications } = useNotificationsContext();

    const handleMarkAllAsRead = useCallback(async () => {
      // Optimistic update
      const updatedNotifications = notifications.map((n) => ({
        ...n,
        isRead: true,
        readAt: new Date(),
      }));
      setNotifications(updatedNotifications);

      // Server action
      await markAllNotificationsAsReadAction();
    }, [notifications, setNotifications]);

    const handleArchiveAll = useCallback(async () => {
      // Optimistic update
      const updatedNotifications = notifications.map((n) => ({
        ...n,
        isArchived: true,
        archivedAt: new Date(),
      }));
      setNotifications(updatedNotifications);

      // Server action
      await archiveAllNotificationsAction();
    }, [notifications, setNotifications]);

    const handleUnarchiveAll = useCallback(async () => {
      // Optimistic update
      const updatedNotifications = notifications.map((n) => ({
        ...n,
        isArchived: false,
        archivedAt: null,
      }));
      setNotifications(updatedNotifications);

      // Server action
      await unarchiveAllNotificationsAction();
    }, [notifications, setNotifications]);

    const handleArchiveRead = useCallback(async () => {
      // Optimistic update
      const updatedNotifications = notifications.map((n) =>
        n.isRead
          ? {
              ...n,
              isArchived: true,
              archivedAt: new Date(),
            }
          : n
      );
      setNotifications(updatedNotifications);

      // Server action
      await archiveReadNotificationsAction();
    }, [notifications, setNotifications]);

    const handleDeleteArchived = useCallback(async () => {
      // Optimistic update
      const updatedNotifications = notifications.filter((n) => !n.isArchived);
      setNotifications(updatedNotifications);

      // Server action
      await deleteArchivedNotificationsAction();
      onDeleteArchived?.();
    }, [notifications, setNotifications, onDeleteArchived]);

    if (activeStatus !== 'all' && activeStatus !== 'archived') {
      return null;
    }

    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button className="size-8" size="icon" variant="ghost">
            <MoreVerticalIcon className="size-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          <If condition={activeStatus === 'all'}>
            <DropdownMenuItem
              disabled={unreadCount === 0}
              onClick={handleMarkAllAsRead}
            >
              <CheckIcon className="mr-2 size-4" />
              Mark all as read
            </DropdownMenuItem>
            <DropdownMenuItem
              disabled={allCount === 0}
              onClick={handleArchiveAll}
            >
              <ArchiveIcon className="mr-2 size-4" />
              Archive all
            </DropdownMenuItem>
            <DropdownMenuItem
              disabled={readCount === 0}
              onClick={handleArchiveRead}
            >
              <ArchiveIcon className="mr-2 size-4" />
              Archive read
            </DropdownMenuItem>
          </If>
          <If condition={activeStatus === 'archived'}>
            <DropdownMenuItem
              disabled={archivedCount === 0}
              onClick={handleUnarchiveAll}
            >
              <ArchiveXIcon className="mr-2 size-4" />
              Unarchive all
            </DropdownMenuItem>
            <DropdownMenuItem
              disabled={archivedCount === 0}
              onClick={handleDeleteArchived}
            >
              <Trash2Icon className="mr-2 size-4" />
              Clear archive
            </DropdownMenuItem>
          </If>
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }
);
