import type { Notification } from '@/entities/notifications/model/types';
import type { NotificationCategoryTypeEnum } from '@lilypad/db/schema';

export type NotificationStatus = 'all' | 'unread' | 'archived';
export type NotificationCategory = NotificationCategoryTypeEnum | 'ALL';

export interface StatusOption {
  id: NotificationStatus;
  label: string;
  count: number;
  icon: React.ComponentType<{ className?: string }>;
}

export interface CategoryTab {
  value: NotificationCategory;
  label: string;
}

export interface NotificationFilters {
  status: NotificationStatus;
  category: NotificationCategory;
}

export interface NotificationCounts {
  all: number;
  unread: number;
  archived: number;
  read: number;
}

export interface NotificationGroups {
  unread: Notification[];
  archived: Notification[];
  unarchived: Notification[];
  read: Notification[];
}
