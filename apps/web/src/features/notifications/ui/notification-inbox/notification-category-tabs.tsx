'use client';

import {
  NotificationCategoryTypeEnum,
  NotificationCategoryTypeEnumMap,
} from '@lilypad/db/schema';
import { Badge } from '@lilypad/ui/components/badge';
import {
  AnimatedTabs,
  AnimatedTabsContent,
  AnimatedTabsList,
  AnimatedTabsTrigger,
} from '@lilypad/ui/components/tabs.animated';
import { cn } from '@lilypad/ui/lib/utils';
import { memo, useMemo } from 'react';

import type { Notification } from '@/entities/notifications/model/types';
import { NotificationList } from './notification-list';
import type {
  CategoryTab,
  NotificationCategory,
  NotificationStatus,
} from './types';

interface NotificationCategoryTabsProps {
  notifications: Notification[];
  activeCategory: NotificationCategory;
  activeStatus: NotificationStatus;
  onCategoryChange: (category: NotificationCategory) => void;
}

const categoryTabs: CategoryTab[] = [
  {
    value: 'ALL',
    label: 'All',
  },
  {
    value: NotificationCategoryTypeEnum.GENERAL,
    label:
      NotificationCategoryTypeEnumMap[NotificationCategoryTypeEnum.GENERAL],
  },
  {
    value: NotificationCategoryTypeEnum.WORKFLOW,
    label:
      NotificationCategoryTypeEnumMap[NotificationCategoryTypeEnum.WORKFLOW],
  },
  {
    value: NotificationCategoryTypeEnum.ADMINISTRATIVE,
    label:
      NotificationCategoryTypeEnumMap[
        NotificationCategoryTypeEnum.ADMINISTRATIVE
      ],
  },
];

export const NotificationCategoryTabs = memo(
  function NotificationCategoryTabsComponent({
    notifications,
    activeCategory,
    activeStatus,
    onCategoryChange,
  }: NotificationCategoryTabsProps) {
    // biome-ignore lint/complexity/noExcessiveCognitiveComplexity: Fix later
    const { notificationsByCategory, countsByCategory } = useMemo(() => {
      const byCategory: Record<NotificationCategory, Notification[]> = {
        ALL: [],
        [NotificationCategoryTypeEnum.GENERAL]: [],
        [NotificationCategoryTypeEnum.WORKFLOW]: [],
        [NotificationCategoryTypeEnum.ADMINISTRATIVE]: [],
      };

      const counts: Record<NotificationCategory, number> = {
        ALL: 0,
        [NotificationCategoryTypeEnum.GENERAL]: 0,
        [NotificationCategoryTypeEnum.WORKFLOW]: 0,
        [NotificationCategoryTypeEnum.ADMINISTRATIVE]: 0,
      };

      for (const notification of notifications) {
        if (notification?.category) {
          byCategory[notification.category]?.push(notification);
          byCategory.ALL?.push(notification);

          if (activeStatus === 'all') {
            if (!(notification.isRead || notification.isArchived)) {
              counts[notification.category] =
                (counts[notification.category] || 0) + 1;
              counts.ALL = (counts.ALL || 0) + 1;
            }
          } else if (activeStatus === 'unread') {
            if (!notification.isRead) {
              counts[notification.category] =
                (counts[notification.category] || 0) + 1;
              counts.ALL = (counts.ALL || 0) + 1;
            }
          } else if (activeStatus === 'archived' && notification.isArchived) {
            counts[notification.category] =
              (counts[notification.category] || 0) + 1;
            counts.ALL = (counts.ALL || 0) + 1;
          }
        }
      }

      return {
        notificationsByCategory: byCategory,
        countsByCategory: counts,
      };
    }, [notifications, activeStatus]);

    return (
      <AnimatedTabs
        defaultValue={activeCategory}
        onValueChange={(value) =>
          onCategoryChange(value as NotificationCategory)
        }
        tabs={categoryTabs}
        value={activeCategory}
      >
        <AnimatedTabsList className="no-scrollbar" enableHorizontalScroll>
          {categoryTabs.map((tab) => {
            const count = countsByCategory[tab.value] || 0;

            return (
              <AnimatedTabsTrigger
                className="gap-2"
                key={tab.value}
                value={tab.value}
              >
                <span>{tab.label}</span>
                {count > 0 && (
                  <Badge
                    className={cn(
                      'ml-1 h-4 rounded-full px-1.5 text-xxs!',
                      activeCategory === tab.value
                        ? 'bg-primary text-primary-foreground'
                        : 'bg-muted text-muted-foreground'
                    )}
                    variant="secondary"
                  >
                    {count > 99 ? '99+' : count}
                  </Badge>
                )}
              </AnimatedTabsTrigger>
            );
          })}
        </AnimatedTabsList>

        {categoryTabs.map((tab) => (
          <AnimatedTabsContent
            className="pb-1"
            key={tab.value}
            value={tab.value}
          >
            <NotificationList
              activeStatus={activeStatus}
              notifications={notificationsByCategory[tab.value] || []}
            />
          </AnimatedTabsContent>
        ))}
      </AnimatedTabs>
    );
  }
);
