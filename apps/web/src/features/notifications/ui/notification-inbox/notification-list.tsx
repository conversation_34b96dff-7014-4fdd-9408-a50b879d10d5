'use client';

import { cn } from '@lilypad/ui/lib/utils';
import { memo, useMemo } from 'react';
import type { Notification } from '@/entities/notifications/model/types';
import { NotificationInboxEmpty } from '../notification-inbox-empty';
import { NotificationItem } from '../notification-item';
import type { NotificationStatus } from './types';

interface NotificationListProps {
  notifications: Notification[];
  activeStatus: NotificationStatus;
}

const sortNotifications = (a: Notification, b: Notification) => {
  const getTimestamp = (notification: Notification) => {
    if (!notification.createdAt) {
      return 0;
    }

    try {
      if (notification.createdAt instanceof Date) {
        return notification.createdAt.getTime();
      }

      return new Date(notification.createdAt).getTime();
    } catch {
      return 0;
    }
  };

  const aTime = getTimestamp(a);
  const bTime = getTimestamp(b);
  return bTime - aTime;
};

export const NotificationList = memo(function NotificationListComponent({
  notifications,
  activeStatus,
}: NotificationListProps) {
  const sortedNotifications = useMemo(
    () => [...notifications].sort(sortNotifications),
    [notifications]
  );

  if (notifications.length === 0) {
    return (
      <div className="flex h-full items-center justify-center">
        <NotificationInboxEmpty
          type={activeStatus === 'archived' ? 'archived' : 'inbox'}
        />
      </div>
    );
  }

  return (
    <div
      className={cn(
        'flex max-h-[calc(98dvh-140px)] min-h-auto w-full flex-col divide-y overflow-y-scroll pl-1'
      )}
    >
      {sortedNotifications.map((notification) => (
        <NotificationItem
          compact={false}
          key={notification.id}
          notification={notification}
        />
      ))}
    </div>
  );
});
