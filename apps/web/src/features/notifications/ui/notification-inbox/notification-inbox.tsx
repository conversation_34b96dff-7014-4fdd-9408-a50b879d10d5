'use client';

import { useCallback, useMemo, useState } from 'react';

import { useNotificationsContext } from '@/shared/contexts/notifications-context';

import { NotificationCategoryTabs } from './notification-category-tabs';
import { NotificationInboxHeader } from './notification-inbox-header';
import type {
  NotificationCategory,
  NotificationCounts,
  NotificationGroups,
  NotificationStatus,
} from './types';

export function NotificationInbox() {
  const { notifications, setNotifications } = useNotificationsContext();

  const [activeStatus, setActiveStatus] = useState<NotificationStatus>('all');
  const [activeCategory, setActiveCategory] =
    useState<NotificationCategory>('ALL');

  const notificationGroups = useMemo<NotificationGroups>(
    () => ({
      unread: notifications.filter((n) => !n.isRead),
      archived: notifications.filter((n) => n.isArchived),
      unarchived: notifications.filter((n) => !n.isArchived),
      read: notifications.filter((n) => n.isRead && !n.isArchived),
    }),
    [notifications]
  );

  const counts = useMemo<NotificationCounts>(
    () => ({
      all: notificationGroups.unarchived.length,
      unread: notificationGroups.unread.length,
      archived: notificationGroups.archived.length,
      read: notificationGroups.read.length,
    }),
    [notificationGroups]
  );

  const filteredNotifications = useMemo(() => {
    switch (activeStatus) {
      case 'unread':
        return notificationGroups.unread;
      case 'archived':
        return notificationGroups.archived;
      default:
        return notificationGroups.unarchived;
    }
  }, [activeStatus, notificationGroups]);

  const handleStatusChange = useCallback((status: NotificationStatus) => {
    setActiveStatus(status);
  }, []);

  const handleCategoryChange = useCallback((category: NotificationCategory) => {
    setActiveCategory(category);
  }, []);

  const handleDeleteArchived = useCallback(() => {
    setNotifications(notifications.filter((n) => !n.isArchived));
  }, [notifications, setNotifications]);

  return (
    <div className="relative w-0 min-w-96 rounded-md bg-background sm:min-w-lg">
      <NotificationInboxHeader
        activeStatus={activeStatus}
        counts={counts}
        onDeleteArchived={handleDeleteArchived}
        onStatusChange={handleStatusChange}
      />

      <NotificationCategoryTabs
        activeCategory={activeCategory}
        activeStatus={activeStatus}
        notifications={filteredNotifications}
        onCategoryChange={handleCategoryChange}
      />
    </div>
  );
}
