'use client';

import { Badge } from '@lilypad/ui/components/badge';
import { Button } from '@lilypad/ui/components/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@lilypad/ui/components/dropdown-menu';
import { If } from '@lilypad/ui/components/if';
import { cn } from '@lilypad/ui/lib/utils';
import {
  ArchiveIcon,
  BellDotIcon,
  BellIcon,
  ChevronDownIcon,
} from 'lucide-react';
import { memo, useMemo } from 'react';

import type { NotificationStatus, StatusOption } from './types';

interface NotificationStatusDropdownProps {
  activeStatus: NotificationStatus;
  onStatusChange: (status: NotificationStatus) => void;
  counts: {
    all: number;
    unread: number;
    archived: number;
  };
}

const statusOptions: Omit<StatusOption, 'count'>[] = [
  {
    id: 'all',
    label: 'Inbox',
    icon: BellIcon,
  },
  {
    id: 'unread',
    label: 'Unread',
    icon: BellDotIcon,
  },
  {
    id: 'archived',
    label: 'Archived',
    icon: ArchiveIcon,
  },
];

export const NotificationStatusDropdown = memo(
  function NotificationStatusDropdownComponent({
    activeStatus,
    onStatusChange,
    counts,
  }: NotificationStatusDropdownProps) {
    const optionsWithCounts = useMemo(
      () =>
        statusOptions.map((option) => ({
          ...option,
          count: counts[option.id],
        })),
      [counts]
    );

    const currentStatusOption = useMemo(
      () => optionsWithCounts.find((option) => option.id === activeStatus),
      [activeStatus, optionsWithCounts]
    );

    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button className="flex items-center gap-2 p-2" variant="ghost">
            {currentStatusOption?.icon && (
              <currentStatusOption.icon className="size-4 text-muted-foreground" />
            )}
            <span className="font-semibold text-sm">
              {currentStatusOption?.label}
            </span>
            <If condition={(currentStatusOption?.count || 0) > 0}>
              <Badge className="px-1.5 py-0.5 text-xs" variant="secondary">
                {currentStatusOption?.count}
              </Badge>
            </If>
            <ChevronDownIcon className="size-3 text-muted-foreground" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-48 space-y-0.5">
          {optionsWithCounts.map((option) => {
            const Icon = option.icon;
            return (
              <DropdownMenuItem
                className={cn(
                  'flex items-center justify-between',
                  activeStatus === option.id && 'bg-accent'
                )}
                key={option.id}
                onClick={() => onStatusChange(option.id)}
              >
                <div className="flex items-center gap-2">
                  <Icon className="size-4" />
                  <span>{option.label}</span>
                </div>
                <If condition={option.count > 0}>
                  <Badge className="px-1.5 py-0 text-xs" variant="secondary">
                    {option.count}
                  </Badge>
                </If>
              </DropdownMenuItem>
            );
          })}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }
);
