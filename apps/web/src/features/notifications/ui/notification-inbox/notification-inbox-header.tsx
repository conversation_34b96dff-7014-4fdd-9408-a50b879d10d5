'use client';

import { routes } from '@lilypad/shared/routes';
import { buttonVariants } from '@lilypad/ui/components/button';
import { AnimatedSettingsIcon } from '@lilypad/ui/icons';
import { cn } from '@lilypad/ui/lib/utils';
import Link from 'next/link';
import { memo } from 'react';
import { NotificationActionsMenu } from './notification-actions-menu';
import { NotificationStatusDropdown } from './notification-status-dropdown';
import type { NotificationCounts, NotificationStatus } from './types';

interface NotificationInboxHeaderProps {
  activeStatus: NotificationStatus;
  onStatusChange: (status: NotificationStatus) => void;
  counts: NotificationCounts;
  onDeleteArchived?: () => void;
}

export const NotificationInboxHeader = memo(
  function NotificationInboxHeaderComponent({
    activeStatus,
    onStatusChange,
    counts,
    onDeleteArchived,
  }: NotificationInboxHeaderProps) {
    return (
      <div className="flex items-center justify-between p-2">
        <NotificationStatusDropdown
          activeStatus={activeStatus}
          counts={{
            all: counts.all,
            unread: counts.unread,
            archived: counts.archived,
          }}
          onStatusChange={onStatusChange}
        />

        <div className="flex items-center gap-1">
          <NotificationActionsMenu
            activeStatus={activeStatus}
            allCount={counts.all}
            archivedCount={counts.archived}
            onDeleteArchived={onDeleteArchived}
            readCount={counts.read}
            unreadCount={counts.unread}
          />

          <Link
            className={cn(
              buttonVariants({ variant: 'ghost', size: 'icon' }),
              'size-8'
            )}
            href={routes.app.settings.notifications.Index}
          >
            <AnimatedSettingsIcon className="size-4" />
          </Link>
        </div>
      </div>
    );
  }
);
