'use client';

import type { Notification } from '@/entities/notifications/model/types';
import { getRelativeDate } from '@lilypad/shared/date';
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from '@lilypad/ui/components/avatar';
import { Badge } from '@lilypad/ui/components/badge';
import { Button } from '@lilypad/ui/components/button';
import { If } from '@lilypad/ui/components/if';
import { cn } from '@lilypad/ui/lib/utils';
import { ArchiveIcon, ArchiveXIcon, CheckIcon } from 'lucide-react';
import { useCallback } from 'react';
import { useNotificationActions } from '../hooks/use-notification-actions';
import { useNotificationStyles } from '../hooks/use-notification-styles';
import { notificationConfigs } from '../model/notification-config';
import { notificationPlugins } from '../model/notification-plugins';

interface NotificationCardProps {
  notification: Notification;
  compact?: boolean;
  onAction?: (actionId: string, notification: Notification) => void;
}

export function NotificationCard({
  notification,
  compact = false,
  onAction,
}: NotificationCardProps) {
  const config = notificationConfigs[notification.type];
  const plugin = notificationPlugins.get(notification.type);

  const { markAsRead, archive, unarchive, isUnread, isArchived } =
    useNotificationActions(notification);

  const onMarkAsRead = useCallback(
    (e: React.MouseEvent<HTMLButtonElement>) => {
      e.stopPropagation();
      markAsRead();
    },
    [markAsRead]
  );

  const onArchive = useCallback(
    (e: React.MouseEvent<HTMLButtonElement>) => {
      e.stopPropagation();
      archive();
    },
    [archive]
  );

  const onUnarchive = useCallback(
    (e: React.MouseEvent<HTMLButtonElement>) => {
      e.stopPropagation();
      unarchive();
    },
    [unarchive]
  );

  const {
    containerStyles,
    unreadIndicatorStyles,
    avatarStyles,
    contentStyles,
    metadataStyles,
    actionButtonStyles,
  } = useNotificationStyles(notification, config, compact);

  const handleNotificationClick = useCallback(
    (event: React.MouseEvent<HTMLDivElement>) => {
      event.preventDefault();
      if (isUnread) {
        markAsRead();
      }
    },
    [isUnread, markAsRead]
  );

  const handleAction = useCallback(
    async (actionId: string, event: React.MouseEvent<HTMLButtonElement>) => {
      event.stopPropagation();

      if (plugin?.beforeAction) {
        const shouldProceed = await plugin.beforeAction(actionId, notification);
        if (!shouldProceed) {
          return;
        }
      }

      const action = config.actions?.find((a) => a.id === actionId);
      if (action) {
        await action.handler(notification);
        if (isUnread) {
          await markAsRead();
        }
      }

      // Call plugin afterAction hook
      if (plugin?.afterAction) {
        await plugin.afterAction(actionId, notification);
      }

      onAction?.(actionId, notification);
    },
    [config.actions, notification, onAction, plugin, isUnread, markAsRead]
  );

  const Icon = config.icon;

  if (!config) {
    console.error(`Unknown notification type: ${notification.type}`);
    return null;
  }

  if (plugin?.render) {
    const customRender = plugin.render(notification, compact);
    if (customRender) {
      return <>{customRender}</>;
    }
  }

  if (
    plugin?.validateNotification &&
    !plugin.validateNotification(notification)
  ) {
    console.error(
      `Invalid notification for type ${notification.type}:`,
      notification
    );
    return null;
  }

  return (
    // biome-ignore lint/a11y/noStaticElementInteractions: Fix later
    // biome-ignore lint/a11y/useKeyWithClickEvents: Fix later
    <div className={containerStyles} onClick={handleNotificationClick}>
      {/* Unread indicator */}
      <If condition={isUnread}>
        <div className={unreadIndicatorStyles} />
      </If>

      {/* Avatar */}
      <Avatar className={avatarStyles}>
        <AvatarImage alt="" src="" />
        <AvatarFallback className={cn(config.iconBgColor, config.iconColor)}>
          <Icon className={cn(compact ? 'size-3' : 'size-4')} />
        </AvatarFallback>
      </Avatar>

      {/* Content */}
      <div className="min-w-0 flex-1 space-y-1">
        <div className="flex items-start justify-between gap-2">
          <p className={contentStyles}>{notification.content}</p>

          {/* Quick actions - visible on hover or always for mobile */}
          <div className={actionButtonStyles}>
            <If condition={isUnread}>
              <Button
                aria-label="Mark as read"
                className="size-6 hover:bg-background"
                onClick={onMarkAsRead}
                size="icon"
                title="Mark as read"
                variant="ghost"
              >
                <CheckIcon className="size-3" />
              </Button>
            </If>

            <If condition={isArchived}>
              <Button
                aria-label="Unarchive notification"
                className="size-6 hover:bg-background"
                onClick={onUnarchive}
                size="icon"
                title="Unarchive notification"
                variant="ghost"
              >
                <ArchiveXIcon className="size-3" />
              </Button>
            </If>

            <If condition={!isArchived}>
              <Button
                aria-label="Archive notification"
                className="size-6 hover:bg-background"
                onClick={onArchive}
                size="icon"
                title="Archive notification"
                variant="ghost"
              >
                <ArchiveIcon className="size-3" />
              </Button>
            </If>
          </div>
        </div>

        {/* Action buttons */}
        <If condition={!!config.actions?.length}>
          <div className="flex items-center gap-2 pt-2">
            {config.actions?.map((action) => {
              const ActionIcon = action.icon;
              return (
                <Button
                  className={cn('h-7 px-3 text-xs', action.className)}
                  key={action.id}
                  onClick={(e) => handleAction(action.id, e)}
                  size="sm"
                  variant={action.variant || 'outline'}
                >
                  {ActionIcon && <ActionIcon className="mr-1 size-3" />}
                  {action.label}
                </Button>
              );
            })}
          </div>
        </If>

        {/* Metadata */}
        <div className="flex items-center justify-between pt-1">
          <span className={metadataStyles}>
            {notification.createdAt && getRelativeDate(notification.createdAt)}
          </span>

          <If condition={isUnread && !!config.badge}>
            <Badge
              className={cn('px-1.5 py-0 text-xs', config.badge?.className)}
              variant="secondary"
            >
              {config.badge?.text || 'New'}
            </Badge>
          </If>
        </div>
      </div>
    </div>
  );
}
