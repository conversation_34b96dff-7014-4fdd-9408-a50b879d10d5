'use client';

import { But<PERSON> } from '@lilypad/ui/components/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@lilypad/ui/components/popover';
import { AnimatedBellIcon } from '@lilypad/ui/icons';
import { NotificationCountBadge } from './notification-count-badge';
import { NotificationInbox } from './notification-inbox';

export function NotificationsPopover() {
  return (
    <Popover modal>
      <PopoverTrigger asChild>
        <Button
          className="group size-8 text-muted-foreground transition-colors duration-200 ease-in-out hover:text-foreground"
          size="icon"
          type="button"
          variant="ghost"
        >
          <NotificationCountBadge>
            <AnimatedBellIcon className="size-4" />
          </NotificationCountBadge>
        </Button>
      </PopoverTrigger>
      <PopoverContent
        align="end"
        className="w-auto overflow-hidden p-0"
        sideOffset={2}
      >
        <NotificationInbox />
      </PopoverContent>
    </Popover>
  );
}
