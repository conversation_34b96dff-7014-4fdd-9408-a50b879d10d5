'use client';

import type { NotificationCategoryTypeEnum } from '@lilypad/db';
import { useSupabase } from '@lilypad/supabase/hooks';
import { useEffect, useRef } from 'react';
import type {
  Notification,
  NotificationDao,
  NotificationPayload,
} from '@/entities/notifications/model/types';
import type { NotificationTypeEnum } from '@lilypad/db/enums';

interface UseStreamNotificationsParams {
  userId: string;
  notifications: Notification[];
  setNotifications: (notifications: Notification[]) => void;
  setNotificationsCount: (notificationsCount: number) => void;
}

export function useStreamNotifications({
  userId,
  notifications,
  setNotifications,
  setNotificationsCount,
}: UseStreamNotificationsParams) {
  const supabase = useSupabase();

  // Use a ref to always have access to the latest notifications without triggering re-subscription
  const notificationsRef = useRef(notifications);
  notificationsRef.current = notifications;

  useEffect(() => {
    if (!userId) {
      console.error('User not found.');
      return;
    }

    const updateNotificationsCount = (updatedNotifications: Notification[]) => {
      const updatedNotificationsCount = updatedNotifications.filter(
        (notification) => !(notification.isArchived || notification.isRead)
      ).length;
      setNotificationsCount(updatedNotificationsCount);
    };

    const handleNotificationChange = (payload: NotificationPayload) => {
      const event = payload.eventType;
      let updatedNotifications: Notification[] = [];

      if (event === 'INSERT') {
        const notification = transformNotification(payload.new);
        updatedNotifications = [...notificationsRef.current, notification];
      }
      if (event === 'UPDATE') {
        const notification = transformNotification(payload.new);
        updatedNotifications = notificationsRef.current.map(
          (n: Notification) =>
            n.id === notification.id ? { ...n, ...notification } : n
        );
      }

      setNotifications(updatedNotifications);
      updateNotificationsCount(updatedNotifications);
    };

    const handleDeleteNotification = (id?: string | number) => {
      const updatedNotifications = notificationsRef.current.filter(
        (n: Notification) => n.id !== id
      );
      setNotifications(updatedNotifications);
      updateNotificationsCount(updatedNotifications);
    };

    const channel = supabase.channel('notifications');

    channel
      .on<NotificationDao>(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'notifications',
          filter: `user_id=eq.${userId}`,
        },
        (payload) => {
          handleNotificationChange(payload);

          if (payload.eventType === 'DELETE') {
            handleDeleteNotification(payload.old.id);
          }
        }
      )
      .subscribe();

    return () => {
      channel.unsubscribe();
    };
  }, [supabase, userId, setNotifications, setNotificationsCount]);
}

function transformNotification(newNotification: NotificationDao): Notification {
  if (!newNotification.id) {
    throw new Error('Notification ID is required');
  }

  return {
    id: newNotification.id,
    userId: newNotification.user_id,
    type: newNotification.type as NotificationTypeEnum,
    content: newNotification.content,
    metadata: newNotification.metadata,
    category: newNotification.category as NotificationCategoryTypeEnum,
    isArchived: newNotification.is_archived ?? false,
    isRead: newNotification.is_read ?? false,
    expiresAt: newNotification.expires_at
      ? new Date(newNotification.expires_at)
      : null,
    readAt: newNotification.read_at ? new Date(newNotification.read_at) : null,
    archivedAt: newNotification.archived_at
      ? new Date(newNotification.archived_at)
      : null,
    createdAt: newNotification.created_at
      ? new Date(newNotification.created_at)
      : new Date(),
  };
}
