import type { Notification } from '@/entities/notifications/model/types';
import type { NotificationTypeEnum } from '@lilypad/db/enums';
import type { ReactNode } from 'react';

export interface NotificationPlugin {
  type: NotificationTypeEnum;
  render?: (notification: Notification, compact?: boolean) => ReactNode;
  beforeAction?: (
    actionId: string,
    notification: Notification
  ) => boolean | Promise<boolean>;
  afterAction?: (
    actionId: string,
    notification: Notification
  ) => void | Promise<void>;
  validateNotification?: (notification: Notification) => boolean;
}

class NotificationPluginRegistry {
  private plugins: Map<NotificationTypeEnum, NotificationPlugin> = new Map();

  register(plugin: NotificationPlugin): void {
    if (this.plugins.has(plugin.type)) {
      console.warn(
        `Plugin for type ${plugin.type} already registered. Overwriting.`
      );
    }
    this.plugins.set(plugin.type, plugin);
  }

  unregister(type: NotificationTypeEnum): void {
    this.plugins.delete(type);
  }

  get(type: NotificationTypeEnum): NotificationPlugin | undefined {
    return this.plugins.get(type);
  }

  has(type: NotificationTypeEnum): boolean {
    return this.plugins.has(type);
  }

  clear(): void {
    this.plugins.clear();
  }
}

// Global plugin registry instance
export const notificationPlugins = new NotificationPluginRegistry();

// Example plugin registration (commented out - use as reference)
/*
notificationPlugins.register({
	type: NotificationTypeEnum.SecurityAlert,
	beforeAction: async (actionId, notification) => {
		if (actionId === 'take-action') {
			// Validate security context before allowing action
			const hasSecurityClearance = await validateUserSecurityClearance();
			if (!hasSecurityClearance) {
				alert('You need elevated permissions to perform this action');
				return false;
			}
		}
		return true;
	},
	afterAction: async (actionId, notification) => {
		if (actionId === 'take-action') {
			// Log security action for audit trail
			await logSecurityAction({
				notificationId: notification.id,
				actionId,
				timestamp: new Date(),
			});
		}
	},
});
*/
