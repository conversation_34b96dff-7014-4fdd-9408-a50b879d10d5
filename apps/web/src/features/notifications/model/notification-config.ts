import { NotificationTypeEnum } from '@lilypad/db/enums';
import type { LucideIcon } from 'lucide-react';
import {
  AlertTriangleIcon,
  ArchiveIcon,
  BellIcon,
  CalendarIcon,
  CheckCircleIcon,
  CheckIcon,
  ClipboardListIcon,
  ClockIcon,
  ExternalLinkIcon,
  EyeIcon,
  FileCheckIcon,
  FileTextIcon,
  InfoIcon,
  RefreshCwIcon,
  ShieldAlertIcon,
  UsersIcon,
  WrenchIcon,
  XCircleIcon,
} from 'lucide-react';
import type {
  Notification,
  ReportReadyNotificationMetadata,
} from '@/entities/notifications/model/types';

export interface NotificationAction {
  id: string;
  label: string;
  icon?: LucideIcon;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'destructive';
  className?: string;
  handler: (notification: Notification) => void | Promise<void>;
}

export interface NotificationConfig {
  icon: LucideIcon;
  iconColor: string;
  iconBgColor: string;
  containerClassName?: string;
  unreadIndicatorColor?: string;
  badge?: {
    text: string;
    className: string;
  };
  actions?: NotificationAction[];
}

export const notificationConfigs: Record<
  NotificationTypeEnum,
  NotificationConfig
> = {
  [NotificationTypeEnum.GENERAL]: {
    icon: BellIcon,
    iconColor: 'text-primary',
    iconBgColor: 'bg-primary/10',
  },

  [NotificationTypeEnum.SYSTEM_UPDATE]: {
    icon: RefreshCwIcon,
    iconColor: 'text-green-600',
    iconBgColor: 'bg-green-100',
    actions: [
      {
        id: 'learn-more',
        label: 'Learn More',
        icon: InfoIcon,
        variant: 'outline',
        handler: (notification) => {
          // TODO: Navigate to system updates page
          console.log('Learn more about system update', notification);
        },
      },
      {
        id: 'update-now',
        label: 'Update Now',
        icon: RefreshCwIcon,
        variant: 'default',
        handler: (notification) => {
          // TODO: Trigger system update
          console.log('Update system', notification);
        },
      },
    ],
  },

  [NotificationTypeEnum.REMINDER]: {
    icon: ClockIcon,
    iconColor: 'text-purple-600',
    iconBgColor: 'bg-purple-100',
    actions: [
      {
        id: 'snooze',
        label: 'Snooze',
        icon: ClockIcon,
        variant: 'outline',
        handler: (notification) => {
          // TODO: Snooze reminder
          console.log('Snooze reminder', notification);
        },
      },
      {
        id: 'dismiss',
        label: 'Dismiss',
        icon: CheckIcon,
        variant: 'ghost',
        handler: (notification) => {
          // TODO: Dismiss reminder
          console.log('Dismiss reminder', notification);
        },
      },
    ],
  },

  [NotificationTypeEnum.TASK_ASSIGNED]: {
    icon: ClipboardListIcon,
    iconColor: 'text-blue-600',
    iconBgColor: 'bg-blue-100',
    actions: [
      {
        id: 'view-task',
        label: 'View Task',
        icon: EyeIcon,
        variant: 'outline',
        handler: (notification) => {
          // TODO: Navigate to task details
          console.log('View task', notification);
        },
      },
      {
        id: 'accept',
        label: 'Accept',
        icon: CheckIcon,
        variant: 'default',
        handler: (notification) => {
          // TODO: Accept task assignment
          console.log('Accept task', notification);
        },
      },
    ],
  },

  [NotificationTypeEnum.DEADLINE_APPROACHING]: {
    icon: ClockIcon,
    iconColor: 'text-red-600',
    iconBgColor: 'bg-red-100',
    containerClassName: 'border-l-4 border-l-red-500',
    unreadIndicatorColor: 'bg-red-500 animate-pulse',
    badge: {
      text: 'Urgent',
      className: 'bg-red-100 text-red-700',
    },
    actions: [
      {
        id: 'snooze',
        label: 'Snooze',
        icon: ClockIcon,
        variant: 'outline',
        handler: (notification) => {
          // TODO: Snooze deadline reminder
          console.log('Snooze deadline', notification);
        },
      },
      {
        id: 'view-details',
        label: 'View Details',
        icon: ExternalLinkIcon,
        variant: 'default',
        handler: (notification) => {
          // TODO: Navigate to deadline details
          console.log('View deadline details', notification);
        },
      },
    ],
  },

  [NotificationTypeEnum.APPROVAL_NEEDED]: {
    icon: FileCheckIcon,
    iconColor: 'text-orange-600',
    iconBgColor: 'bg-orange-100',
    actions: [
      {
        id: 'review',
        label: 'Review',
        icon: EyeIcon,
        variant: 'outline',
        handler: (notification) => {
          // TODO: Navigate to approval request
          console.log('Review approval request', notification);
        },
      },
      {
        id: 'approve',
        label: 'Approve',
        icon: CheckIcon,
        variant: 'default',
        handler: (notification) => {
          // TODO: Quick approve action
          console.log('Approve request', notification);
        },
      },
    ],
  },

  [NotificationTypeEnum.POLICY_UPDATE]: {
    icon: FileTextIcon,
    iconColor: 'text-indigo-600',
    iconBgColor: 'bg-indigo-100',
    actions: [
      {
        id: 'review-policy',
        label: 'Review Policy',
        icon: FileTextIcon,
        variant: 'outline',
        handler: (notification) => {
          // TODO: Navigate to policy document
          console.log('Review policy', notification);
        },
      },
      {
        id: 'acknowledge',
        label: 'Acknowledge',
        icon: CheckIcon,
        variant: 'default',
        handler: (notification) => {
          // TODO: Acknowledge policy update
          console.log('Acknowledge policy', notification);
        },
      },
    ],
  },

  [NotificationTypeEnum.MAINTENANCE_ALERT]: {
    icon: WrenchIcon,
    iconColor: 'text-gray-600',
    iconBgColor: 'bg-gray-100',
    actions: [
      {
        id: 'view-schedule',
        label: 'View Schedule',
        icon: ClockIcon,
        variant: 'outline',
        handler: (notification) => {
          // TODO: Navigate to maintenance schedule
          console.log('View maintenance schedule', notification);
        },
      },
    ],
  },

  [NotificationTypeEnum.SECURITY_ALERT]: {
    icon: ShieldAlertIcon,
    iconColor: 'text-yellow-600',
    iconBgColor: 'bg-yellow-100',
    containerClassName: 'border-l-4 border-l-yellow-500',
    unreadIndicatorColor: 'bg-yellow-500',
    badge: {
      text: 'Security Alert',
      className: 'bg-yellow-100 text-yellow-700',
    },
    actions: [
      {
        id: 'learn-more',
        label: 'Learn More',
        icon: ExternalLinkIcon,
        variant: 'outline',
        handler: (notification) => {
          // TODO: Navigate to security documentation
          console.log('Learn more about security alert', notification);
        },
      },
      {
        id: 'take-action',
        label: 'Take Action',
        icon: ShieldAlertIcon,
        variant: 'default',
        className: 'bg-yellow-200 hover:bg-yellow-300',
        handler: (notification) => {
          // TODO: Navigate to security settings
          console.log('Take security action', notification);
        },
      },
    ],
  },

  [NotificationTypeEnum.TASK_ACCEPTED]: {
    icon: CheckCircleIcon,
    iconColor: 'text-green-600',
    iconBgColor: 'bg-green-100',
    actions: [
      {
        id: 'view-task',
        label: 'View Task',
        icon: EyeIcon,
        variant: 'outline',
        handler: (notification) => {
          // TODO: Navigate to accepted task details
          console.log('View accepted task', notification);
        },
      },
      {
        id: 'start-task',
        label: 'Start Task',
        icon: ClipboardListIcon,
        variant: 'default',
        handler: (notification) => {
          // TODO: Begin working on task
          console.log('Start task', notification);
        },
      },
    ],
  },

  [NotificationTypeEnum.TASK_REJECTED]: {
    icon: XCircleIcon,
    iconColor: 'text-red-600',
    iconBgColor: 'bg-red-100',
    actions: [
      {
        id: 'view-reason',
        label: 'View Reason',
        icon: EyeIcon,
        variant: 'outline',
        handler: (notification) => {
          // TODO: Navigate to rejection details
          console.log('View rejection reason', notification);
        },
      },
      {
        id: 'reassign',
        label: 'Reassign',
        icon: RefreshCwIcon,
        variant: 'default',
        handler: (notification) => {
          // TODO: Reassign task to another user
          console.log('Reassign task', notification);
        },
      },
    ],
  },

  [NotificationTypeEnum.TASK_COMPLETED]: {
    icon: CheckCircleIcon,
    iconColor: 'text-green-600',
    iconBgColor: 'bg-green-100',
    badge: {
      text: 'Completed',
      className: 'bg-green-100 text-green-700',
    },
    actions: [
      {
        id: 'view-results',
        label: 'View Results',
        icon: EyeIcon,
        variant: 'outline',
        handler: (notification) => {
          // TODO: Navigate to completed task results
          console.log('View task results', notification);
        },
      },
      {
        id: 'archive',
        label: 'Archive',
        icon: ArchiveIcon,
        variant: 'ghost',
        handler: (notification) => {
          // TODO: Archive completed task
          console.log('Archive task', notification);
        },
      },
    ],
  },

  [NotificationTypeEnum.TASK_CANCELLED]: {
    icon: XCircleIcon,
    iconColor: 'text-gray-600',
    iconBgColor: 'bg-gray-100',
    actions: [
      {
        id: 'view-details',
        label: 'View Details',
        icon: EyeIcon,
        variant: 'outline',
        handler: (notification) => {
          // TODO: Navigate to cancelled task details
          console.log('View cancelled task details', notification);
        },
      },
      {
        id: 'archive',
        label: 'Archive',
        icon: ArchiveIcon,
        variant: 'ghost',
        handler: (notification) => {
          // TODO: Archive cancelled task
          console.log('Archive cancelled task', notification);
        },
      },
    ],
  },

  [NotificationTypeEnum.TASK_DUE_SOON]: {
    icon: ClockIcon,
    iconColor: 'text-orange-600',
    iconBgColor: 'bg-orange-100',
    containerClassName: 'border-l-4 border-l-orange-500',
    unreadIndicatorColor: 'bg-orange-500',
    badge: {
      text: 'Due Soon',
      className: 'bg-orange-100 text-orange-700',
    },
    actions: [
      {
        id: 'view-task',
        label: 'View Task',
        icon: EyeIcon,
        variant: 'outline',
        handler: (notification) => {
          // TODO: Navigate to task due soon
          console.log('View task due soon', notification);
        },
      },
      {
        id: 'work-on-it',
        label: 'Work on It',
        icon: ClipboardListIcon,
        variant: 'default',
        handler: (notification) => {
          // TODO: Begin working on task
          console.log('Work on task', notification);
        },
      },
    ],
  },

  [NotificationTypeEnum.TASK_OVERDUE]: {
    icon: AlertTriangleIcon,
    iconColor: 'text-red-600',
    iconBgColor: 'bg-red-100',
    containerClassName: 'border-l-4 border-l-red-500',
    unreadIndicatorColor: 'bg-red-500 animate-pulse',
    badge: {
      text: 'Overdue',
      className: 'bg-red-100 text-red-700',
    },
    actions: [
      {
        id: 'view-task',
        label: 'View Task',
        icon: EyeIcon,
        variant: 'outline',
        handler: (notification) => {
          // TODO: Navigate to overdue task
          console.log('View overdue task', notification);
        },
      },
      {
        id: 'complete-now',
        label: 'Complete Now',
        icon: CheckIcon,
        variant: 'destructive',
        handler: (notification) => {
          // TODO: Navigate to complete overdue task
          console.log('Complete overdue task', notification);
        },
      },
    ],
  },

  [NotificationTypeEnum.EVALUATION_SCHEDULED]: {
    icon: CalendarIcon,
    iconColor: 'text-blue-600',
    iconBgColor: 'bg-blue-100',
    actions: [
      {
        id: 'view-schedule',
        label: 'View Schedule',
        icon: CalendarIcon,
        variant: 'outline',
        handler: (notification) => {
          // TODO: Navigate to evaluation schedule
          console.log('View evaluation schedule', notification);
        },
      },
      {
        id: 'prepare',
        label: 'Prepare',
        icon: FileCheckIcon,
        variant: 'default',
        handler: (notification) => {
          // TODO: Navigate to evaluation preparation
          console.log('Prepare for evaluation', notification);
        },
      },
    ],
  },

  [NotificationTypeEnum.RATING_SCALES_REMINDER]: {
    icon: FileCheckIcon,
    iconColor: 'text-purple-600',
    iconBgColor: 'bg-purple-100',
    actions: [
      {
        id: 'view-rating-scales',
        label: 'View Rating Scales',
        icon: EyeIcon,
        variant: 'outline',
        handler: (notification) => {
          // TODO: Navigate to rating scales
          console.log('View rating scales', notification);
        },
      },
      {
        id: 'dismiss',
        label: 'Dismiss',
        icon: CheckIcon,
        variant: 'ghost',
        handler: (notification) => {
          // TODO: Dismiss reminder
          console.log('Dismiss reminder', notification);
        },
      },
    ],
  },

  [NotificationTypeEnum.UPCOMING_EVALUATION_REMINDER]: {
    icon: CalendarIcon,
    iconColor: 'text-purple-600',
    iconBgColor: 'bg-purple-100',
    containerClassName: 'border-l-4 border-l-purple-500',
    unreadIndicatorColor: 'bg-purple-500',
    badge: {
      text: 'Upcoming',
      className: 'bg-purple-100 text-purple-700',
    },
    actions: [
      {
        id: 'view-evaluation',
        label: 'View Evaluation',
        icon: EyeIcon,
        variant: 'outline',
        handler: (notification) => {
          // TODO: Navigate to evaluation details
          console.log('View upcoming evaluation', notification);
        },
      },
      {
        id: 'prepare',
        label: 'Prepare',
        icon: FileCheckIcon,
        variant: 'default',
        handler: (notification) => {
          // TODO: Navigate to evaluation preparation
          console.log('Prepare for evaluation', notification);
        },
      },
    ],
  },

  [NotificationTypeEnum.MEETING_SCHEDULED]: {
    icon: UsersIcon,
    iconColor: 'text-teal-600',
    iconBgColor: 'bg-teal-100',
    actions: [
      {
        id: 'view-meeting',
        label: 'View Meeting',
        icon: EyeIcon,
        variant: 'outline',
        handler: (notification) => {
          // TODO: Navigate to meeting details
          console.log('View meeting details', notification);
        },
      },
      {
        id: 'join-meeting',
        label: 'Join Meeting',
        icon: ExternalLinkIcon,
        variant: 'default',
        handler: (notification) => {
          // TODO: Join meeting directly
          console.log('Join meeting', notification);
        },
      },
    ],
  },

  [NotificationTypeEnum.REPORT_READY]: {
    icon: FileCheckIcon,
    iconColor: 'text-green-600',
    iconBgColor: 'bg-green-100',
    badge: {
      text: 'Ready',
      className: 'bg-green-100 text-green-700',
    },
    actions: [
      {
        id: 'view-report',
        label: 'View Report',
        icon: EyeIcon,
        variant: 'outline',
        handler: (notification) => {
          const { reportUrl } =
            notification.metadata as ReportReadyNotificationMetadata;
          window.location.href = reportUrl;
        },
      },
      {
        id: 'download',
        label: 'Download',
        icon: ExternalLinkIcon,
        variant: 'default',
        handler: (notification) => {
          const { reportUrl } =
            notification.metadata as ReportReadyNotificationMetadata;
          window.open(reportUrl, '_blank');
        },
      },
    ],
  },
};
