import { z } from 'zod';

export const markNotificationAsReadSchema = z.object({
  notificationId: z.string().uuid(),
});

export const archiveNotificationSchema = z.object({
  notificationId: z.string().uuid(),
});

export const unarchiveNotificationSchema = z.object({
  notificationId: z.string().uuid(),
});

export const notificationPreferencesFormSchema = z.object({
  isAllNotificationsEnabled: z.boolean(),
  isInAppNotificationsEnabled: z.boolean(),
  isPushNotificationsEnabled: z.boolean(),
  isEmailEnabled: z.boolean(),
});

export type NotificationPreferencesFormData = z.infer<
  typeof notificationPreferencesFormSchema
>;
