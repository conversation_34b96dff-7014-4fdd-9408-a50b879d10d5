'use client';

import type { UserNotificationPreference } from '@lilypad/db/schema/types';
import { useZodForm } from '@lilypad/shared/hooks/use-zod-form';
import { toast } from '@lilypad/ui/components/sonner';
import { updateNotificationPreferencesAction } from '@/entities/notifications/api/update-user-preferences.action';
import {
  type NotificationPreferencesFormData,
  notificationPreferencesFormSchema,
} from '../model/schema';

interface UseNotificationPreferencesParams {
  notificationPreferences: UserNotificationPreference;
}

export function useNotificationPreferences({
  notificationPreferences,
}: UseNotificationPreferencesParams) {
  const methods = useZodForm({
    schema: notificationPreferencesFormSchema,
    defaultValues: getDefaultValues(notificationPreferences),
  });

  const canSubmit =
    !methods.formState.isSubmitting &&
    (!methods.formState.isSubmitted || methods.formState.isDirty);

  const onSubmit = async (data: NotificationPreferencesFormData) => {
    if (!canSubmit) {
      return;
    }
    const result = await updateNotificationPreferencesAction(data);
    if (result?.serverError || result?.validationErrors) {
      toast.error("Couldn't update notification preferences");
    } else {
      toast.success('Notification preferences updated');
    }
  };

  return {
    methods,
    canSubmit,
    onSubmit,
  };
}

function getDefaultValues(preferences: UserNotificationPreference) {
  return {
    isAllNotificationsEnabled:
      preferences.isInAppNotificationsEnabled &&
      preferences.isPushNotificationsEnabled &&
      preferences.isEmailEnabled,
    isInAppNotificationsEnabled: preferences.isInAppNotificationsEnabled,
    isPushNotificationsEnabled: preferences.isPushNotificationsEnabled,
    isEmailEnabled: preferences.isEmailEnabled,
  };
}
