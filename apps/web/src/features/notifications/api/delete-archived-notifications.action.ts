'use server';

import { caching, UserCache<PERSON><PERSON> } from '@/shared/caching';
import { authActionClient } from '@/shared/safe-action';
import { NotificationsService } from '@lilypad/core/services/notifications';
import { createDatabaseClient } from '@lilypad/db/client';
import { logger } from '@lilypad/shared/logger';
import { revalidateTag } from 'next/cache';

export const deleteArchivedNotificationsAction = authActionClient
  .metadata({ actionName: 'deleteArchivedNotifications' })
  .action(async ({ ctx: { user } }) => {
    if (!user?.id) {
      throw new Error('User not authenticated');
    }
    const userId = user.id;
    const db = await createDatabaseClient();

    const notification = new NotificationsService(db);

    await notification.deleteArchived(userId);

    logger.info({ userId }, '✅ SUCCESSFULLY DELETED ARCHIVED NOTIFICATIONS');

    revalidateTag(caching.createUserTag(UserCacheKey.Notifications, userId));
  });
