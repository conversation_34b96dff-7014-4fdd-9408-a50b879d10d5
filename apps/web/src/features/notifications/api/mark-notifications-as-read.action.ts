'use server';

import { caching, UserCacheKey } from '@/shared/caching';
import { authActionClient } from '@/shared/safe-action';
import { createDatabaseClient } from '@lilypad/db/client';
import { logger } from '@lilypad/shared/logger';
import { revalidateTag } from 'next/cache';

import { NotificationsService } from '@lilypad/core/services/notifications';
import { markNotificationAsReadSchema } from '../model/schema';

export const markNotificationAsReadAction = authActionClient
  .metadata({ actionName: 'markNotificationAsRead' })
  .schema(markNotificationAsReadSchema)
  .action(async ({ ctx: { user }, parsedInput: { notificationId } }) => {
    const userId = user?.id;
    if (!userId) {
      throw new Error('User not authenticated');
    }

    const db = await createDatabaseClient();

    const notification = new NotificationsService(db);

    await notification.markAsRead(userId, notificationId);

    logger.info(
      { userId, notificationId },
      '✅ SUCCESSFULLY MARKED NOTIFICATION AS READ'
    );

    revalidateTag(caching.createUserTag(UserCacheKey.Notifications, userId));
  });

export const markAllNotificationsAsReadAction = authActionClient
  .metadata({ actionName: 'markAllNotificationsAsRead' })
  .action(async ({ ctx: { user } }) => {
    const userId = user?.id;
    if (!userId) {
      throw new Error('User not authenticated');
    }

    const db = await createDatabaseClient();

    const notification = new NotificationsService(db);

    await notification.markAllAsRead(userId);

    logger.info({ userId }, '✅ SUCCESSFULLY MARKED ALL NOTIFICATIONS AS READ');

    revalidateTag(caching.createUserTag(UserCacheKey.Notifications, userId));
  });
