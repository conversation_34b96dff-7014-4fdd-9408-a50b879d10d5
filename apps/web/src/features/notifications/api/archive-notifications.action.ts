'use server';

import { caching, UserCacheKey } from '@/shared/caching';
import { authActionClient } from '@/shared/safe-action';
import { createDatabaseClient } from '@lilypad/db/client';
import { logger } from '@lilypad/shared/logger';
import { revalidateTag } from 'next/cache';

import { NotificationsService } from '@lilypad/core/services/notifications';
import {
  archiveNotificationSchema,
  unarchiveNotificationSchema,
} from '../model/schema';

export const archiveNotificationAction = authActionClient
  .schema(archiveNotificationSchema)
  .metadata({ actionName: 'archiveNotification' })
  .action(async ({ ctx: { user }, parsedInput: { notificationId } }) => {
    const userId = user?.id;
    if (!userId) {
      throw new Error('User not found');
    }

    const db = await createDatabaseClient();

    const notification = new NotificationsService(db);

    await notification.archive(userId, notificationId);

    logger.info(
      { userId, notificationId },
      '✅ SUCCESSFULLY ARCHIVED NOTIFICATION'
    );

    revalidateTag(caching.createUserTag(UserCacheKey.Notifications, userId));
  });

export const unarchiveNotificationAction = authActionClient
  .schema(unarchiveNotificationSchema)
  .metadata({ actionName: 'unarchiveNotification' })
  .action(async ({ ctx: { user }, parsedInput: { notificationId } }) => {
    const userId = user?.id;
    if (!userId) {
      throw new Error('User not found');
    }

    const db = await createDatabaseClient();

    const notification = new NotificationsService(db);

    await notification.unarchive(userId, notificationId);

    logger.info({ userId }, '✅ SUCCESSFULLY UNARCHIVED NOTIFICATION');

    revalidateTag(caching.createUserTag(UserCacheKey.Notifications, userId));
  });

export const archiveAllNotificationsAction = authActionClient
  .metadata({ actionName: 'archiveAllNotifications' })
  .action(async ({ ctx: { user } }) => {
    const userId = user?.id;
    if (!userId) {
      throw new Error('User not found');
    }

    const db = await createDatabaseClient();

    const notification = new NotificationsService(db);

    await notification.archiveAll(userId);

    logger.info({ userId }, '✅ SUCCESSFULLY ARCHIVED ALL NOTIFICATIONS');

    revalidateTag(caching.createUserTag(UserCacheKey.Notifications, userId));
  });

export const unarchiveAllNotificationsAction = authActionClient
  .metadata({ actionName: 'unarchiveAllNotifications' })
  .action(async ({ ctx: { user } }) => {
    const userId = user?.id;
    if (!userId) {
      throw new Error('User not found');
    }

    const db = await createDatabaseClient();

    const notification = new NotificationsService(db);

    await notification.unarchiveAll(userId);

    logger.info({ userId }, '✅ SUCCESSFULLY UNARCHIVED ALL NOTIFICATIONS');

    revalidateTag(caching.createUserTag(UserCacheKey.Notifications, userId));
  });

export const archiveReadNotificationsAction = authActionClient
  .metadata({ actionName: 'archiveReadNotifications' })
  .action(async ({ ctx: { user } }) => {
    const userId = user?.id;
    if (!userId) {
      throw new Error('User not found');
    }

    const db = await createDatabaseClient();

    const notification = new NotificationsService(db);

    await notification.archiveRead(userId);

    logger.info({ userId }, '✅ SUCCESSFULLY ARCHIVED READ NOTIFICATIONS');

    revalidateTag(caching.createUserTag(UserCacheKey.Notifications, userId));
  });
