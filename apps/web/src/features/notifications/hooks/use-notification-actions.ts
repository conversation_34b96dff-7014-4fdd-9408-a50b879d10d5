import type { Notification } from '@/entities/notifications/model/types';
import { useNotificationsContext } from '@/shared/contexts/notifications-context';
import { useCallback } from 'react';
import {
  archiveNotificationAction,
  unarchiveNotificationAction,
} from '../api/archive-notifications.action';
import { markNotificationAsReadAction } from '../api/mark-notifications-as-read.action';

export function useNotificationActions(notification: Notification) {
  const { notifications, setNotifications } = useNotificationsContext();

  const markAsRead = useCallback(async () => {
    if (notification.isRead) {
      return;
    }

    // Optimistic update
    const updatedNotifications = notifications.map((n) =>
      n.id === notification.id ? { ...n, isRead: true, readAt: new Date() } : n
    );
    setNotifications(updatedNotifications);

    // Server action
    await markNotificationAsReadAction({ notificationId: notification.id });
  }, [notification.id, notification.isRead, notifications, setNotifications]);

  const archive = useCallback(async () => {
    // Optimistic update
    const updatedNotifications = notifications.map((n) =>
      n.id === notification.id
        ? { ...n, isArchived: true, archivedAt: new Date() }
        : n
    );
    setNotifications(updatedNotifications);

    // Server action
    await archiveNotificationAction({ notificationId: notification.id });
  }, [notification.id, notifications, setNotifications]);

  const unarchive = useCallback(async () => {
    const updatedNotifications = notifications.map((n) =>
      n.id === notification.id
        ? { ...n, isArchived: false, archivedAt: null }
        : n
    );
    setNotifications(updatedNotifications);

    // Server action
    await unarchiveNotificationAction({ notificationId: notification.id });
  }, [notification.id, notifications, setNotifications]);

  return {
    markAsRead,
    archive,
    unarchive,
    isUnread: !notification.isRead,
    isArchived: notification.isArchived,
  };
}
