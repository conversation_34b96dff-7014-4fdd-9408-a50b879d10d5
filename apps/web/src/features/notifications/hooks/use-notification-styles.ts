import type { Notification } from '@/entities/notifications/model/types';
import { cn } from '@lilypad/ui/lib/utils';
import type { NotificationConfig } from '../model/notification-config';

export function useNotificationStyles(
  notification: Notification,
  config: NotificationConfig,
  compact = false
) {
  const isUnread = !notification.isRead;

  const containerStyles = cn(
    'relative flex gap-3 p-4 transition-all duration-200 ease-in-out',
    'group cursor-pointer hover:bg-accent/50',
    isUnread && 'bg-accent/30',
    compact && 'p-3',
    isUnread && config.containerClassName
  );

  const unreadIndicatorStyles = cn(
    'absolute top-2 left-2 h-2 w-2 rounded-full',
    config.unreadIndicatorColor || 'bg-primary/80'
  );

  const avatarStyles = cn('shrink-0', compact ? 'size-6' : 'size-8');

  const contentStyles = cn(
    'break-words text-sm leading-relaxed',
    isUnread ? 'font-medium text-foreground' : 'text-muted-foreground'
  );

  const metadataStyles = cn(
    'text-xs',
    isUnread ? 'text-muted-foreground' : 'text-muted-foreground/70'
  );

  const actionButtonStyles = cn(
    'flex shrink-0 items-center gap-1',
    'opacity-0 transition-opacity duration-200 group-hover:opacity-100',
    'md:opacity-0 md:group-hover:opacity-100',
    'max-md:opacity-100'
  );

  return {
    containerStyles,
    unreadIndicatorStyles,
    avatarStyles,
    contentStyles,
    metadataStyles,
    actionButtonStyles,
    isUnread,
  };
}
