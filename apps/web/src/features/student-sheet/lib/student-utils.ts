// biome-ignore lint/style/noEnum: Can be used as a type
export enum StudentStatus {
  COMPLETED = 'completed',
  IN_PROGRESS = 'in progress',
  SCHEDULED = 'scheduled',
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

// biome-ignore lint/style/noEnum: Can be used as a type
export enum PerformanceLevel {
  HIGH = 'high',
  AVERAGE = 'average',
  BELOW_AVERAGE = 'below_average',
  LOW = 'low',
}

export const getStatusVariant = (status: string) => {
  switch (status.toLowerCase()) {
    case StudentStatus.COMPLETED:
    case StudentStatus.ACTIVE:
      return 'success';
    case StudentStatus.IN_PROGRESS:
      return 'default';
    case StudentStatus.SCHEDULED:
      return 'secondary';
    default:
      return 'outline';
  }
};

export const getPerformanceLevel = (percentile: string): PerformanceLevel => {
  const num = Number.parseInt(percentile, 10);
  if (num >= 75) {
    return PerformanceLevel.HIGH;
  }
  if (num >= 50) {
    return PerformanceLevel.AVERAGE;
  }
  if (num >= 25) {
    return PerformanceLevel.BELOW_AVERAGE;
  }
  return PerformanceLevel.LOW;
};

export const getPerformanceVariant = (percentile: string) => {
  const level = getPerformanceLevel(percentile);
  switch (level) {
    case PerformanceLevel.HIGH:
      return 'success';
    case PerformanceLevel.AVERAGE:
      return 'default';
    case PerformanceLevel.BELOW_AVERAGE:
      return 'secondary';
    default:
      return 'destructive';
  }
};
