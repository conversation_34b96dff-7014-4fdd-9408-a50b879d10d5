import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

interface SelectionState {
  selectedIds: Set<string>;
}

interface SelectionActions {
  toggleSelection: (id: string) => void;
  toggleAllSelection: (allIds: string[]) => void;
  clearSelection: () => void;
  selectMultiple: (ids: string[]) => void;
  getSelectedCount: () => number;
  isSelected: (id: string) => boolean;
}

type SelectionStore = SelectionState & SelectionActions;

export const useSelectionStore = create<SelectionStore>()(
  subscribeWithSelector(
    immer((set, get) => ({
      selectedIds: new Set<string>(),

      toggleSelection: (id) =>
        set((state) => {
          if (state.selectedIds.has(id)) {
            state.selectedIds.delete(id);
          } else {
            state.selectedIds.add(id);
          }
        }),

      toggleAllSelection: (allIds) =>
        set((state) => {
          if (state.selectedIds.size === allIds.length) {
            state.selectedIds.clear();
          } else {
            state.selectedIds = new Set(allIds);
          }
        }),

      clearSelection: () =>
        set((state) => {
          state.selectedIds.clear();
        }),

      selectMultiple: (ids) =>
        set((state) => {
          state.selectedIds = new Set(ids);
        }),

      getSelectedCount: () => get().selectedIds.size,
      isSelected: (id) => get().selectedIds.has(id),
    }))
  )
);
