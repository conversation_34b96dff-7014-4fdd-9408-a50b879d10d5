import { del, get, set } from 'idb-keyval';
import { nanoid } from 'nanoid';
import { create } from 'zustand';
import {
  createJSONStorage,
  persist,
  subscribeWithSelector,
} from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { createEmptyStudent } from '../create-student';
import type { StudentRowData } from '../schema';

const storage = createJSONStorage<StudentsDataStore>(() => ({
  getItem: async (name: string): Promise<string | null> => {
    try {
      const value = await get(name);
      return value || null;
    } catch (error) {
      return null;
    }
  },
  setItem: async (name: string, value: string): Promise<void> => {
    try {
      await set(name, value);
    } catch (error) {
      console.error('
  },
  removeItem: async (name: string): Promise<void> => {
    try {
      await del(name);
    } catch (error) {
      console.error('Error removing from IndexedDB:', error);
    }
}));

interface StudentsDataState {
  students: Map<string, StudentRowData>;
  _hasHydrated: boolean;
}

interface StudentsDataActions {
  // CRUD operations
  addStudent: () => string;
  updateStudent: (id: string, data: Partial<StudentRowData>) => void;
  deleteStudent: (id: string) => void;
  duplicateStudent: (id: string) => string | null;

  // Batch operations
  batchUpdateStudents: (
    updates: Array<{ id: string; data: Partial<StudentRowData> }>
  ) => void;
  importStudents: (
    students: StudentRowData[],
    replaceExisting?: boolean
  ) => void;

  // State management
  reset: () => void;
  setHasHydrated: (state: boolean) => void;

  // Getters
  getStudent: (id: string) => StudentRowData | undefined;
  getStudentsArray: () => StudentRowData[];
  getStudentsCount: () => number;
}

type StudentsDataStore = StudentsDataState & StudentsDataActions;

export const useStudentsDataStore = create<StudentsDataStore>()(
  subscribeWithSelector(
    persist(
      immer((set, get) => {
        const initialStudent = createEmptyStudent();
        return {
          students: new Map([[initialStudent.id, initialStudent]]),
          _hasHydrated: false,

          addStudent: () => {
            const student = createEmptyStudent();
            set((state) => {
              state.students.set(student.id, student);
            });
            return student.id;
          },

          updateStudent: (id, data) =>
            set((state) => {
              const student = state.students.get(id);
              if (!student) {
                return;
              }

              const updatedStudent = { ...student, ...data };

              // Handle language-related updates
              if (data.languageIds !== undefined) {
                if (
                  updatedStudent.primaryLanguageId &&
                  !data.languageIds.includes(updatedStudent.primaryLanguageId)
                ) {
                  updatedStudent.primaryLanguageId =
                    data.languageIds.length > 0 ? data.languageIds[0] : null;
                }
                if (data.languageIds.length === 1) {
                  updatedStudent.primaryLanguageId = data.languageIds[0];
                }
              }

              if (
                data.primaryLanguageId !== undefined &&
                data.primaryLanguageId !== null &&
                !updatedStudent.languageIds.includes(data.primaryLanguageId)
              ) {
                updatedStudent.primaryLanguageId = null;
              }

              state.students.set(id, updatedStudent);
            }),

          deleteStudent: (id) =>
            set((state) => {
              state.students.delete(id);
              // Ensure at least one student exists
              if (state.students.size === 0) {
                const newStudent = createEmptyStudent();
                state.students.set(newStudent.id, newStudent);
              }
            }),

          duplicateStudent: (id) => {
            const student = get().students.get(id);
            if (!student) {
              return null;
            }

            const newStudent = {
              ...student,
              id: nanoid(),
              studentIdNumber: '', // Clear unique ID
            };

            set((state) => {
              state.students.set(newStudent.id, newStudent);
            });

            return newStudent.id;
          },

          batchUpdateStudents: (updates) =>
            set((state) => {
              for (const { id, data } of updates) {
                const student = state.students.get(id);
                if (!student) {
                  continue;
                }

                const updatedStudent = { ...student, ...data };

                // Apply same language logic as updateStudent
                if (data.languageIds !== undefined) {
                  if (
                    updatedStudent.primaryLanguageId &&
                    !data.languageIds.includes(updatedStudent.primaryLanguageId)
                  ) {
                    updatedStudent.primaryLanguageId =
                      data.languageIds.length > 0 ? data.languageIds[0] : null;
                  }
                  if (data.languageIds.length === 1) {
                    updatedStudent.primaryLanguageId = data.languageIds[0];
                  }
                }

                state.students.set(id, updatedStudent);
              }
            }),

          importStudents: (students, replaceExisting = false) =>
            set((state) => {
              if (replaceExisting) {
                state.students.clear();
              }

              for (const student of students) {
                state.students.set(student.id, student);
              }

              // Ensure at least one student exists
              if (state.students.size === 0) {
                const newStudent = createEmptyStudent();
                state.students.set(newStudent.id, newStudent);
              }
            }),

          reset: () =>
            set((state) => {
              state.students.clear();
              const newStudent = createEmptyStudent();
              state.students.set(newStudent.id, newStudent);
            }),

          setHasHydrated: (hasHydrated) =>
            set((state) => {
              state._hasHydrated = hasHydrated;
            }),

          getStudent: (id) => get().students.get(id),
          getStudentsArray: () => Array.from(get().students.values()),
          getStudentsCount: () => get().students.size,
        };
      }),
      {
        name: 'lilypad-students-data',
        storage,
        version: 1,
        partialize: (state) => ({
          students: Array.from(state.students.entries()).map(
            ([id, student]) => [
              id,
              {
                ...student,
                // Convert dates to ISO strings for persistence
                createdAt: student.createdAt.toISOString(),
                updatedAt: student.updatedAt.toISOString(),
                dateOfBirth: student.dateOfBirth?.toISOString() || null,
              },
            ]
          ),
        }),
        onRehydrateStorage: () => (state) => {
          // Convert persisted data back to Map with proper dates
          if (state?.students) {
            const entries = Array.isArray(state.students) ? state.students : [];
            state.students = new Map(
              entries.map(([id, student]) => [
                id,
                {
                  ...student,
                  // Convert ISO strings back to dates
                  createdAt: new Date(student.createdAt),
                  updatedAt: new Date(student.updatedAt),
                  dateOfBirth: student.dateOfBirth ? new Date(student.dateOfBirth) : null,
                },
              ])
            );
          }
          state?.setHasHydrated(true);
        },
      }
    )
  )
);
