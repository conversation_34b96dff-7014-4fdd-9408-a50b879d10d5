import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import type { StudentSaveResult } from '../schema';

interface UIState {
  isLoading: boolean;
  isSaving: boolean;
  isImporting: boolean;
  importProgress: number;
  saveResults: StudentSaveResult[];
}

interface UIActions {
  setLoading: (loading: boolean) => void;
  setSaving: (saving: boolean) => void;
  setImporting: (importing: boolean) => void;
  setImportProgress: (progress: number) => void;
  setSaveResults: (results: StudentSaveResult[]) => void;
  clearSaveResults: () => void;
}

type UIStore = UIState & UIActions;

export const useStudentsUIStore = create<UIStore>()(
  immer((set) => ({
    isLoading: false,
    isSaving: false,
    isImporting: false,
    importProgress: 0,
    saveResults: [],

    setLoading: (loading) =>
      set((state) => {
        state.isLoading = loading;
      }),

    setSaving: (saving) =>
      set((state) => {
        state.isSaving = saving;
      }),

    setImporting: (importing) =>
      set((state) => {
        state.isImporting = importing;
      }),

    setImportProgress: (progress) =>
      set((state) => {
        state.importProgress = progress;
      }),

    setSaveResults: (results) =>
      set((state) => {
        state.saveResults = results;
      }),

    clearSaveResults: () =>
      set((state) => {
        state.saveResults = [];
      }),
  }))
);
