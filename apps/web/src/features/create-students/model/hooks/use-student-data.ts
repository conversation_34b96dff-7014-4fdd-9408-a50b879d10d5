import { useCallback } from 'react';
import { useShallow } from 'zustand/react/shallow';
import type { StudentRowData } from '../schema';
import { useStudentsDataStore } from '../stores/students-data.store';
import { useValidationStore } from '../stores/students-validation.store';

// Hook to subscribe to a single student
export function useStudent(id: string) {
  const student = useStudentsDataStore(
    useCallback((state) => state.getStudent(id), [id])
  );
  const errors = useValidationStore(
    useCallback((state) => state.getErrors(id), [id])
  );
  const hasErrors = useValidationStore(
    useCallback((state) => state.hasErrors(id), [id])
  );

  return {
    student,
    errors,
    hasErrors,
  };
}

// Hook to subscribe to a specific student field
export function useStudentField<K extends keyof StudentRowData>(
  id: string,
  field: K
): StudentRowData[K] | undefined {
  return useStudentsDataStore(
    useCallback((state) => state.getStudent(id)?.[field], [id, field])
  );
}

// Hook for student count only
export function useStudentsCount() {
  return useStudentsDataStore((state) => state.getStudentsCount());
}

// Hook for all students (use sparingly)
export function useAllStudents() {
  return useStudentsDataStore(useShallow((state) => state.getStudentsArray()));
}

// Hook for student operations
export function useStudentOperations() {
  const addStudent = useStudentsDataStore((state) => state.addStudent);
  const updateStudent = useStudentsDataStore((state) => state.updateStudent);
  const deleteStudent = useStudentsDataStore((state) => state.deleteStudent);
  const duplicateStudent = useStudentsDataStore(
    (state) => state.duplicateStudent
  );
  const batchUpdateStudents = useStudentsDataStore(
    (state) => state.batchUpdateStudents
  );
  const validateStudent = useValidationStore((state) => state.validateStudent);

  return {
    addStudent,
    updateStudent: useCallback(
      (id: string, data: Partial<StudentRowData>) => {
        updateStudent(id, data);
        const student = useStudentsDataStore.getState().getStudent(id);
        if (student) {
          validateStudent(student);
        }
      },
      [updateStudent, validateStudent]
    ),
    deleteStudent,
    duplicateStudent,
    batchUpdateStudents,
  };
}
