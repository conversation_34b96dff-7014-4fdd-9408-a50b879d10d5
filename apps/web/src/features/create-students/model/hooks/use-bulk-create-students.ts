import { useTRPC } from '@lilypad/api/client';
import type { TRPCClientError } from '@lilypad/api/client';
import { toast } from '@lilypad/ui/components/sonner';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useCallback } from 'react';
import type { StudentSaveResult } from '../schema';

interface UseBulkCreateStudentsOptions {
  onSuccess?: (results: StudentSaveResult[]) => void;
  onError?: (
    error: TRPCClientError,
    students?: {
      id: string;
      studentIdNumber: string;
      firstName: string;
      lastName: string;
    }[]
  ) => void;
}

export function useBulkCreateStudents(
  options: UseBulkCreateStudentsOptions = {}
) {
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  const handleSuccess = useCallback(
    (results: StudentSaveResult[]) => {
      void queryClient.invalidateQueries({
        queryKey: [['students']],
        exact: false,
      });

      options.onSuccess?.(results);
    },
    [queryClient, options]
  );

  const handleError = useCallback(
    (error: TRPCClientError) => {
      const message = error.message || 'An unexpected error occurred';
      toast.error(`Failed to create students: ${message}`);

      options.onError?.(error);
    },
    [options]
  );

  const {
    mutate,
    mutateAsync,
    isPending: isSaving,
    error,
    data,
  } = useMutation(
    trpc.students.bulkCreateStudents.mutationOptions({
      onSuccess: handleSuccess,
      onError: handleError,
    })
  );

  return {
    bulkCreateStudents: mutate,
    bulkCreateStudentsAsync: mutateAsync,
    isSaving,
    error,
    data,
  };
}
