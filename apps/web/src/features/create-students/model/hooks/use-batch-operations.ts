import { unstable_batchedUpdates } from 'react-dom';
import type { StudentRowData } from '../schema';
import { useStudentsDataStore } from '../stores/students-data.store';
import { useSelectionStore } from '../stores/students-selection.store';
import { useValidationStore } from '../stores/students-validation.store';

export function useBatchOperations() {
  const batchUpdateStudents = useStudentsDataStore(
    (state) => state.batchUpdateStudents
  );
  const deleteStudent = useStudentsDataStore((state) => state.deleteStudent);
  const duplicateStudent = useStudentsDataStore(
    (state) => state.duplicateStudent
  );
  const getStudentsArray = useStudentsDataStore(
    (state) => state.getStudentsArray
  );
  const selectedIds = useSelectionStore((state) => state.selectedIds);
  const clearSelection = useSelectionStore((state) => state.clearSelection);
  const validateAllStudents = useValidationStore(
    (state) => state.validateAllStudents
  );

  const deleteSelectedRows = () => {
    unstable_batchedUpdates(() => {
      const idsToDelete = Array.from(selectedIds);

      // Delete all selected students in batch
      for (const id of idsToDelete) {
        deleteStudent(id);
      }

      // Clear selection after deletion
      clearSelection();
    });
  };

  const duplicateSelectedRows = () => {
    unstable_batchedUpdates(() => {
      const idsToDuplicate = Array.from(selectedIds);
      const newIds: string[] = [];

      // Duplicate all selected students
      for (const id of idsToDuplicate) {
        const newId = duplicateStudent(id);
        if (newId) newIds.push(newId);
      }

      // Clear current selection
      clearSelection();
    });
  };

  const validateAll = () => {
    const students = getStudentsArray();
    return validateAllStudents(students);
  };

  const batchUpdate = (
    updates: Array<{ id: string; data: Partial<StudentRowData> }>
  ) => {
    unstable_batchedUpdates(() => {
      batchUpdateStudents(updates);

      // Validate affected students
      const affectedStudents = updates
        .map(({ id }) => getStudentsArray().find((s) => s.id === id))
        .filter(Boolean) as StudentRowData[];

      for (const student of affectedStudents) {
        useValidationStore.getState().validateStudent(student);
      }
    });
  };

  return {
    deleteSelectedRows,
    duplicateSelectedRows,
    validateAll,
    batchUpdate,
  };
}
