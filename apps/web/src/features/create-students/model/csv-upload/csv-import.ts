import <PERSON> from 'papaparse';
import { csvRowSchema } from '../schema';
import type {
  CSVImportError,
  CSVImportResult,
  CSVRowData,
  StudentRowData,
} from '../schema';
import { CSVDataMapper } from './csv-mapping';

interface CSVImportOptions {
  validateOnParse: boolean;
  batchSize: number;
  onProgress?: (progress: number) => void;
}

interface ParsedCSVData {
  data: CSVRowData[];
  errors: Papa.ParseError[];
  meta: Papa.ParseMeta;
}

export class CSVImportService {
  private mapper: CSVDataMapper;
  private abortController: AbortController | null = null;
  private worker: Worker | null = null;

  constructor(
    schools: Array<{ id: string; name: string }>,
    languages: Array<{ id: string; name: string; emoji?: string }>
  ) {
    this.mapper = new CSVDataMapper(schools, languages);
    // Initialize worker if available
    if (typeof Worker !== 'undefined') {
      try {
        this.worker = new Worker('/csv-parser.worker.js');
      } catch (error) {
        console.warn('Failed to initialize CSV parser worker:', error);
      }
    }
  }

  async parseCSV(
    file: File,
    options: CSVImportOptions = {
      validateOnParse: true,
      batchSize: 100,
    }
  ): Promise<CSVImportResult> {
    const startTime = performance.now();
    this.abortController = new AbortController();

    try {
      // Use web worker for large files if available
      if (this.worker && file.size > 1024 * 1024) {
        // 1MB threshold
        return await this.parseCSVWithWorker(file, options, startTime);
      }

      // Use streaming for large files without worker
      if (file.size > 1024 * 1024) {
        // 1MB threshold
        return await this.parseCSVStreaming(file, options, startTime);
      }

      return await this.parseCSVStandard(file, options, startTime);
    } catch (error) {
      return this.createErrorResult(
        [
          {
            row: 0,
            message:
              error instanceof Error ? error.message : 'Unknown error occurred',
            severity: 'error' as const,
          },
        ],
        performance.now() - startTime
      );
    } finally {
      this.abortController = null;
    }
  }

  private parseCSVWithWorker(
    file: File,
    options: CSVImportOptions,
    startTime: number
  ): Promise<CSVImportResult> {
    return new Promise((resolve) => {
      if (!this.worker) {
        // Fallback to streaming if worker is not available
        this.parseCSVStreaming(file, options, startTime).then(resolve);
        return;
      }

      let parsedData: CSVRowData[] = [];

      const handleMessage = async (event: MessageEvent) => {
        const { type, data, progress, error } = event.data;

        switch (type) {
          case 'progress':
            if (options.onProgress) {
              options.onProgress(progress);
            }
            break;

          case 'complete': {
            // Remove event listener
            this.worker?.removeEventListener('message', handleMessage);

            // Validate headers
            const validationError = this.validateHeaders(
              data.meta.fields || []
            );

            if (validationError) {
              resolve(
                this.createErrorResult(
                  [validationError],
                  performance.now() - startTime
                )
              );
              return;
            }

            // Process the parsed data
            parsedData = data.data as CSVRowData[];
            const processResult = await this.processCSVData(
              parsedData,
              options
            );
            processResult.stats.processingTime = performance.now() - startTime;
            resolve(processResult);
            break;
          }

          case 'error':
            this.worker?.removeEventListener('message', handleMessage);
            resolve(
              this.createErrorResult(
                [{ row: 0, message: error, severity: 'error' }],
                performance.now() - startTime
              )
            );
            break;
        }
      };

      // Add event listener
      this.worker.addEventListener('message', handleMessage);

      // Start parsing
      this.worker.postMessage({
        type: 'parse',
        data: {
          file,
          options: {
            sendProgress: true,
          },
        },
      });
    });
  }

  private parseCSVStandard(
    file: File,
    options: CSVImportOptions,
    startTime: number
  ): Promise<CSVImportResult> {
    return new Promise((resolve) => {
      Papa.parse<Record<string, string>>(file, {
        header: true,
        skipEmptyLines: true,
        transformHeader: (header: string) => header.trim(),
        complete: async (results: Papa.ParseResult<Record<string, string>>) => {
          // Validate headers
          const validationError = this.validateHeaders(
            results.meta.fields || []
          );
          if (validationError) {
            resolve(
              this.createErrorResult(
                [validationError],
                performance.now() - startTime
              )
            );
            return;
          }

          // Process data
          const processResult = await this.processCSVData(
            results.data as CSVRowData[],
            options
          );
          processResult.stats.processingTime = performance.now() - startTime;
          resolve(processResult);
        },
        error: (error) => {
          resolve(
            this.createErrorResult(
              [{ row: 0, message: error.message, severity: 'error' }],
              performance.now() - startTime
            )
          );
        },
      });
    });
  }

  private parseCSVStreaming(
    file: File,
    options: CSVImportOptions,
    startTime: number
  ): Promise<CSVImportResult> {
    return new Promise((resolve) => {
      const students: StudentRowData[] = [];
      const errors: CSVImportError[] = [];
      const warnings: CSVImportError[] = [];
      let rowCount = 0;
      let processedCount = 0;
      let headersValidated = false;
      const batchBuffer: CSVRowData[] = [];

      Papa.parse(file, {
        header: true,
        skipEmptyLines: true,
        transformHeader: (header: string) => header.trim(),
        chunk: async (
          chunk: Papa.ParseResult<Record<string, string>>,
          parser: Papa.Parser
        ) => {
          // Validate headers on first chunk
          if (!headersValidated) {
            const validationError = this.validateHeaders(
              chunk.meta.fields || []
            );
            if (validationError) {
              parser.abort();
              resolve(
                this.createErrorResult(
                  [validationError],
                  performance.now() - startTime
                )
              );
              return;
            }
            headersValidated = true;
          }

          // Check if parsing was aborted
          if (this.abortController?.signal.aborted) {
            parser.abort();
            resolve({
              success: false,
              data: students,
              errors: [
                ...errors,
                { row: 0, message: 'Import cancelled', severity: 'error' },
              ],
              warnings,
              stats: {
                totalRows: rowCount,
                validRows: students.length,
                invalidRows: errors.length,
                processingTime: performance.now() - startTime,
              },
            });
            return;
          }

          // Accumulate rows in buffer
          batchBuffer.push(...(chunk.data as CSVRowData[]));
          rowCount += chunk.data.length;

          // Process buffer when it reaches batch size
          if (batchBuffer.length >= options.batchSize) {
            parser.pause();

            const batch = batchBuffer.splice(0, options.batchSize);
            const batchResult = await this.processBatch(
              batch,
              processedCount + 2
            ); // +2 for header

            students.push(...batchResult.students);
            errors.push(...batchResult.errors);
            warnings.push(...batchResult.warnings);
            processedCount += batch.length;

            // Update progress
            if (options.onProgress && file.size > 0) {
              const estimatedProgress = Math.min(
                99,
                (chunk.meta.cursor / file.size) * 100
              );
              options.onProgress(estimatedProgress);
            }

            // Allow UI to update
            await new Promise((resolve) =>
              requestIdleCallback(() => resolve(null))
            );

            parser.resume();
          }
        },
        complete: async () => {
          // Process remaining buffer
          if (batchBuffer.length > 0) {
            const batchResult = await this.processBatch(
              batchBuffer,
              processedCount + 2
            );
            students.push(...batchResult.students);
            errors.push(...batchResult.errors);
            warnings.push(...batchResult.warnings);
          }

          if (options.onProgress) {
            options.onProgress(100);
          }

          resolve({
            success: students.length > 0,
            data: students,
            errors,
            warnings,
            stats: {
              totalRows: rowCount,
              validRows: students.length,
              invalidRows: errors.length,
              processingTime: performance.now() - startTime,
            },
          });
        },
        error: (error) => {
          resolve(
            this.createErrorResult(
              [{ row: 0, message: error.message, severity: 'error' }],
              performance.now() - startTime
            )
          );
        },
      });
    });
  }

  private async processBatch(
    batch: CSVRowData[],
    startRowIndex: number
  ): Promise<{
    students: StudentRowData[];
    errors: CSVImportError[];
    warnings: CSVImportError[];
  }> {
    const students: StudentRowData[] = [];
    const errors: CSVImportError[] = [];
    const warnings: CSVImportError[] = [];

    for (let i = 0; i < batch.length; i++) {
      const rowIndex = startRowIndex + i;
      const csvRow = batch[i];

      try {
        // Validate CSV row structure
        const validation = csvRowSchema.safeParse(csvRow);
        if (!validation.success) {
          errors.push({
            row: rowIndex,
            message: validation.error.errors
              .map((e) => `${e.path.join('.')}: ${e.message}`)
              .join('; '),
            severity: 'error',
          });
          continue;
        }

        // Map CSV row to student data
        const mappingResult = this.mapper.mapRow(validation.data);

        // Add any mapping errors
        for (const error of mappingResult.errors) {
          errors.push({
            row: rowIndex,
            message: error,
            severity: 'error',
          });
        }

        // Add any mapping warnings
        for (const warning of mappingResult.warnings) {
          warnings.push({
            row: rowIndex,
            message: warning,
            severity: 'warning',
          });
        }

        // Only add student if no critical errors
        if (mappingResult.errors.length === 0) {
          students.push(mappingResult.studentData);
        }
      } catch (error) {
        errors.push({
          row: rowIndex,
          message:
            error instanceof Error ? error.message : 'Unknown processing error',
          severity: 'error',
        });
      }
    }

    return { students, errors, warnings };
  }

  private async processCSVData(
    csvRows: CSVRowData[],
    options: CSVImportOptions
  ): Promise<CSVImportResult> {
    const students: StudentRowData[] = [];
    const errors: CSVImportError[] = [];
    const warnings: CSVImportError[] = [];

    const batchSize = options.batchSize;
    const totalRows = csvRows.length;

    for (let i = 0; i < csvRows.length; i += batchSize) {
      const batch = csvRows.slice(i, Math.min(i + batchSize, csvRows.length));
      const batchResult = await this.processBatch(batch, i + 2); // +2 for header

      students.push(...batchResult.students);
      errors.push(...batchResult.errors);
      warnings.push(...batchResult.warnings);

      // Report progress
      if (options.onProgress) {
        const progress = Math.min(100, ((i + batchSize) / totalRows) * 100);
        options.onProgress(progress);
      }

      // Allow UI to update between batches
      await new Promise((resolve) => requestIdleCallback(() => resolve(null)));
    }

    return {
      success: students.length > 0,
      data: students,
      errors,
      warnings,
      stats: {
        totalRows: csvRows.length,
        validRows: students.length,
        invalidRows: csvRows.length - students.length,
        processingTime: 0, // Will be set by caller
      },
    };
  }

  private validateHeaders(headers: string[]): CSVImportError | null {
    const expectedHeaders = [
      'Student ID',
      'First Name',
      'Middle Name',
      'Last Name',
      'Preferred Name',
      'Date of Birth',
      'Grade',
      'School',
      'Languages',
      'Guardian Name',
      'Guardian Email',
      'Guardian Phone',
    ];

    const missingHeaders = expectedHeaders.filter(
      (header) => !headers.includes(header) && header !== 'Middle Name' // Middle name is optional
    );

    if (missingHeaders.length > 0) {
      return {
        row: 0,
        message: `Missing required headers: ${missingHeaders.join(', ')}`,
        severity: 'error',
      };
    }

    return null;
  }

  private createErrorResult(
    errors: CSVImportError[],
    processingTime: number
  ): CSVImportResult {
    return {
      success: false,
      data: [],
      errors,
      warnings: [],
      stats: {
        totalRows: 0,
        validRows: 0,
        invalidRows: 0,
        processingTime,
      },
    };
  }

  validateFile(file: File): { valid: boolean; error?: string } {
    // Check file type
    if (!file.name.toLowerCase().endsWith('.csv')) {
      return { valid: false, error: 'File must be a CSV file' };
    }

    // Check file size (limit to 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      return { valid: false, error: 'File size must be less than 10MB' };
    }

    // Check if file is empty
    if (file.size === 0) {
      return { valid: false, error: 'File cannot be empty' };
    }

    return { valid: true };
  }

  abort() {
    if (this.abortController && !this.abortController.signal.aborted) {
      this.abortController.abort();
    }
    // Terminate worker if it exists
    if (this.worker) {
      this.worker.terminate();
      this.worker = null;
    }
  }

  cleanup() {
    // Clean up worker when service is no longer needed
    if (this.worker) {
      this.worker.terminate();
      this.worker = null;
    }
  }
}
