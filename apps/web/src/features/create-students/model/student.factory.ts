import { GenderEnum, SchoolGradeEnum } from '@lilypad/db/enums';
import { nanoid } from 'nanoid';
import type { StudentRowData } from './schema';

export const createEmptyStudent = (): StudentRowData => ({
  id: nanoid(),
  studentIdNumber: '',
  firstName: '',
  middleName: '',
  lastName: '',
  preferredName: '',
  dateOfBirth: new Date(),
  dateOfConsent: new Date(),
  grade: SchoolGradeEnum.KINDERGARTEN,
  gender: GenderEnum.MALE,
  primarySchoolId: '',
  languageIds: [],
  primaryLanguageId: null,
  parents: [],
  documents: [],
  specialNeedsIndicator: false,
  hasErrors: false,
  errors: {},
});
