import { z } from 'zod';
// Import core schemas from API package
import {
  type Student,
  type StudentSaveResult,
  type SerializableFile,
  bulkStudentSchema,
  documentSchema,
  parentSchema,
  studentSaveResultSchema,
  studentSchema,
} from '@lilypad/api/schemas/students';

// Import file utilities
import { fileToSerializable } from '@lilypad/shared/lib/file-utils';

export { parentSchema, documentSchema, studentSaveResultSchema };

export type ParentFormData = z.input<typeof parentSchema>;
export type DocumentFormData = z.infer<typeof documentSchema>;

export const studentRowSchema = studentSchema
  .innerType()
  .extend({
    hasErrors: z.boolean().default(false),
    errors: z.record(z.string()).default({}),
  })
  .refine(
    (data) => {
      // If there are languages selected, primaryLanguageId must be set
      if (data.languageIds.length > 0 && !data.primaryLanguageId) {
        return false;
      }
      // If primaryLanguageId is set, it must be one of the selected languages
      if (
        data.primaryLanguageId &&
        !data.languageIds.includes(data.primaryLanguageId)
      ) {
        return false;
      }
      return true;
    },
    {
      message:
        'Primary language must be selected and must be one of the selected languages',
      path: ['primaryLanguageId'],
    }
  );

export type StudentRowData = z.infer<typeof studentRowSchema>;

export const bulkStudentSaveSchema = z.object({
  students: z
    .array(studentRowSchema)
    .min(1, 'At least one student is required'),
});

export const csvRowSchema = z.object({
  'Student ID': z.string().min(1, 'Student ID is required'),
  'First Name': z.string().min(1, 'First name is required'),
  'Middle Name': z.string().optional(),
  'Last Name': z.string().min(1, 'Last name is required'),
  'Preferred Name': z.string().min(1, 'Preferred name is required'),
  'Date of Birth': z
    .string()
    .regex(
      /^\d{1,2}\/\d{1,2}\/\d{4}$/,
      'Invalid date format (MM/DD/YYYY expected)'
    ),
  Grade: z.string().min(1, 'Grade is required'),
  School: z.string().min(1, 'School is required'),
  Languages: z.string().min(1, 'At least one language is required'),
  'Guardian Name': z.string().min(1, 'Guardian name is required'),
  'Guardian Email': z.string().email('Invalid email format'),
  'Guardian Phone': z
    .string()
    .min(10, 'Phone number must be at least 10 digits'),
});

export type CSVRowData = z.infer<typeof csvRowSchema>;

export const csvImportResultSchema = z.object({
  success: z.boolean(),
  data: z.array(studentRowSchema),
  errors: z.array(
    z.object({
      row: z.number(),
      field: z.string().optional(),
      message: z.string(),
      severity: z.enum(['error', 'warning']),
    })
  ),
  warnings: z.array(
    z.object({
      row: z.number(),
      field: z.string().optional(),
      message: z.string(),
      severity: z.enum(['error', 'warning']),
    })
  ),
  stats: z.object({
    totalRows: z.number(),
    validRows: z.number(),
    invalidRows: z.number(),
    processingTime: z.number(),
  }),
});

export type BulkStudentSaveData = z.infer<typeof bulkStudentSaveSchema>;
export type { StudentSaveResult };
export type CSVImportResult = z.infer<typeof csvImportResultSchema>;
export type CSVImportError = CSVImportResult['errors'][0];
export type CSVImportWarning = CSVImportResult['warnings'][0];

export async function convertStudentRowForSave(
  student: StudentRowData
): Promise<Student> {
  const convertedDocuments = await Promise.all(
    student.documents.map(async (doc) => ({
      ...doc,
      file:
        doc.file instanceof File
          ? await fileToSerializable(doc.file)
          : doc.file,
    }))
  );

  return {
    ...student,
    documents: convertedDocuments,
  };
}
