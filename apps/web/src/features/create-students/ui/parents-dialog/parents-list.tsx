'use client';

import { Badge } from '@lilypad/ui/components/badge';
import { If } from '@lilypad/ui/components/if';
import { ScrollArea } from '@lilypad/ui/components/scroll-area';
import { UsersRoundIcon } from 'lucide-react';
import React from 'react';
import type { ParentFormData } from '../../model/schema';
import { ParentCard } from './parent-card';

interface ParentsListProps {
  parents: ParentFormData[];
  onEditParent: (index: number) => void;
  onDeleteParent: (index: number) => void;
}

export const ParentsList = React.memo(function ParentsListComponent({
  parents,
  onEditParent,
  onDeleteParent,
}: ParentsListProps) {
  return (
    <div className="space-y-4 border-l pl-4">
      <h3 className="flex items-center gap-2 font-medium text-sm">
        <span>Parents/Guardians</span>
        <Badge size="sm" variant="secondary">
          {parents.length}
        </Badge>
      </h3>
      <ScrollArea className="h-[400px] pr-4">
        <If condition={parents.length === 0}>
          <div className="flex h-full flex-col items-center justify-center py-8 text-center text-muted-foreground">
            <UsersRoundIcon className="mx-auto mb-2 size-8 opacity-20" />
            <p className="text-sm">No parents/guardians added yet</p>
          </div>
        </If>
        <If condition={parents.length > 0}>
          <div className="space-y-2">
            {parents.map((parent, index) => (
              <ParentCard
                index={index}
                key={parent.primaryEmail ?? parent.primaryPhone}
                onDelete={onDeleteParent}
                onEdit={onEditParent}
                parent={parent}
              />
            ))}
          </div>
        </If>
      </ScrollArea>
    </div>
  );
});
