'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import {
  ParentRelationshipEnum,
  ParentRelationshipEnumMap,
} from '@lilypad/db/enums';
import { Button } from '@lilypad/ui/components/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@lilypad/ui/components/form';
import { Input } from '@lilypad/ui/components/input';
import { OptionalBadge } from '@lilypad/ui/components/optional-badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@lilypad/ui/components/select';
import { Toggle } from '@lilypad/ui/components/toggle';
import { CheckIcon } from 'lucide-react';
import React from 'react';
import { useForm } from 'react-hook-form';
import { type ParentFormData, parentSchema } from '../../model/schema';

interface ParentFormProps {
  onSubmit: (data: ParentFormData) => void;
  editingParent?: ParentFormData | null;
  isEditing: boolean;
}

const defaultValues: ParentFormData = {
  firstName: '',
  middleName: '',
  lastName: '',
  relationshipType: ParentRelationshipEnum.MOTHER,
  primaryEmail: '',
  secondaryEmail: '',
  primaryPhone: '',
  secondaryPhone: '',
  isPrimaryContact: false,
  hasPickupAuthorization: false,
};

export function ParentForm({
  onSubmit,
  editingParent,
  isEditing,
}: ParentFormProps) {
  const form = useForm<ParentFormData>({
    resolver: zodResolver(parentSchema),
    defaultValues,
  });

  // Reset form when editing parent changes
  React.useEffect(() => {
    if (editingParent) {
      form.reset({
        firstName: editingParent.firstName || '',
        middleName: editingParent.middleName || '',
        lastName: editingParent.lastName || '',
        relationshipType:
          editingParent.relationshipType || ParentRelationshipEnum.MOTHER,
        primaryEmail: editingParent.primaryEmail || '',
        secondaryEmail: editingParent.secondaryEmail || '',
        primaryPhone: editingParent.primaryPhone || '',
        secondaryPhone: editingParent.secondaryPhone || '',
        isPrimaryContact: editingParent.isPrimaryContact,
        hasPickupAuthorization: editingParent.hasPickupAuthorization ?? true,
      });
    } else {
      form.reset(defaultValues);
    }
  }, [editingParent, form]);

  const handleSubmit = (data: ParentFormData) => {
    onSubmit(data);
    form.reset(defaultValues);
  };

  return (
    <div className="space-y-4">
      <h3 className="font-medium text-sm">
        {isEditing ? 'Edit Parent/Guardian' : 'Add Parent/Guardian'}
      </h3>
      <Form {...form}>
        <form className="space-y-3" onSubmit={form.handleSubmit(handleSubmit)}>
          <div className="grid grid-cols-2 gap-3">
            <FormField
              control={form.control}
              name="firstName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>First Name</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="John" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="lastName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Last Name</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Doe" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="middleName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Middle Name <OptionalBadge />
                  </FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Michael" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="relationshipType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Relationship</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Select relationship" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent className="w-full">
                      {Object.entries(ParentRelationshipEnumMap).map(
                        ([value, label]) => (
                          <SelectItem key={value} value={value}>
                            {label}
                          </SelectItem>
                        )
                      )}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="primaryEmail"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Primary Email</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="<EMAIL>"
                      type="email"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="secondaryEmail"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Secondary Email</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="<EMAIL>"
                      type="email"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="grid grid-cols-2 gap-3">
            <FormField
              control={form.control}
              name="primaryPhone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Primary Phone</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="(*************" type="tel" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="secondaryPhone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Secondary Phone</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="(*************" type="tel" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="flex gap-3 space-y-2">
            <FormField
              control={form.control}
              name="isPrimaryContact"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Toggle
                      className="data-[state=on]:border-primary [&>svg]:hidden [&>svg]:size-4 data-[state=on]:[&>svg]:block"
                      onPressedChange={field.onChange}
                      pressed={field.value}
                      size="sm"
                      variant="outline"
                    >
                      <CheckIcon />
                      <span className="text-xs">Is Primary Contact</span>
                    </Toggle>
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="hasPickupAuthorization"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                  <FormControl>
                    <Toggle
                      className="data-[state=on]:border-primary [&>svg]:hidden [&>svg]:size-4 data-[state=on]:[&>svg]:block"
                      onPressedChange={field.onChange}
                      pressed={field.value}
                      size="sm"
                      variant="outline"
                    >
                      <CheckIcon />
                      <span className="text-xs">Has Pickup Authorization</span>
                    </Toggle>
                  </FormControl>
                </FormItem>
              )}
            />
          </div>

          <Button className="mt-4 w-full" size="sm" type="submit">
            {isEditing ? 'Update Parent/Guardian' : 'Add Parent/Guardian'}
          </Button>
        </form>
      </Form>
    </div>
  );
}
