'use client';

import { ParentRelationshipEnumMap } from '@lilypad/db/enums';
import { Badge } from '@lilypad/ui/components/badge';
import { Button } from '@lilypad/ui/components/button';
import { If } from '@lilypad/ui/components/if';
import { Trash2Icon } from 'lucide-react';
import React from 'react';
import type { ParentFormData } from '../../model/schema';

interface ParentCardProps {
  parent: ParentFormData;
  index: number;
  onEdit: (index: number) => void;
  onDelete: (index: number) => void;
}

export const ParentCard = React.memo(function ParentCardComponent({
  parent,
  index,
  onEdit,
  onDelete,
}: ParentCardProps) {
  const handleEdit = React.useCallback(() => {
    onEdit(index);
  }, [index, onEdit]);

  const handleDelete = React.useCallback(() => {
    onDelete(index);
  }, [index, onDelete]);

  return (
    <div className="space-y-2 rounded-lg border p-3 transition-colors hover:bg-accent/50">
      <div className="flex items-start justify-between">
        <div>
          <p className="font-medium">
            {parent.firstName} {parent.middleName && `${parent.middleName} `}
            {parent.lastName}
          </p>
          <p className="text-muted-foreground text-sm">
            {ParentRelationshipEnumMap[parent.relationshipType]}
          </p>
        </div>
        <div className="flex gap-1">
          <Button onClick={handleEdit} size="sm" type="button" variant="ghost">
            Edit
          </Button>
          <Button
            onClick={handleDelete}
            size="sm"
            type="button"
            variant="cancel"
          >
            <Trash2Icon className="size-4" />
          </Button>
        </div>
      </div>

      <div className="flex flex-wrap gap-2">
        <If condition={parent.isPrimaryContact}>
          <Badge className="text-xs" variant="default">
            Primary Contact
          </Badge>
        </If>
        <If condition={parent.hasPickupAuthorization}>
          <Badge className="text-xs" variant="secondary">
            Pickup Authorized
          </Badge>
        </If>
      </div>

      <If condition={parent.primaryEmail || parent.primaryPhone}>
        <div className="space-y-1 text-muted-foreground text-xs">
          <If condition={parent.primaryEmail}>
            <p>Email: {parent.primaryEmail}</p>
          </If>
          <If condition={parent.primaryPhone}>
            <p>Phone: {parent.primaryPhone}</p>
          </If>
        </div>
      </If>
    </div>
  );
});
