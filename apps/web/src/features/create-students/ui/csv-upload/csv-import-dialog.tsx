'use client';

import { Badge } from '@lilypad/ui/components/badge';
import { But<PERSON> } from '@lilypad/ui/components/button';
import {
  Choicebox,
  ChoiceboxItem,
  ChoiceboxItemContent,
  ChoiceboxItemDescription,
  ChoiceboxItemHeader,
  ChoiceboxItemIndicator,
  ChoiceboxItemTitle,
} from '@lilypad/ui/components/choicebox';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@lilypad/ui/components/dialog';
import { If } from '@lilypad/ui/components/if';
import { Label } from '@lilypad/ui/components/label';
import { toast } from '@lilypad/ui/components/sonner';
import { Download } from 'lucide-react';
import React from 'react';
import type { CSVImportResult, StudentRowData } from '../../model';
import { CSVImportService } from '../../model/csv-upload/csv-import';
import { CSVFileInfo } from './csv-file-info';
import { CSVImportDropzone } from './csv-import-dropzone';
import { CSVImportErrors } from './csv-import-errors';
import { CSVImportProgress } from './csv-import-progress';
import { CSVPreviewTable } from './csv-preview-table';

interface CSVImportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onImport: (students: StudentRowData[], replaceExisting: boolean) => void;
  schools: Array<{ id: string; name: string }>;
  languages: Array<{ id: string; name: string; emoji?: string }>;
}

export function CSVImportDialog({
  open,
  onOpenChange,
  onImport,
  schools,
  languages,
}: CSVImportDialogProps) {
  const [selectedFile, setSelectedFile] = React.useState<File | null>(null);
  const [importMode, setImportMode] = React.useState<'append' | 'replace'>(
    'append'
  );
  const [isImporting, setIsImporting] = React.useState(false);
  const [importProgress, setImportProgress] = React.useState(0);
  const [importResult, setImportResult] =
    React.useState<CSVImportResult | null>(null);

  const importService = React.useMemo(
    () => new CSVImportService(schools, languages),
    [schools, languages]
  );

  // Cleanup worker when component unmounts
  React.useEffect(() => {
    return () => {
      importService.cleanup();
    };
  }, [importService]);

  const handleFileSelect = React.useCallback(
    (file: File) => {
      const validation = importService.validateFile(file);

      if (!validation.valid) {
        toast.error(validation.error);
        return;
      }
      setSelectedFile(file);
      setImportResult(null);
    },
    [importService]
  );

  const handleFileRemove = React.useCallback(() => {
    setSelectedFile(null);
    setImportResult(null);
    setImportProgress(0);
  }, []);

  const handleImport = React.useCallback(async () => {
    if (!selectedFile) {
      return;
    }

    setIsImporting(true);
    setImportProgress(0);

    try {
      const result = await importService.parseCSV(selectedFile, {
        validateOnParse: true,
        batchSize: 100,
        onProgress: setImportProgress,
      });

      setImportResult(result);

      if (result.success && result.data.length > 0) {
        toast.success(
          `Successfully parsed ${result.data.length} students from CSV`
        );
      }
    } catch (error) {
      toast.error(
        error instanceof Error ? error.message : 'Failed to import CSV'
      );
    } finally {
      setIsImporting(false);
    }
  }, [selectedFile, importService]);

  const handleReset = React.useCallback(() => {
    setSelectedFile(null);
    setImportResult(null);
    setImportProgress(0);
    setImportMode('append');
  }, []);

  const handleConfirmImport = React.useCallback(() => {
    if (!importResult?.success) {
      return;
    }

    onImport(importResult.data, importMode === 'replace');
    handleReset();
  }, [importResult, importMode, onImport, handleReset]);

  const handleClose = React.useCallback(() => {
    handleReset();
    onOpenChange(false);
  }, [handleReset, onOpenChange]);

  return (
    <Dialog onOpenChange={handleClose} open={open}>
      <DialogContent className="flex max-h-[90vh] min-h-[400px] max-w-4xl flex-col overflow-hidden p-0">
        <DialogHeader className="p-6 pb-0">
          <DialogTitle>Import Students</DialogTitle>
          <DialogDescription>
            Upload a CSV file to bulk import student data. Use the provided
            template to ensure the data is formatted correctly.
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto px-6 py-0">
          <If condition={!(selectedFile || isImporting || importResult)}>
            <div className="space-y-4">
              <div className="flex items-center justify-center gap-2 text-muted-foreground text-sm">
                <a
                  className="w-full"
                  download
                  href="/docs/students_template.csv"
                >
                  <Button className="w-full gap-2" size="sm" variant="outline">
                    <Download className="size-4" />
                    Download CSV template
                  </Button>
                </a>
              </div>
              <CSVImportDropzone
                isDisabled={isImporting}
                onFileSelect={handleFileSelect}
              />
            </div>
          </If>

          <If condition={selectedFile && !isImporting && !importResult}>
            <div className="space-y-4">
              <CSVFileInfo file={selectedFile} onRemove={handleFileRemove} />
            </div>
          </If>

          <If condition={isImporting}>
            <CSVImportProgress
              fileName={selectedFile?.name || ''}
              progress={importProgress}
              status="Parsing and validating student data..."
            />
          </If>

          <If condition={importResult}>
            <div className="space-y-4">
              <CSVImportErrors
                errors={importResult?.errors || []}
                warnings={importResult?.warnings || []}
              />

              <If
                condition={importResult?.data && importResult.data.length > 0}
              >
                <div className="space-y-4">
                  <div>
                    <h4 className="mb-2 font-medium text-sm">
                      Preview{' '}
                      <Badge variant="secondary">First 5 Students</Badge>
                    </h4>
                    <CSVPreviewTable
                      data={importResult?.data.slice(0, 5) || []}
                      errors={importResult?.errors || []}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Import Mode</Label>
                    <Choicebox
                      className="grid grid-cols-2 gap-3"
                      onValueChange={(value) =>
                        setImportMode(value as 'append' | 'replace')
                      }
                      value={importMode}
                    >
                      <ChoiceboxItem value="append">
                        <ChoiceboxItemHeader>
                          <ChoiceboxItemTitle>
                            Add to existing
                          </ChoiceboxItemTitle>
                          <ChoiceboxItemDescription className="text-xs">
                            Keep your current students and add the new ones from
                            the CSV
                          </ChoiceboxItemDescription>
                        </ChoiceboxItemHeader>
                        <ChoiceboxItemContent>
                          <ChoiceboxItemIndicator />
                        </ChoiceboxItemContent>
                      </ChoiceboxItem>

                      <ChoiceboxItem value="replace">
                        <ChoiceboxItemHeader>
                          <ChoiceboxItemTitle>Replace all</ChoiceboxItemTitle>
                          <ChoiceboxItemDescription className="text-xs">
                            Remove all existing students and import only the
                            ones from the CSV
                          </ChoiceboxItemDescription>
                        </ChoiceboxItemHeader>
                        <ChoiceboxItemContent>
                          <ChoiceboxItemIndicator />
                        </ChoiceboxItemContent>
                      </ChoiceboxItem>
                    </Choicebox>
                  </div>
                </div>
              </If>
            </div>
          </If>
        </div>

        <DialogFooter className="bg-muted px-6 py-4">
          <If condition={!(importResult || isImporting)}>
            <Button onClick={handleClose} size="sm" variant="cancel">
              Cancel
            </Button>
            <Button disabled={!selectedFile} onClick={handleImport} size="sm">
              Import CSV
            </Button>
          </If>

          <If condition={isImporting}>
            <Button
              disabled
              onClick={() => importService.abort()}
              variant="outline"
            >
              Importing...
            </Button>
          </If>

          <If condition={importResult}>
            <Button onClick={handleReset} size="sm" variant="outline">
              Import Another
            </Button>
            <Button
              disabled={
                !importResult?.success || importResult.data.length === 0
              }
              onClick={handleConfirmImport}
              size="sm"
            >
              Import {importResult?.data.length || 0} Students
            </Button>
          </If>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
