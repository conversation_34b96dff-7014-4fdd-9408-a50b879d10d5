'use client';

import { cn } from '@lilypad/ui/lib/utils';
import { FileSpreadsheet } from 'lucide-react';
import React from 'react';

interface CSVImportDropzoneProps {
  onFileSelect: (file: File) => void;
  isDisabled?: boolean;
}

export const CSVImportDropzone = React.memo(
  function CSVImportDropzoneComponent({
    onFileSelect,
    isDisabled = false,
  }: CSVImportDropzoneProps) {
    const [dragActive, setDragActive] = React.useState(false);
    const inputRef = React.useRef<HTMLInputElement>(null);

    const handleDrag = React.useCallback((e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      if (e.type === 'dragenter' || e.type === 'dragover') {
        setDragActive(true);
      } else if (e.type === 'dragleave') {
        setDragActive(false);
      }
    }, []);

    const handleDrop = React.useCallback(
      (e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setDragActive(false);

        if (isDisabled) {
          return;
        }

        const files = Array.from(e.dataTransfer.files);
        const csvFile = files.find((file) =>
          file.name.toLowerCase().endsWith('.csv')
        );

        if (csvFile) {
          onFileSelect(csvFile);
        }
      },
      [onFileSelect, isDisabled]
    );

    const handleFileChange = React.useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
          onFileSelect(file);
        }
      },
      [onFileSelect]
    );

    return (
      // biome-ignore lint/a11y/noStaticElementInteractions: Not needed
      // biome-ignore lint/nursery/noNoninteractiveElementInteractions: Not needed
      <div
        className={cn(
          'flex cursor-pointer flex-col items-center rounded-lg border border-dashed bg-muted/50 p-8 text-center text-muted-foreground text-xs transition-colors hover:bg-muted',
          dragActive
            ? 'border-primary bg-primary/5'
            : 'border-muted-foreground/25 hover:border-muted-foreground/50',
          isDisabled && 'cursor-not-allowed opacity-50'
        )}
        onClick={() => inputRef.current?.click()}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <input
          accept=".csv"
          className="hidden"
          disabled={isDisabled}
          onChange={handleFileChange}
          ref={inputRef}
          type="file"
        />

        <FileSpreadsheet className="mx-auto size-8 rounded-sm bg-muted p-1" />
        <h3 className="mt-4">Drag and drop CSV file here</h3>
        <p>or click to browse for a file</p>
      </div>
    );
  }
);
