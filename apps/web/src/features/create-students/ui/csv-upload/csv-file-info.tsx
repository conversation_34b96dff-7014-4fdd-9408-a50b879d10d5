'use client';

import { Badge } from '@lilypad/ui/components/badge';
import { Button } from '@lilypad/ui/components/button';
import { Card, CardContent } from '@lilypad/ui/components/card';
import { FileSpreadsheet, X } from 'lucide-react';
import React from 'react';

interface CSVFileInfoProps {
  file: File | null;
  onRemove: () => void;
}

export const CSVFileInfo = React.memo(function CSVFileInfoFunction({
  file,
  onRemove,
}: CSVFileInfoProps) {
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) {
      return '0 Bytes';
    }
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`;
  };

  const formatDate = (date: Date): string => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  if (!file) {
    return null;
  }

  return (
    <Card className="p-2 shadow-none">
      <CardContent className="flex items-center justify-between p-0">
        <div className="flex items-center gap-3">
          <div className="rounded-lg bg-primary/10 p-2">
            <FileSpreadsheet className="h-8 w-8 text-primary" />
          </div>
          <div className="space-y-1">
            <p className="font-medium text-sm">{file.name}</p>
            <div className="flex items-center gap-3 text-muted-foreground text-xs">
              <Badge className="text-xs" variant="secondary">
                {formatFileSize(file.size)}
              </Badge>
              <span>Modified: {formatDate(new Date(file.lastModified))}</span>
            </div>
          </div>
        </div>
        <Button
          className="size-8"
          onClick={onRemove}
          size="icon"
          variant="ghost"
        >
          <X className="size-4" />
          <span className="sr-only">Remove file</span>
        </Button>
      </CardContent>
    </Card>
  );
});
