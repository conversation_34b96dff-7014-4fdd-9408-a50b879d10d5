'use client';

import { Progress } from '@lilypad/ui/components/progress';
import { FileSpreadsheet } from 'lucide-react';
import React from 'react';

interface CSVImportProgressProps {
  fileName: string;
  progress: number;
  status: string;
}

export const CSVImportProgress = React.memo(
  function CSVImportProgressComponent({
    fileName,
    progress,
    status,
  }: CSVImportProgressProps) {
    return (
      <div className="space-y-4">
        <div className="flex items-center gap-3">
          <FileSpreadsheet className="size-10 text-muted-foreground" />
          <div className="flex-1">
            <p className="font-medium">{fileName}</p>
            <p className="text-muted-foreground text-sm">{status}</p>
          </div>
        </div>
        <Progress className="h-2" value={progress} />
        <p className="text-center text-muted-foreground text-sm">
          {Math.round(progress)}% complete
        </p>
      </div>
    );
  }
);
