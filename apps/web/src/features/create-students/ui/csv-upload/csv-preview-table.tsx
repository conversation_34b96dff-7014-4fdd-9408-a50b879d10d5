'use client';

import { Badge } from '@lilypad/ui/components/badge';
import { If } from '@lilypad/ui/components/if';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@lilypad/ui/components/table';
import { cn } from '@lilypad/ui/lib/utils';

import { SchoolGradeBadge } from '@/shared/ui/students/school-grade-badge';
import { StudentGenderBadge } from '@/shared/ui/students/student-gender-badge';
import type { CSVImportError, StudentRowData } from '../../model/schema';

interface CSVPreviewTableProps {
  data: StudentRowData[];
  errors: CSVImportError[];
  maxRows?: number;
}

export function CSVPreviewTable({
  data,
  errors,
  maxRows = 10,
}: CSVPreviewTableProps) {
  const displayData = data.slice(0, maxRows);

  // Create a map of student IDs to errors for quick lookup
  const errorMap = new Map<string, CSVImportError[]>();
  for (const error of errors) {
    const studentWithError = data.find((_, index) => index + 2 === error.row); // +2 for header and 1-indexing
    if (studentWithError) {
      if (!errorMap.has(studentWithError.id)) {
        errorMap.set(studentWithError.id, []);
      }
      errorMap.get(studentWithError.id)?.push(error);
    }
  }

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const getParentSummary = (parents: StudentRowData['parents']) => {
    if (parents.length === 0) {
      return 'No parents';
    }
    if (parents.length === 1) {
      return `${parents[0].firstName} ${parents[0].lastName}`;
    }
    return `${parents[0].firstName} ${parents[0].lastName} + ${parents.length - 1} more`;
  };

  return (
    <div className="relative max-h-[200px] overflow-y-auto rounded-lg border">
      <Table className="relative">
        <TableHeader className="sticky top-0 z-10 bg-muted">
          <TableRow>
            <TableHead className="w-[100px]">Student ID</TableHead>
            <TableHead>Name</TableHead>
            <TableHead>Date of Birth</TableHead>
            <TableHead>Grade</TableHead>
            <TableHead>Gender</TableHead>
            <TableHead>Parents</TableHead>
            <TableHead>Status</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {displayData.map((student) => {
            const hasErrors = errorMap.has(student.id);
            const studentErrors = errorMap.get(student.id) || [];

            return (
              <TableRow
                className={cn(hasErrors && 'bg-red-50 dark:bg-red-950/20')}
                key={student.id}
              >
                <TableCell className="font-mono text-sm">
                  {student.studentIdNumber}
                </TableCell>
                <TableCell>
                  <div>
                    <p className="font-medium">
                      {student.firstName}
                      {student.middleName && ` ${student.middleName}`}{' '}
                      {student.lastName}
                    </p>
                    <p className="text-muted-foreground text-sm">
                      Preferred: {student.preferredName}
                    </p>
                  </div>
                </TableCell>
                <TableCell>{formatDate(student.dateOfBirth)}</TableCell>
                <TableCell>
                  <SchoolGradeBadge grade={student.grade} />
                </TableCell>
                <TableCell>
                  <StudentGenderBadge gender={student.gender} />
                </TableCell>
                <TableCell className="text-sm">
                  {getParentSummary(student.parents)}
                </TableCell>
                <TableCell>
                  <If
                    condition={hasErrors}
                    fallback={
                      <Badge className="text-xs" variant="success">
                        Valid
                      </Badge>
                    }
                  >
                    <div className="space-y-1">
                      <Badge className="text-xs" variant="destructive">
                        {studentErrors.length} Error
                        {studentErrors.length !== 1 ? 's' : ''}
                      </Badge>
                      {studentErrors.slice(0, 2).map((error, errorIndex) => (
                        <p
                          className="text-destructive text-xs"
                          key={errorIndex}
                        >
                          {error.message}
                        </p>
                      ))}
                      {studentErrors.length > 2 && (
                        <p className="text-muted-foreground text-xs">
                          +{studentErrors.length - 2} more errors
                        </p>
                      )}
                    </div>
                  </If>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
}
