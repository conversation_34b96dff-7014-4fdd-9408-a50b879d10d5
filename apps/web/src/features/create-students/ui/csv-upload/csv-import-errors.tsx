'use client';

import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@lilypad/ui/components/alert';
import { Badge } from '@lilypad/ui/components/badge';
import { If } from '@lilypad/ui/components/if';
import { ScrollArea } from '@lilypad/ui/components/scroll-area';
import { AlertCircle, AlertTriangle } from 'lucide-react';
import React from 'react';
import type { CSVImportError, CSVImportWarning } from '../../model/schema';

interface CSVImportErrorsProps {
  errors: CSVImportError[];
  warnings: CSVImportWarning[];
}

export const CSVImportErrors = React.memo(function CSVImportErrorsComponent({
  errors,
  warnings,
}: CSVImportErrorsProps) {
  if (errors.length === 0 && warnings.length === 0) {
    return null;
  }

  return (
    <div className="space-y-4">
      <If condition={errors.length > 0}>
        <Alert variant="destructive">
          <AlertCircle className="size-4" />
          <AlertTitle>Import Errors</AlertTitle>
          <AlertDescription>
            <ScrollArea className="mt-2 h-24">
              <ul className="space-y-1 text-sm">
                {errors.map((error, index) => (
                  <li className="flex items-start gap-2" key={index}>
                    <Badge className="mt-0.5" variant="destructive">
                      Row {error.row}
                    </Badge>
                    <span>{error.message}</span>
                  </li>
                ))}
              </ul>
            </ScrollArea>
          </AlertDescription>
        </Alert>
      </If>

      <If condition={warnings.length > 0}>
        <Alert>
          <AlertTriangle className="size-4" />
          <AlertTitle>Import Warnings</AlertTitle>
          <AlertDescription>
            <ScrollArea className="mt-2 h-24">
              <ul className="space-y-1 text-sm">
                {warnings.map((warning, index) => (
                  <li className="flex items-start gap-2" key={index}>
                    <Badge className="mt-0.5" variant="secondary">
                      Row {warning.row}
                    </Badge>
                    <span>{warning.message}</span>
                  </li>
                ))}
              </ul>
            </ScrollArea>
          </AlertDescription>
        </Alert>
      </If>
    </div>
  );
});
