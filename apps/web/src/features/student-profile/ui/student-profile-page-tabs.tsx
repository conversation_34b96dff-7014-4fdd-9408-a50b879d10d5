'use client';

import {
  AnimatedTabs,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  AnimatedTabsTrigger,
  type RoutedTab,
} from '@lilypad/ui/components/tabs.animated';
import {
  ActivityIcon,
  ChartCandlestickIcon,
  FileTextIcon,
  FolderKanbanIcon,
  FolderOpenIcon,
  NotebookPenIcon,
  NotepadTextIcon,
} from 'lucide-react';
import { usePathname, useRouter } from 'next/navigation';
import type { StudentProfile } from '@/entities/students/model/schema';

interface StudentProfilePageTabsProps {
  student: StudentProfile;
}

export function StudentProfilePageTabs({
  student,
}: StudentProfilePageTabsProps) {
  const pathname = usePathname();
  const router = useRouter();

  const tabs: RoutedTab[] = [
    {
      label: 'Activity',
      value: 'activity',
      href: `/students/${student.id}`,
      subRoutes: [`/students/${student.id}`],
    },
    {
      label: 'Cases',
      value: 'cases',
      href: `/students/${student.id}/cases`,
      subRoutes: [`/students/${student.id}/cases`],
    },
    {
      label: 'Plans',
      value: 'plans',
      href: `/students/${student.id}/plans`,
    },
    {
      label: 'Assessments',
      value: 'assessments',
      href: `/students/${student.id}/assessments`,
    },
    {
      label: 'Documents',
      value: 'documents',
      href: `/students/${student.id}/documents`,
    },
    {
      label: 'Reports',
      value: 'reports',
      href: `/students/${student.id}/reports`,
    },
    {
      label: 'Notes',
      value: 'notes',
      href: `/students/${student.id}/notes`,
    },
  ];

  const handleNavigate = (href: string, tabValue: string) => {
    console.log('Navigating to:', href, 'for tab:', tabValue);
    router.push(href);
  };

  return (
    <AnimatedTabs
      className="flex h-full flex-col"
      currentPath={pathname}
      defaultValue="activity"
      enableRouting={true}
      onNavigate={handleNavigate}
      tabs={tabs}
    >
      <AnimatedTabsList enableHorizontalScroll>
        <AnimatedTabsTrigger value="activity">
          <ActivityIcon className="size-4 text-muted-foreground" />
          Activity
        </AnimatedTabsTrigger>
        <AnimatedTabsTrigger value="cases">
          <FolderKanbanIcon className="size-4 text-muted-foreground" />
          Cases
        </AnimatedTabsTrigger>
        <AnimatedTabsTrigger value="plans">
          <NotebookPenIcon className="size-4 text-muted-foreground" />
          Plans
        </AnimatedTabsTrigger>
        <AnimatedTabsTrigger value="assessments">
          <ChartCandlestickIcon className="size-4 text-muted-foreground" />
          Assessments
        </AnimatedTabsTrigger>
        <AnimatedTabsTrigger value="documents">
          <FolderOpenIcon className="size-4 text-muted-foreground" />
          Documents
        </AnimatedTabsTrigger>
        <AnimatedTabsTrigger value="reports">
          <FileTextIcon className="size-4 text-muted-foreground" />
          Reports
        </AnimatedTabsTrigger>
        <AnimatedTabsTrigger value="notes">
          <NotepadTextIcon className="size-4 text-muted-foreground" />
          Notes
        </AnimatedTabsTrigger>
      </AnimatedTabsList>
    </AnimatedTabs>
  );
}
