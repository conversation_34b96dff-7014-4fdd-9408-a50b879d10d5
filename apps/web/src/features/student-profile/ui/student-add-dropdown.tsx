'use client';

import { AddAssessmentDialog } from '@/features/assessment-management/ui';
import { AddCaseDialog } from '@/features/case-management/ui/add-case-dialog';
import { AddPlanDialog } from '@/features/plan-management/ui/add-plan-dialog';
import { Button } from '@lilypad/ui/components/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@lilypad/ui/components/dropdown-menu';
import { If } from '@lilypad/ui/components/if';
import { Kbd, KbdKey, KbdSeparator } from '@lilypad/ui/components/kbd';
import { useMediaQuery } from '@lilypad/ui/hooks/use-media-query';
import { MediaQueries } from '@lilypad/ui/lib/media-queries';
import {
  ChartCandlestickIcon,
  FileTextIcon,
  FolderKanbanIcon,
  FolderOpenIcon,
  type LucideIcon,
  NotebookPenIcon,
  NotepadTextIcon,
  PlusIcon,
} from 'lucide-react';
import { useCallback, useMemo, useState } from 'react';
import { useHotkeys } from 'react-hotkeys-hook';

interface StudentAddDropdownProps {
  studentId: string;
}

export function StudentAddDropdown({ studentId }: StudentAddDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [dialogStates, setDialogStates] = useState<DialogState>(
    getInitialDialogState()
  );

  const options = getAddOptions();

  const actionHandlers = useMemo(
    () => ({
      [StudentAction.CASE]: () =>
        setDialogStates((prev) => ({ ...prev, [StudentAction.CASE]: true })),
      [StudentAction.ASSESSMENT]: () =>
        setDialogStates((prev) => ({
          ...prev,
          [StudentAction.ASSESSMENT]: true,
        })),
      [StudentAction.PLAN]: () =>
        setDialogStates((prev) => ({ ...prev, [StudentAction.PLAN]: true })),
      [StudentAction.DOCUMENT]: () =>
        console.log('Document dialog not implemented yet'),
      [StudentAction.REPORT]: () =>
        console.log('Report dialog not implemented yet'),
      [StudentAction.NOTE]: () =>
        console.log('Note dialog not implemented yet'),
    }),
    []
  );

  const createDialogHandler = useCallback((action: StudentAction) => {
    return (open: boolean) => {
      setDialogStates((prev) => ({ ...prev, [action]: open }));
    };
  }, []);

  const toggleDropdown = useCallback(() => {
    setIsOpen((prev) => !prev);
  }, []);

  const handleAction = useCallback(
    (action: StudentAction) => {
      const handler = actionHandlers[action];
      if (handler) {
        handler();
      }
      setIsOpen(false);
    },
    [actionHandlers]
  );

  const isAnyDialogOpen = useMemo(() => {
    return Object.values(dialogStates).some(Boolean);
  }, [dialogStates]);

  useStudentAddHotkeys(isAnyDialogOpen, toggleDropdown, handleAction);

  const isDesktop = useMediaQuery(MediaQueries.MdUp, { ssr: false });

  return (
    <>
      <DropdownMenu onOpenChange={setIsOpen} open={isOpen}>
        <DropdownMenuTrigger asChild>
          <Button className="gap-2 pr-1.5" size="sm" variant="outline">
            <If
              condition={isDesktop}
              fallback={<PlusIcon className="size-4" />}
            >
              <span>Add</span>
              <Kbd size="sm" variant="ghost">
                <KbdKey className="mt-0.5 text-sm">⌥</KbdKey>
                <KbdSeparator />
                <KbdKey>M</KbdKey>
              </Kbd>
            </If>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          {options.map((option) => {
            const Icon = option.icon;
            return (
              <DropdownMenuItem
                className="flex items-center justify-between"
                key={option.id}
                onClick={() => handleAction(option.id)}
              >
                <span className="flex items-center gap-2">
                  <Icon className="mr-2 h-4 w-4" />
                  {option.label}
                </span>
                <Kbd size="sm" variant="ghost">
                  <KbdKey>⌥</KbdKey>
                  <KbdSeparator />
                  <KbdKey>{option.hotkeyDisplay}</KbdKey>
                </Kbd>
              </DropdownMenuItem>
            );
          })}
        </DropdownMenuContent>
      </DropdownMenu>

      <AddCaseDialog
        onOpenChange={createDialogHandler(StudentAction.CASE)}
        open={dialogStates[StudentAction.CASE]}
        studentId={studentId}
      />

      <AddAssessmentDialog
        onOpenChange={createDialogHandler(StudentAction.ASSESSMENT)}
        open={dialogStates[StudentAction.ASSESSMENT]}
        studentId={studentId}
      />

      <AddPlanDialog
        onOpenChange={createDialogHandler(StudentAction.PLAN)}
        open={dialogStates[StudentAction.PLAN]}
        studentId={studentId}
      />
    </>
  );
}

// biome-ignore lint/style/noEnum: Can be enum
enum StudentAction {
  CASE = 'case',
  ASSESSMENT = 'assessment',
  PLAN = 'plan',
  DOCUMENT = 'document',
  REPORT = 'report',
  NOTE = 'note',
}

interface DialogState {
  [StudentAction.CASE]: boolean;
  [StudentAction.ASSESSMENT]: boolean;
  [StudentAction.PLAN]: boolean;
  [StudentAction.DOCUMENT]: boolean;
  [StudentAction.REPORT]: boolean;
  [StudentAction.NOTE]: boolean;
}

function getInitialDialogState(): DialogState {
  return {
    [StudentAction.CASE]: false,
    [StudentAction.ASSESSMENT]: false,
    [StudentAction.PLAN]: false,
    [StudentAction.DOCUMENT]: false,
    [StudentAction.REPORT]: false,
    [StudentAction.NOTE]: false,
  };
}

interface AddOption {
  id: StudentAction;
  label: string;
  icon: LucideIcon;
  hotkey: string;
  hotkeyDisplay: string;
}

function getAddOptions(): AddOption[] {
  return [
    {
      id: StudentAction.CASE,
      label: 'Case',
      icon: FolderKanbanIcon,
      hotkey: 'alt+c',
      hotkeyDisplay: 'C',
    },
    {
      id: StudentAction.ASSESSMENT,
      label: 'Assessment',
      icon: ChartCandlestickIcon,
      hotkey: 'alt+e',
      hotkeyDisplay: 'E',
    },
    {
      id: StudentAction.PLAN,
      label: 'Plan',
      icon: NotebookPenIcon,
      hotkey: 'alt+p',
      hotkeyDisplay: 'P',
    },
    {
      id: StudentAction.DOCUMENT,
      label: 'Document',
      icon: FolderOpenIcon,
      hotkey: 'alt+d',
      hotkeyDisplay: 'D',
    },
    {
      id: StudentAction.REPORT,
      label: 'Report',
      icon: FileTextIcon,
      hotkey: 'alt+r',
      hotkeyDisplay: 'R',
    },
    {
      id: StudentAction.NOTE,
      label: 'Note',
      icon: NotepadTextIcon,
      hotkey: 'alt+n',
      hotkeyDisplay: 'N',
    },
  ];
}

function useStudentAddHotkeys(
  isAnyDialogOpen: boolean,
  toggleDropdown: () => void,
  onAction: (action: StudentAction) => void
) {
  useHotkeys('alt+m', toggleDropdown, [toggleDropdown], {
    enabled: !isAnyDialogOpen,
  });
  // biome-ignore format: Custom formatted
  useHotkeys("alt+c", () => onAction(StudentAction.CASE), [onAction], {
		enabled: !isAnyDialogOpen,
	});
  // biome-ignore format: Custom formatted
  useHotkeys("alt+e", () => onAction(StudentAction.ASSESSMENT), [onAction], {
		enabled: !isAnyDialogOpen,
	});
  // biome-ignore format: Custom formatted
  useHotkeys("alt+p", () => onAction(StudentAction.PLAN), [onAction], {
		enabled: !isAnyDialogOpen,
	});
  // biome-ignore format: Custom formatted
  useHotkeys("alt+d", () => onAction(StudentAction.DOCUMENT), [onAction], {
		enabled: !isAnyDialogOpen,
	});
  // biome-ignore format: Custom formatted
  useHotkeys("alt+r", () => onAction(StudentAction.REPORT), [onAction], {
		enabled: !isAnyDialogOpen,
	});
  // biome-ignore format: Custom formatted
  useHotkeys("alt+n", () => onAction(StudentAction.NOTE), [onAction], {
		enabled: !isAnyDialogOpen,
	});
}
