import type { StudentProfile } from '@/entities/students/model/schema';
import { calculateAge } from '@/shared/lib/utils';
import { StudentAvatar } from '@/shared/ui/students/student-avatar';
import { StudentGenderBadge } from '@/shared/ui/students/student-gender-badge';
import { StudentStatusBadge } from '@/shared/ui/students/student-status-badge';
import {
  ParentRelationshipEnumMap,
  type SchoolGradeEnum,
  SchoolGradeEnumMap,
} from '@lilypad/db/enums';
import type { Language, Parent } from '@lilypad/db/types';
import { Badge } from '@lilypad/ui/components/badge';
import { If } from '@lilypad/ui/components/if';
import { Label } from '@lilypad/ui/components/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@lilypad/ui/components/popover';
import { Separator } from '@lilypad/ui/components/separator';
import { formatDate } from '@lilypad/ui/lib/utils';
import {
  BookMarkedIcon,
  CakeIcon,
  Globe2Icon,
  GraduationCapIcon,
  InfoIcon,
  LandmarkIcon,
  MailIcon,
  PhoneIcon,
  SchoolIcon,
  VenusAndMarsIcon,
} from 'lucide-react';
import { useMemo } from 'react';

interface StudentProfileDetailsProps {
  student: StudentProfile;
}

export function StudentProfileDetails({ student }: StudentProfileDetailsProps) {
  return (
    <div className="flex w-full flex-col">
      <div className="flex flex-col gap-4 p-4 ">
        <div className="flex flex-row items-start gap-4">
          <StudentAvatar className="size-20" student={student} />
          <div className="flex flex-col">
            <h1 className="truncate font-bold text-xl">{student.fullName}</h1>
            <If condition={student.preferredName}>
              <p className="flex items-center gap-1 text-muted-foreground text-xs">
                <InfoIcon className="size-3" />
                Prefers to be called {student.preferredName}
              </p>
            </If>
            <Badge className="mt-3 border border-orange-200 bg-orange-100 text-orange-500 text-xs dark:border-orange-800 dark:bg-orange-950 dark:text-orange-400">
              Plan Pending
            </Badge>
          </div>
        </div>
        <Property
          details={<StudentGenderBadge gender={student.gender} />}
          icon={<VenusAndMarsIcon className="size-4" />}
          placeholder="N/A"
          term="Gender"
        />
        <Property
          details={
            <div className="flex flex-row items-center gap-2">
              <span>{formatDate(student.dateOfBirth)}</span>
              <span className="text-muted-foreground text-xs">
                ({calculateAge(student.dateOfBirth)} years old)
              </span>
            </div>
          }
          icon={<CakeIcon className="size-4" />}
          placeholder="N/A"
          term="Date of Birth"
        />
        <Property
          details={student.primarySchool?.name}
          icon={<SchoolIcon className="size-4" />}
          placeholder="N/A"
          term="School"
        />
        <Property
          details={student.primarySchool?.district}
          icon={<LandmarkIcon className="size-4" />}
          placeholder="N/A"
          term="District"
        />
        <Property
          details={
            <Badge
              className="border border-blue-200 bg-blue-50 text-blue-500 text-xs dark:border-blue-800 dark:bg-blue-950 dark:text-blue-400"
              variant="outline"
            >
              {SchoolGradeEnumMap[student.grade as SchoolGradeEnum]}
            </Badge>
          }
          icon={<GraduationCapIcon className="size-4" />}
          placeholder="N/A"
          term="Grade"
        />
        <Property
          details={<StudentStatusBadge status={student.enrollmentStatus} />}
          icon={<BookMarkedIcon className="size-4" />}
          placeholder="N/A"
          term="Enrollment Status"
        />
        <Property
          details={<LanguagesList languages={student.languages} />}
          icon={<Globe2Icon className="size-4" />}
          placeholder="N/A"
          term="Languages"
        />
      </div>

      <Separator />
      <div className="flex w-full flex-col gap-2 p-4">
        <h2 className="font-semibold text-sm">Emergency Contact</h2>
        <If
          condition={student.emergencyContactName}
          fallback={
            <p className="text-muted-foreground text-sm">
              No emergency contact
            </p>
          }
        >
          <EmergencyContactCard student={student} />
        </If>
      </div>
      <Separator />
      <div className="flex w-full flex-col gap-2 p-4">
        <h2 className="font-semibold text-sm">Guardians</h2>
        <div className="flex flex-col gap-2">
          {student.parents?.map((parent) => (
            <ParentCard key={parent.id} parent={parent} />
          ))}
        </div>
      </div>
    </div>
  );
}

function EmergencyContactCard({ student }: { student: StudentProfile }) {
  return (
    <div className="flex flex-col gap-2 rounded-md border border-border p-4">
      <p className="text-muted-foreground text-sm">
        {student.emergencyContactName}
      </p>
      <p className="flex flex-row items-center gap-2 text-sm">
        <PhoneIcon className="size-3 text-muted-foreground" />
        {student.emergencyContactPhone}
      </p>
    </div>
  );
}

type ParentCardProps = {
  parent: Omit<
    Parent,
    'createdAt' | 'updatedAt' | 'isDeleted' | 'deletedAt' | 'deletedBy'
  >;
};

function ParentCard({ parent }: ParentCardProps) {
  return (
    <div className="flex flex-col gap-2 rounded-md border border-border p-4 text-sm">
      <div className="flex flex-row items-center justify-between gap-2">
        <span>{parent.fullName}</span>
        <span className="rounded-sm border border-border bg-muted px-1.5 py-0.5 text-muted-foreground text-xs">
          {ParentRelationshipEnumMap[parent.relationshipType]}
        </span>
      </div>
      <div className="flex flex-col gap-1 text-xs">
        <If condition={parent.primaryEmail}>
          <span className="flex flex-row items-center gap-2">
            <MailIcon className="size-3 text-muted-foreground" />
            {parent.primaryEmail}
          </span>
        </If>
        <If condition={parent.primaryPhone}>
          <span className="flex flex-row items-center gap-2">
            <PhoneIcon className="size-3 text-muted-foreground" />
            {parent.primaryPhone}
          </span>
        </If>
      </div>
    </div>
  );
}

function LanguagesList({
  languages,
}: {
  languages: (Language & { isPrimary: boolean })[] | null;
}) {
  const sortedLanguages = useMemo(() => {
    return languages?.sort((a, b) => {
      if (a.isPrimary) {
        return -1;
      }
      if (b.isPrimary) {
        return 1;
      }
      return 0;
    });
  }, [languages]);

  const primaryLanguage = useMemo(() => {
    return sortedLanguages?.at(0);
  }, [sortedLanguages]);

  if (!sortedLanguages) {
    return 'No languages';
  }

  return (
    <div className="flex flex-row items-center gap-1">
      <Badge className="gap-1 pr-0.5 text-xs" variant="outline">
        {primaryLanguage?.emoji} {primaryLanguage?.name}
        <span className="rounded-sm bg-blue-100 px-1.5 py-0.5 text-[10px] text-blue-500 dark:border-blue-800 dark:bg-blue-950 dark:text-blue-400">
          Primary
        </span>
      </Badge>
      <If condition={sortedLanguages.length > 1}>
        <Popover>
          <PopoverTrigger className="cursor-pointer">
            <Badge className="gap-1 text-xs" variant="outline">
              + {sortedLanguages.length - 1}
            </Badge>
          </PopoverTrigger>
          <PopoverContent className="w-36">
            <Label className="mb-1 text-muted-foreground text-xs">
              Other Languages
            </Label>
            {sortedLanguages.slice(1).map((language) => (
              <Badge
                className="gap-1 truncate text-xs"
                key={language.id}
                variant="outline"
              >
                {language.emoji} {language.name}
              </Badge>
            ))}
          </PopoverContent>
        </Popover>
      </If>
    </div>
  );
}

type PropertyProps = {
  icon: React.ReactNode;
  term: string;
  details?: React.ReactNode;
  placeholder: string;
};

function Property(props: PropertyProps): React.JSX.Element {
  return (
    <div className="flex w-full flex-row items-center justify-between text-sm">
      <dt className="flex min-w-24 flex-row items-center gap-2 text-muted-foreground">
        {props.icon}
        {props.term}
      </dt>
      <dd className="flex items-center overflow-hidden text-ellipsis">
        {props.details ? (
          props.details
        ) : (
          <p className="text-muted-foreground opacity-65">
            {props.placeholder}
          </p>
        )}
      </dd>
    </div>
  );
}
