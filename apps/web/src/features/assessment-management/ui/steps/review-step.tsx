import {
  CheckCircleIcon,
  ClockIcon,
  FileTextIcon,
  ShieldCheckIcon,
  UserIcon,
} from 'lucide-react';
import type { UseFormReturn } from 'react-hook-form';

import { Badge } from '@lilypad/ui/components/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@lilypad/ui/components/card';
import { Separator } from '@lilypad/ui/components/separator';

import { MOCK_CASES, MOCK_PSYCHOLOGISTS } from '../../model/mock-data';
import {
  type CreateAssessmentSessionFormData,
  SessionStatusEnumMap,
  SessionTypeEnumMap,
} from '../../model/schema';

interface ReviewStepProps {
  form: UseFormReturn<CreateAssessmentSessionFormData>;
}

export function ReviewStep({ form }: ReviewStepProps) {
  const formData = form.watch();

  // Get related data
  const psychologist = MOCK_PSYCHOLOGISTS.find(
    (p) => p.id === formData.psychologistId
  );
  const selectedCase = formData.caseId
    ? MOCK_CASES.find((c) => c.id === formData.caseId)
    : null;

  // Calculate total estimated time from selected tests
  const totalEstimatedTime = formData.testAdministrations.reduce(
    (total, admin) => {
      // For our simplified tests, we can add specific times based on the test
      const estimatedTimes = { 'wisc-5': 65, 'wiat-4': 45, 'basc-3': 30 };
      return (
        total +
        (estimatedTimes[admin.batteryId as keyof typeof estimatedTimes] || 0)
      );
    },
    0
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <CheckCircleIcon className="h-5 w-5 text-green-600" />
        <div>
          <h3 className="font-semibold text-lg">Review Assessment Session</h3>
          <p className="text-muted-foreground text-sm">
            Please review all information before creating the assessment session
          </p>
        </div>
      </div>

      {/* Session Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Session Overview</CardTitle>
          <CardDescription>
            Basic assessment session information
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <div className="font-medium text-muted-foreground text-sm">
                Psychologist
              </div>
              <div className="flex items-center gap-2">
                <UserIcon className="h-4 w-4" />
                <span>{psychologist?.name || 'Unknown'}</span>
              </div>
            </div>

            <div className="space-y-2">
              <div className="font-medium text-muted-foreground text-sm">
                Session Date
              </div>
              <div className="flex items-center gap-2">
                <span>
                  {formData.sessionDate
                    ? formData.sessionDate.toLocaleDateString()
                    : 'Not set'}
                </span>
              </div>
            </div>

            <div className="space-y-2">
              <div className="font-medium text-muted-foreground text-sm">
                Session Type
              </div>
              <div>
                <Badge variant="outline">
                  {formData.sessionType
                    ? SessionTypeEnumMap[formData.sessionType]
                    : 'Not selected'}
                </Badge>
              </div>
            </div>

            <div className="space-y-2">
              <div className="font-medium text-muted-foreground text-sm">
                Status
              </div>
              <div>
                <Badge variant="secondary">
                  {formData.sessionStatus
                    ? SessionStatusEnumMap[formData.sessionStatus]
                    : 'Not selected'}
                </Badge>
              </div>
            </div>

            {formData.location && (
              <div className="space-y-2">
                <div className="font-medium text-muted-foreground text-sm">
                  Location
                </div>
                <div className="flex items-center gap-2">
                  <span>{formData.location}</span>
                </div>
              </div>
            )}

            <div className="space-y-2">
              <div className="font-medium text-muted-foreground text-sm">
                Estimated Duration
              </div>
              <div className="flex items-center gap-2">
                <ClockIcon className="h-4 w-4" />
                <span>
                  {formData.sessionDuration || totalEstimatedTime} minutes
                  {formData.sessionDuration &&
                    formData.sessionDuration !== totalEstimatedTime && (
                      <span className="text-muted-foreground">
                        {' '}
                        (Auto: {totalEstimatedTime} min)
                      </span>
                    )}
                </span>
              </div>
            </div>
          </div>

          {selectedCase && (
            <>
              <Separator />
              <div className="space-y-2">
                <div className="font-medium text-muted-foreground text-sm">
                  Associated Case
                </div>
                <div className="flex items-center gap-2">
                  <FileTextIcon className="h-4 w-4" />
                  <span>{selectedCase.displayText}</span>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Selected Tests */}
      <Card>
        <CardHeader>
          <CardTitle>Selected Test Batteries</CardTitle>
          <CardDescription>
            {formData.testAdministrations.length} test
            {formData.testAdministrations.length !== 1 ? 's' : ''} selected
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {formData.testAdministrations.map((admin, index) => {
              // Get estimated time for display
              const estimatedTimes = {
                'wisc-5': 65,
                'wiat-4': 45,
                'basc-3': 30,
              };
              const estimatedTime =
                estimatedTimes[admin.batteryId as keyof typeof estimatedTimes];

              return (
                <div key={admin.batteryId} className="rounded-lg border p-4">
                  <div className="mb-2 flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">
                        {admin.administrationOrder}
                      </Badge>
                      <span className="font-semibold">{admin.batteryName}</span>
                      <Badge variant="secondary" className="text-xs">
                        {admin.batteryCode}
                      </Badge>
                    </div>

                    {estimatedTime && (
                      <div className="flex items-center text-muted-foreground text-sm">
                        <ClockIcon className="mr-1 h-3 w-3" />
                        {estimatedTime}min
                      </div>
                    )}
                  </div>

                  {admin.administrationNotes && (
                    <div className="mt-2 rounded bg-muted p-2 text-sm">
                      <div className="mb-1 font-medium text-muted-foreground">
                        Notes:
                      </div>
                      <div>{admin.administrationNotes}</div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Accommodations */}
      {formData.accommodationsProvided.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ShieldCheckIcon className="h-4 w-4" />
              Testing Accommodations
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {formData.accommodationsProvided.map((accommodation) => (
                <Badge key={accommodation} variant="secondary">
                  {accommodation}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Additional Information */}
      {(formData.referralReason ||
        formData.backgroundInformation ||
        formData.behavioralObservations ||
        formData.testingConditions ||
        formData.environmentalFactors ||
        formData.validityConcerns) && (
        <Card>
          <CardHeader>
            <CardTitle>Additional Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {formData.referralReason && (
              <div className="space-y-2">
                <div className="font-medium text-muted-foreground text-sm">
                  Referral Reason
                </div>
                <div className="rounded bg-muted p-3 text-sm">
                  {formData.referralReason}
                </div>
              </div>
            )}

            {formData.backgroundInformation && (
              <div className="space-y-2">
                <div className="font-medium text-muted-foreground text-sm">
                  Background Information
                </div>
                <div className="rounded bg-muted p-3 text-sm">
                  {formData.backgroundInformation}
                </div>
              </div>
            )}

            {formData.testingConditions && (
              <div className="space-y-2">
                <div className="font-medium text-muted-foreground text-sm">
                  Testing Conditions
                </div>
                <div className="rounded bg-muted p-3 text-sm">
                  {formData.testingConditions}
                </div>
              </div>
            )}

            {formData.environmentalFactors && (
              <div className="space-y-2">
                <div className="font-medium text-muted-foreground text-sm">
                  Environmental Factors
                </div>
                <div className="rounded bg-muted p-3 text-sm">
                  {formData.environmentalFactors}
                </div>
              </div>
            )}

            {formData.behavioralObservations && (
              <div className="space-y-2">
                <div className="font-medium text-muted-foreground text-sm">
                  Behavioral Observations
                </div>
                <div className="rounded bg-muted p-3 text-sm">
                  {formData.behavioralObservations}
                </div>
              </div>
            )}

            {formData.validityConcerns && (
              <div className="space-y-2">
                <div className="font-medium text-muted-foreground text-sm">
                  Validity Concerns
                </div>
                <div className="rounded bg-muted p-3 text-sm">
                  {formData.validityConcerns}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
