import { InfoIcon, NotepadTextIcon } from 'lucide-react';
import { useCallback } from 'react';
import type { UseFormReturn } from 'react-hook-form';

import { Badge } from '@lilypad/ui/components/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@lilypad/ui/components/card';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@lilypad/ui/components/form';
import { Textarea } from '@lilypad/ui/components/textarea';

import type { CreateAssessmentSessionFormData } from '../../model/schema';

interface TestConfigurationStepProps {
  form: UseFormReturn<CreateAssessmentSessionFormData>;
}

export function TestConfigurationStep({ form }: TestConfigurationStepProps) {
  const testAdministrations = form.watch('testAdministrations');

  const handleNotesChange = useCallback(
    (adminIndex: number, notes: string) => {
      const currentAdministrations = form.getValues('testAdministrations');
      const updatedAdministrations = [...currentAdministrations];
      updatedAdministrations[adminIndex].administrationNotes = notes;
      form.setValue('testAdministrations', updatedAdministrations, {
        shouldValidate: true,
      });
    },
    [form]
  );

  if (testAdministrations.length === 0) {
    return (
      <div className="py-8 text-center">
        <div className="text-muted-foreground">
          <InfoIcon className="mx-auto mb-4 h-12 w-12 opacity-50" />
          <h3 className="mb-2 font-semibold text-lg">No Tests Selected</h3>
          <p>
            Please go back to the previous step and select test batteries to
            configure.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="font-semibold text-lg">Configure Selected Tests</h3>
        <p className="text-muted-foreground text-sm">
          Review selected tests and add administration notes
        </p>
      </div>

      <div className="space-y-4">
        {testAdministrations.map((admin, adminIndex) => (
          <Card key={admin.batteryId}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Badge variant="outline" className="text-sm">
                    {admin.administrationOrder}
                  </Badge>
                  <div>
                    <CardTitle className="text-base">
                      {admin.batteryName}
                    </CardTitle>
                    <CardDescription className="flex items-center gap-2">
                      <Badge variant="secondary" className="text-xs">
                        {admin.batteryCode}
                      </Badge>
                      <span className="text-muted-foreground text-xs">
                        {admin.category}
                      </span>
                    </CardDescription>
                  </div>
                </div>
              </div>
            </CardHeader>

            <CardContent className="space-y-4">
              {/* Administration Notes */}
              <FormField
                control={form.control}
                name={`testAdministrations.${adminIndex}.administrationNotes`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Administration Notes</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Add any specific notes for administering this test..."
                        className="min-h-[80px] w-full"
                        {...field}
                        onChange={(e) => {
                          field.onChange(e);
                          handleNotesChange(adminIndex, e.target.value);
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>
        ))}
      </div>

      {/* <div className="bg-muted/50 p-4 rounded-lg">
				<h4 className="font-medium mb-2">Configuration Notes</h4>
				<p className="text-sm text-muted-foreground">
					In a full implementation, this step would allow you to select
					specific:
				</p>
				<ul className="text-sm text-muted-foreground mt-2 ml-4 space-y-1">
					<li>• Test indices or composite scores to calculate</li>
					<li>• Individual subtests to administer</li>
					<li>• Administration order and timing</li>
					<li>• Test-specific accommodations</li>
				</ul>
			</div> */}
    </div>
  );
}
