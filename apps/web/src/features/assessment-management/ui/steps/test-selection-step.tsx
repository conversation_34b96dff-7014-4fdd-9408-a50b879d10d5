import {
  BookOpenIcon,
  BrainIcon,
  ClockIcon,
  HeartIcon,
  PlusIcon,
} from 'lucide-react';
import { useCallback, useState } from 'react';
import type { UseFormReturn } from 'react-hook-form';

import { Badge } from '@lilypad/ui/components/badge';
import { But<PERSON> } from '@lilypad/ui/components/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@lilypad/ui/components/card';
import { Checkbox } from '@lilypad/ui/components/checkbox';
import { FormMessage } from '@lilypad/ui/components/form';
import { Label } from '@lilypad/ui/components/label';

import type { CreateAssessmentSessionFormData } from '../../model/schema';

interface TestSelectionStepProps {
  form: UseFormReturn<CreateAssessmentSessionFormData>;
}

// Simplified test batteries for selection
const SIMPLE_TEST_BATTERIES = [
  {
    id: 'wisc-5',
    name: 'WISC-<PERSON>',
    fullName: 'Wechsler Intelligence Scale for Children, Fifth Edition',
    category: 'Cognitive',
    estimatedTime: 65,
    description: 'Comprehensive cognitive assessment for children ages 6-16',
  },
  {
    id: 'wiat-4',
    name: 'WIAT-4',
    fullName: 'Wechsler Individual Achievement Test, Fourth Edition',
    category: 'Academic',
    estimatedTime: 45,
    description: 'Academic achievement assessment',
  },
  {
    id: 'basc-3',
    name: 'BASC-3',
    fullName: 'Behavior Assessment System for Children, Third Edition',
    category: 'Social-Emotional',
    estimatedTime: 30,
    description: 'Comprehensive behavioral and emotional assessment',
  },
];

export function TestSelectionStep({ form }: TestSelectionStepProps) {
  const testAdministrations = form.watch('testAdministrations');
  const selectedBatteryIds = testAdministrations.map(
    (admin) => admin.batteryId
  );

  const handleBatteryToggle = useCallback(
    (batteryId: string, checked: boolean) => {
      const currentAdministrations = form.getValues('testAdministrations');

      if (checked) {
        // Add battery
        const battery = SIMPLE_TEST_BATTERIES.find((b) => b.id === batteryId);
        if (battery) {
          const newAdmin = {
            batteryId: battery.id,
            batteryName: battery.fullName,
            batteryCode: battery.name,
            category: battery.category,
            administrationOrder: currentAdministrations.length + 1,
            selectedIndices: [],
            selectedSubtests: [],
            accommodationsUsed: [],
            administrationNotes: '',
          };

          form.setValue(
            'testAdministrations',
            [...currentAdministrations, newAdmin],
            { shouldValidate: true }
          );
        }
      } else {
        // Remove battery and reorder
        const updatedAdministrations = currentAdministrations
          .filter((admin) => admin.batteryId !== batteryId)
          .map((admin, index) => ({
            ...admin,
            administrationOrder: index + 1,
          }));

        form.setValue('testAdministrations', updatedAdministrations, {
          shouldValidate: true,
        });
      }
    },
    [form]
  );

  const totalEstimatedTime = selectedBatteryIds.reduce((total, batteryId) => {
    const battery = SIMPLE_TEST_BATTERIES.find((b) => b.id === batteryId);
    return total + (battery?.estimatedTime || 0);
  }, 0);

  return (
    <div className="space-y-6">
      {/* Header with estimated time */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="font-semibold text-lg">Select Test Batteries</h3>
          <p className="text-muted-foreground text-sm">
            Choose the psychological tests to administer in this session
          </p>
        </div>

        {selectedBatteryIds.length > 0 && (
          <div className="text-right">
            <div className="font-medium text-sm">
              {selectedBatteryIds.length} test
              {selectedBatteryIds.length !== 1 ? 's' : ''} selected
            </div>
            <div className="flex items-center text-muted-foreground text-xs">
              <ClockIcon className="mr-1 h-3 w-3" />
              Estimated time: {totalEstimatedTime} minutes
            </div>
          </div>
        )}
      </div>

      {/* Test Battery Selection */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {SIMPLE_TEST_BATTERIES.map((battery) => {
          const isSelected = selectedBatteryIds.includes(battery.id);

          return (
            <Card
              key={battery.id}
              className={`cursor-pointer transition-all hover:shadow-md ${
                isSelected ? 'border-primary ring-2 ring-primary' : ''
              }`}
              onClick={() => handleBatteryToggle(battery.id, !isSelected)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-2">
                    <Checkbox
                      checked={isSelected}
                      onCheckedChange={(checked) =>
                        handleBatteryToggle(battery.id, !!checked)
                      }
                      onClick={(e) => e.stopPropagation()}
                    />
                    <div>
                      <CardTitle className="font-semibold text-base">
                        {battery.name}
                      </CardTitle>
                      <div className="mt-1 flex items-center gap-2">
                        <Badge variant="secondary" className="text-xs">
                          {battery.category}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center text-muted-foreground text-sm">
                    <ClockIcon className="mr-1 h-4 w-4" />
                    {battery.estimatedTime}min
                  </div>
                </div>
              </CardHeader>

              <CardContent className="pt-0">
                <CardDescription className="text-sm">
                  {battery.description}
                </CardDescription>
                <div className="mt-2 text-muted-foreground text-xs">
                  {battery.fullName}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Validation Message */}
      <FormMessage>
        {form.formState.errors.testAdministrations?.message ||
          (selectedBatteryIds.length === 0
            ? 'Please select at least one test battery'
            : '')}
      </FormMessage>

      {/* Selected Tests Summary */}
      {selectedBatteryIds.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Selected Tests Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {testAdministrations.map((admin, index) => {
                const battery = SIMPLE_TEST_BATTERIES.find(
                  (b) => b.id === admin.batteryId
                );
                if (!battery) return null;

                return (
                  <div
                    key={admin.batteryId}
                    className="flex items-center justify-between border-b py-2 last:border-b-0"
                  >
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">
                        {index + 1}
                      </Badge>
                      <span className="font-medium">{battery.name}</span>
                      <Badge variant="secondary" className="text-xs">
                        {battery.category}
                      </Badge>
                    </div>

                    <div className="flex items-center text-muted-foreground text-sm">
                      <ClockIcon className="mr-1 h-3 w-3" />
                      {battery.estimatedTime}min
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
