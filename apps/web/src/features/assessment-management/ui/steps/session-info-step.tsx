import { format } from 'date-fns';
import { CalendarIcon, ClockIcon, UserIcon } from 'lucide-react';
import type { UseFormReturn } from 'react-hook-form';

import { Button } from '@lilypad/ui/components/button';
import { Calendar } from '@lilypad/ui/components/calendar';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@lilypad/ui/components/form';
import { Input } from '@lilypad/ui/components/input';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@lilypad/ui/components/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@lilypad/ui/components/select';
import { Textarea } from '@lilypad/ui/components/textarea';
import { cn } from '@lilypad/ui/lib/utils';

import { MOCK_CASES, MOCK_PSYCHOLOGISTS } from '../../model/mock-data';
import {
  COMMON_LOCATIONS,
  type CreateAssessmentSessionFormData,
  SessionStatusEnumMap,
  SessionTypeEnumMap,
} from '../../model/schema';

interface SessionInfoStepProps {
  form: UseFormReturn<CreateAssessmentSessionFormData>;
}

export function SessionInfoStep({ form }: SessionInfoStepProps) {
  return (
    <div className="space-y-6">
      <div className="grid gap-6 md:grid-cols-2">
        {/* Case Selection */}
        <FormField
          control={form.control}
          name="caseId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Case (Optional)</FormLabel>
              <Select onValueChange={field.onChange} value={field.value}>
                <FormControl>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select a case" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {MOCK_CASES.map((caseItem) => (
                    <SelectItem key={caseItem.id} value={caseItem.id}>
                      {caseItem.displayText}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Psychologist Selection */}
        <FormField
          control={form.control}
          name="psychologistId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                <UserIcon className="mr-1 inline h-4 w-4" />
                Psychologist *
              </FormLabel>
              <Select onValueChange={field.onChange} value={field.value}>
                <FormControl>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select psychologist" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {MOCK_PSYCHOLOGISTS.map((psychologist) => (
                    <SelectItem key={psychologist.id} value={psychologist.id}>
                      {psychologist.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        {/* Session Date */}
        <FormField
          control={form.control}
          name="sessionDate"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>
                <CalendarIcon className="mr-1 inline h-4 w-4" />
                Session Date *
              </FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant="outline"
                      className={cn(
                        'w-full pl-3 text-left font-normal',
                        !field.value && 'text-muted-foreground'
                      )}
                    >
                      {field.value ? (
                        format(field.value, 'PPP')
                      ) : (
                        <span>Pick a date</span>
                      )}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={field.value}
                    onSelect={field.onChange}
                    disabled={(date) =>
                      date < new Date() || date < new Date('1900-01-01')
                    }
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Session Duration */}
        <FormField
          control={form.control}
          name="sessionDuration"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                <ClockIcon className="mr-1 inline h-4 w-4" />
                Duration (minutes)
              </FormLabel>
              <FormControl>
                <Input
                  type="number"
                  placeholder="e.g., 90"
                  className="w-full"
                  {...field}
                  onChange={(e) =>
                    field.onChange(Number(e.target.value) || undefined)
                  }
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Session Type */}
        <FormField
          control={form.control}
          name="sessionType"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Session Type *</FormLabel>
              <Select onValueChange={field.onChange} value={field.value}>
                <FormControl>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {Object.entries(SessionTypeEnumMap).map(([key, label]) => (
                    <SelectItem key={key} value={key}>
                      {label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Location */}
        <FormField
          control={form.control}
          name="location"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Location</FormLabel>
              <Select onValueChange={field.onChange} value={field.value}>
                <FormControl>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select location" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {COMMON_LOCATIONS.map((location) => (
                    <SelectItem key={location} value={location}>
                      {location}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Session Status */}
        <FormField
          control={form.control}
          name="sessionStatus"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Status *</FormLabel>
              <Select onValueChange={field.onChange} value={field.value}>
                <FormControl>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {Object.entries(SessionStatusEnumMap).map(([key, label]) => (
                    <SelectItem key={key} value={key}>
                      {label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* Referral Reason */}
      <FormField
        control={form.control}
        name="referralReason"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Referral Reason</FormLabel>
            <FormDescription>
              Provide context for why this assessment is being conducted
            </FormDescription>
            <FormControl>
              <Textarea
                placeholder="Describe the reason for this assessment..."
                className="min-h-[100px] w-full"
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}
