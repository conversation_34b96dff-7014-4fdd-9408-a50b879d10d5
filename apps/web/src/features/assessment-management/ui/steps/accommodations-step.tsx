import { <PERSON><PERSON><PERSON>, ClockI<PERSON>, ShieldCheckIcon, XIcon } from 'lucide-react';
import { useCallback } from 'react';
import type { UseFormReturn } from 'react-hook-form';

import { Badge } from '@lilypad/ui/components/badge';
import { Button } from '@lilypad/ui/components/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@lilypad/ui/components/card';
import { Checkbox } from '@lilypad/ui/components/checkbox';
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@lilypad/ui/components/form';
import { Input } from '@lilypad/ui/components/input';
import { Label } from '@lilypad/ui/components/label';
import { Textarea } from '@lilypad/ui/components/textarea';

import {
  COMMON_ACCOMMODATIONS,
  type CreateAssessmentSessionFormData,
} from '../../model/schema';

interface AccommodationsStepProps {
  form: UseFormReturn<CreateAssessmentSessionFormData>;
}

export function AccommodationsStep({ form }: AccommodationsStepProps) {
  const accommodationsProvided = form.watch('accommodationsProvided');

  const handleAccommodationToggle = useCallback(
    (accommodation: string, checked: boolean) => {
      const currentAccommodations = form.getValues('accommodationsProvided');

      if (checked) {
        // Add accommodation
        form.setValue(
          'accommodationsProvided',
          [...currentAccommodations, accommodation],
          { shouldValidate: true }
        );
      } else {
        // Remove accommodation
        form.setValue(
          'accommodationsProvided',
          currentAccommodations.filter((acc) => acc !== accommodation),
          { shouldValidate: true }
        );
      }
    },
    [form]
  );

  return (
    <div className="space-y-6">
      <div>
        <h3 className="flex items-center gap-2 font-semibold text-lg">
          <ShieldCheckIcon className="h-5 w-5" />
          Accommodations & Session Details
        </h3>
        <p className="text-muted-foreground text-sm">
          Select testing accommodations and provide additional session
          information
        </p>
      </div>

      {/* Accommodations Selection */}
      <div className="space-y-4">
        <div>
          <Label className="font-semibold text-base">
            Testing Accommodations
          </Label>
          <p className="mb-3 text-muted-foreground text-sm">
            Select all accommodations that will be provided during testing
          </p>

          <div className="grid gap-3 md:grid-cols-2">
            {COMMON_ACCOMMODATIONS.map((accommodation) => {
              const isSelected = accommodationsProvided.includes(accommodation);

              return (
                <div
                  key={accommodation}
                  className="flex items-center space-x-3"
                >
                  <Checkbox
                    id={`accommodation-${accommodation}`}
                    checked={isSelected}
                    onCheckedChange={(checked) =>
                      handleAccommodationToggle(
                        accommodation,
                        checked as boolean
                      )
                    }
                  />
                  <Label
                    htmlFor={`accommodation-${accommodation}`}
                    className="cursor-pointer font-normal text-sm"
                  >
                    {accommodation}
                  </Label>
                </div>
              );
            })}
          </div>

          {accommodationsProvided.length > 0 && (
            <p className="mt-2 text-muted-foreground text-xs">
              {accommodationsProvided.length} accommodation
              {accommodationsProvided.length !== 1 ? 's' : ''} selected
            </p>
          )}
        </div>
      </div>

      {/* Background Information */}
      <FormField
        control={form.control}
        name="backgroundInformation"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Background Information</FormLabel>
            <FormDescription>
              Include relevant history, previous assessments, or other
              contextual information
            </FormDescription>
            <FormControl>
              <Textarea
                placeholder="Provide relevant background information about the student..."
                className="min-h-[100px] w-full"
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Testing Conditions */}
      <FormField
        control={form.control}
        name="testingConditions"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Testing Conditions</FormLabel>
            <FormDescription>
              Note the physical environment, lighting, noise level, etc.
            </FormDescription>
            <FormControl>
              <Textarea
                placeholder="Describe the testing environment and conditions..."
                className="min-h-[80px] w-full"
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Environmental Factors */}
      <FormField
        control={form.control}
        name="environmentalFactors"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Environmental Factors</FormLabel>
            <FormDescription>
              Include distractions, interruptions, or other environmental
              considerations
            </FormDescription>
            <FormControl>
              <Textarea
                placeholder="Note any environmental factors that may impact testing..."
                className="min-h-[80px] w-full"
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Behavioral Observations */}
      <FormField
        control={form.control}
        name="behavioralObservations"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Behavioral Observations</FormLabel>
            <FormDescription>
              Note student's demeanor, cooperation, attention, effort level,
              etc.
            </FormDescription>
            <FormControl>
              <Textarea
                placeholder="Record behavioral observations during testing..."
                className="min-h-[80px] w-full"
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Validity Concerns */}
      <FormField
        control={form.control}
        name="validityConcerns"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Validity Concerns</FormLabel>
            <FormDescription>
              Document any factors that may have impacted the validity of test
              results
            </FormDescription>
            <FormControl>
              <Textarea
                placeholder="Note any concerns about test validity..."
                className="min-h-[80px] w-full"
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}
