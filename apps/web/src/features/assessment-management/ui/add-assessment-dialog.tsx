'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@lilypad/ui/components/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@lilypad/ui/components/dialog';
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from '@lilypad/ui/components/drawer';
import { Form } from '@lilypad/ui/components/form';
import { toast } from '@lilypad/ui/components/sonner';
import { defineStepper, useButtonLabel } from '@lilypad/ui/components/stepper';
import { useMediaQuery } from '@lilypad/ui/hooks/use-media-query';
import { MediaQueries } from '@lilypad/ui/lib/media-queries';
import { cn } from '@lilypad/ui/lib/utils';
import {
  ArrowRightIcon,
  CalendarClockIcon,
  CheckCircleIcon,
  ClipboardListIcon,
  FileTextIcon,
  PlusIcon,
  ShieldCheckIcon,
} from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';

import { SessionStatusEnum } from '@lilypad/db/schema/enums';
import { MOCK_PSYCHOLOGISTS } from '../model/mock-data';
import {
  AssessmentStepId,
  type CreateAssessmentSessionFormData,
  createAssessmentSessionSchema,
} from '../model/schema';

import { AccommodationsStep } from './steps/accommodations-step';
import { ReviewStep } from './steps/review-step';
import { SessionInfoStep } from './steps/session-info-step';
import { TestConfigurationStep } from './steps/test-configuration-step';
import { TestSelectionStep } from './steps/test-selection-step';

interface AddAssessmentDialogProps {
  studentId: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

type Step = {
  id: AssessmentStepId;
  title: string;
  description: string;
  icon: React.ReactNode;
};

const ASSESSMENT_STEPS: Step[] = [
  {
    id: AssessmentStepId.SESSION_INFO,
    title: 'Session Info',
    description: 'Basic session information',
    icon: <CalendarClockIcon className="size-4" />,
  },
  {
    id: AssessmentStepId.TEST_SELECTION,
    title: 'Test Selection',
    description: 'Choose test batteries',
    icon: <ClipboardListIcon className="size-4" />,
  },
  {
    id: AssessmentStepId.TEST_CONFIGURATION,
    title: 'Test Configuration',
    description: 'Configure selected tests',
    icon: <FileTextIcon className="size-4" />,
  },
  {
    id: AssessmentStepId.ACCOMMODATIONS,
    title: 'Accommodations',
    description: 'Select accommodations',
    icon: <ShieldCheckIcon className="size-4" />,
  },
  {
    id: AssessmentStepId.REVIEW,
    title: 'Review',
    description: 'Review and submit',
    icon: <CheckCircleIcon className="size-4" />,
  },
];

const { Stepper, useStepper } = defineStepper(...ASSESSMENT_STEPS);

function AssessmentDialogContent({
  studentId,
  onClose,
}: {
  studentId: string;
  onClose: () => void;
}) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const stepperMethods = useStepper();
  const mdUp = useMediaQuery(MediaQueries.MdUp, { ssr: false });

  const form = useForm<CreateAssessmentSessionFormData>({
    resolver: zodResolver(createAssessmentSessionSchema),
    defaultValues: {
      studentId,
      caseId: '',
      psychologistId: MOCK_PSYCHOLOGISTS[0]?.id || '',
      sessionDate: new Date(),
      sessionDuration: 0,
      sessionType: '',
      location: '',
      sessionStatus: SessionStatusEnum.SCHEDULED,
      testAdministrations: [],
      referralReason: '',
      backgroundInformation: '',
      behavioralObservations: '',
      testingConditions: '',
      environmentalFactors: '',
      validityConcerns: '',
      accommodationsProvided: [],
    },
  });

  const validateCurrentStep = async (): Promise<boolean> => {
    const currentStepId = stepperMethods.current.id;

    switch (currentStepId) {
      case AssessmentStepId.SESSION_INFO:
        return await form.trigger([
          'psychologistId',
          'sessionDate',
          'sessionType',
          'sessionStatus',
        ]);
      case AssessmentStepId.TEST_SELECTION:
        return await form.trigger(['testAdministrations']);
      case AssessmentStepId.TEST_CONFIGURATION:
        return true;
      case AssessmentStepId.ACCOMMODATIONS:
        return true;
      case AssessmentStepId.REVIEW:
        return await form.trigger();
      default:
        return true;
    }
  };

  const isCurrentStepComplete = async () => {
    return await validateCurrentStep();
  };

  const handleNext = async () => {
    const isValid = await validateCurrentStep();
    if (!isValid) return;
    stepperMethods.next();
  };

  const handleSubmit = async (data: CreateAssessmentSessionFormData) => {
    setIsSubmitting(true);
    try {
      console.log('Assessment session data:', data);
      // TODO: Implement actual submission logic
      toast.success('Assessment session created successfully!');
      onClose();
    } catch (error) {
      console.error('Failed to create assessment session:', error);
      toast.error('Failed to create assessment session. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCreateAssessment = async () => {
    const isValid = await form.trigger();
    if (!isValid) {
      toast.error(
        'Please complete all required fields before creating the assessment.'
      );
      return;
    }

    const formData = form.getValues();
    await handleSubmit(formData);
  };

  const buttonLabel = useButtonLabel(stepperMethods, 'Create Assessment');

  const renderCurrentStep = () => {
    switch (stepperMethods.current.id) {
      case AssessmentStepId.SESSION_INFO:
        return <SessionInfoStep form={form} />;
      case AssessmentStepId.TEST_SELECTION:
        return <TestSelectionStep form={form} />;
      case AssessmentStepId.TEST_CONFIGURATION:
        return <TestConfigurationStep form={form} />;
      case AssessmentStepId.ACCOMMODATIONS:
        return <AccommodationsStep form={form} />;
      case AssessmentStepId.REVIEW:
        return <ReviewStep form={form} />;
      default:
        return null;
    }
  };

  const renderForm = (
    <div className="flex min-h-0 flex-1 gap-6 border-b px-6">
      {mdUp && (
        <div className="w-1/4 flex-shrink-0 border-r">
          <Stepper.Navigation className="py-6">
            {stepperMethods.all.map((step) => (
              <Stepper.Step key={step.id} of={step.id} icon={step.icon}>
                <Stepper.Title className="font-medium text-sm">
                  {step.title}
                </Stepper.Title>
                <Stepper.Description className="text-muted-foreground text-xs">
                  {step.description}
                </Stepper.Description>
              </Stepper.Step>
            ))}
          </Stepper.Navigation>
        </div>
      )}

      <div className="min-h-0 flex-1 overflow-hidden py-4">
        <Stepper.Panel className="h-full overflow-y-auto">
          <Form {...form}>{renderCurrentStep()}</Form>
        </Stepper.Panel>
      </div>
    </div>
  );

  const renderButtons = (
    <div className="flex-shrink-0 rounded-b-xl bg-secondary p-4">
      <Stepper.Controls className="flex justify-between">
        <Button variant="cancel" size="sm" onClick={onClose}>
          Cancel
        </Button>
        <div className="flex gap-2">
          {!stepperMethods.isFirst && (
            <Button variant="outline" size="sm" onClick={stepperMethods.prev}>
              Previous
            </Button>
          )}
          <Button
            size="sm"
            onClick={
              stepperMethods.isLast ? handleCreateAssessment : handleNext
            }
            disabled={stepperMethods.isLast && isSubmitting}
            loading={stepperMethods.isLast && isSubmitting}
          >
            {buttonLabel}
            {!stepperMethods.isLast && <ArrowRightIcon className="size-4" />}
          </Button>
        </div>
      </Stepper.Controls>
    </div>
  );

  return (
    <>
      {mdUp ? (
        <DialogContent
          className="flex h-[90vh] max-h-[90vh] min-w-5xl flex-col gap-0 p-0"
          onInteractOutside={(e) => e.preventDefault()}
          onEscapeKeyDown={(e) => e.preventDefault()}
        >
          <DialogDescription className="sr-only">
            Schedule Assessment Session
          </DialogDescription>
          <DialogHeader className="flex-shrink-0 border-b px-6 py-5">
            <DialogTitle>Schedule Assessment Session</DialogTitle>
            <DialogDescription className="text-muted-foreground text-sm">
              Configure and schedule a new psychological assessment session.
            </DialogDescription>
          </DialogHeader>
          {renderForm}
          {renderButtons}
        </DialogContent>
      ) : (
        <DrawerContent className="flex max-h-[90vh] flex-col">
          <DrawerHeader className="flex-shrink-0 border-b text-left">
            <DrawerTitle>Schedule Assessment Session</DrawerTitle>
            <DrawerDescription className="text-muted-foreground text-sm">
              Configure and schedule a new psychological assessment session.
            </DrawerDescription>
          </DrawerHeader>
          <div className={cn('min-h-0 flex-1 overflow-y-auto', 'p-4')}>
            <Form {...form}>{renderCurrentStep()}</Form>
          </div>
          {renderButtons}
        </DrawerContent>
      )}
    </>
  );
}

export function AddAssessmentDialog({
  studentId,
  open,
  onOpenChange,
}: AddAssessmentDialogProps) {
  const [resetKey, setResetKey] = useState(0);

  const handleClose = () => {
    onOpenChange(false);
  };

  const handleReset = () => {
    setResetKey((prev) => prev + 1);
  };

  const handleOpenChange = (isOpen: boolean) => {
    if (!isOpen) {
      handleReset();
    }
    onOpenChange(isOpen);
  };

  const isDesktop = useMediaQuery(MediaQueries.MdUp, { ssr: false });

  return (
    <>
      {isDesktop ? (
        <Dialog open={open} onOpenChange={handleOpenChange}>
          <Stepper.Provider variant="vertical">
            <AssessmentDialogContent
              key={resetKey}
              studentId={studentId}
              onClose={handleClose}
            />
          </Stepper.Provider>
        </Dialog>
      ) : (
        <Drawer open={open} onOpenChange={handleOpenChange}>
          <Stepper.Provider variant="vertical">
            <AssessmentDialogContent
              key={resetKey}
              studentId={studentId}
              onClose={handleClose}
            />
          </Stepper.Provider>
        </Drawer>
      )}
    </>
  );
}
