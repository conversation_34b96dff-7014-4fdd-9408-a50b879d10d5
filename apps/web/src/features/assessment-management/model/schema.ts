import {
  SessionStatusEnum,
  SessionStatusEnumMap,
  SessionTypeEnum,
  SessionTypeEnumMap,
} from '@lilypad/db/schema/enums';
import { z } from 'zod';

// Re-export database types
export type {
  TestBattery,
  TestIndex,
  Subtest,
  AssessmentSession,
  TestAdministration,
} from '@lilypad/db/schema/types';

// Assessment Dialog Steps
export enum AssessmentStepId {
  SESSION_INFO = 'SESSION_INFO',
  TEST_SELECTION = 'TEST_SELECTION',
  TEST_CONFIGURATION = 'TEST_CONFIGURATION',
  ACCOMMODATIONS = 'ACCOMMODATIONS',
  REVIEW = 'REVIEW',
}

// Test Administration Configuration Schema for the form
export const testAdministrationConfigSchema = z.object({
  batteryId: z.string().min(1, 'Battery ID is required'),
  batteryName: z.string().min(1, 'Battery name is required'),
  batteryCode: z.string().min(1, 'Battery code is required'),
  category: z.string().min(1, 'Category is required'),
  administrationOrder: z.number().int().positive('Order must be positive'),
  selectedIndices: z.array(
    z.object({
      indexId: z.string().uuid('Invalid index ID'),
      indexName: z.string().min(1, 'Index name is required'),
      indexCode: z.string().optional(),
      isRequired: z.boolean(),
    })
  ),
  selectedSubtests: z.array(
    z.object({
      subtestId: z.string().uuid('Invalid subtest ID'),
      subtestName: z.string().min(1, 'Subtest name is required'),
      subtestCode: z.string().min(1, 'Subtest code is required'),
      indexId: z.string().uuid().optional(),
      isRequired: z.boolean(),
    })
  ),
  accommodationsUsed: z.array(z.string()),
  administrationNotes: z.string().optional(),
});

// Assessment Session Form Schema - Allow empty strings for optional enum fields
export const createAssessmentSessionSchema = z.object({
  studentId: z.string().uuid('Invalid student ID'),
  caseId: z.string().optional().or(z.literal('')),
  psychologistId: z.string().uuid('Invalid psychologist ID'),
  sessionDate: z.date({
    required_error: 'Session date is required',
    invalid_type_error: 'Invalid session date',
  }),
  sessionDuration: z
    .number()
    .int()
    .positive('Duration must be positive')
    .max(480, 'Duration cannot exceed 8 hours')
    .optional()
    .or(z.literal(0)),
  sessionType: z.nativeEnum(SessionTypeEnum).optional().or(z.literal('')),
  location: z.string().optional(),
  sessionStatus: z.nativeEnum(SessionStatusEnum, {
    required_error: 'Session status is required',
    invalid_type_error: 'Invalid session status',
  }),
  testAdministrations: z
    .array(testAdministrationConfigSchema)
    .min(1, 'Please select at least one test battery'),
  referralReason: z.string().optional(),
  backgroundInformation: z.string().optional(),
  behavioralObservations: z.string().optional(),
  testingConditions: z.string().optional(),
  environmentalFactors: z.string().optional(),
  validityConcerns: z.string().optional(),
  accommodationsProvided: z.array(z.string()),
});

export type CreateAssessmentSessionFormData = z.infer<
  typeof createAssessmentSessionSchema
>;

export type TestAdministrationConfig = z.infer<
  typeof testAdministrationConfigSchema
>;

// Export enum maps for use in the form
export { SessionTypeEnumMap, SessionStatusEnumMap };

// Common accommodation options
export const COMMON_ACCOMMODATIONS = [
  'Extended time',
  'Frequent breaks',
  'Small group setting',
  'Individual administration',
  'Large print materials',
  'Audio recording permitted',
  'Reduced distractions',
  'Preferential seating',
  'Alternative response format',
  'Read aloud instructions',
] as const;

// Common locations
export const COMMON_LOCATIONS = [
  'School Psychology Office',
  'Classroom',
  'Conference Room',
  'Resource Room',
  'Library',
  'Testing Center',
  'Clinical Office',
  'Home Setting',
] as const;
