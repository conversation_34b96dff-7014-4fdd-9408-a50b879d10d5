import {
  IndexTypeEnum,
  SubtestTypeEnum,
  TestCategoryEnum,
} from '@lilypad/db/schema/enums';
import type { Subtest, TestBattery, TestIndex } from '@lilypad/db/schema/types';

// Mock Test Batteries
export const MOCK_TEST_BATTERIES: TestBattery[] = [
  // Cognitive Batteries
  {
    id: '550e8400-e29b-41d4-a716-************',
    category: TestCategoryEnum.COGNITIVE_ASSESSMENT,
    name: 'Wechsler Intelligence Scale for Children, Fifth Edition',
    code: 'WISC-V',
    version: '5.0',
    publisher: 'Pearson',
    ageRangeMin: 72, // 6 years in months
    ageRangeMax: 192, // 16 years in months
    administrationTime: 65,
    description: 'Comprehensive cognitive assessment for children ages 6-16',
    normingInformation: {
      sampleSize: 2200,
      normingYear: 2014,
      demographicRepresentation: 'US Census matched',
    },
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440001',
    category: TestCategoryEnum.COGNITIVE_ASSESSMENT,
    name: 'Wechsler Adult Intelligence Scale, Fourth Edition',
    code: 'WAIS-IV',
    version: '4.0',
    publisher: 'Pearson',
    ageRangeMin: 192, // 16 years in months
    ageRangeMax: 1080, // 90 years in months
    administrationTime: 90,
    description: 'Comprehensive cognitive assessment for adults ages 16-90',
    normingInformation: null,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440002',
    category: TestCategoryEnum.COGNITIVE_ASSESSMENT,
    name: 'Reynolds Intellectual Assessment Scales, Second Edition',
    code: 'RIAS-2',
    version: '2.0',
    publisher: 'PAR',
    ageRangeMin: 36, // 3 years in months
    ageRangeMax: 948, // 79 years in months
    administrationTime: 45,
    description: 'Brief intelligence assessment with memory subtests',
    normingInformation: null,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  // Academic Batteries
  {
    id: '550e8400-e29b-41d4-a716-446655440003',
    category: TestCategoryEnum.ACADEMIC_ACHIEVEMENT,
    name: 'Wechsler Individual Achievement Test, Fourth Edition',
    code: 'WIAT-IV',
    version: '4.0',
    publisher: 'Pearson',
    ageRangeMin: 48, // 4 years in months
    ageRangeMax: 600, // 50 years in months
    administrationTime: 90,
    description: 'Comprehensive academic achievement assessment',
    normingInformation: null,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440004',
    category: TestCategoryEnum.ACADEMIC_ACHIEVEMENT,
    name: 'Kaufman Test of Educational Achievement, Third Edition',
    code: 'KTEA-3',
    version: '3.0',
    publisher: 'Pearson',
    ageRangeMin: 48, // 4 years in months
    ageRangeMax: 300, // 25 years in months
    administrationTime: 85,
    description: 'Comprehensive academic skills assessment',
    normingInformation: null,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  // Social/Emotional Batteries
  {
    id: '550e8400-e29b-41d4-a716-446655440005',
    category: TestCategoryEnum.SOCIAL_EMOTIONAL_ASSESSMENT,
    name: 'Behavior Assessment System for Children, Third Edition',
    code: 'BASC-3',
    version: '3.0',
    publisher: 'Pearson',
    ageRangeMin: 24, // 2 years in months
    ageRangeMax: 252, // 21 years in months
    administrationTime: 30,
    description: 'Comprehensive behavioral and emotional assessment',
    normingInformation: null,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440006',
    category: TestCategoryEnum.SOCIAL_EMOTIONAL_ASSESSMENT,
    name: 'Behavior Rating Inventory of Executive Function, Second Edition',
    code: 'BRIEF-2',
    version: '2.0',
    publisher: 'PAR',
    ageRangeMin: 60, // 5 years in months
    ageRangeMax: 216, // 18 years in months
    administrationTime: 15,
    description: 'Assessment of executive function behaviors',
    normingInformation: null,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

// Mock Test Indices for WISC-V
export const MOCK_WISC_V_INDICES: TestIndex[] = [
  {
    id: '550e8400-e29b-41d4-a716-************',
    batteryId: '550e8400-e29b-41d4-a716-************',
    name: 'Verbal Comprehension Index',
    code: 'VCI',
    indexType: IndexTypeEnum.PRIMARY,
    description: 'Measures verbal reasoning and conceptual understanding',
    scoreRangeMin: 45,
    scoreRangeMax: 155,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines:
      'Assesses crystallized intelligence and verbal reasoning abilities',
    sortOrder: 1,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: '550e8400-e29b-41d4-a716-************',
    batteryId: '550e8400-e29b-41d4-a716-************',
    name: 'Visual Spatial Index',
    code: 'VSI',
    indexType: IndexTypeEnum.PRIMARY,
    description:
      'Measures visual-spatial processing and construction abilities',
    scoreRangeMin: 45,
    scoreRangeMax: 155,
    meanScore: 100,
    standardDeviation: 15,
    sortOrder: 2,
    interpretiveGuidelines: null,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: '550e8400-e29b-41d4-a716-************',
    batteryId: '550e8400-e29b-41d4-a716-************',
    name: 'Fluid Reasoning Index',
    code: 'FRI',
    indexType: IndexTypeEnum.PRIMARY,
    description: 'Measures fluid reasoning and novel problem solving',
    scoreRangeMin: 45,
    scoreRangeMax: 155,
    meanScore: 100,
    standardDeviation: 15,
    sortOrder: 3,
    interpretiveGuidelines: null,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: '550e8400-e29b-41d4-a716-************',
    batteryId: '550e8400-e29b-41d4-a716-************',
    name: 'Working Memory Index',
    code: 'WMI',
    indexType: IndexTypeEnum.PRIMARY,
    description: 'Measures working memory and attention',
    scoreRangeMin: 45,
    scoreRangeMax: 155,
    meanScore: 100,
    standardDeviation: 15,
    sortOrder: 4,
    interpretiveGuidelines: null,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: '550e8400-e29b-41d4-a716-************',
    batteryId: '550e8400-e29b-41d4-a716-************',
    name: 'Processing Speed Index',
    code: 'PSI',
    indexType: IndexTypeEnum.PRIMARY,
    description: 'Measures processing speed and visual scanning',
    scoreRangeMin: 45,
    scoreRangeMax: 155,
    meanScore: 100,
    standardDeviation: 15,
    sortOrder: 5,
    interpretiveGuidelines: null,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: '550e8400-e29b-41d4-a716-************',
    batteryId: '550e8400-e29b-41d4-a716-************',
    name: 'Full Scale IQ',
    code: 'FSIQ',
    indexType: IndexTypeEnum.COMPOSITE,
    description: 'Overall cognitive ability composite score',
    scoreRangeMin: 45,
    scoreRangeMax: 155,
    meanScore: 100,
    standardDeviation: 15,
    sortOrder: 6,
    interpretiveGuidelines: null,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

// Mock Subtests for WISC-V
export const MOCK_WISC_V_SUBTESTS: Subtest[] = [
  {
    id: '550e8400-e29b-41d4-a716-************',
    batteryId: '550e8400-e29b-41d4-a716-************',
    name: 'Block Design',
    code: 'BD',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Visual-spatial construction and analysis',
    measuredAbilities: [
      'visual-spatial processing',
      'construction',
      'analysis',
    ],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions:
      'Present design cards and blocks to reproduce patterns',
    sortOrder: 1,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: '550e8400-e29b-41d4-a716-************',
    batteryId: '550e8400-e29b-41d4-a716-************',
    name: 'Similarities',
    code: 'SI',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Verbal reasoning and conceptual thinking',
    measuredAbilities: [
      'verbal reasoning',
      'abstract thinking',
      'categorization',
    ],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: null,
    sortOrder: 2,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: '550e8400-e29b-41d4-a716-************',
    batteryId: '550e8400-e29b-41d4-a716-************',
    name: 'Matrix Reasoning',
    code: 'MR',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Fluid reasoning and pattern recognition',
    measuredAbilities: [
      'fluid reasoning',
      'pattern recognition',
      'visual processing',
    ],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: null,
    sortOrder: 3,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: '550e8400-e29b-41d4-a716-************',
    batteryId: '550e8400-e29b-41d4-a716-************',
    name: 'Digit Span',
    code: 'DS',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Working memory and attention',
    measuredAbilities: ['working memory', 'attention', 'sequencing'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: null,
    sortOrder: 4,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: '550e8400-e29b-41d4-a716-************',
    batteryId: '550e8400-e29b-41d4-a716-************',
    name: 'Coding',
    code: 'CD',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Processing speed and visual scanning',
    measuredAbilities: ['processing speed', 'visual scanning', 'fine motor'],
    timeLimitMinutes: 2,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: null,
    sortOrder: 5,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

// Mock Cases Data
export const MOCK_CASES = [
  {
    id: '550e8400-e29b-41d4-a716-446655440018',
    displayText: 'Case #2024-001: Academic Assessment',
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440019',
    displayText: 'Case #2024-002: Behavioral Concerns',
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440020',
    displayText: 'Case #2024-003: Cognitive Evaluation',
  },
];

export const MOCK_PSYCHOLOGISTS = [
  {
    id: '550e8400-e29b-41d4-a716-446655440021',
    name: 'Dr. Sarah Johnson',
    email: '<EMAIL>',
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440022',
    name: 'Dr. Michael Chen',
    email: '<EMAIL>',
  },
];

// Helper Functions
export function getMockBatteriesByCategory(
  category: TestCategoryEnum
): typeof MOCK_TEST_BATTERIES {
  return MOCK_TEST_BATTERIES.filter((battery) => battery.category === category);
}

export function getMockIndicesForBattery(
  batteryId: string
): typeof MOCK_WISC_V_INDICES {
  return MOCK_WISC_V_INDICES.filter((index) => index.batteryId === batteryId);
}

export function getMockSubtestsForBattery(
  batteryId: string
): typeof MOCK_WISC_V_SUBTESTS {
  return MOCK_WISC_V_SUBTESTS.filter(
    (subtest) => subtest.batteryId === batteryId
  );
}

export function calculateEstimatedTime(batteryIds: string[]): number {
  return batteryIds.reduce((total, batteryId) => {
    const battery = MOCK_TEST_BATTERIES.find((b) => b.id === batteryId);
    return total + (battery?.administrationTime || 0);
  }, 0);
}
