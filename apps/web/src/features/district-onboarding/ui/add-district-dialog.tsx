'use client';

import { useUser } from '@/shared/contexts/user-context';
import { useTRPC } from '@lilypad/api/client';
import { AddressTypeEnum } from '@lilypad/db/enums';
import { Button } from '@lilypad/ui/components/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@lilypad/ui/components/dialog';
import { toast } from '@lilypad/ui/components/sonner';
import { defineStepper, useButtonLabel } from '@lilypad/ui/components/stepper';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  ArrowRightIcon,
  CheckIcon,
  LandmarkIcon,
  PlusIcon,
  SchoolIcon,
  UsersIcon,
} from 'lucide-react';
import { useState } from 'react';
import { useDistrictConfigurationStore } from '../model';
import { StepId } from '../model/types';
import { DistrictForm } from './district-form';
import { MembersForm } from './members-form';
import { ReviewForm } from './review-form';
import { SchoolsForm } from './school-form';

type Step = {
  id: StepId;
  title: string;
  description: string;
  icon: React.ReactNode;
};

const STEPS: Step[] = [
  {
    id: StepId.DISTRICT,
    title: 'District',
    description: 'Add district details',
    icon: <LandmarkIcon className="size-4" />,
  },
  {
    id: StepId.SCHOOLS,
    title: 'Schools',
    description: 'Add schools to the district',
    icon: <SchoolIcon className="size-4" />,
  },
  {
    id: StepId.MEMBERS,
    title: 'Members',
    description: 'Add district members',
    icon: <UsersIcon className="size-4" />,
  },
  {
    id: StepId.REVIEW,
    title: 'Review',
    description: 'Review the district information',
    icon: <CheckIcon className="size-4" />,
  },
];

const { Stepper, useStepper } = defineStepper(...STEPS);

interface DistrictDialogContentProps {
  onClose: () => void;
}

function DistrictDialogContent({ onClose }: DistrictDialogContentProps) {
  const { user } = useUser();
  const stepperMethods = useStepper();
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  const { mutateAsync: onboardDistrict, isPending } = useMutation(
    trpc.districts.createOnboarding.mutationOptions({
      onSuccess: async () => {
        toast.success('District created successfully!');
        await queryClient.invalidateQueries({
          queryKey: [['districts', 'getDistricts']],
          exact: false,
        });
        onClose();
      },
      onError: (error) => {
        console.error('Error creating district:', error);
        toast.error(
          'An unexpected error occurred while creating the district.'
        );
      },
    })
  );

  const district = useDistrictConfigurationStore((state) => state.district);
  const schools = useDistrictConfigurationStore((state) => state.schools);
  const members = useDistrictConfigurationStore((state) => state.members);
  const districtComplete = useDistrictConfigurationStore((state) =>
    state.isDistrictComplete()
  );
  const schoolsComplete = useDistrictConfigurationStore((state) =>
    state.isSchoolsComplete()
  );
  const membersComplete = useDistrictConfigurationStore((state) =>
    state.isMembersComplete()
  );

  const canNavigateToStep = (stepId: StepId) => {
    switch (stepId) {
      case StepId.DISTRICT:
        return true;
      case StepId.SCHOOLS:
        return districtComplete;
      case StepId.MEMBERS:
        return districtComplete && schoolsComplete;
      case StepId.REVIEW:
        return districtComplete && schoolsComplete && membersComplete;
      default:
        return false;
    }
  };

  const isCurrentStepComplete = () => {
    const currentStepId = stepperMethods.current.id;
    switch (currentStepId) {
      case StepId.DISTRICT:
        return districtComplete;
      case StepId.SCHOOLS:
        return schoolsComplete;
      case StepId.MEMBERS:
        return membersComplete;
      case StepId.REVIEW:
        return districtComplete && schoolsComplete && membersComplete;
      default:
        return false;
    }
  };

  const handleStepClick = (stepId: StepId) => {
    if (canNavigateToStep(stepId)) {
      stepperMethods.goTo(stepId);
    }
  };

  const handleCreateDistrict = () => {
    if (!(district && schools.length && members.length)) {
      toast.error(
        'Please complete all required sections before creating the district.'
      );
      return;
    }

    // Transform the store data to match the server action schema
    const transformedSchools = schools.map((school) => ({
      tempId: school.id,
      generalInfo: {
        name: school.name,
        slug: school.slug,
        type: school.type,
        website: school.website,
        ncesId: school.ncesId,
      },
      addresses: {
        physical: {
          type: AddressTypeEnum.PHYSICAL,
          address: school.physicalAddress.address,
          address2: school.physicalAddress.address2,
          city: school.physicalAddress.city,
          state: school.physicalAddress.state,
          zipcode: school.physicalAddress.zipcode,
        },
        postal: {
          type: AddressTypeEnum.POSTAL,
          address: school.postalAddress.address,
          address2: school.postalAddress.address2,
          city: school.postalAddress.city,
          state: school.postalAddress.state,
          zipcode: school.postalAddress.zipcode,
          sameAsPhysical: school.postalAddress.sameAsPhysical,
        },
      },
    }));

    const transformedMembers = members.map((member) => ({
      firstName: member.firstName,
      lastName: member.lastName,
      email: member.email,
      role: member.role, // Must include at least one SPECIAL_ED_DIRECTOR
      schoolIds: member.schoolIds,
    }));

    const payload = {
      district: {
        generalInfo: district.generalInfo,
        address: district.address,
        availabilities: district.availabilities,
        preferences: district.preferences,
      },
      schools: transformedSchools,
      members: transformedMembers,
    };

    onboardDistrict({
      district: payload.district,
      schools: payload.schools,
      members: payload.members,
      // biome-ignore lint/style/noNonNullAssertion: Fix later
      currentUserId: user?.id!,
    });
  };

  const canProceedToReview =
    districtComplete && schoolsComplete && membersComplete;
  const buttonLabel = useButtonLabel(stepperMethods, 'Create District');

  return (
    <DialogContent
      className="flex h-[90vh] max-h-[90vh] min-w-5xl flex-col gap-0 p-0"
      onCloseAutoFocus={() => null}
      onEscapeKeyDown={(e) => e.preventDefault()}
      onInteractOutside={(e) => e.preventDefault()}
    >
      <DialogDescription className="sr-only">
        Configure District
      </DialogDescription>
      <DialogHeader className="flex-shrink-0 border-b px-6 py-5">
        <DialogTitle>Configure District</DialogTitle>
      </DialogHeader>

      <div className="flex min-h-0 flex-1 gap-6 border-b px-6">
        <div className="w-1/4 flex-shrink-0 border-r">
          <Stepper.Navigation className="py-6">
            {stepperMethods.all.map((step) => (
              <Stepper.Step
                disabled={!canNavigateToStep(step.id)}
                icon={step.icon}
                key={step.id}
                of={step.id}
                onClick={() => handleStepClick(step.id)}
              >
                <Stepper.Title className="font-medium text-sm">
                  {step.title}
                </Stepper.Title>
                <Stepper.Description className="text-muted-foreground text-xs">
                  {step.description}
                </Stepper.Description>
              </Stepper.Step>
            ))}
          </Stepper.Navigation>
        </div>

        <div className="min-h-0 flex-1 overflow-hidden py-4">
          {stepperMethods.current.id === StepId.DISTRICT && (
            <Stepper.Panel className="h-full overflow-y-auto">
              <DistrictForm onComplete={() => stepperMethods.next()} />
            </Stepper.Panel>
          )}
          {stepperMethods.current.id === StepId.SCHOOLS && (
            <Stepper.Panel className="h-full overflow-y-auto">
              <SchoolsForm />
            </Stepper.Panel>
          )}
          {stepperMethods.current.id === StepId.MEMBERS && (
            <Stepper.Panel className="h-full overflow-y-auto">
              <MembersForm />
            </Stepper.Panel>
          )}
          {stepperMethods.current.id === StepId.REVIEW && (
            <Stepper.Panel className="h-full overflow-y-auto">
              <ReviewForm />
            </Stepper.Panel>
          )}
        </div>
      </div>

      <div className="flex-shrink-0 rounded-b-xl bg-secondary p-4">
        <Stepper.Controls className="flex justify-between">
          <DialogClose asChild>
            <Button size="sm" variant="cancel">
              Cancel
            </Button>
          </DialogClose>
          <div className="flex gap-2">
            {!stepperMethods.isFirst && (
              <Button
                onClick={stepperMethods.prev}
                size="sm"
                type="button"
                variant="outline"
              >
                Previous
              </Button>
            )}
            <Button
              disabled={
                stepperMethods.isLast
                  ? !canProceedToReview || isPending
                  : !isCurrentStepComplete()
              }
              loading={stepperMethods.isLast && isPending}
              onClick={
                stepperMethods.isLast
                  ? handleCreateDistrict
                  : stepperMethods.next
              }
              size="sm"
            >
              {buttonLabel}
              {!stepperMethods.isLast && <ArrowRightIcon className="size-4" />}
            </Button>
          </div>
        </Stepper.Controls>
      </div>
    </DialogContent>
  );
}

export function AddDistrictDialog() {
  const [isOpen, setIsOpen] = useState(false);
  const [resetKey, setResetKey] = useState(0);

  const resetAll = useDistrictConfigurationStore((state) => state.resetAll);

  const handleClose = () => {
    setIsOpen(false);
  };

  const handleReset = () => {
    resetAll();
    setResetKey((prev) => prev + 1);
  };

  const handleOpenChange = (open: boolean) => {
    if (open) {
      setIsOpen(open);
    } else {
      handleReset();
      handleClose();
    }
  };

  return (
    <Dialog onOpenChange={handleOpenChange} open={isOpen}>
      <DialogTrigger asChild>
        <Button size="sm" variant="outline">
          <PlusIcon className="size-4" />
          Add District
        </Button>
      </DialogTrigger>
      <Stepper.Provider variant="vertical">
        <DistrictDialogContent key={resetKey} onClose={handleClose} />
      </Stepper.Provider>
    </Dialog>
  );
}
