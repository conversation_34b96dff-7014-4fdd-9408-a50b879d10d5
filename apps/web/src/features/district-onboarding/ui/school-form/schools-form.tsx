'use client';

import { If } from '@lilypad/ui/components/if';
import {
  selectSchools,
  selectUI,
  useDistrictConfigurationStore,
} from '../../model';
import { SchoolEmptyState } from './school-empty-state';
import { SchoolFormDialog } from './school-form-dialog';
import { SchoolsList } from './schools-list';

export function SchoolsForm() {
  // Store hooks
  const schools = useDistrictConfigurationStore(selectSchools);
  const ui = useDistrictConfigurationStore(selectUI);
  const removeSchool = useDistrictConfigurationStore(
    (state) => state.removeSchool
  );
  const openSchoolForm = useDistrictConfigurationStore(
    (state) => state.openSchoolForm
  );

  return (
    <div className="flex h-full flex-col space-y-6 pb-4">
      <div className="flex-shrink-0">
        <h3 className="mb-1 font-semibold">Schools</h3>
        <p className="text-muted-foreground text-sm">
          Add schools that belong to this district.
        </p>
      </div>

      <div className="flex-1 overflow-y-auto">
        <If
          condition={schools.length === 0}
          fallback={
            <SchoolsList
              onAddSchool={() => openSchoolForm()}
              onEditSchool={(school) => openSchoolForm(school.id)}
              onRemoveSchool={removeSchool}
              schools={schools}
            />
          }
        >
          <SchoolEmptyState onAddSchool={() => openSchoolForm()} />
        </If>
      </div>

      <If condition={ui.schoolForm.isOpen}>
        <SchoolFormDialog />
      </If>
    </div>
  );
}
