'use client';

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  DialogTitle,
} from '@lilypad/ui/components/dialog';
import { selectUI, useDistrictConfigurationStore } from '../../model';
import type { NewSchool } from '../../model/types';
import { SchoolForm } from './school-form';

export function SchoolFormDialog() {
  const ui = useDistrictConfigurationStore(selectUI);
  const addSchool = useDistrictConfigurationStore((state) => state.addSchool);
  const updateSchool = useDistrictConfigurationStore(
    (state) => state.updateSchool
  );
  const closeSchoolForm = useDistrictConfigurationStore(
    (state) => state.closeSchoolForm
  );

  const handleFormSubmit = (data: NewSchool) => {
    if (ui.schoolForm.mode === 'edit' && ui.schoolForm.editingSchool) {
      updateSchool(ui.schoolForm.editingSchool.id, {
        name: data.generalInfo.name,
        slug: data.generalInfo.slug,
        type: data.generalInfo.type,
        website: data.generalInfo.website || '',
        ncesId: data.generalInfo.ncesId,
        physicalAddress: {
          address: data.addresses.physical.address,
          address2: data.addresses.physical.address2,
          city: data.addresses.physical.city,
          state: data.addresses.physical.state,
          zipcode: data.addresses.physical.zipcode,
        },
        postalAddress: {
          address: data.addresses.postal.address,
          address2: data.addresses.postal.address2,
          city: data.addresses.postal.city,
          state: data.addresses.postal.state,
          zipcode: data.addresses.postal.zipcode,
          sameAsPhysical: data.addresses.postal.sameAsPhysical,
        },
      });
    } else {
      addSchool(data);
    }
  };

  return (
    <Dialog onOpenChange={closeSchoolForm} open={ui.schoolForm.isOpen}>
      <DialogContent className="max-h-[90vh] min-w-2xl overflow-hidden p-0">
        <DialogHeader className="border-b p-5">
          <DialogTitle>
            {ui.schoolForm.mode === 'edit' ? 'Edit School' : 'Add New School'}
          </DialogTitle>
        </DialogHeader>
        <div className="overflow-auto">
          <SchoolForm
            initialData={ui.schoolForm.editingSchool}
            isEditing={ui.schoolForm.mode === 'edit'}
            onCancel={closeSchoolForm}
            onComplete={handleFormSubmit}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
}
