'use client';

import { SchoolTypeEnum, SchoolTypeEnumMap } from '@lilypad/db/enums';
import { generateSlug } from '@lilypad/shared';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@lilypad/ui/components/form';
import { Input } from '@lilypad/ui/components/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@lilypad/ui/components/select';
import { useFormContext } from 'react-hook-form';
import type { CreateSchoolForm } from '../../model/schemas';

export function SchoolGeneralInfoForm() {
  const form = useFormContext<CreateSchoolForm>();

  const handleNameChange = (value: string) => {
    form.setValue('generalInfo.name', value);
    form.setValue('generalInfo.slug', generateSlug(value));
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 gap-5 md:grid-cols-2">
        <FormField
          control={form.control}
          name="generalInfo.name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>School Name</FormLabel>
              <FormControl>
                <Input
                  placeholder="e.g., Lincoln Elementary School"
                  {...field}
                  onChange={(e) => handleNameChange(e.target.value)}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="generalInfo.slug"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Slug</FormLabel>
              <FormControl>
                <Input
                  placeholder="e.g., lincoln-elementary-school"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="generalInfo.type"
          render={({ field }) => (
            <FormItem>
              <FormLabel>School Type</FormLabel>
              <Select defaultValue={field.value} onValueChange={field.onChange}>
                <FormControl>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select school type" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {Object.values(SchoolTypeEnum).map((type) => (
                    <SelectItem key={type} value={type}>
                      {SchoolTypeEnumMap[type]}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="generalInfo.ncesId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>NCES ID</FormLabel>
              <FormControl>
                <Input placeholder="e.g., 123456789012" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="generalInfo.website"
          render={({ field }) => (
            <FormItem className="md:col-span-2">
              <FormLabel>Website (Optional)</FormLabel>
              <FormControl>
                <Input placeholder="https://www.schoolname.edu" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
}
