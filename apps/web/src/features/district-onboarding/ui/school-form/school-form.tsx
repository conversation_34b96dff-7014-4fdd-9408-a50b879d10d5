'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { AddressTypeEnum, SchoolTypeEnum } from '@lilypad/db/enums';
import { Button } from '@lilypad/ui/components/button';
import { Form } from '@lilypad/ui/components/form';
import { defineStepper, useButtonLabel } from '@lilypad/ui/components/stepper';
import { ArrowRightIcon, InfoIcon, MapPinIcon } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { type CreateSchoolForm, createSchoolSchema } from '../../model/schemas';
import type { NewSchool, School } from '../../model/types';
import { SchoolAddressForm } from './school-address-form';
import { SchoolGeneralInfoForm } from './school-general-info-form';

const SchoolStepId = {
  GENERAL_INFO: 'general-info',
  ADDRESSES: 'addresses',
} as const;

type SchoolStepId = (typeof SchoolStepId)[keyof typeof SchoolStepId];

const SCHOOL_STEPS = [
  {
    id: SchoolStepId.GENERAL_INFO,
    title: 'General Info',
    description: 'Basic school information',
    icon: <InfoIcon className="size-4" />,
  },
  {
    id: SchoolStepId.ADDRESSES,
    title: 'Addresses',
    description: 'Physical and postal addresses',
    icon: <MapPinIcon className="size-4" />,
  },
];

const { Stepper: SchoolStepper } = defineStepper(...SCHOOL_STEPS);

interface SchoolFormProps {
  initialData: School | null;
  isEditing?: boolean;
  onComplete: (data: NewSchool) => void;
  onCancel?: () => void;
}

export function SchoolForm({
  initialData,
  isEditing = false,
  onComplete,
  onCancel,
}: SchoolFormProps) {
  const form = useForm<CreateSchoolForm>({
    resolver: zodResolver(createSchoolSchema),
    defaultValues:
      isEditing && initialData
        ? {
            generalInfo: {
              name: initialData.name,
              slug:
                initialData.slug ||
                initialData.name.toLowerCase().replace(/\s+/g, '-'),
              type: initialData.type,
              website: initialData.website || '',
              ncesId: initialData.ncesId,
            },
            addresses: {
              physical: {
                type: AddressTypeEnum.PHYSICAL,
                address: initialData.physicalAddress.address,
                address2: initialData.physicalAddress.address2 || '',
                city: initialData.physicalAddress.city,
                state: initialData.physicalAddress.state,
                zipcode: initialData.physicalAddress.zipcode,
              },
              postal: {
                type: AddressTypeEnum.POSTAL,
                address: initialData.postalAddress.address,
                address2: initialData.postalAddress.address2 || '',
                city: initialData.postalAddress.city,
                state: initialData.postalAddress.state,
                zipcode: initialData.postalAddress.zipcode,
                sameAsPhysical: initialData.postalAddress.sameAsPhysical,
              },
            },
          }
        : {
            generalInfo: {
              type: SchoolTypeEnum.REGULAR_PUBLIC_PRIMARY,
              name: '',
              slug: '',
              website: '',
              ncesId: '',
            },
            addresses: {
              physical: {
                type: AddressTypeEnum.PHYSICAL,
                address: '',
                address2: '',
                city: '',
                state: '',
                zipcode: '',
              },
              postal: {
                type: AddressTypeEnum.POSTAL,
                address: '',
                address2: '',
                city: '',
                state: '',
                zipcode: '',
                sameAsPhysical: true,
              },
            },
          },
    mode: 'onBlur',
  });

  const validateCurrentStep = async (
    stepId: SchoolStepId
  ): Promise<boolean> => {
    const fieldsToValidate: (keyof CreateSchoolForm)[] = [];

    switch (stepId) {
      case SchoolStepId.GENERAL_INFO:
        fieldsToValidate.push('generalInfo');
        break;
      case SchoolStepId.ADDRESSES:
        fieldsToValidate.push('addresses');
        break;
      default:
        return false;
    }

    return await form.trigger(fieldsToValidate);
  };

  const handleSchoolComplete = (values: CreateSchoolForm) => {
    onComplete(values);
  };

  return (
    <Form {...form}>
      <SchoolStepper.Provider className="h-full" variant="horizontal">
        {({ methods }) => {
          // biome-ignore lint/correctness/useHookAtTopLevel: Fix later
          const buttonLabel = useButtonLabel(
            methods,
            isEditing ? 'Update School' : 'Create School'
          );

          return (
            <div className="flex h-full flex-col">
              {/* Navigation */}
              <SchoolStepper.Navigation className="flex-shrink-0 border-b px-5 pb-4">
                {methods.all.map((step: (typeof SCHOOL_STEPS)[number]) => (
                  <SchoolStepper.Step
                    icon={step.icon}
                    key={step.id}
                    of={step.id}
                    onClick={() => methods.goTo(step.id)}
                  >
                    <SchoolStepper.Title className="font-medium text-sm">
                      {step.title}
                    </SchoolStepper.Title>
                    <SchoolStepper.Description className="text-muted-foreground text-xs">
                      {step.description}
                    </SchoolStepper.Description>
                  </SchoolStepper.Step>
                ))}
              </SchoolStepper.Navigation>

              {/* Content */}
              <div className="max-h-[65vh] flex-1 overflow-hidden p-5">
                {methods.switch({
                  [SchoolStepId.GENERAL_INFO]: () => (
                    <SchoolStepper.Panel className="h-full overflow-y-auto">
                      <SchoolGeneralInfoForm />
                    </SchoolStepper.Panel>
                  ),
                  [SchoolStepId.ADDRESSES]: () => (
                    <SchoolStepper.Panel className="h-full overflow-y-auto">
                      <SchoolAddressForm />
                    </SchoolStepper.Panel>
                  ),
                })}
              </div>

              {/* Controls */}
              <SchoolStepper.Controls className="flex-shrink-0 justify-between border-t bg-secondary p-4">
                {onCancel && (
                  <Button
                    onClick={onCancel}
                    size="sm"
                    type="button"
                    variant="cancel"
                  >
                    Cancel
                  </Button>
                )}
                <div className="flex gap-2">
                  <Button
                    disabled={methods.isFirst}
                    onClick={methods.prev}
                    size="sm"
                    type="button"
                    variant="outline"
                  >
                    Previous
                  </Button>
                  <Button
                    onClick={async () => {
                      const isValid = await validateCurrentStep(
                        methods.current.id as SchoolStepId
                      );
                      if (isValid) {
                        if (methods.isLast) {
                          form.handleSubmit(handleSchoolComplete)();
                        } else {
                          methods.next();
                        }
                      }
                    }}
                    size="sm"
                    type="button"
                  >
                    {buttonLabel}
                    {!methods.isLast && <ArrowRightIcon className="size-4" />}
                  </Button>
                </div>
              </SchoolStepper.Controls>
            </div>
          );
        }}
      </SchoolStepper.Provider>
    </Form>
  );
}
