'use client';

import { But<PERSON> } from '@lilypad/ui/components/button';
import { Card, CardHeader, CardTitle } from '@lilypad/ui/components/card';
import { If } from '@lilypad/ui/components/if';
import { SchoolTypeBadge } from '@lilypad/ui/components/school-type-badge';
import {
  EditIcon,
  GlobeIcon,
  MapPinIcon,
  SchoolIcon,
  Trash2Icon,
} from 'lucide-react';
import { memo, useCallback } from 'react';
import type { School } from '../../model/types';

interface SchoolCardProps {
  school: School;
  onEdit?: (school: School) => void;
  onRemove?: (id: string) => void;
  readOnly?: boolean;
}

export const SchoolCard = memo(function SchoolCardComponent({
  school,
  onEdit,
  onRemove,
  readOnly = false,
}: SchoolCardProps) {
  const handleEdit = useCallback(() => {
    onEdit?.(school);
  }, [onEdit, school]);

  const handleRemove = useCallback(() => {
    onRemove?.(school.id);
  }, [onRemove, school.id]);

  return (
    <Card className="relative py-4 shadow-none">
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex-1 space-y-2">
            <div className="flex items-center gap-2">
              <CardTitle>{school.name}</CardTitle>
              <div className="w-fit rounded-md bg-muted px-2 py-1 text-xs">
                NCES ID: {school.ncesId}
              </div>
            </div>

            <div className="space-y-1">
              <div className="flex items-center gap-2 text-muted-foreground text-sm">
                <SchoolIcon className="size-3.5" />
                <SchoolTypeBadge schoolType={school.type} />

                {school.website && (
                  <>
                    <span>•</span>
                    <GlobeIcon className="size-3.5" />
                    <a
                      href={school.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="hover:text-foreground hover:underline"
                    >
                      Website
                    </a>
                  </>
                )}
              </div>

              <div className="flex items-start gap-2 text-muted-foreground text-sm">
                <MapPinIcon className="mt-0.5 size-3.5" />
                <div>
                  {school.physicalAddress.address},{' '}
                  {school.physicalAddress.city}, {school.physicalAddress.state}{' '}
                  {school.physicalAddress.zipcode}
                </div>
              </div>
            </div>
          </div>

          <If condition={!readOnly && (onEdit || onRemove)}>
            <div className="flex items-center gap-1">
              <If condition={!!onEdit}>
                <Button
                  className="text-muted-foreground hover:text-foreground"
                  onClick={handleEdit}
                  size="sm"
                  variant="ghost"
                >
                  <EditIcon className="size-4" />
                </Button>
              </If>
              <If condition={!!onRemove}>
                <Button
                  className="hover:text-destructive"
                  onClick={handleRemove}
                  size="sm"
                  variant="ghost"
                >
                  <Trash2Icon className="size-4" />
                </Button>
              </If>
            </div>
          </If>
        </div>
      </CardHeader>
    </Card>
  );
});
