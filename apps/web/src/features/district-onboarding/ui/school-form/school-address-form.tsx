'use client';

import { Checkbox } from '@lilypad/ui/components/checkbox';
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@lilypad/ui/components/form';
import { Input } from '@lilypad/ui/components/input';
import { Separator } from '@lilypad/ui/components/separator';
import { StateSelect } from '@lilypad/ui/components/state-select';
import { MailIcon, MapPinIcon } from 'lucide-react';
import { useFormContext } from 'react-hook-form';
import type { CreateSchoolForm } from '../../model/schemas';

export function SchoolAddressForm() {
  const form = useFormContext<CreateSchoolForm>();

  const sameAsPhysical = form.watch('addresses.postal.sameAsPhysical');

  const handleSameAsPhysicalChange = (checked: boolean) => {
    form.setValue('addresses.postal.sameAsPhysical', checked);

    if (checked) {
      const physicalAddress = form.getValues('addresses.physical');
      form.setValue('addresses.postal.address', physicalAddress.address);
      form.setValue('addresses.postal.address2', physicalAddress.address2);
      form.setValue('addresses.postal.city', physicalAddress.city);
      form.setValue('addresses.postal.state', physicalAddress.state);
      form.setValue('addresses.postal.zipcode', physicalAddress.zipcode);
    }
  };

  const handlePhysicalFieldChange = (
    value: string,
    onChange: (val: string) => void,
    postalFieldName: 'address' | 'address2' | 'city' | 'state' | 'zipcode'
  ) => {
    onChange(value);
    if (sameAsPhysical) {
      form.setValue(`addresses.postal.${postalFieldName}`, value);
    }
  };

  return (
    <div className="space-y-8">
      {/* Physical Address */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <MapPinIcon className="size-4" />
          <h5 className="font-medium">Physical Address</h5>
        </div>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
          <FormField
            control={form.control}
            name="addresses.physical.address"
            render={({ field }) => (
              <FormItem className="md:col-span-3">
                <FormLabel>Street Address</FormLabel>
                <FormControl>
                  <Input
                    placeholder="123 Main Street"
                    {...field}
                    onChange={(e) =>
                      handlePhysicalFieldChange(
                        e.target.value,
                        field.onChange,
                        'address'
                      )
                    }
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="addresses.physical.address2"
            render={({ field }) => (
              <FormItem className="md:col-span-3">
                <FormLabel>Address Line 2 (Optional)</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Suite 100, Building A, etc."
                    {...field}
                    onChange={(e) =>
                      handlePhysicalFieldChange(
                        e.target.value,
                        field.onChange,
                        'address2'
                      )
                    }
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="addresses.physical.city"
            render={({ field }) => (
              <FormItem>
                <FormLabel>City</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Springfield"
                    {...field}
                    onChange={(e) =>
                      handlePhysicalFieldChange(
                        e.target.value,
                        field.onChange,
                        'city'
                      )
                    }
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="addresses.physical.state"
            render={({ field }) => (
              <FormItem>
                <FormLabel>State</FormLabel>
                <FormControl>
                  <StateSelect
                    onChange={(value) =>
                      handlePhysicalFieldChange(value, field.onChange, 'state')
                    }
                    value={field.value}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="addresses.physical.zipcode"
            render={({ field }) => (
              <FormItem>
                <FormLabel>ZIP Code</FormLabel>
                <FormControl>
                  <Input
                    placeholder="12345"
                    {...field}
                    onChange={(e) =>
                      handlePhysicalFieldChange(
                        e.target.value,
                        field.onChange,
                        'zipcode'
                      )
                    }
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>

      <Separator />

      {/* Postal Address */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <MailIcon className="size-4" />
          <h5 className="font-medium">Postal Address</h5>
        </div>

        <FormField
          control={form.control}
          name="addresses.postal.sameAsPhysical"
          render={({ field }) => (
            <FormItem className="flex flex-row items-start space-x-3 space-y-0">
              <FormControl>
                <Checkbox
                  checked={field.value}
                  onCheckedChange={handleSameAsPhysicalChange}
                />
              </FormControl>
              <div className="space-y-1 leading-none">
                <FormLabel>Same as physical address</FormLabel>
                <FormDescription>
                  Check this if the postal address is the same as the physical
                  address
                </FormDescription>
              </div>
            </FormItem>
          )}
        />

        {!sameAsPhysical && (
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            <FormField
              control={form.control}
              name="addresses.postal.address"
              render={({ field }) => (
                <FormItem className="md:col-span-3">
                  <FormLabel>Street Address</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="P.O. Box 123 or 456 Oak Street"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="addresses.postal.address2"
              render={({ field }) => (
                <FormItem className="md:col-span-3">
                  <FormLabel>Address Line 2 (Optional)</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Suite 100, Building A, etc."
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="addresses.postal.city"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>City</FormLabel>
                  <FormControl>
                    <Input placeholder="Springfield" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="addresses.postal.state"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>State</FormLabel>
                  <FormControl>
                    <StateSelect
                      value={field.value}
                      onChange={field.onChange}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="addresses.postal.zipcode"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>ZIP Code</FormLabel>
                  <FormControl>
                    <Input placeholder="12345" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        )}
      </div>
    </div>
  );
}
