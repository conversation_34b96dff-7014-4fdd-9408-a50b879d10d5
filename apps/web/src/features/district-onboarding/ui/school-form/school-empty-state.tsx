'use client';

import { But<PERSON> } from '@lilypad/ui/components/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardTitle,
} from '@lilypad/ui/components/card';
import { PlusIcon, SchoolIcon } from 'lucide-react';
import { memo } from 'react';

interface SchoolEmptyStateProps {
  onAddSchool: () => void;
}

export const SchoolEmptyState = memo(function SchoolEmptyStateComponent({
  onAddSchool,
}: SchoolEmptyStateProps) {
  return (
    <Card className="border-dashed shadow-none">
      <CardContent className="flex flex-col items-center justify-center gap-4 py-12 text-center">
        <SchoolIcon className="size-12 text-muted" />
        <CardTitle>No schools added yet</CardTitle>
        <CardDescription>
          Start by adding a school to the district.
        </CardDescription>
        <Button
          className="flex items-center gap-2"
          onClick={onAddSchool}
          size="sm"
        >
          <PlusIcon className="size-4" />
          Add School
        </Button>
      </CardContent>
    </Card>
  );
});
