'use client';

import { But<PERSON> } from '@lilypad/ui/components/button';
import { PlusIcon } from 'lucide-react';
import { memo } from 'react';
import type { School } from '../../model/types';
import { SchoolCard } from './school-card';

interface SchoolsListProps {
  schools: School[];
  onAddSchool: () => void;
  onEditSchool: (school: School) => void;
  onRemoveSchool: (id: string) => void;
}

export const SchoolsList = memo(function SchoolsListComponent({
  schools,
  onAddSchool,
  onEditSchool,
  onRemoveSchool,
}: SchoolsListProps) {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <p className="text-muted-foreground text-sm">
          {schools.length} school{schools.length !== 1 ? 's' : ''} added
        </p>
        <Button
          className="flex items-center gap-2"
          onClick={onAddSchool}
          size="sm"
          variant="outline"
        >
          <PlusIcon className="size-4" />
          Add School
        </Button>
      </div>

      <div className="grid gap-4">
        {schools.map((school) => (
          <SchoolCard
            key={school.id}
            onEdit={onEditSchool}
            onRemove={onRemoveSchool}
            school={school}
          />
        ))}
      </div>
    </div>
  );
});
