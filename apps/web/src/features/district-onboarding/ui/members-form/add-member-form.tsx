import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { RoleEnum, RoleEnumMap } from '@lilypad/db/enums';
import { Button } from '@lilypad/ui/components/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@lilypad/ui/components/card';
import { Checkbox } from '@lilypad/ui/components/checkbox';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@lilypad/ui/components/form';
import { If } from '@lilypad/ui/components/if';
import { Input } from '@lilypad/ui/components/input';
import { Label } from '@lilypad/ui/components/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@lilypad/ui/components/select';
import { useForm } from 'react-hook-form';
import { type NewMember, newMemberSchema } from '../../model/schemas';
import type { School } from '../../model/types';

const ROLES = Object.entries(RoleEnumMap)
  .filter(([key]) => key !== RoleEnum.SUPER_USER)
  .map(([key, value]) => ({
    value: key,
    label: value,
  }));

export interface AddMemberFormProps {
  schools: School[];
  newMember: NewMember;
  onMemberChange: (member: Partial<NewMember>) => void;
  onSave: (data: NewMember) => void;
  onCancel: () => void;
  isEditing: boolean;
}

export function AddMemberForm({
  schools,
  newMember: initialNewMember,
  onMemberChange,
  onSave,
  onCancel,
  isEditing,
}: AddMemberFormProps) {
  const form = useForm<NewMember>({
    resolver: zodResolver(newMemberSchema),
    values: initialNewMember,
    mode: 'onBlur',
  });

  const handleFieldChange = () => {
    const values = form.getValues();
    onMemberChange(values);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSave)}>
        <Card>
          <CardHeader>
            <CardTitle>Add New Member</CardTitle>
            <CardDescription>
              Enter the member's information and assign them to schools. They
              will receive an invitation email.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="firstName"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        placeholder="First Name"
                        {...field}
                        onBlur={() => {
                          field.onBlur();
                          handleFieldChange();
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="lastName"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        placeholder="Last Name"
                        {...field}
                        onBlur={() => {
                          field.onBlur();
                          handleFieldChange();
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="Email Address"
                        {...field}
                        onBlur={() => {
                          field.onBlur();
                          handleFieldChange();
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="role"
                render={({ field }) => (
                  <FormItem>
                    <Select
                      onValueChange={(value) => {
                        field.onChange(value);
                        handleFieldChange();
                      }}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Select role" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {ROLES.map((role) => (
                          <SelectItem key={role.value} value={role.value}>
                            {role.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <If condition={schools.length > 0}>
              <FormField
                control={form.control}
                name="schoolIds"
                render={() => (
                  <FormItem>
                    <div className="space-y-3">
                      <Label className="font-medium text-sm">
                        Assign to Schools{' '}
                        <span className="text-destructive">*</span>
                      </Label>
                      <p className="text-muted-foreground text-xs">
                        Select which schools this member will have access to.
                      </p>
                      <div className="grid max-h-40 grid-cols-1 gap-2 overflow-y-auto md:grid-cols-2">
                        {schools.map((school) => (
                          <FormField
                            control={form.control}
                            key={school.id}
                            name="schoolIds"
                            render={({ field }) => (
                              <FormItem className="flex items-center space-x-2">
                                <FormControl>
                                  <Checkbox
                                    checked={field.value?.includes(school.id)}
                                    onCheckedChange={(checked) => {
                                      const newValue = checked
                                        ? [...(field.value || []), school.id]
                                        : field.value?.filter(
                                            (value) => value !== school.id
                                          ) || [];
                                      field.onChange(newValue);
                                      handleFieldChange();
                                    }}
                                  />
                                </FormControl>
                                <Label
                                  className="cursor-pointer font-normal text-sm"
                                  htmlFor={`school-${school.id}`}
                                >
                                  {school.name}
                                </Label>
                              </FormItem>
                            )}
                          />
                        ))}
                      </div>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </If>

            <If condition={schools.length === 0}>
              <div className="py-6 text-center text-muted-foreground">
                <p className="text-sm">
                  No schools available. Please add schools first before adding
                  members.
                </p>
              </div>
            </If>

            <div className="flex items-center justify-end gap-2">
              <Button
                onClick={onCancel}
                size="sm"
                type="button"
                variant="outline"
              >
                Cancel
              </Button>
              <Button
                disabled={schools.length === 0 || form.formState.isSubmitting}
                size="sm"
                type="submit"
              >
                {isEditing ? 'Update Member' : 'Add Member'}
              </Button>
            </div>
          </CardContent>
        </Card>
      </form>
    </Form>
  );
}
