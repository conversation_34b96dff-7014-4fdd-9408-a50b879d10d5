import { But<PERSON> } from '@lilypad/ui/components/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardTitle,
} from '@lilypad/ui/components/card';
import { PlusIcon, UsersIcon } from 'lucide-react';

interface MembersEmptyStateProps {
  onAddMember: () => void;
}

export function MembersEmptyState({ onAddMember }: MembersEmptyStateProps) {
  return (
    <Card className="border-dashed shadow-none">
      <CardContent className="flex flex-col items-center justify-center gap-4 py-12 text-center">
        <UsersIcon className="size-12 text-muted" />
        <CardTitle>No members added yet</CardTitle>
        <CardDescription>Start by adding a district member.</CardDescription>
        <Button
          className="flex items-center gap-2"
          onClick={onAddMember}
          size="sm"
        >
          <PlusIcon className="size-4" />
          Add Member
        </Button>
      </CardContent>
    </Card>
  );
}
