'use client';

import { RoleEnum } from '@lilypad/db/enums';
import { If } from '@lilypad/ui/components/if';
import {
  selectFormData,
  selectMembers,
  selectSchools,
  selectUI,
  useDistrictConfigurationStore,
} from '../../model';
import type { NewMember } from '../../model/schemas';
import type { Member } from '../../model/types';
import { AddMemberForm } from './add-member-form';
import { MembersEmptyState } from './members-empty-state';
import { MembersList } from './members-list';

export function MembersForm() {
  // Store hooks
  const schools = useDistrictConfigurationStore(selectSchools);
  const members = useDistrictConfigurationStore(selectMembers);
  const ui = useDistrictConfigurationStore(selectUI);
  const formData = useDistrictConfigurationStore(selectFormData);
  const addMember = useDistrictConfigurationStore((state) => state.addMember);
  const updateMember = useDistrictConfigurationStore(
    (state) => state.updateMember
  );
  const removeMember = useDistrictConfigurationStore(
    (state) => state.removeMember
  );
  const openMemberForm = useDistrictConfigurationStore(
    (state) => state.openMemberForm
  );
  const closeMemberForm = useDistrictConfigurationStore(
    (state) => state.closeMemberForm
  );

  const handleSaveMember = (member: NewMember) => {
    if (ui.memberForm.mode === 'edit' && ui.memberForm.editingMember) {
      updateMember(ui.memberForm.editingMember.id, member);
    } else {
      addMember(member);
    }
  };

  const handleEditMember = (member: Member) => {
    openMemberForm(member.id);
  };

  return (
    <div className="flex h-full flex-col space-y-6">
      <div className="flex-shrink-0">
        <h3 className="mb-1 font-semibold">District Members</h3>
        <p className="text-muted-foreground text-sm">
          Add members who can access this district. Each member will receive
          invitations to join.
        </p>
      </div>

      <If condition={members.length === 0 && !ui.memberForm.isOpen}>
        <MembersEmptyState onAddMember={() => openMemberForm()} />
      </If>

      <If condition={members.length > 0 || ui.memberForm.isOpen}>
        <div className="flex-1 overflow-y-auto">
          <If condition={members.length > 0 && !ui.memberForm.isOpen}>
            <MembersList
              isAdding={ui.memberForm.isOpen}
              members={members}
              onAddMember={() => openMemberForm()}
              onEditMember={handleEditMember}
              onRemoveMember={removeMember}
              schools={schools}
            />
          </If>

          <If condition={ui.memberForm.isOpen}>
            <AddMemberForm
              isEditing={ui.memberForm.mode === 'edit'}
              newMember={
                formData.currentMember || {
                  firstName: '',
                  lastName: '',
                  email: '',
                  role: RoleEnum.SCHOOL_ADMIN,
                  schoolIds: [],
                }
              }
              onCancel={closeMemberForm}
              onMemberChange={() => null} // Will be handled by the form itself
              onSave={handleSaveMember}
              schools={schools}
            />
          </If>
        </div>
      </If>
    </div>
  );
}
