import { Badge } from '@lilypad/ui/components/badge';
import { But<PERSON> } from '@lilypad/ui/components/button';
import { Card, CardContent, CardTitle } from '@lilypad/ui/components/card';
import { If } from '@lilypad/ui/components/if';
import { RolesBadge } from '@lilypad/ui/components/roles-badge';
import {
  EditIcon,
  MailIcon,
  SchoolIcon,
  ShieldUserIcon,
  Trash2Icon,
} from 'lucide-react';
import type { Member, School } from '../../model/types';

export interface MemberCardProps {
  member: Member;
  schools: School[];
  onEdit?: (member: MemberCardProps['member']) => void;
  onRemove?: (id: string) => void;
  readOnly?: boolean;
}

export function MemberCard({
  member,
  schools,
  onEdit,
  onRemove,
  readOnly = false,
}: MemberCardProps) {
  const associatedSchools = schools.filter((school) =>
    member.schoolIds.includes(school.id)
  );
  return (
    <Card className="py-4 shadow-none">
      <CardContent>
        <div className="flex items-start justify-between">
          <div className="flex-1 space-y-2">
            <CardTitle>
              {member.firstName} {member.lastName}
            </CardTitle>

            <div className="flex items-center gap-2">
              <ShieldUserIcon className="size-4 text-muted-foreground" />
              <RolesBadge role={member.role} />
              <MailIcon className="size-3.5 text-muted-foreground " />
              <p className="text-muted-foreground text-sm">{member.email}</p>
            </div>

            <div className="flex items-start gap-2">
              <SchoolIcon className="mt-0.5 size-3.5 text-muted-foreground " />
              <div className="min-w-0">
                <If
                  condition={associatedSchools.length > 0}
                  fallback={
                    <p className="text-destructive text-xs">
                      No schools assigned
                    </p>
                  }
                >
                  <div className="flex flex-wrap gap-1">
                    {associatedSchools.map((school) => (
                      <Badge
                        className="text-xs"
                        key={school.id}
                        variant="outline"
                      >
                        {school.name}
                      </Badge>
                    ))}
                  </div>
                </If>
              </div>
            </div>
          </div>

          <If condition={!readOnly && (onEdit || onRemove)}>
            <div className="flex items-center gap-1">
              <If condition={!!onEdit}>
                <Button
                  className="hover:text-primary"
                  onClick={() => onEdit?.(member)}
                  size="sm"
                  variant="ghost"
                >
                  <EditIcon className="size-4" />
                </Button>
              </If>
              <If condition={!!onRemove}>
                <Button
                  className="hover:text-destructive"
                  onClick={() => onRemove?.(member.id)}
                  size="sm"
                  variant="ghost"
                >
                  <Trash2Icon className="size-4" />
                </Button>
              </If>
            </div>
          </If>
        </div>
      </CardContent>
    </Card>
  );
}
