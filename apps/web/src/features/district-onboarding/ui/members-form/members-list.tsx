import { Button } from '@lilypad/ui/components/button';
import { If } from '@lilypad/ui/components/if';
import { PlusIcon } from 'lucide-react';
import type { Member, School } from '../../model/types';
import { MemberCard } from './member-card';

export interface MembersListProps {
  members: Member[];
  schools: School[];
  onEditMember: (member: Member) => void;
  onRemoveMember: (id: string) => void;
  isAdding: boolean;
  onAddMember: () => void;
}

export function MembersList({
  members,
  schools,
  onEditMember,
  onRemoveMember,
  isAdding,
  onAddMember,
}: MembersListProps) {
  return (
    <div className="space-y-4">
      <div className="flex flex-shrink-0 items-center justify-between">
        <p className="text-muted-foreground text-sm">
          {members.length} member{members.length !== 1 ? 's' : ''} added
        </p>
        <If condition={!isAdding}>
          <Button
            className="flex items-center gap-2"
            onClick={onAddMember}
            size="sm"
            variant="outline"
          >
            <PlusIcon className="size-4" />
            Add Member
          </Button>
        </If>
      </div>

      <div className="flex flex-1 flex-col gap-3 overflow-y-auto">
        {members.map((member) => (
          <MemberCard
            key={member.id}
            member={member}
            onEdit={onEditMember}
            onRemove={onRemoveMember}
            schools={schools}
          />
        ))}
      </div>
    </div>
  );
}
