'use client';

import { Badge } from '@lilypad/ui/components/badge';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@lilypad/ui/components/card';
import { DistrictTypeBadge } from '@lilypad/ui/components/district-type-badge';
import { If } from '@lilypad/ui/components/if';
import { Label } from '@lilypad/ui/components/label';
import { Separator } from '@lilypad/ui/components/separator';
import { AlertCircleIcon, CheckCircleIcon } from 'lucide-react';
import {
  selectDistrict,
  selectMembers,
  selectSchools,
  useDistrictConfigurationStore,
} from '../model';
import { MemberCard } from './members-form/member-card';
import { SchoolCard } from './school-form/school-card';

export function ReviewForm() {
  // Store hooks
  const district = useDistrictConfigurationStore(selectDistrict);
  const schools = useDistrictConfigurationStore(selectSchools);
  const members = useDistrictConfigurationStore(selectMembers);
  const isDistrictComplete = useDistrictConfigurationStore((state) =>
    state.isDistrictComplete()
  );
  const isSchoolsComplete = useDistrictConfigurationStore((state) =>
    state.isSchoolsComplete()
  );
  const isMembersComplete = useDistrictConfigurationStore((state) =>
    state.isMembersComplete()
  );

  return (
    <div className="space-y-6 pb-4">
      <div>
        <h3 className="mb-1 font-semibold">Review</h3>
        <p className="text-muted-foreground text-sm">
          Review all information before creating this district. You can edit any
          section by going back to previous steps.
        </p>
      </div>

      {/* District Information */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>District Information</CardTitle>
            <Badge
              className="flex items-center gap-1"
              variant={isDistrictComplete ? 'success' : 'secondary'}
            >
              <If condition={isDistrictComplete}>
                <CheckCircleIcon className="mr-1 size-3" />
                Complete
              </If>
              <If condition={!isDistrictComplete}>
                <AlertCircleIcon className="mr-1 size-3" />
                Incomplete
              </If>
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <If condition={district}>
            {(districtData) => (
              <>
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div className="space-y-1">
                    <Label className="text-muted-foreground">
                      District Name
                    </Label>
                    <p className="font-medium text-sm">
                      {districtData.generalInfo.name || 'Not specified'}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <Label className="text-muted-foreground">Type</Label>
                    <DistrictTypeBadge
                      districtType={districtData.generalInfo.type}
                    />
                  </div>
                  <div className="space-y-1">
                    <Label className="text-muted-foreground">Website</Label>
                    <p className="text-blue-600 text-sm">
                      {districtData.generalInfo.website || 'Not specified'}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <Label className="text-muted-foreground">County</Label>
                    <p className="text-sm">
                      {districtData.generalInfo.county || 'Not specified'}
                    </p>
                  </div>
                </div>

                <Separator />

                <div className="space-y-1">
                  <Label className="text-muted-foreground">Address</Label>
                  <p className="text-sm">
                    {districtData.address.address
                      ? `${districtData.address.address}${districtData.address.address2 ? `, ${districtData.address.address2}` : ''}, ${districtData.address.city}, ${districtData.address.state} ${districtData.address.zipcode}`
                      : 'Not specified'}
                  </p>
                </div>

                <div className="flex flex-col gap-1 space-y-1">
                  <Label className="text-muted-foreground">Preferences</Label>
                  <div className="flex flex-wrap gap-2">
                    {districtData.preferences.chromebookSetup && (
                      <Badge variant="secondary">Chromebook setup</Badge>
                    )}
                    {districtData.preferences.quietSpaceForEvaluation && (
                      <Badge variant="secondary">
                        Quiet space for evaluation
                      </Badge>
                    )}
                    {districtData.preferences.internetStability && (
                      <Badge variant="secondary">Internet stability</Badge>
                    )}
                  </div>
                </div>
              </>
            )}
          </If>
        </CardContent>
      </Card>

      {/* Schools Information */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Schools</CardTitle>
            <Badge variant={isSchoolsComplete ? 'success' : 'secondary'}>
              <If condition={isSchoolsComplete}>
                <CheckCircleIcon className="mr-1 size-3" />
                {schools.length} Added
              </If>
              <If condition={!isSchoolsComplete}>
                <AlertCircleIcon className="mr-1 size-3" />
                None Added
              </If>
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <If
            condition={schools.length > 0}
            fallback={
              <div className="py-6 text-center text-muted-foreground">
                <p>
                  No schools added yet. You can add schools after creating the
                  district.
                </p>
              </div>
            }
          >
            <div className="space-y-3">
              Ç
              {schools.map((school) => (
                <SchoolCard key={school.id} readOnly school={school} />
              ))}
            </div>
          </If>
        </CardContent>
      </Card>

      {/* Members Information */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Members</CardTitle>
            <Badge variant={isMembersComplete ? 'success' : 'secondary'}>
              <If condition={isMembersComplete}>
                <CheckCircleIcon className="mr-1 size-3" />
                {members.length} Added
              </If>
              <If condition={!isMembersComplete}>
                <AlertCircleIcon className="mr-1 size-3" />
                None Added
              </If>
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <If
            condition={members.length > 0}
            fallback={
              <div className="py-6 text-center text-muted-foreground">
                <p>
                  No team members added yet. You can invite members after
                  creating the district.
                </p>
              </div>
            }
          >
            <div className="space-y-3">
              {members.map((member) => (
                <MemberCard
                  key={member.id}
                  member={member}
                  readOnly
                  schools={schools}
                />
              ))}
            </div>
          </If>
        </CardContent>
      </Card>
    </div>
  );
}
