'use client';

import { <PERSON><PERSON> } from '@lilypad/ui/components/button';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@lilypad/ui/components/form';
import { If } from '@lilypad/ui/components/if';
import { Switch } from '@lilypad/ui/components/switch';
import { TimePicker } from '@lilypad/ui/components/time-picker';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@lilypad/ui/components/tooltip';
import { PlusIcon, Trash2Icon } from 'lucide-react';
import { memo, useCallback } from 'react';
import { useFormContext } from 'react-hook-form';
import { DAYS_OF_WEEK } from '../../model/constants';
import type { CreateDistrictForm } from '../../model/schemas';
import { calculateNextTimeSlot } from '../../model/time-utils';
import type { DayConfig, DayOfWeek } from '../../model/types';
import { CopyTimesPopover } from './copy-times-popover';

interface DayScheduleRowProps {
  dayConfig: DayConfig;
  onFormChange?: () => void;
}

export const DayScheduleRow = memo(function DayScheduleRowComponent({
  dayConfig,
  onFormChange,
}: DayScheduleRowProps) {
  const form = useFormContext<CreateDistrictForm>();
  const { key: dayKey, label } = dayConfig;

  const isEnabled = form.watch(
    `availabilities.availabilitySchedule.${dayKey}.enabled` as const
  );

  const timeSlots =
    form.watch(
      `availabilities.availabilitySchedule.${dayKey}.slots` as const
    ) || [];

  const addTimeSlot = useCallback(() => {
    const currentSlots =
      form.getValues(
        `availabilities.availabilitySchedule.${dayKey}.slots` as const
      ) || [];

    const newSlot = calculateNextTimeSlot(currentSlots);

    form.setValue(
      `availabilities.availabilitySchedule.${dayKey}.slots` as const,
      [...currentSlots, newSlot]
    );

    onFormChange?.();
  }, [form, dayKey, onFormChange]);

  const removeTimeSlot = useCallback(
    (index: number) => {
      const currentSlots =
        form.getValues(
          `availabilities.availabilitySchedule.${dayKey}.slots` as const
        ) || [];
      form.setValue(
        `availabilities.availabilitySchedule.${dayKey}.slots` as const,
        currentSlots.filter((_, i) => i !== index)
      );
      onFormChange?.();
    },
    [form, dayKey, onFormChange]
  );

  const handleToggleDay = useCallback(
    (enabled: boolean) => {
      form.setValue(
        `availabilities.availabilitySchedule.${dayKey}.enabled` as const,
        enabled
      );

      // Initialize first slot when enabling a day
      if (enabled && timeSlots.length === 0) {
        form.setValue(
          `availabilities.availabilitySchedule.${dayKey}.slots` as const,
          [{ startTime: '09:00', endTime: '17:00' }]
        );
      }

      onFormChange?.();
    },
    [form, dayKey, onFormChange, timeSlots.length]
  );

  return (
    <div className="grid grid-cols-8 items-start">
      {/* Column 1: Day Toggle - Fixed width */}
      <div className="col-span-2">
        <DayToggleSection
          dayKey={dayKey}
          isEnabled={isEnabled}
          label={label}
          onToggle={handleToggleDay}
        />
      </div>

      {/* Column 2: Time Slots Area - Expandable */}
      <div className="col-span-4 space-y-2">
        <If condition={isEnabled && timeSlots.length > 0}>
          <TimeSlotRow
            canRemove={false}
            dayKey={dayKey}
            onFormChange={onFormChange}
            onRemove={removeTimeSlot}
            slotIndex={0}
          />
        </If>

        <If condition={isEnabled && timeSlots.length > 1}>
          <div className="space-y-2">
            {timeSlots.slice(1).map((_, index) => (
              <TimeSlotRow
                canRemove={true}
                dayKey={dayKey}
                key={index + 1}
                onFormChange={onFormChange}
                onRemove={removeTimeSlot}
                slotIndex={index + 1}
              />
            ))}
          </div>
        </If>
        <If condition={!isEnabled}>
          <div className="flex w-full items-center justify-center gap-2 rounded-md border border-dashed bg-muted p-1.5 text-center">
            <p className="w-full text-center text-muted-foreground text-sm">
              Unavailable
            </p>
          </div>
        </If>
      </div>

      {/* Column 3: Actions - Fixed width */}
      <div className="col-span-1 flex justify-start">
        <If condition={isEnabled}>
          <DayActions dayKey={dayKey} onAddTimeSlot={addTimeSlot} />
        </If>
      </div>
    </div>
  );
});

interface DayToggleSectionProps {
  dayKey: DayOfWeek;
  label: string;
  isEnabled: boolean;
  onToggle: (enabled: boolean) => void;
}

const DayToggleSection = memo(function DayToggleSectionComponent({
  dayKey,
  label,
  onToggle,
}: DayToggleSectionProps) {
  const form = useFormContext<CreateDistrictForm>();

  return (
    <FormField
      control={form.control}
      name={`availabilities.availabilitySchedule.${dayKey}.enabled` as const}
      render={({ field }) => (
        <FormItem className="mt-1 flex items-center space-x-2">
          <FormControl>
            <Switch
              checked={field.value}
              onCheckedChange={(checked) => {
                field.onChange(checked);
                onToggle(checked);
              }}
            />
          </FormControl>
          <FormLabel className="whitespace-nowrap font-medium text-sm">
            {label}
          </FormLabel>
        </FormItem>
      )}
    />
  );
});

interface TimeSlotRowProps {
  dayKey: DayOfWeek;
  slotIndex: number;
  onFormChange?: () => void;
  canRemove: boolean;
  onRemove: (index: number) => void;
}

const TimeSlotRow = memo(function TimeSlotRowComponent({
  dayKey,
  slotIndex,
  onFormChange,
  canRemove,
  onRemove,
}: TimeSlotRowProps) {
  const form = useFormContext<CreateDistrictForm>();

  return (
    <div className="flex flex-wrap items-center gap-2 sm:flex-nowrap">
      <div className="flex min-w-0 items-center gap-2">
        <FormField
          control={form.control}
          name={
            `availabilities.availabilitySchedule.${dayKey}.slots.${slotIndex}.startTime` as const
          }
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <TimePicker
                  className="w-[100px] sm:w-[120px]"
                  onChange={(value) => {
                    field.onChange(value);
                    onFormChange?.();
                  }}
                  placeholder="Start"
                  value={field.value}
                />
              </FormControl>
            </FormItem>
          )}
        />
        <span className="text-muted-foreground text-sm">-</span>
        <FormField
          control={form.control}
          name={
            `availabilities.availabilitySchedule.${dayKey}.slots.${slotIndex}.endTime` as const
          }
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <TimePicker
                  className="w-[100px] sm:w-[120px]"
                  onChange={(value) => {
                    field.onChange(value);
                    onFormChange?.();
                  }}
                  placeholder="End"
                  value={field.value}
                />
              </FormControl>
            </FormItem>
          )}
        />
      </div>

      <If condition={canRemove}>
        <Button
          className="size-8"
          onClick={() => onRemove(slotIndex)}
          size="icon"
          type="button"
          variant="ghost"
        >
          <Trash2Icon className="size-4" />
        </Button>
      </If>
    </div>
  );
});

interface DayActionsProps {
  dayKey: DayOfWeek;
  onAddTimeSlot: () => void;
}

const DayActions = memo(function DayActionsComponent({
  dayKey,
  onAddTimeSlot,
}: DayActionsProps) {
  return (
    <div className="flex shrink-0 items-center gap-1">
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            className="size-8 p-1"
            onClick={onAddTimeSlot}
            size="icon"
            type="button"
            variant="ghost"
          >
            <PlusIcon className="size-4" />
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Add time slot</p>
        </TooltipContent>
      </Tooltip>

      <CopyTimesPopover enabledDays={DAYS_OF_WEEK} sourceDayKey={dayKey} />
    </div>
  );
});
