'use client';

import { TIMEZONES } from '@lilypad/shared/constants/timezones';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@lilypad/ui/components/card';
import {
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@lilypad/ui/components/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@lilypad/ui/components/select';
import { memo, useCallback } from 'react';
import { useFormContext } from 'react-hook-form';
import { useDistrictConfigurationStore } from '../../model';
import { DAYS_OF_WEEK } from '../../model/constants';
import type { CreateDistrictForm } from '../../model/schemas';
import { DayScheduleRow } from './day-schedule-row';

interface AvailabilityScheduleSectionProps {
  onFormChange?: () => void;
}

export const AvailabilityScheduleSection = memo(
  function AvailabilityScheduleSectionComponent({
    onFormChange,
  }: AvailabilityScheduleSectionProps) {
    const form = useFormContext<CreateDistrictForm>();

    // Store integration following established pattern
    const updateDistrictForm = useDistrictConfigurationStore(
      (state) => state.updateDistrictForm
    );

    const handleFormChange = useCallback(() => {
      const formValues = form.getValues() as CreateDistrictForm;
      const availabilities = formValues.availabilities;

      // Update store
      updateDistrictForm({ availabilities });

      // Notify parent
      onFormChange?.();
    }, [form, updateDistrictForm, onFormChange]);

    const handleTimezoneChange = useCallback(
      (value: string) => {
        form.setValue('availabilities.timezone', value);
        handleFormChange();
      },
      [form, handleFormChange]
    );

    return (
      <Card className="border-none p-0 shadow-none">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 px-0">
          <div className="flex flex-col">
            <CardTitle className="text-sm">Evaluations Scheduling</CardTitle>
            <CardDescription className="text-xs">
              Configure the preferred evaluation hours for this district.
            </CardDescription>
          </div>

          <TimezoneSelector
            onValueChange={handleTimezoneChange}
            value={form.watch('availabilities.timezone')}
          />
        </CardHeader>

        <CardContent className="space-y-4 px-0">
          {DAYS_OF_WEEK.map((dayConfig) => (
            <DayScheduleRow
              dayConfig={dayConfig}
              key={dayConfig.key}
              onFormChange={handleFormChange}
            />
          ))}
        </CardContent>
      </Card>
    );
  }
);

interface TimezoneSelectorProps {
  value: string;
  onValueChange: (val: string) => void;
}

const TimezoneSelector = memo(function TimezoneSelectorComponent({
  value,
  onValueChange,
}: TimezoneSelectorProps) {
  const form = useFormContext<CreateDistrictForm>();

  return (
    <FormField
      control={form.control}
      name="availabilities.timezone"
      render={() => (
        <FormItem>
          <Select onValueChange={onValueChange} value={value}>
            <FormControl>
              <SelectTrigger>
                <SelectValue placeholder="Select timezone" />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              {TIMEZONES.map((timezone) => (
                <SelectItem key={timezone.value} value={timezone.value}>
                  <div className="flex items-center gap-1">
                    <span className="rounded-sm bg-muted px-1 py-0.5 text-muted-foreground text-xs">
                      {timezone.prefix}
                    </span>
                    <span className="text-sm">{timezone.label}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <FormMessage />
        </FormItem>
      )}
    />
  );
});
