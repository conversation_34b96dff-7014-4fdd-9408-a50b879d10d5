'use client';

import { useFormContext } from 'react-hook-form';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@lilypad/ui/components/form';
import { Input } from '@lilypad/ui/components/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@lilypad/ui/components/select';
import { DistrictTypeMap } from '@lilypad/db/enums';
import type { CreateDistrictForm } from '../../model/schemas';
import { useDistrictConfigurationStore } from '../../model';
import { useCallback, memo } from 'react';
import { generateSlug } from '@lilypad/shared';

export const DistrictGeneralInfoForm = memo(
  function DistrictGeneralInfoFormComponent() {
    const { control, setValue, getValues } =
      useFormContext<CreateDistrictForm>();

    // Store hook
    const updateDistrictForm = useDistrictConfigurationStore(
      (state) => state.updateDistrictForm
    );

    const handleNameChange = (value: string) => {
      setValue('generalInfo.name', value);
      setValue('generalInfo.slug', generateSlug(value));
    };

    const handleBlur = useCallback(() => {
      const formValues = getValues() as CreateDistrictForm;
      const generalInfo = formValues.generalInfo;

      // Update store
      updateDistrictForm({ generalInfo });
    }, [getValues, updateDistrictForm]);

    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <FormField
            control={control}
            name="generalInfo.name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>District Name</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Enter district name"
                    {...field}
                    onBlur={() => {
                      field.onBlur();
                      handleBlur();
                    }}
                    onChange={(e) => handleNameChange(e.target.value)}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="generalInfo.slug"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Slug</FormLabel>
                <FormControl>
                  <Input
                    placeholder="district-slug"
                    {...field}
                    onBlur={() => {
                      field.onBlur();
                      handleBlur();
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="generalInfo.type"
            render={({ field }) => (
              <FormItem>
                <FormLabel>District Type</FormLabel>
                <Select
                  defaultValue={field.value}
                  onValueChange={(value) => {
                    field.onChange(value);
                    handleBlur();
                  }}
                >
                  <FormControl>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select district type" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {Object.entries(DistrictTypeMap).map(([value, label]) => (
                      <SelectItem key={value} value={value}>
                        {label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="generalInfo.website"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Website</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    onBlur={() => {
                      field.onBlur();
                      handleBlur();
                    }}
                    placeholder="https://district.edu"
                    type="url"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="generalInfo.ncesId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>NCES ID</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Enter NCES ID"
                    {...field}
                    onBlur={() => {
                      field.onBlur();
                      handleBlur();
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="generalInfo.stateId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>State ID</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Enter state ID"
                    {...field}
                    onBlur={() => {
                      field.onBlur();
                      handleBlur();
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="generalInfo.county"
            render={({ field }) => (
              <FormItem>
                <FormLabel>County</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Enter county"
                    {...field}
                    onBlur={() => {
                      field.onBlur();
                      handleBlur();
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="generalInfo.numSchools"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Number of Schools</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    onBlur={() => {
                      field.onBlur();
                      handleBlur();
                    }}
                    onChange={(e) => {
                      const value = e.target.value;
                      field.onChange(
                        value === '' ? undefined : Number.parseInt(value, 10)
                      );
                    }}
                    placeholder="Enter number of schools"
                    type="number"
                    value={field.value ?? ''}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="generalInfo.numStudents"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Number of Students</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    onBlur={() => {
                      field.onBlur();
                      handleBlur();
                    }}
                    onChange={(e) => {
                      const value = e.target.value;
                      field.onChange(
                        value === '' ? undefined : Number.parseInt(value, 10)
                      );
                    }}
                    placeholder="Enter number of students"
                    type="number"
                    value={field.value ?? ''}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="generalInfo.invoiceEmail"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Invoice Email</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    onBlur={() => {
                      field.onBlur();
                      handleBlur();
                    }}
                    placeholder="Enter invoice email"
                    type="email"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>
    );
  }
);
