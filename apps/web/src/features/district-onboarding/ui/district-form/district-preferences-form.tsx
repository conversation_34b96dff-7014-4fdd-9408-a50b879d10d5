'use client';

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
} from '@lilypad/ui/components/form';
import { Switch } from '@lilypad/ui/components/switch';
import { useFormContext } from 'react-hook-form';
import type { CreateDistrictForm } from '../../model/schemas';

export function DistrictPreferencesForm() {
  const { control } = useFormContext<CreateDistrictForm>();

  return (
    <div className="space-y-6">
      <div className="space-y-6">
        <FormField
          control={control}
          name="preferences.chromebookSetup"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel>Chromebook for evaluations set-up</FormLabel>
                <FormDescription>
                  Receive Chromebook for evaluations set-up and support.
                </FormDescription>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="preferences.quietSpaceForEvaluation"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel>
                  Quiet space for evaluation to take place at schools
                </FormLabel>
                <FormDescription>
                  Provide a quiet space for evaluations to take place at
                  schools.
                </FormDescription>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="preferences.internetStability"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel>Internet stability at school buildings</FormLabel>
                <FormDescription>
                  Ensure reliable internet connectivity at school buildings for
                  evaluations.
                </FormDescription>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />
      </div>
    </div>
  );
}
