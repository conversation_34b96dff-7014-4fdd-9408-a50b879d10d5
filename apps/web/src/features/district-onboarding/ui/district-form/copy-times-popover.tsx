'use client';

import { But<PERSON> } from '@lilypad/ui/components/button';
import { Checkbox } from '@lilypad/ui/components/checkbox';
import { If } from '@lilypad/ui/components/if';
import { Label } from '@lilypad/ui/components/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@lilypad/ui/components/popover';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@lilypad/ui/components/tooltip';
import { CopyIcon } from 'lucide-react';
import { useState } from 'react';
import { useFormContext } from 'react-hook-form';
import type { CreateDistrictForm } from '../../model/schemas';
import type { DayConfig, DayOfWeek } from '../../model/types';

interface CopyTimesPopoverProps {
  sourceDayKey: DayOfWeek;
  enabledDays: DayConfig[];
}

export function CopyTimesPopover({
  sourceDayKey,
  enabledDays,
}: CopyTimesPopoverProps) {
  const form = useFormContext<CreateDistrictForm>();
  const [selectedDays, setSelectedDays] = useState<DayOfWeek[]>([]);
  const [isOpen, setIsOpen] = useState(false);

  const isEnabled = form.watch(
    `availabilities.availabilitySchedule.${sourceDayKey}.enabled` as const
  );

  const handleDayToggle = (dayKey: DayOfWeek, checked: boolean) => {
    setSelectedDays((prev) =>
      checked ? [...prev, dayKey] : prev.filter((key) => key !== dayKey)
    );
  };

  const handleSelectAll = () => {
    const allDayKeys = enabledDays.map((day) => day.key);
    setSelectedDays(allDayKeys);
  };

  const handleDeselectAll = () => {
    setSelectedDays([]);
  };

  const areAllSelected = selectedDays.length === enabledDays.length;

  const copyTimes = () => {
    const sourceSlots =
      form.getValues(
        `availabilities.availabilitySchedule.${sourceDayKey}.slots` as const
      ) || [];
    const sourceEnabled = form.getValues(
      `availabilities.availabilitySchedule.${sourceDayKey}.enabled` as const
    );

    for (const dayKey of selectedDays) {
      // Copy enabled state
      form.setValue(
        `availabilities.availabilitySchedule.${dayKey}.enabled` as const,
        sourceEnabled
      );

      // Copy time slots
      if (sourceEnabled && sourceSlots.length > 0) {
        form.setValue(
          `availabilities.availabilitySchedule.${dayKey}.slots` as const,
          [...sourceSlots]
        );
      }
    }

    setIsOpen(false);
    // Reset selection for next time
    setSelectedDays([]);
  };

  return (
    <Tooltip>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <TooltipTrigger asChild>
          <If condition={isEnabled}>
            <PopoverTrigger asChild>
              <Button
                className="size-8 p-0"
                size="sm"
                type="button"
                variant="ghost"
              >
                <CopyIcon className="size-4" />
              </Button>
            </PopoverTrigger>
          </If>
        </TooltipTrigger>
        <TooltipContent>
          <p>Copy times to</p>
        </TooltipContent>

        <PopoverContent align="end" className="w-80">
          <div className="space-y-4">
            <div>
              <h4 className="font-medium text-sm">Copy times to:</h4>
              <p className="text-muted-foreground text-xs">
                Select which days should have the same schedule
              </p>
            </div>

            <div className="flex items-center space-x-2">
              <Button
                onClick={areAllSelected ? handleDeselectAll : handleSelectAll}
                size="sm"
                type="button"
                variant="outline"
              >
                {areAllSelected ? 'Deselect All' : 'Select All'}
              </Button>
            </div>

            <div className="space-y-3">
              {enabledDays.map((day) => (
                <div key={day.key} className="flex items-center space-x-2">
                  <Checkbox
                    checked={selectedDays.includes(day.key)}
                    id={`copy-${day.key}`}
                    onCheckedChange={(checked) =>
                      handleDayToggle(day.key, !!checked)
                    }
                  />
                  <Label className="text-sm" htmlFor={`copy-${day.key}`}>
                    {day.label}
                  </Label>
                </div>
              ))}
            </div>

            <div className="flex justify-end space-x-2 border-t pt-2">
              <Button
                onClick={() => setIsOpen(false)}
                size="sm"
                type="button"
                variant="outline"
              >
                Cancel
              </Button>
              <Button
                disabled={selectedDays.length === 0}
                onClick={copyTimes}
                size="sm"
                type="button"
              >
                Apply
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </Tooltip>
  );
}
