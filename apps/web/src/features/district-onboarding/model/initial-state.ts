import {
  AddressTypeEnum,
  DistrictTypeEnum,
  RoleEnum,
  SchoolTypeEnum,
} from '@lilypad/db/enums';
import type {
  CreateDistrictForm,
  CreateSchoolForm,
  NewMember,
} from './schemas';
import type {
  DistrictFormData,
  FormState,
  StoreState,
  UIState,
} from './store.types';
import { StepId } from './types';

// District initial state factories
export function getInitialDistrictData(): DistrictFormData {
  const defaultDaySchedule = {
    enabled: true,
    slots: [
      {
        startTime: '08:00',
        endTime: '17:00',
      },
    ],
  };

  return {
    generalInfo: {
      name: '',
      slug: '',
      type: DistrictTypeEnum.UNIFIED_DISTRICT,
      website: '',
      ncesId: '',
      stateId: '',
      county: '',
      numSchools: undefined,
      numStudents: undefined,
      invoiceEmail: '',
    },
    address: {
      type: AddressTypeEnum.PHYSICAL,
      address: '',
      address2: undefined,
      city: '',
      state: '',
      zipcode: '',
    },
    availabilities: {
      availabilitySchedule: {
        monday: { ...defaultDaySchedule },
        tuesday: { ...defaultDaySchedule },
        wednesday: { ...defaultDaySchedule },
        thursday: { ...defaultDaySchedule },
        friday: { ...defaultDaySchedule },
        saturday: { enabled: false, slots: [] },
        sunday: { enabled: false, slots: [] },
      },
      timezone: 'America/New_York',
    },
    preferences: {
      chromebookSetup: false,
      quietSpaceForEvaluation: false,
      internetStability: false,
    },
  };
}

export function getInitialDistrictForm(): CreateDistrictForm {
  const districtData = getInitialDistrictData();
  return {
    generalInfo: districtData.generalInfo,
    address: districtData.address,
    availabilities: districtData.availabilities,
    preferences: districtData.preferences,
  };
}

// School initial state factories
export function getInitialSchoolForm(): CreateSchoolForm {
  return {
    generalInfo: {
      name: '',
      slug: '',
      type: SchoolTypeEnum.REGULAR_PUBLIC_PRIMARY,
      website: '',
      ncesId: '',
    },
    addresses: {
      physical: {
        type: AddressTypeEnum.PHYSICAL,
        address: '',
        address2: '',
        city: '',
        state: '',
        zipcode: '',
      },
      postal: {
        type: AddressTypeEnum.POSTAL,
        address: '',
        address2: '',
        city: '',
        state: '',
        zipcode: '',
        sameAsPhysical: true,
      },
    },
  };
}

// Member initial state factories
export function getInitialMemberForm(): NewMember {
  return {
    firstName: '',
    lastName: '',
    email: '',
    role: RoleEnum.SCHOOL_ADMIN,
    schoolIds: [],
  };
}

// Form state factory
export function getInitialFormState(): FormState {
  return {
    district: getInitialDistrictForm(),
    currentSchool: null,
    currentMember: null,
  };
}

// UI state factory
export function getInitialUIState(): UIState {
  return {
    currentStep: StepId.DISTRICT,
    schoolForm: {
      isOpen: false,
      mode: 'create',
      editingSchool: null,
    },
    memberForm: {
      isOpen: false,
      mode: 'create',
      editingMember: null,
    },
    isSubmitting: false,
  };
}

// Complete store state factory
export function getInitialStoreState(): StoreState {
  return {
    district: getInitialDistrictData(),
    schools: [],
    members: [],
    formData: getInitialFormState(),
    ui: getInitialUIState(),
  };
}

// Reset factories for specific parts
export function resetDistrictState() {
  return {
    district: getInitialDistrictData(),
    formData: {
      ...getInitialFormState(),
      district: getInitialDistrictForm(),
    },
  };
}

export function resetSchoolsState() {
  return {
    schools: [],
    formData: {
      currentSchool: null,
    },
    ui: {
      schoolForm: {
        isOpen: false,
        mode: 'create' as const,
        editingSchool: null,
      },
    },
  };
}

export function resetMembersState() {
  return {
    members: [],
    formData: {
      currentMember: null,
    },
    ui: {
      memberForm: {
        isOpen: false,
        mode: 'create' as const,
        editingMember: null,
      },
    },
  };
}

export function resetUIState() {
  return {
    ui: getInitialUIState(),
  };
}
