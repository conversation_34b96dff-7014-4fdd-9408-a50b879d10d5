import {
  AddressTypeEnum,
  DistrictTypeEnum,
  PreferenceCategoryEnum,
  PreferenceTypeEnum,
  RoleEnum,
  SchoolTypeEnum,
} from '@lilypad/db/enums';
import { z } from 'zod';

export const districtGeneralInfoSchema = z.object({
  name: z.string().min(1, 'District name is required').max(255),
  slug: z.string().min(1, 'Slug is required').max(255),
  type: z.nativeEnum(DistrictTypeEnum),
  website: z.string().url('Please enter a valid website URL'),
  ncesId: z.string().min(1, 'NCES ID is required').max(255),
  stateId: z.string().min(1, 'State ID is required').max(255),
  county: z.string().min(1, 'County is required').max(255),
  numSchools: z
    .number()
    .min(0, 'Number of schools must be 0 or greater')
    .optional(),
  numStudents: z
    .number()
    .min(0, 'Number of students must be 0 or greater')
    .optional(),
  invoiceEmail: z.string().email('Please enter a valid email address'),
});

export const districtAddressSchema = z.object({
  type: z.nativeEnum(AddressTypeEnum),
  address: z.string().min(1, 'Address is required').max(255),
  address2: z.string().max(255).optional(),
  city: z.string().min(1, 'City is required').max(255),
  state: z.string().min(1, 'State is required').max(255),
  zipcode: z.string().min(1, 'ZIP code is required').max(255),
});

const timeSlotSchema = z.object({
  startTime: z.string(),
  endTime: z.string(),
});

const dayScheduleSchema = z.object({
  enabled: z.boolean(),
  slots: z.array(timeSlotSchema).default([]),
});

export const districtAvailabilitiesSchema = z.object({
  availabilitySchedule: z.object({
    monday: dayScheduleSchema,
    tuesday: dayScheduleSchema,
    wednesday: dayScheduleSchema,
    thursday: dayScheduleSchema,
    friday: dayScheduleSchema,
    saturday: dayScheduleSchema,
    sunday: dayScheduleSchema,
  }),
  timezone: z.string().default('America/New_York'),
});

export const districtPreferencesSchema = z.object({
  chromebookSetup: z.boolean(),
  quietSpaceForEvaluation: z.boolean(),
  internetStability: z.boolean(),
});

// Preference mapping for database storage
export const PREFERENCE_MAPPINGS = {
  chromebookSetup: {
    category: PreferenceCategoryEnum.EVALUATION,
    key: 'chromebook_setup',
    type: PreferenceTypeEnum.BOOLEAN,
  },
  quietSpaceForEvaluation: {
    category: PreferenceCategoryEnum.EVALUATION,
    key: 'quiet_space_for_evaluation',
    type: PreferenceTypeEnum.BOOLEAN,
  },
  internetStability: {
    category: PreferenceCategoryEnum.EVALUATION,
    key: 'internet_stability',
    type: PreferenceTypeEnum.BOOLEAN,
  },
} as const;

export const createDistrictSchema = z.object({
  generalInfo: districtGeneralInfoSchema,
  address: districtAddressSchema,
  availabilities: districtAvailabilitiesSchema,
  preferences: districtPreferencesSchema,
});

export type DistrictGeneralInfoForm = z.infer<typeof districtGeneralInfoSchema>;
export type DistrictAddressForm = z.infer<typeof districtAddressSchema>;
export type DistrictAvailabilitiesForm = z.infer<
  typeof districtAvailabilitiesSchema
>;
export type DistrictPreferencesForm = z.infer<typeof districtPreferencesSchema>;
export type CreateDistrictForm = z.infer<typeof createDistrictSchema>;

// School schemas
export const schoolGeneralInfoSchema = z.object({
  name: z.string().min(1, 'School name is required').max(255),
  slug: z.string().min(1, 'Slug is required').max(255),
  type: z.nativeEnum(SchoolTypeEnum),
  website: z
    .string()
    .url('Please enter a valid website URL')
    .optional()
    .or(z.literal('')),
  ncesId: z.string().min(1, 'NCES ID is required').max(255),
});

export const schoolAddressSchema = z.object({
  physical: z.object({
    type: z.nativeEnum(AddressTypeEnum),
    address: z.string().min(1, 'Address is required').max(255),
    address2: z.string().max(255).optional(),
    city: z.string().min(1, 'City is required').max(255),
    state: z.string().min(1, 'State is required').max(255),
    zipcode: z.string().min(1, 'ZIP code is required').max(255),
  }),
  postal: z.object({
    type: z.nativeEnum(AddressTypeEnum),
    address: z.string().min(1, 'Address is required').max(255),
    address2: z.string().max(255).optional(),
    city: z.string().min(1, 'City is required').max(255),
    state: z.string().min(1, 'State is required').max(255),
    zipcode: z.string().min(1, 'ZIP code is required').max(255),
    sameAsPhysical: z.boolean().optional(),
  }),
});

export const createSchoolSchema = z.object({
  generalInfo: schoolGeneralInfoSchema,
  addresses: schoolAddressSchema,
});

export const updateSchoolSchema = createSchoolSchema.extend({
  id: z.string().uuid(),
});

export const newMemberSchema = z.object({
  firstName: z.string().min(1, 'First name is required').max(255),
  lastName: z.string().min(1, 'Last name is required').max(255),
  email: z.string().email('Please enter a valid email address'),
  role: z.nativeEnum(RoleEnum),
  schoolIds: z.array(z.string().min(1, 'School ID is required')),
});

export const updateMemberSchema = newMemberSchema.extend({
  id: z.string().uuid(),
});

// Unified district configuration schema for server action
export const createDistrictConfigurationSchema = z.object({
  district: createDistrictSchema,
  schools: z
    .array(
      createSchoolSchema.extend({
        tempId: z.string(),
      })
    )
    .min(1, 'At least one school is required'),
  members: z
    .array(newMemberSchema)
    .min(1, 'At least one member is required')
    .refine(
      (members) => members.some((m) => m.role === RoleEnum.SPECIAL_ED_DIRECTOR),
      'At least one SPECIAL_ED_DIRECTOR is required'
    ),
});

export type SchoolGeneralInfoForm = z.infer<typeof schoolGeneralInfoSchema>;
export type SchoolAddressForm = z.infer<typeof schoolAddressSchema>;
export type CreateSchoolForm = z.infer<typeof createSchoolSchema>;
export type UpdateSchoolForm = z.infer<typeof updateSchoolSchema>;
export type NewMember = z.infer<typeof newMemberSchema>;
export type UpdateMember = z.infer<typeof updateMemberSchema>;

// New unified types
export type CreateDistrictConfigurationForm = z.infer<
  typeof createDistrictConfigurationSchema
>;
export type PreferenceMapping =
  (typeof PREFERENCE_MAPPINGS)[keyof typeof PREFERENCE_MAPPINGS];
