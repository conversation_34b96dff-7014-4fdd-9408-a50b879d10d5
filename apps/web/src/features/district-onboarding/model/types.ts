import type { RoleEnum, SchoolTypeEnum } from '@lilypad/db/enums';
import type {
  CreateDistrictForm,
  SchoolAddressForm,
  SchoolGeneralInfoForm,
} from './schemas';

// biome-ignore lint/style/noEnum: Not needed
export enum StepId {
  DISTRICT = 'district',
  SCHOOLS = 'schools',
  MEMBERS = 'members',
  REVIEW = 'review',
}

export interface School {
  id: string;
  name: string;
  slug: string;
  type: SchoolTypeEnum;
  website?: string;
  ncesId: string;
  physicalAddress: {
    address: string;
    address2?: string;
    city: string;
    state: string;
    zipcode: string;
  };
  postalAddress: {
    address: string;
    address2?: string;
    city: string;
    state: string;
    zipcode: string;
    sameAsPhysical?: boolean;
  };
  status: 'draft' | 'configured';
}

export interface NewSchool {
  generalInfo: SchoolGeneralInfoForm;
  addresses: SchoolAddressForm;
}

export interface Member {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: RoleEnum;
  status: 'pending' | 'invited' | 'active';
  schoolIds: string[]; // Associated school IDs
}

export interface MemberSchoolAssociation {
  memberId: string;
  schoolIds: string[];
}

export type DayOfWeek =
  keyof CreateDistrictForm['availabilities']['availabilitySchedule'];

export interface TimeSlot {
  startTime: string;
  endTime: string;
}

export interface DayConfig {
  key: DayOfWeek;
  label: string;
}
