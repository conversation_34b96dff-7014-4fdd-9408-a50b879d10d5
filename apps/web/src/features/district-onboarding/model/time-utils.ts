import type { TimeSlot } from './types';

/**
 * Calculates the next time slot based on existing slots
 */
export function calculateNextTimeSlot(currentSlots: TimeSlot[]): TimeSlot {
  // Default time slot
  let newStartTime = '09:00';
  let newEndTime = '10:00';

  if (currentSlots.length > 0) {
    const lastSlot = currentSlots.at(-1);
    if (lastSlot?.endTime) {
      const [hours, minutes] = lastSlot.endTime.split(':').map(Number);
      const nextHour = hours + 1;

      // Ensure we don't go beyond 23:59
      const validNextHour = Math.min(nextHour, 23);
      const validEndHour = Math.min(nextHour + 1, 23);

      newStartTime = `${validNextHour.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
      newEndTime = `${validEndHour.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
    }
  }

  return {
    startTime: newStartTime,
    endTime: newEndTime,
  };
}

/**
 * Validates if a time slot is valid (start time is before end time)
 */
export function isValidTimeSlot(timeSlot: TimeSlot): boolean {
  const startTime = parseTime(timeSlot.startTime);
  const endTime = parseTime(timeSlot.endTime);

  return startTime < endTime;
}

/**
 * Parses time string (HH:MM) to minutes since midnight
 */
function parseTime(timeString: string): number {
  const [hours, minutes] = timeString.split(':').map(Number);
  return hours * 60 + minutes;
}

/**
 * Formats time in 24-hour format to 12-hour format for display
 */
export function formatTimeForDisplay(time24?: string): string {
  if (!time24) {
    return 'N/A';
  }

  const [hours, minutes] = time24.split(':').map(Number);
  const period = hours >= 12 ? 'PM' : 'AM';
  // biome-ignore lint/style/noNestedTernary: Not needed
  const hour12 = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;

  return `${hour12}:${minutes.toString().padStart(2, '0')} ${period}`;
}

/**
 * Checks if two time slots overlap
 */
export function doTimeSlotsOverlap(slot1: TimeSlot, slot2: TimeSlot): boolean {
  const start1 = parseTime(slot1.startTime);
  const end1 = parseTime(slot1.endTime);
  const start2 = parseTime(slot2.startTime);
  const end2 = parseTime(slot2.endTime);

  return start1 < end2 && start2 < end1;
}
