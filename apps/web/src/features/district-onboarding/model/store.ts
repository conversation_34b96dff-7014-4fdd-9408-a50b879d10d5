import { AddressTypeEnum } from '@lilypad/db/enums';
import { generateId } from '@lilypad/ui/data-table/lib/utils';
import { create } from 'zustand';
import {
  getInitialDistrictForm,
  getInitialMemberForm,
  getInitialSchoolForm,
  getInitialStoreState,
} from './initial-state';
import type { CreateSchoolForm, NewMember } from './schemas';
import type { DistrictConfigurationStore } from './store.types';
import type { Member, School, StepId } from './types';

function transformSchoolFormToSchool(schoolForm: CreateSchoolForm): School {
  return {
    id: generateId(),
    name: schoolForm.generalInfo.name,
    slug: schoolForm.generalInfo.slug,
    type: schoolForm.generalInfo.type,
    website: schoolForm.generalInfo.website || '',
    ncesId: schoolForm.generalInfo.ncesId,
    physicalAddress: {
      address: schoolForm.addresses.physical.address,
      address2: schoolForm.addresses.physical.address2,
      city: schoolForm.addresses.physical.city,
      state: schoolForm.addresses.physical.state,
      zipcode: schoolForm.addresses.physical.zipcode,
    },
    postalAddress: {
      address: schoolForm.addresses.postal.address,
      address2: schoolForm.addresses.postal.address2,
      city: schoolForm.addresses.postal.city,
      state: schoolForm.addresses.postal.state,
      zipcode: schoolForm.addresses.postal.zipcode,
      sameAsPhysical: schoolForm.addresses.postal.sameAsPhysical,
    },
    status: 'draft',
  };
}

function transformMemberFormToMember(memberForm: NewMember): Member {
  return {
    id: generateId(),
    email: memberForm.email,
    firstName: memberForm.firstName,
    lastName: memberForm.lastName,
    role: memberForm.role,
    status: 'pending',
    schoolIds: memberForm.schoolIds,
  };
}

// Create the store
export const useDistrictConfigurationStore = create<DistrictConfigurationStore>(
  (set, get) => ({
    // Initial state
    ...getInitialStoreState(),

    // District actions
    updateDistrict: (data) =>
      set((state) => ({
        district: state.district ? { ...state.district, ...data } : null,
      })),

    updateDistrictForm: (data) =>
      set((state) => ({
        formData: {
          ...state.formData,
          district: { ...state.formData.district, ...data },
        },
      })),

    resetDistrict: () =>
      set((state) => ({
        district: null,
        formData: {
          ...state.formData,
          district: getInitialDistrictForm(),
        },
      })),

    // School actions
    addSchool: (schoolForm) =>
      set((state) => {
        const newSchool = transformSchoolFormToSchool(schoolForm);
        return {
          schools: [...state.schools, newSchool],
          formData: {
            ...state.formData,
            currentSchool: null,
          },
          ui: {
            ...state.ui,
            schoolForm: {
              isOpen: false,
              mode: 'create',
              editingSchool: null,
            },
          },
        };
      }),

    updateSchool: (id, updates) =>
      set((state) => ({
        schools: state.schools.map((school) =>
          school.id === id ? { ...school, ...updates } : school
        ),
        formData: {
          ...state.formData,
          currentSchool: null,
        },
        ui: {
          ...state.ui,
          schoolForm: {
            isOpen: false,
            mode: 'create',
            editingSchool: null,
          },
        },
      })),

    removeSchool: (id) =>
      set((state) => ({
        schools: state.schools.filter((school) => school.id !== id),
        // Also remove school from member associations
        members: state.members.map((member) => ({
          ...member,
          schoolIds: member.schoolIds.filter((schoolId) => schoolId !== id),
        })),
      })),

    openSchoolForm: (schoolId) =>
      set((state) => {
        const editingSchool = schoolId
          ? state.schools.find((school) => school.id === schoolId)
          : null;

        return {
          ui: {
            ...state.ui,
            schoolForm: {
              isOpen: true,
              mode: schoolId ? 'edit' : 'create',
              editingSchool: editingSchool || null,
            },
          },
          formData: {
            ...state.formData,
            currentSchool: editingSchool
              ? {
                  generalInfo: {
                    name: editingSchool.name,
                    slug: editingSchool.slug,
                    type: editingSchool.type,
                    website: editingSchool.website || '',
                    ncesId: editingSchool.ncesId,
                  },
                  addresses: {
                    physical: {
                      type: AddressTypeEnum.PHYSICAL,
                      address: editingSchool.physicalAddress.address,
                      address2: editingSchool.physicalAddress.address2,
                      city: editingSchool.physicalAddress.city,
                      state: editingSchool.physicalAddress.state,
                      zipcode: editingSchool.physicalAddress.zipcode,
                    },
                    postal: {
                      type: AddressTypeEnum.POSTAL,
                      address: editingSchool.postalAddress.address,
                      address2: editingSchool.postalAddress.address2,
                      city: editingSchool.postalAddress.city,
                      state: editingSchool.postalAddress.state,
                      zipcode: editingSchool.postalAddress.zipcode,
                      sameAsPhysical:
                        editingSchool.postalAddress.sameAsPhysical,
                    },
                  },
                }
              : getInitialSchoolForm(),
          },
        };
      }),

    closeSchoolForm: () =>
      set((state) => ({
        ui: {
          ...state.ui,
          schoolForm: {
            isOpen: false,
            mode: 'create',
            editingSchool: null,
          },
        },
        formData: {
          ...state.formData,
          currentSchool: null,
        },
      })),

    updateCurrentSchoolForm: (data) =>
      set((state) => ({
        formData: {
          ...state.formData,
          currentSchool: state.formData.currentSchool
            ? { ...state.formData.currentSchool, ...data }
            : null,
        },
      })),

    resetCurrentSchoolForm: () =>
      set((state) => ({
        formData: {
          ...state.formData,
          currentSchool: getInitialSchoolForm(),
        },
      })),

    // Member actions
    addMember: (memberForm) =>
      set((state) => {
        const newMember = transformMemberFormToMember(memberForm);
        return {
          members: [...state.members, newMember],
          formData: {
            ...state.formData,
            currentMember: null,
          },
          ui: {
            ...state.ui,
            memberForm: {
              isOpen: false,
              mode: 'create',
              editingMember: null,
            },
          },
        };
      }),

    updateMember: (id, updates) =>
      set((state) => ({
        members: state.members.map((member) =>
          member.id === id ? { ...member, ...updates } : member
        ),
        formData: {
          ...state.formData,
          currentMember: null,
        },
        ui: {
          ...state.ui,
          memberForm: {
            isOpen: false,
            mode: 'create',
            editingMember: null,
          },
        },
      })),

    removeMember: (id) =>
      set((state) => ({
        members: state.members.filter((member) => member.id !== id),
      })),

    openMemberForm: (memberId) =>
      set((state) => {
        const editingMember = memberId
          ? state.members.find((member) => member.id === memberId)
          : null;

        return {
          ui: {
            ...state.ui,
            memberForm: {
              isOpen: true,
              mode: memberId ? 'edit' : 'create',
              editingMember: editingMember || null,
            },
          },
          formData: {
            ...state.formData,
            currentMember: editingMember
              ? {
                  firstName: editingMember.firstName,
                  lastName: editingMember.lastName,
                  email: editingMember.email,
                  role: editingMember.role,
                  schoolIds: editingMember.schoolIds,
                }
              : getInitialMemberForm(),
          },
        };
      }),

    closeMemberForm: () =>
      set((state) => ({
        ui: {
          ...state.ui,
          memberForm: {
            isOpen: false,
            mode: 'create',
            editingMember: null,
          },
        },
        formData: {
          ...state.formData,
          currentMember: null,
        },
      })),

    updateCurrentMemberForm: (data) =>
      set((state) => ({
        formData: {
          ...state.formData,
          currentMember: state.formData.currentMember
            ? { ...state.formData.currentMember, ...data }
            : null,
        },
      })),

    resetCurrentMemberForm: () =>
      set((state) => ({
        formData: {
          ...state.formData,
          currentMember: getInitialMemberForm(),
        },
      })),

    // Navigation actions
    setCurrentStep: (step) =>
      set((state) => ({
        ui: {
          ...state.ui,
          currentStep: step,
        },
      })),

    goToNextStep: () =>
      set((state) => {
        const steps = ['district', 'schools', 'members', 'review'] as StepId[];
        const currentIndex = steps.indexOf(state.ui.currentStep);
        const nextStep = steps[currentIndex + 1];

        if (nextStep && get().canNavigateToStep(nextStep)) {
          return {
            ui: {
              ...state.ui,
              currentStep: nextStep,
            },
          };
        }
        return state;
      }),

    goToPreviousStep: () =>
      set((state) => {
        const steps = ['district', 'schools', 'members', 'review'] as StepId[];
        const currentIndex = steps.indexOf(state.ui.currentStep);
        const prevStep = steps[currentIndex - 1];

        if (prevStep) {
          return {
            ui: {
              ...state.ui,
              currentStep: prevStep,
            },
          };
        }
        return state;
      }),

    canNavigateToStep: (step) => {
      const districtComplete = get().isDistrictComplete();
      const schoolsComplete = get().isSchoolsComplete();
      const membersComplete = get().isMembersComplete();

      switch (step) {
        case 'district':
          return true;
        case 'schools':
          return districtComplete;
        case 'members':
          return districtComplete && schoolsComplete;
        case 'review':
          return districtComplete && schoolsComplete && membersComplete;
        default:
          return false;
      }
    },

    // Global actions
    resetAll: () => set(() => getInitialStoreState()),

    setSubmitting: (isSubmitting) =>
      set((state) => ({
        ui: {
          ...state.ui,
          isSubmitting,
        },
      })),

    // Computed selectors
    isDistrictComplete: () => {
      const { formData } = get();
      const district = formData.district;
      if (!district) {
        return false;
      }

      const { generalInfo, address, availabilities } = district;

      // Check if at least one day is enabled
      const hasEnabledDay = Object.values(
        availabilities.availabilitySchedule
      ).some((day) => day.enabled);

      return !!(
        generalInfo.name &&
        generalInfo.type &&
        generalInfo.county &&
        address.address &&
        address.city &&
        address.state &&
        address.zipcode &&
        availabilities.timezone &&
        hasEnabledDay
      );
    },

    isSchoolsComplete: () => {
      const { schools } = get();
      return schools.length > 0;
    },

    isMembersComplete: () => {
      const { members } = get();
      return members.length > 0;
    },

    canProceedToReview: () => {
      const store = get();
      return (
        store.isDistrictComplete() &&
        store.isSchoolsComplete() &&
        store.isMembersComplete()
      );
    },

    isCurrentStepComplete: () => {
      const { ui } = get();
      const store = get();

      switch (ui.currentStep) {
        case 'district':
          return store.isDistrictComplete();
        case 'schools':
          return store.isSchoolsComplete();
        case 'members':
          return store.isMembersComplete();
        case 'review':
          return store.canProceedToReview();
        default:
          return false;
      }
    },

    getSchoolById: (id) => {
      const { schools } = get();
      return schools.find((school) => school.id === id);
    },

    getMemberById: (id) => {
      const { members } = get();
      return members.find((member) => member.id === id);
    },

    getMembersBySchoolId: (schoolId) => {
      const { members } = get();
      return members.filter((member) => member.schoolIds.includes(schoolId));
    },
  })
);

export const selectDistrict = (state: DistrictConfigurationStore) =>
  state.district;
export const selectSchools = (state: DistrictConfigurationStore) =>
  state.schools;
export const selectMembers = (state: DistrictConfigurationStore) =>
  state.members;
export const selectCurrentStep = (state: DistrictConfigurationStore) =>
  state.ui.currentStep;
export const selectFormData = (state: DistrictConfigurationStore) =>
  state.formData;
export const selectUI = (state: DistrictConfigurationStore) => state.ui;
