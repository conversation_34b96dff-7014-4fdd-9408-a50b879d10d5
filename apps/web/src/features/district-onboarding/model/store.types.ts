import type { AddressTypeEnum, DistrictTypeEnum } from '@lilypad/db/enums';
import type {
  CreateDistrictForm,
  CreateSchoolForm,
  NewMember,
} from './schemas';
import type { Member, School, StepId } from './types';

// Core data interfaces
export interface DistrictFormData {
  generalInfo: {
    name: string;
    slug: string;
    type: DistrictTypeEnum;
    website: string;
    ncesId: string;
    stateId: string;
    county: string;
    numSchools?: number;
    numStudents?: number;
    invoiceEmail: string;
  };
  address: {
    type: AddressTypeEnum;
    address: string;
    address2?: string;
    city: string;
    state: string;
    zipcode: string;
  };
  availabilities: {
    availabilitySchedule: {
      monday: {
        enabled: boolean;
        slots: Array<{
          startTime: string;
          endTime: string;
        }>;
      };
      tuesday: {
        enabled: boolean;
        slots: Array<{
          startTime: string;
          endTime: string;
        }>;
      };
      wednesday: {
        enabled: boolean;
        slots: Array<{
          startTime: string;
          endTime: string;
        }>;
      };
      thursday: {
        enabled: boolean;
        slots: Array<{
          startTime: string;
          endTime: string;
        }>;
      };
      friday: {
        enabled: boolean;
        slots: Array<{
          startTime: string;
          endTime: string;
        }>;
      };
      saturday: {
        enabled: boolean;
        slots: Array<{
          startTime: string;
          endTime: string;
        }>;
      };
      sunday: {
        enabled: boolean;
        slots: Array<{
          startTime: string;
          endTime: string;
        }>;
      };
    };
    timezone: string;
  };
  preferences: {
    chromebookSetup: boolean;
    quietSpaceForEvaluation: boolean;
    internetStability: boolean;
  };
}

// Form state interfaces
export interface FormState {
  district: CreateDistrictForm;
  currentSchool: CreateSchoolForm | null;
  currentMember: NewMember | null;
}

// UI state interfaces
export interface SchoolFormState {
  isOpen: boolean;
  mode: 'create' | 'edit';
  editingSchool: School | null;
}

export interface MemberFormState {
  isOpen: boolean;
  mode: 'create' | 'edit';
  editingMember: Member | null;
}

export interface UIState {
  currentStep: StepId;
  schoolForm: SchoolFormState;
  memberForm: MemberFormState;
  isSubmitting: boolean;
}

// Store action interfaces
export interface DistrictActions {
  updateDistrict: (data: Partial<DistrictFormData>) => void;
  updateDistrictForm: (data: Partial<CreateDistrictForm>) => void;
  resetDistrict: () => void;
}

export interface SchoolActions {
  addSchool: (school: CreateSchoolForm) => void;
  updateSchool: (id: string, updates: Partial<School>) => void;
  removeSchool: (id: string) => void;
  openSchoolForm: (schoolId?: string) => void;
  closeSchoolForm: () => void;
  updateCurrentSchoolForm: (data: Partial<CreateSchoolForm>) => void;
  resetCurrentSchoolForm: () => void;
}

export interface MemberActions {
  addMember: (member: NewMember) => void;
  updateMember: (id: string, updates: Partial<Member>) => void;
  removeMember: (id: string) => void;
  openMemberForm: (memberId?: string) => void;
  closeMemberForm: () => void;
  updateCurrentMemberForm: (data: Partial<NewMember>) => void;
  resetCurrentMemberForm: () => void;
}

export interface NavigationActions {
  setCurrentStep: (step: StepId) => void;
  goToNextStep: () => void;
  goToPreviousStep: () => void;
  canNavigateToStep: (step: StepId) => boolean;
}

export interface GlobalActions {
  resetAll: () => void;
  setSubmitting: (isSubmitting: boolean) => void;
}

// Computed selectors interfaces
export interface ComputedSelectors {
  isDistrictComplete: () => boolean;
  isSchoolsComplete: () => boolean;
  isMembersComplete: () => boolean;
  canProceedToReview: () => boolean;
  isCurrentStepComplete: () => boolean;
  getSchoolById: (id: string) => School | undefined;
  getMemberById: (id: string) => Member | undefined;
  getMembersBySchoolId: (schoolId: string) => Member[];
}

// Main store interface
export interface DistrictConfigurationStore
  extends DistrictActions,
    SchoolActions,
    MemberActions,
    NavigationActions,
    GlobalActions,
    ComputedSelectors {
  // Core state
  district: DistrictFormData | null;
  schools: School[];
  members: Member[];

  // Form state
  formData: FormState;

  // UI state
  ui: UIState;
}

// Store creation helpers
export type StoreState = Pick<
  DistrictConfigurationStore,
  'district' | 'schools' | 'members' | 'formData' | 'ui'
>;

export type StoreActions = Omit<
  DistrictConfigurationStore,
  'district' | 'schools' | 'members' | 'formData' | 'ui'
>;
