import { TestCategoryEnum } from '@lilypad/db/schema';

export interface TestCategoryConstant {
  name: string;
  categoryType: TestCategoryEnum;
  description: string;
  sortOrder: number;
  isActive: boolean;
}

export const TEST_CATEGORIES: TestCategoryConstant[] = [
  {
    name: 'Cognitive Assessment',
    categoryType: TestCategoryEnum.COGNITIVE_ASSESSMENT,
    description:
      'Tests measuring intellectual ability, cognitive skills, and related cognitive processes',
    sortOrder: 1,
    isActive: true,
  },
  {
    name: 'Academic Achievement',
    categoryType: TestCategoryEnum.ACADEMIC_ACHIEVEMENT,
    description:
      'Tests measuring academic skills in reading, writing, mathematics, and related areas',
    sortOrder: 2,
    isActive: true,
  },
  {
    name: 'Social-Emotional Assessment',
    categoryType: TestCategoryEnum.SOCIAL_EMOTIONAL_ASSESSMENT,
    description:
      'Tests measuring emotional functioning, social skills, and behavioral patterns',
    sortOrder: 3,
    isActive: true,
  },
  {
    name: 'Neuropsychological Assessment',
    categoryType: TestCategoryEnum.NEUROPSYCHOLOGICAL_ASSESSMENT,
    description:
      'Tests measuring specific cognitive functions and neuropsychological processes',
    sortOrder: 4,
    isActive: true,
  },
  {
    name: 'Adaptive Behavior',
    categoryType: TestCategoryEnum.ADAPTIVE_BEHAVIOR,
    description:
      'Tests measuring everyday adaptive functioning and life skills',
    sortOrder: 5,
    isActive: true,
  },
];

export const TEST_CATEGORY_BY_TYPE = TEST_CATEGORIES.reduce(
  (acc, category) => {
    acc[category.categoryType] = category;
    return acc;
  },
  {} as Record<TestCategoryEnum, TestCategoryConstant>
);

export const TEST_CATEGORY_BY_NAME = TEST_CATEGORIES.reduce(
  (acc, category) => {
    acc[category.name] = category;
    return acc;
  },
  {} as Record<string, TestCategoryConstant>
);
