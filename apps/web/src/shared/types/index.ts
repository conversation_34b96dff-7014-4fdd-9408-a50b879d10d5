import type { DatabaseType } from '@lilypad/db/client';
import type { RoleEnum } from '@lilypad/db/schema';
import type { UserIdentity } from '@lilypad/supabase';
import type { Supabase } from '@lilypad/supabase/types';

export interface UserRole {
  id: string;
  name: RoleEnum;
  userId: string;
}

export interface UserPermission {
  id: string;
  name: string;
  roleId: string;
  roleName: RoleEnum;
}

export interface UserRolePermissions {
  userId: string;
  roles: UserRole[];
  permissions: UserPermission[];
}

// Association types
export interface UserDistrict {
  districtId: string;
  userId: string;
  district?: {
    id: string;
    name: string;
    slug: string;
  };
}

export interface UserSchool {
  schoolId: string;
  userId: string;
  school?: {
    id: string;
    name: string;
    districtId: string;
  };
}

// Single comprehensive User type
export interface User {
  // Basic info
  id: string;
  email: string;
  avatar: string | null;
  fullName: string;
  isOnboarded: boolean;
  identities: UserIdentity[] | undefined;

  // Associations
  userDistricts: UserDistrict[];
  userSchools: UserSchool[];

  // Permissions
  rolePermissions: UserRolePermissions;
}

// Server-side context (only used in server components)
export interface AuthContext {
  user: User;
  db: DatabaseType;
  supabase: Supabase;
}
