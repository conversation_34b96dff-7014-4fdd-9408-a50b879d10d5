import { unstable_cache } from 'next/cache';
import { env } from '@/env';

// biome-ignore lint/style/noEnum: Can be used as a type
export enum UserCacheKey {
  Profile = 'Profile',
  Notifications = 'Notifications',
  NotificationPreferences = 'NotificationPreferences',
  MultiFactorAuthentication = 'MultiFactorAuthentication',
}

// biome-ignore lint/style/noEnum: Can be used as a type
export enum CaseCacheKey {
  All = 'All',
  ByStudentId = 'ByStudentId',
}

// biome-ignore lint/style/noEnum: Can be used as a type
export enum StudentCacheKey {
  Profile = 'Profile',
}

export class Caching {
  private readonly USER_PREFIX = 'user';
  private readonly STUDENT_PREFIX = 'student';
  private readonly CASE_PREFIX = 'case';
  private readonly CASE_BY_USER_ACCESS_PREFIX = 'case-by-user-access';

  private joinKeyParts(...parts: string[]): string[] {
    return parts.filter((part) => part.length > 0);
  }

  private joinTagParts(...parts: string[]): string {
    return parts.filter((part) => part.length > 0).join(':');
  }

  createUserKeyParts(
    key: UserCacheKey,
    userId: string,
    ...additionalKeyParts: string[]
  ): string[] {
    if (!userId) {
      throw new Error('User ID cannot be empty');
    }
    return this.joinKeyParts(
      this.USER_PREFIX,
      userId,
      UserCacheKey[key].toLowerCase(),
      ...additionalKeyParts
    );
  }

  createUserTag(
    key: UserCacheKey,
    userId: string,
    ...additionalTagParts: string[]
  ): string {
    if (!userId) {
      throw new Error('User ID cannot be empty');
    }
    return this.joinTagParts(
      this.USER_PREFIX,
      userId,
      UserCacheKey[key].toLowerCase(),
      ...additionalTagParts
    );
  }

  createStudentKeyParts(
    key: StudentCacheKey,
    studentId: string,
    ...additionalKeyParts: string[]
  ): string[] {
    return this.joinKeyParts(
      this.STUDENT_PREFIX,
      studentId,
      StudentCacheKey[key].toLowerCase(),
      ...additionalKeyParts
    );
  }

  createStudentTag(
    key: StudentCacheKey,
    studentId: string,
    ...additionalTagParts: string[]
  ): string {
    return this.joinTagParts(
      this.STUDENT_PREFIX,
      studentId,
      StudentCacheKey[key].toLowerCase(),
      ...additionalTagParts
    );
  }

  createStudentCasesKeyParts(
    studentId: string,
    ...additionalKeyParts: string[]
  ): string[] {
    return this.joinKeyParts(
      this.CASE_PREFIX,
      studentId,
      CaseCacheKey.ByStudentId.toLowerCase(),
      ...additionalKeyParts
    );
  }

  createStudentCasesTag(
    studentId: string,
    ...additionalTagParts: string[]
  ): string {
    return this.joinTagParts(
      this.CASE_PREFIX,
      studentId,
      CaseCacheKey.ByStudentId.toLowerCase(),
      ...additionalTagParts
    );
  }

  createCasesByUserAccessKeyParts(
    userId: string,
    ...additionalKeyParts: string[]
  ): string[] {
    return this.joinKeyParts(
      this.CASE_BY_USER_ACCESS_PREFIX,
      userId,
      CaseCacheKey.ByStudentId.toLowerCase(),
      ...additionalKeyParts
    );
  }

  createCasesByUserAccessTag(
    userId: string,
    ...additionalTagParts: string[]
  ): string {
    return this.joinTagParts(
      this.CASE_BY_USER_ACCESS_PREFIX,
      userId,
      CaseCacheKey.ByStudentId.toLowerCase(),
      ...additionalTagParts
    );
  }

  createCaseKeyParts(
    key: CaseCacheKey,
    caseId: string,
    ...additionalKeyParts: string[]
  ): string[] {
    return this.joinKeyParts(
      this.CASE_PREFIX,
      caseId,
      CaseCacheKey[key].toLowerCase(),
      ...additionalKeyParts
    );
  }

  createCaseTag(
    key: CaseCacheKey,
    caseId: string,
    ...additionalTagParts: string[]
  ): string {
    return this.joinTagParts(
      this.CASE_PREFIX,
      caseId,
      CaseCacheKey[key].toLowerCase(),
      ...additionalTagParts
    );
  }
}

export const caching = new Caching();

export const defaultRevalidateTimeInSeconds =
  env.ENVIRONMENT === 'production' ? 3600 : 120;

type CacheOptions = {
  revalidate?: number | false;
  tags?: string[];
};

/**
 * A wrapper around Next.js unstable_cache that provides better TypeScript support
 * and consistent caching patterns across the application.
 */

// biome-ignore lint/suspicious/noExplicitAny: <explanation>
export function cache<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  keyParts: string[],
  options: CacheOptions = {}
): T {
  const { revalidate = 3600, tags = [] } = options;

  return unstable_cache(fn, keyParts, {
    revalidate,
    tags: [...tags, ...keyParts],
  }) as T;
}

export function createCacheKey(...parts: (string | number)[]): string {
  return parts.filter(Boolean).join(':');
}

export function invalidateCache(tags: string[]): void {
  // This would be implemented with revalidateTag when available
  console.warn('Cache invalidation not yet implemented:', tags);
}

export const CACHE_DURATIONS = {
  SHORT: 60, // 1 minute
  MEDIUM: 300, // 5 minutes
  LONG: 3600, // 1 hour
  VERY_LONG: 86_400, // 24 hours
} as const;

export const CACHE_TAGS = {
  USERS: 'users',
  DISTRICTS: 'districts',
} as const;
