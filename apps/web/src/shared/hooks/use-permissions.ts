'use client';

import { RoleEnum } from '@lilypad/db/enums';
import { useMemo } from 'react';
import { useUser } from '@/shared/contexts/user-context';

export function useRole(role: RoleEnum | RoleEnum[] | null) {
  const { user } = useUser();

  return useMemo(() => {
    if (!(user?.rolePermissions && role)) {
      return { hasRole: false, roles: [] };
    }

    const roles = user.rolePermissions.roles;
    const targetRoles = Array.isArray(role) ? role : [role];
    const hasRole = roles.some((userRole) =>
      targetRoles.includes(userRole.name)
    );

    return { hasRole, roles };
  }, [user?.rolePermissions, role]);
}

export function usePermission(permission: string | string[] | null) {
  const { user } = useUser();

  return useMemo(() => {
    if (!(user?.rolePermissions && permission)) {
      return { hasPermission: false, permissions: [] };
    }

    const permissions = user.rolePermissions.permissions;
    const targetPermissions = Array.isArray(permission)
      ? permission
      : [permission];
    const hasPermission = permissions.some((userPerm) =>
      targetPermissions.includes(userPerm.name)
    );

    return { hasPermission, permissions };
  }, [user?.rolePermissions, permission]);
}

export function useAnyPermission(permissions: string[]) {
  const { user } = useUser();

  return useMemo(() => {
    if (!user?.rolePermissions || permissions.length === 0) {
      return { hasAnyPermission: false, matchedPermissions: [] };
    }

    const userPermissions = user.rolePermissions.permissions;
    const matchedPermissions = userPermissions.filter((userPerm) =>
      permissions.includes(userPerm.name)
    );

    return {
      hasAnyPermission: matchedPermissions.length > 0,
      matchedPermissions,
    };
  }, [user?.rolePermissions, permissions]);
}

export function useAllPermissions(permissions: string[]) {
  const { user } = useUser();

  return useMemo(() => {
    if (!user?.rolePermissions || permissions.length === 0) {
      return { hasAllPermissions: false, missingPermissions: permissions };
    }

    const userPermissionNames = new Set(
      user.rolePermissions.permissions.map((p) => p.name)
    );

    const missingPermissions = permissions.filter(
      (permission) => !userPermissionNames.has(permission)
    );

    return {
      hasAllPermissions: missingPermissions.length === 0,
      missingPermissions,
    };
  }, [user?.rolePermissions, permissions]);
}

export function useResourceAction(
  resource: string | null,
  action: string | null
) {
  const permission = resource && action ? `${resource}:${action}` : null;
  const { hasPermission } = usePermission(permission);

  return useMemo(
    () => ({
      canPerformAction: hasPermission,
      permission,
    }),
    [hasPermission, permission]
  );
}

export function useResourceAccess(resource: string | null) {
  const { user } = useUser();

  return useMemo(() => {
    if (!(user?.rolePermissions && resource)) {
      return { canAccessResource: false, resourcePermissions: [] };
    }

    const resourcePermissions = user.rolePermissions.permissions
      .filter((p) => p.name.startsWith(`${resource}:`))
      .map((p) => p.name.split(':')[1]);

    return {
      canAccessResource: resourcePermissions.length > 0,
      resourcePermissions,
    };
  }, [user?.rolePermissions, resource]);
}

export function useSuperUser() {
  const { hasRole } = useRole(RoleEnum.SUPER_USER);

  return useMemo(
    () => ({
      isSuperUser: hasRole,
    }),
    [hasRole]
  );
}

export function useRoleOrHigher(minimumRole: RoleEnum | null) {
  const { user } = useUser();

  return useMemo(() => {
    if (!(user?.rolePermissions && minimumRole)) {
      return { hasRoleOrHigher: false, currentHighestRole: null };
    }

    // Role hierarchy (highest to lowest)
    const roleHierarchy: RoleEnum[] = [
      RoleEnum.SUPER_USER,
      RoleEnum.SPECIAL_ED_DIRECTOR,
      RoleEnum.CLINICAL_DIRECTOR,
      RoleEnum.SCHOOL_COORDINATOR,
      RoleEnum.SCHOOL_ADMIN,
      RoleEnum.CASE_MANAGER,
      RoleEnum.PSYCHOLOGIST,
      RoleEnum.PROCTOR,
      RoleEnum.ASSISTANT,
    ];

    const userRoles = user.rolePermissions.roles.map((r) => r.name);
    const userRoleIndices = userRoles
      .map((role) => roleHierarchy.indexOf(role))
      .filter((index) => index !== -1);

    const currentHighestRole =
      userRoleIndices.length > 0
        ? roleHierarchy[Math.min(...userRoleIndices)]
        : null;

    const minimumIndex = roleHierarchy.indexOf(minimumRole);
    const hasRoleOrHigher = userRoleIndices.some(
      (index) => index <= minimumIndex
    );

    return { hasRoleOrHigher, currentHighestRole };
  }, [user?.rolePermissions, minimumRole]);
}

export function useUserDistricts() {
  const { user } = useUser();
  return user?.userDistricts || [];
}

export function useUserSchools() {
  const { user } = useUser();
  return user?.userSchools || [];
}
