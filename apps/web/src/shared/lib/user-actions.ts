import { revalidateTag } from 'next/cache';
import type { User } from '@/shared/types';

/**
 * Utility for server actions to update both server cache and return fresh user data
 * for client state updates
 */
export function revalidateUserCache(userId: string) {
  // Invalidate all user-related cache tags
  revalidateTag(`user-profile-${userId}`);
  revalidateTag('user-permissions');
  revalidateTag('user-districts');
  revalidateTag('user-schools');
}

/**
 * Helper type for server actions that update user data
 */
export interface UserActionResult<T = unknown> {
  success: boolean;
  data?: T;
  updatedUser?: User;
  error?: string;
}

/**
 * Usage example in server actions:
 *
 * export async function updateUserProfile(data: FormData) {
 *   try {
 *     // 1. Update database
 *     await updateUserInDatabase(data)
 *
 *     // 2. Invalidate cache and get fresh user data
 *     await revalidateUserCache(userId)
 *     const updatedUser = await getUser()
 *
 *     // 3. Return updated user for client state update
 *     return { success: true, updatedUser }
 *   } catch (error) {
 *     return { success: false, error: error.message }
 *   }
 * }
 *
 * // In client component:
 * const { updateUser } = useUser()
 *
 * const handleSubmit = async (formData) => {
 *   const result = await updateUserProfile(formData)
 *   if (result.success && result.updatedUser) {
 *     updateUser(result.updatedUser) // Update client state
 *   }
 * }
 */
