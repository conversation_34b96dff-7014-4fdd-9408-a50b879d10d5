import { getClientIp } from '@/shared/lib/ip';
import {
  getCurrentUserWithPermissions,
  requireSuperUser,
} from '@lilypad/db/repository/auth';
import { ratelimit } from '@lilypad/kv/ratelimit';
import {
  ForbiddenError,
  GatewayError,
  NotFoundError,
  PreConditionError,
  ValidationError,
} from '@lilypad/shared/errors';
import { logger } from '@lilypad/shared/logger';
import { baseUrl } from '@lilypad/shared/routes';
import { AuthError, PostgrestError } from '@lilypad/supabase';
import {
  createSafeActionClient,
  DEFAULT_SERVER_ERROR_MESSAGE,
} from 'next-safe-action';
import { z } from 'zod';

const IS_SECURE = new URL(baseUrl.App).protocol === 'https:';

export const actionClient = createSafeActionClient({
  handleServerError(e) {
    logger.error({ error: e }, 'Server Error');

    if (
      e instanceof ValidationError ||
      e instanceof ForbiddenError ||
      e instanceof NotFoundError ||
      e instanceof PreConditionError ||
      e instanceof GatewayError ||
      e instanceof AuthError ||
      e instanceof PostgrestError
    ) {
      return e.message;
    }

    return DEFAULT_SERVER_ERROR_MESSAGE;
  },
  defineMetadataSchema() {
    return z.object({
      actionName: z.string(),
    });
  },
});

export const rateLimitedActionClient = actionClient.use(
  async ({ next, metadata }) => {
    const response = next({ ctx: {} });
    if (IS_SECURE) {
      const ip = await getClientIp();
      const uniqueIdentifier = `${ip}-${metadata?.actionName}`;
      const { success, remaining } = await ratelimit.limit(uniqueIdentifier);

      if (!success) {
        logger.error({ remaining }, 'Rate limit exceeded');
        throw new Error('Too many requests');
      }
      return response;
    }
    return response;
  }
);

export const authActionClient = rateLimitedActionClient.use(
  async ({ next }) => {
    const user = await getCurrentUserWithPermissions();
    return next({
      ctx: {
        user,
      },
    });
  }
);

export const superUserActionClient = rateLimitedActionClient.use(
  async ({ next }) => {
    const user = await requireSuperUser();
    return next({
      ctx: {
        user,
      },
    });
  }
);
