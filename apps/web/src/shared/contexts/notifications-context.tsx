'use client';

import React from 'react';
import type { Notification } from '@/entities/notifications/model/types';
import { useStreamNotifications } from '@/features/notifications/model/use-stream-notifications';

interface NotificationsContextState {
  notifications: Notification[];
  setNotifications: (notifications: Notification[]) => void;
  notificationsCount: number;
  setNotificationsCount: (notificationsCount: number) => void;
}

const NotificationsContext = React.createContext<NotificationsContextState>({
  notifications: [],
  setNotifications: () => {
    return;
  },
  notificationsCount: 0,
  setNotificationsCount: () => {
    return;
  },
});

export const NotificationsProvider = ({
  children,
  userId,
  notifications: initialNotifications,
}: React.PropsWithChildren<{
  userId: string;
  notifications: Notification[];
}>) => {
  const [notifications, setNotifications] =
    React.useState<Notification[]>(initialNotifications);
  const [notificationsCount, setNotificationsCount] = React.useState<number>(
    initialNotifications.filter((n) => !(n.isArchived || n.isRead)).length || 0
  );

  useStreamNotifications({
    userId,
    notifications,
    setNotifications,
    setNotificationsCount,
  });

  return (
    <NotificationsContext.Provider
      value={{
        notifications,
        setNotifications,
        notificationsCount,
        setNotificationsCount,
      }}
    >
      {children}
    </NotificationsContext.Provider>
  );
};

export function useNotificationsContext() {
  const context = React.useContext(NotificationsContext);
  if (context === undefined) {
    throw new Error(
      'useNotificationsContext must be used within an NotificationsContext'
    );
  }
  return context;
}
