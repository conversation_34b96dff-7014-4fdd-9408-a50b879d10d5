'use client';

import React, { createContext, useContext, useState } from 'react';
import type { User } from '@/shared/types';

/**
 * User Context Provider
 *
 * This context provides global access to the user data that was fetched server-side
 * in the layout. It does NOT handle client-side data fetching or refreshing.
 *
 * Data Updates:
 * - Server Actions: Use `updateUser()` to sync client state after server updates
 * - Auth Changes: Handled by AuthChangeListener which calls `clearUser()` on sign out
 * - Navigation: Server components re-fetch fresh data automatically
 *
 * @example
 * // In server action:
 * const result = await updateUserInDB(data)
 * revalidateUserCache(userId)
 * const updatedUser = await getUser()
 * return { success: true, updatedUser }
 *
 * // In client component:
 * const { updateUser } = useUser()
 * const result = await serverAction(data)
 * if (result.updatedUser) updateUser(result.updatedUser)
 */

interface UserContextState {
  user: User | null;
  isAuthenticated: boolean;
  updateUser: (user: User | null) => void;
  clearUser: () => void;
}

const UserContext = createContext<UserContextState | null>(null);

interface UserProviderProps {
  children: React.ReactNode;
  initialUser: User | null;
}

export function UserProvider({ children, initialUser }: UserProviderProps) {
  const [user, setUser] = useState<User | null>(initialUser);

  const isAuthenticated = !!user;

  // Simple update function for server actions to update client state
  const updateUser = React.useCallback((updatedUser: User | null) => {
    setUser(updatedUser);
  }, []);

  const clearUser = React.useCallback(() => {
    setUser(null);
  }, []);

  const contextValue = React.useMemo<UserContextState>(
    () => ({
      user,
      isAuthenticated,
      updateUser,
      clearUser,
    }),
    [user, isAuthenticated, updateUser, clearUser]
  );

  return (
    <UserContext.Provider value={contextValue}>{children}</UserContext.Provider>
  );
}

export function useUser(): UserContextState {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
}
