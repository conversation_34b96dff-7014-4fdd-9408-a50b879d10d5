'use client';

import type { RoleEnum } from '@lilypad/db/schema';
import type { ReactNode } from 'react';
import { useUser } from '../contexts/user-context';
import {
  useAllPermissions,
  useAnyPermission,
  usePermission,
  useResourceAccess,
  useResourceAction,
  useRole,
  useRoleOrHigher,
  useSuperUser,
} from '../hooks/use-permissions';

interface AuthGuardProps {
  children: ReactNode;
  fallback?: ReactNode;
}

function DefaultFallback() {
  return (
    <div className="flex items-center justify-center p-4 text-muted-foreground text-sm">
      Access denied
    </div>
  );
}

// Authentication guard
export function AuthGuard({
  children,
  fallback = <DefaultFallback />,
}: AuthGuardProps) {
  const { isAuthenticated } = useUser();

  if (!isAuthenticated) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

// Role-based guard
interface RoleGuardProps extends AuthGuardProps {
  userRole: RoleEnum | RoleEnum[];
}

export function RoleGuard({
  userRole,
  children,
  fallback = <DefaultFallback />,
}: RoleGuardProps) {
  const { hasRole } = useRole(userRole);

  if (!hasRole) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

// Permission-based guard
interface PermissionGuardProps extends AuthGuardProps {
  permission: string | string[];
}

export function PermissionGuard({
  permission,
  children,
  fallback = <DefaultFallback />,
}: PermissionGuardProps) {
  const { hasPermission } = usePermission(permission);

  if (!hasPermission) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

// Any permission guard
interface AnyPermissionGuardProps extends AuthGuardProps {
  permissions: string[];
}

export function AnyPermissionGuard({
  permissions,
  children,
  fallback = <DefaultFallback />,
}: AnyPermissionGuardProps) {
  const { hasAnyPermission } = useAnyPermission(permissions);

  if (!hasAnyPermission) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

// All permissions guard
interface AllPermissionsGuardProps extends AuthGuardProps {
  permissions: string[];
}

export function AllPermissionsGuard({
  permissions,
  children,
  fallback = <DefaultFallback />,
}: AllPermissionsGuardProps) {
  const { hasAllPermissions } = useAllPermissions(permissions);

  if (!hasAllPermissions) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

// Resource action guard
interface ResourceActionGuardProps extends AuthGuardProps {
  resource: string;
  action: string;
}

export function ResourceActionGuard({
  resource,
  action,
  children,
  fallback = <DefaultFallback />,
}: ResourceActionGuardProps) {
  const { canPerformAction } = useResourceAction(resource, action);

  if (!canPerformAction) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

// Resource access guard
interface ResourceAccessGuardProps extends AuthGuardProps {
  resource: string;
}

export function ResourceAccessGuard({
  resource,
  children,
  fallback = <DefaultFallback />,
}: ResourceAccessGuardProps) {
  const { canAccessResource } = useResourceAccess(resource);

  if (!canAccessResource) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

// Super user guard
export function SuperUserGuard({
  children,
  fallback = <DefaultFallback />,
}: AuthGuardProps) {
  const { isSuperUser } = useSuperUser();

  if (!isSuperUser) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

// Role or higher guard
interface RoleOrHigherGuardProps extends AuthGuardProps {
  minimumRole: RoleEnum;
}

export function RoleOrHigherGuard({
  minimumRole,
  children,
  fallback = <DefaultFallback />,
}: RoleOrHigherGuardProps) {
  const { hasRoleOrHigher } = useRoleOrHigher(minimumRole);

  if (!hasRoleOrHigher) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

// Inverse guards (show content when user DOESN'T have permission)

// Show content when user is NOT authenticated
export function UnauthenticatedGuard({
  children,
  fallback = <DefaultFallback />,
}: AuthGuardProps) {
  const { isAuthenticated } = useUser();

  if (isAuthenticated) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

// Show content when user DOESN'T have role
export function NoRoleGuard({
  userRole: role,
  children,
  fallback = <DefaultFallback />,
}: RoleGuardProps) {
  const { hasRole } = useRole(role);

  if (hasRole) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

// Show content when user DOESN'T have permission
export function NoPermissionGuard({
  permission,
  children,
  fallback = <DefaultFallback />,
}: PermissionGuardProps) {
  const { hasPermission } = usePermission(permission);

  if (hasPermission) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}
