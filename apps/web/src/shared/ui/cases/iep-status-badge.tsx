import type { IepStatusEnum } from '@lilypad/db/enums';
import { cn, cva, type VariantProps } from '@lilypad/ui/lib/utils';

const iepStatusBadgeVariants = cva(
  'inline-flex items-center rounded-md border px-2 py-0.5 font-semibold text-xs transition-colors',
  {
    variants: {
      status: {
        ACTIVE:
          'border-green-200 bg-green-100 text-green-800 dark:border-green-700/50 dark:bg-green-800/20 dark:text-green-300',
        INACTIVE:
          'border-gray-200 bg-gray-100 text-gray-800 dark:border-gray-700/50 dark:bg-gray-800/20 dark:text-gray-300',
      },
    },
  }
);

const iepStatusDisplayMap = {
  ACTIVE: 'Active',
  INACTIVE: 'Inactive',
} as const;

interface IepStatusBadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof iepStatusBadgeVariants> {
  status: IepStatusEnum;
}

export function IepStatusBadge({
  className,
  status,
  ...props
}: IepStatusBadgeProps) {
  if (!status) {
    return null;
  }

  const displayName = iepStatusDisplayMap[status];

  return (
    <div
      className={cn(iepStatusBadgeVariants({ status }), className)}
      {...props}
    >
      {displayName}
    </div>
  );
}

export { iepStatusBadgeVariants };
