import { type CaseTypeEnum, CaseTypeEnumMap } from '@lilypad/db/enums';
import { cn, cva, type VariantProps } from '@lilypad/ui/lib/utils';

const caseTypeBadgeVariants = cva(
  'inline-flex items-center rounded-md border px-2 py-0.5 font-semibold text-xs transition-colors',
  {
    variants: {
      type: {
        INITIAL_EVALUATION:
          'border-blue-200 bg-blue-100 text-blue-800 dark:border-blue-700/50 dark:bg-blue-800/20 dark:text-blue-300',
        TRIENNIAL_EVALUATION:
          'border-green-200 bg-green-100 text-green-800 dark:border-green-700/50 dark:bg-green-800/20 dark:text-green-300',
        REEVALUATION:
          'border-yellow-200 bg-yellow-100 text-yellow-800 dark:border-yellow-700/50 dark:bg-yellow-800/20 dark:text-yellow-300',
        INDEPENDENT_EVALUATION:
          'border-purple-200 bg-purple-100 text-purple-800 dark:border-purple-700/50 dark:bg-purple-800/20 dark:text-purple-300',
        CHANGE_OF_PLACEMENT:
          'border-indigo-200 bg-indigo-100 text-indigo-800 dark:border-indigo-700/50 dark:bg-indigo-800/20 dark:text-indigo-300',
        DISCIPLINE_EVALUATION:
          'border-red-200 bg-red-100 text-red-800 dark:border-red-700/50 dark:bg-red-800/20 dark:text-red-300',
        TRANSITION_EVALUATION:
          'border-teal-200 bg-teal-100 text-teal-800 dark:border-teal-700/50 dark:bg-teal-800/20 dark:text-teal-300',
      },
    },
  }
);

interface CaseTypeBadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof caseTypeBadgeVariants> {
  type: CaseTypeEnum;
}

export function CaseTypeBadge({
  className,
  type,
  ...props
}: CaseTypeBadgeProps) {
  if (!type) {
    return null;
  }

  const displayName = CaseTypeEnumMap[type];

  return (
    <div className={cn(caseTypeBadgeVariants({ type }), className)} {...props}>
      {displayName}
    </div>
  );
}

export { caseTypeBadgeVariants };
