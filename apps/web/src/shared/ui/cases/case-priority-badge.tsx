import { type CasePriorityEnum, CasePriorityEnumMap } from '@lilypad/db/enums';
import { cn, cva, type VariantProps } from '@lilypad/ui/lib/utils';

const casePriorityBadgeVariants = cva(
  'inline-flex items-center rounded-md border px-2 py-0.5 font-semibold text-xs transition-colors',
  {
    variants: {
      priority: {
        LOW: 'border-gray-200 bg-gray-100 text-gray-800 dark:border-gray-700/50 dark:bg-gray-800/20 dark:text-gray-300',
        MEDIUM:
          'border-yellow-200 bg-yellow-100 text-yellow-800 dark:border-yellow-700/50 dark:bg-yellow-800/20 dark:text-yellow-300',
        HIGH: 'border-orange-200 bg-orange-100 text-orange-800 dark:border-orange-700/50 dark:bg-orange-800/20 dark:text-orange-300',
        URGENT:
          'border-red-200 bg-red-100 text-red-800 dark:border-red-700/50 dark:bg-red-800/20 dark:text-red-300',
      },
    },
  }
);

interface CasePriorityBadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof casePriorityBadgeVariants> {
  priority: CasePriorityEnum;
}

export function CasePriorityBadge({
  className,
  priority,
  ...props
}: CasePriorityBadgeProps) {
  if (!priority) {
    return null;
  }

  const displayName = CasePriorityEnumMap[priority];

  return (
    <div
      className={cn(casePriorityBadgeVariants({ priority }), className)}
      {...props}
    >
      {displayName}
    </div>
  );
}

export { casePriorityBadgeVariants };
