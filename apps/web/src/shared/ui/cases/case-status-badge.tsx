import type { CaseStatusEnum } from '@lilypad/db/enums';
import { cn, cva, type VariantProps } from '@lilypad/ui/lib/utils';

const caseStatusBadgeVariants = cva(
  'inline-flex items-center rounded-md border px-2 py-0.5 font-semibold text-xs transition-colors',
  {
    variants: {
      status: {
        READY_FOR_EVALUATION:
          'border-blue-200 bg-blue-100 text-blue-800 dark:border-blue-700/50 dark:bg-blue-800/20 dark:text-blue-300',
        EVALUATION_IN_PROGRESS:
          'border-yellow-200 bg-yellow-100 text-yellow-800 dark:border-yellow-700/50 dark:bg-yellow-800/20 dark:text-yellow-300',
        REPORT_IN_PROGRESS:
          'border-orange-200 bg-orange-100 text-orange-800 dark:border-orange-700/50 dark:bg-orange-800/20 dark:text-orange-300',
        AWAITING_MEETING:
          'border-purple-200 bg-purple-100 text-purple-800 dark:border-purple-700/50 dark:bg-purple-800/20 dark:text-purple-300',
        MEETING_COMPLETE:
          'border-green-200 bg-green-100 text-green-800 dark:border-green-700/50 dark:bg-green-800/20 dark:text-green-300',
      },
    },
  }
);

const caseStatusDisplayMap = {
  READY_FOR_EVALUATION: 'Ready for Evaluation',
  EVALUATION_IN_PROGRESS: 'Evaluation in Progress',
  REPORT_IN_PROGRESS: 'Report in Progress',
  AWAITING_MEETING: 'Awaiting Meeting',
  MEETING_COMPLETE: 'Meeting Complete',
} as const;

interface CaseStatusBadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof caseStatusBadgeVariants> {
  status: CaseStatusEnum;
}

export function CaseStatusBadge({
  className,
  status,
  ...props
}: CaseStatusBadgeProps) {
  if (!status) {
    return null;
  }

  const displayName = caseStatusDisplayMap[status];

  return (
    <div
      className={cn(caseStatusBadgeVariants({ status }), className)}
      {...props}
    >
      {displayName}
    </div>
  );
}

export { caseStatusBadgeVariants };
