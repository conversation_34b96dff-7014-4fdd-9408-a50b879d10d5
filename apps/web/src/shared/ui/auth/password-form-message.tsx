import type { Maybe } from '@lilypad/shared';
import {
  MINIMUM_PASSWORD_LENGTH,
  passwordValidator,
} from '@lilypad/supabase/auth';
import { useFormField } from '@lilypad/ui/components/form';
import { cn } from '@lilypad/ui/lib/utils';
import { CircleCheck, InfoIcon } from 'lucide-react';
import { useMemo } from 'react';

export type PasswordFormMessageProps = {
  password: Maybe<string>;
};

export function PasswordFormMessage({
  password,
}: PasswordFormMessageProps): React.JSX.Element {
  const { error, formMessageId } = useFormField();

  const containsLowerAndUpperCase =
    passwordValidator.containsLowerAndUpperCase(password);
  const hasMinimumLength = passwordValidator.hasMinimumLength(password);
  const containsNumber = passwordValidator.containsNumber(password);
  const isPasswordValid =
    containsLowerAndUpperCase && hasMinimumLength && containsNumber;

  const getRequirementToShow = () => {
    if (isPasswordValid) {
      return {
        met: true,
        text: 'All requirements met',
      };
    }
    if (!hasMinimumLength) {
      return {
        met: false,
        text: `${MINIMUM_PASSWORD_LENGTH} or more characters`,
      };
    }
    if (!containsLowerAndUpperCase) {
      return {
        met: false,
        text: 'Uppercase and lowercase letters',
      };
    }
    return {
      met: false,
      text: 'At least one number',
    };
  };

  const requirement = getRequirementToShow();

  const textColor = useMemo(() => {
    if (requirement.met) {
      return 'text-green-700';
    }
    if (error) {
      return 'text-destructive';
    }
    return 'text-muted-foreground';
  }, [requirement.met, error]);

  return (
    <div
      className={cn('flex items-center gap-1.5 px-1 text-xs', textColor)}
      id={formMessageId}
    >
      {requirement.met ? (
        <CircleCheck className="h-3.5 w-3.5" />
      ) : (
        <InfoIcon className="h-3.5 w-3.5" />
      )}
      <p>{requirement.text}</p>
    </div>
  );
}
