import { SessionTypeEnum, SessionTypeEnumMap } from '@lilypad/db/enums';
import { cn, cva, type VariantProps } from '@lilypad/ui/lib/utils';

const sessionTypeBadgeVariants = cva(
  'inline-flex items-center rounded-md border px-2 py-0.5 font-semibold text-xs transition-colors',
  {
    variants: {
      type: {
        [SessionTypeEnum.INITIAL_EVALUATION]:
          'border-purple-200 bg-purple-100 text-purple-800 dark:border-purple-700/50 dark:bg-purple-800/20 dark:text-purple-300',
        [SessionTypeEnum.TRIENNIAL_EVALUATION]:
          'border-blue-200 bg-blue-100 text-blue-800 dark:border-blue-700/50 dark:bg-blue-800/20 dark:text-blue-300',
        [SessionTypeEnum.REEVALUATION]:
          'border-indigo-200 bg-indigo-100 text-indigo-800 dark:border-indigo-700/50 dark:bg-indigo-800/20 dark:text-indigo-300',
        [SessionTypeEnum.INDEPENDENT_EVALUATION]:
          'border-teal-200 bg-teal-100 text-teal-800 dark:border-teal-700/50 dark:bg-teal-800/20 dark:text-teal-300',
        [SessionTypeEnum.PROGRESS_MONITORING]:
          'border-cyan-200 bg-cyan-100 text-cyan-800 dark:border-cyan-700/50 dark:bg-cyan-800/20 dark:text-cyan-300',
        [SessionTypeEnum.DIAGNOSTIC_CLARIFICATION]:
          'border-amber-200 bg-amber-100 text-amber-800 dark:border-amber-700/50 dark:bg-amber-800/20 dark:text-amber-300',
        [SessionTypeEnum.COMPREHENSIVE_EVALUATION]:
          'border-emerald-200 bg-emerald-100 text-emerald-800 dark:border-emerald-700/50 dark:bg-emerald-800/20 dark:text-emerald-300',
      },
    },
  }
);

interface SessionTypeBadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof sessionTypeBadgeVariants> {
  type: SessionTypeEnum;
}

export function SessionTypeBadge({
  type,
  className,
  ...props
}: SessionTypeBadgeProps) {
  if (!type) {
    return null;
  }

  const displayName = SessionTypeEnumMap[type];

  return (
    <div
      className={cn(sessionTypeBadgeVariants({ type }), className)}
      {...props}
    >
      {displayName}
    </div>
  );
}

export { sessionTypeBadgeVariants };
