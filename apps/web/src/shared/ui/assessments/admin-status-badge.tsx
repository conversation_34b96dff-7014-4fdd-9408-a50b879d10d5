import { AdminStatusEnum } from '@lilypad/db/schema/enums';
import { Badge } from '@lilypad/ui/components/badge';
import { type VariantProps, cva } from '@lilypad/ui/lib/utils';
import type React from 'react';

const adminStatusVariants = cva('font-medium text-xs', {
  variants: {
    status: {
      [AdminStatusEnum.PLANNED]:
        'border-blue-200 bg-blue-50 text-blue-700 dark:border-blue-800 dark:bg-blue-950 dark:text-blue-300',
      [AdminStatusEnum.IN_PROGRESS]:
        'border-amber-200 bg-amber-50 text-amber-700 dark:border-amber-800 dark:bg-amber-950 dark:text-amber-300',
      [AdminStatusEnum.COMPLETED]:
        'border-emerald-200 bg-emerald-50 text-emerald-700 dark:border-emerald-800 dark:bg-emerald-950 dark:text-emerald-300',
      [AdminStatusEnum.DISCONTINUED]:
        'border-red-200 bg-red-50 text-red-700 dark:border-red-800 dark:bg-red-950 dark:text-red-300',
      [AdminStatusEnum.INVALID]:
        'border-red-200 bg-red-50 text-red-700 dark:border-red-800 dark:bg-red-950 dark:text-red-300',
      [AdminStatusEnum.PARTIAL]:
        'border-orange-200 bg-orange-50 text-orange-700 dark:border-orange-800 dark:bg-orange-950 dark:text-orange-300',
    },
  },
});

const adminStatusLabels = {
  [AdminStatusEnum.PLANNED]: 'Planned',
  [AdminStatusEnum.IN_PROGRESS]: 'In Progress',
  [AdminStatusEnum.COMPLETED]: 'Completed',
  [AdminStatusEnum.DISCONTINUED]: 'Discontinued',
  [AdminStatusEnum.INVALID]: 'Invalid',
  [AdminStatusEnum.PARTIAL]: 'Partial',
};

interface AdminStatusBadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof adminStatusVariants> {
  status: AdminStatusEnum;
}

export const AdminStatusBadge: React.FC<AdminStatusBadgeProps> = ({
  status,
  className,
  ...props
}) => {
  return (
    <Badge
      className={adminStatusVariants({ status, className })}
      variant="outline"
      {...props}
    >
      {adminStatusLabels[status]}
    </Badge>
  );
};
