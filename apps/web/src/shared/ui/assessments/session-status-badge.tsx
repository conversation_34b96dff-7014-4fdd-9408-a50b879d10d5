import {
  SessionStatusEnum,
  SessionStatusEnumMap,
} from '@lilypad/db/schema/enums';
import { cn, cva, type VariantProps } from '@lilypad/ui/lib/utils';

const sessionStatusBadgeVariants = cva(
  'inline-flex items-center rounded-md border px-2 py-0.5 font-semibold text-xs transition-colors',
  {
    variants: {
      status: {
        [SessionStatusEnum.SCHEDULED]:
          'border-blue-200 bg-blue-100 text-blue-800 dark:border-blue-700/50 dark:bg-blue-800/20 dark:text-blue-300',
        [SessionStatusEnum.IN_PROGRESS]:
          'border-yellow-200 bg-yellow-100 text-yellow-800 dark:border-yellow-700/50 dark:bg-yellow-800/20 dark:text-yellow-300',
        [SessionStatusEnum.COMPLETED]:
          'border-green-200 bg-green-100 text-green-800 dark:border-green-700/50 dark:bg-green-800/20 dark:text-green-300',
        [SessionStatusEnum.CANCELLED]:
          'border-red-200 bg-red-100 text-red-800 dark:border-red-700/50 dark:bg-red-800/20 dark:text-red-300',
        [SessionStatusEnum.RESCHEDULED]:
          'border-orange-200 bg-orange-100 text-orange-800 dark:border-orange-700/50 dark:bg-orange-800/20 dark:text-orange-300',
        [SessionStatusEnum.INCOMPLETE]:
          'border-gray-200 bg-gray-100 text-gray-800 dark:border-gray-700/50 dark:bg-gray-800/20 dark:text-gray-300',
      },
    },
  }
);

interface SessionStatusBadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof sessionStatusBadgeVariants> {
  status: SessionStatusEnum;
}

export function SessionStatusBadge({
  status,
  className,
  ...props
}: SessionStatusBadgeProps) {
  if (!status) {
    return null;
  }

  const displayName = SessionStatusEnumMap[status];

  return (
    <div
      className={cn(sessionStatusBadgeVariants({ status }), className)}
      {...props}
    >
      {displayName}
    </div>
  );
}

export { sessionStatusBadgeVariants };
