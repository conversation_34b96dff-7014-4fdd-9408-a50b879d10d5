import { type SchoolGradeEnum, SchoolGradeEnumMap } from '@lilypad/db/enums';
import { cn, cva, type VariantProps } from '@lilypad/ui/lib/utils';

const schoolGradeBadgeVariants = cva(
  'inline-flex items-center rounded-md border px-2 py-0.5 font-semibold text-xs transition-colors',
  {
    variants: {
      grade: {
        // Early Childhood - Purple theme
        PK: 'border-purple-200 bg-purple-100 text-purple-800 dark:border-purple-700/50 dark:bg-purple-800/20 dark:text-purple-300',
        K: 'border-purple-200 bg-purple-100 text-purple-800 dark:border-purple-700/50 dark:bg-purple-800/20 dark:text-purple-300',

        // Elementary - Lime theme (grades 1-5)
        '1': 'border-lime-200 bg-lime-100 text-lime-800 dark:border-lime-700/50 dark:bg-lime-800/20 dark:text-lime-300',
        '2': 'border-lime-200 bg-lime-100 text-lime-800 dark:border-lime-700/50 dark:bg-lime-800/20 dark:text-lime-300',
        '3': 'border-lime-200 bg-lime-100 text-lime-800 dark:border-lime-700/50 dark:bg-lime-800/20 dark:text-lime-300',
        '4': 'border-lime-200 bg-lime-100 text-lime-800 dark:border-lime-700/50 dark:bg-lime-800/20 dark:text-lime-300',
        '5': 'border-lime-200 bg-lime-100 text-lime-800 dark:border-lime-700/50 dark:bg-lime-800/20 dark:text-lime-300',

        // Middle School - Fuchsia theme (grades 6-8)
        '6': 'border-fuchsia-200 bg-fuchsia-100 text-fuchsia-800 dark:border-fuchsia-700/50 dark:bg-fuchsia-800/20 dark:text-fuchsia-300',
        '7': 'border-fuchsia-200 bg-fuchsia-100 text-fuchsia-800 dark:border-fuchsia-700/50 dark:bg-fuchsia-800/20 dark:text-fuchsia-300',
        '8': 'border-fuchsia-200 bg-fuchsia-100 text-fuchsia-800 dark:border-fuchsia-700/50 dark:bg-fuchsia-800/20 dark:text-fuchsia-300',

        // High School - Indigo theme (grades 9-12)
        '9': 'border-indigo-200 bg-indigo-100 text-indigo-800 dark:border-indigo-700/50 dark:bg-indigo-800/20 dark:text-indigo-300',
        '10': 'border-indigo-200 bg-indigo-100 text-indigo-800 dark:border-indigo-700/50 dark:bg-indigo-800/20 dark:text-indigo-300',
        '11': 'border-indigo-200 bg-indigo-100 text-indigo-800 dark:border-indigo-700/50 dark:bg-indigo-800/20 dark:text-indigo-300',
        '12': 'border-indigo-200 bg-indigo-100 text-indigo-800 dark:border-indigo-700/50 dark:bg-indigo-800/20 dark:text-indigo-300',

        // Special categories - Gray theme
        U: 'border-gray-200 bg-gray-100 text-gray-800 dark:border-gray-700/50 dark:bg-gray-800/20 dark:text-gray-300',
        PG: 'border-indigo-200 bg-indigo-100 text-indigo-800 dark:border-indigo-700/50 dark:bg-indigo-800/20 dark:text-indigo-300',
      },
    },
  }
);

interface SchoolGradeBadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof schoolGradeBadgeVariants> {
  grade: SchoolGradeEnum;
}

export function SchoolGradeBadge({
  className,
  grade,
  ...props
}: SchoolGradeBadgeProps) {
  if (!grade) {
    return null;
  }

  return (
    <div
      className={cn(schoolGradeBadgeVariants({ grade }), className)}
      {...props}
    >
      {SchoolGradeEnumMap[grade]}
    </div>
  );
}

export { schoolGradeBadgeVariants };
