import {
  type EnrollmentStatusEnum,
  EnrollmentStatusEnumMap,
} from '@lilypad/db/enums';
import { cn, cva, type VariantProps } from '@lilypad/ui/lib/utils';

const studentStatusBadgeVariants = cva(
  'inline-flex items-center rounded-md border px-2 py-0.5 font-semibold text-xs transition-colors',
  {
    variants: {
      status: {
        ENROLLED:
          'border-green-200 bg-green-100 text-green-800 dark:border-green-700/50 dark:bg-green-800/20 dark:text-green-300',
        WITHDRAWN:
          'border-red-200 bg-red-100 text-red-800 dark:border-red-700/50 dark:bg-red-800/20 dark:text-red-300',
        TRANSFERRED:
          'border-blue-200 bg-blue-100 text-blue-800 dark:border-blue-700/50 dark:bg-blue-800/20 dark:text-blue-300',
        GRADUATED:
          'border-purple-200 bg-purple-100 text-purple-800 dark:border-purple-700/50 dark:bg-purple-800/20 dark:text-purple-300',
        SUSPENDED:
          'border-orange-200 bg-orange-100 text-orange-800 dark:border-orange-700/50 dark:bg-orange-800/20 dark:text-orange-300',
        EXPELLED:
          'border-red-200 bg-red-100 text-red-800 dark:border-red-700/50 dark:bg-red-800/20 dark:text-red-300',
        DECEASED:
          'border-gray-200 bg-gray-100 text-gray-800 dark:border-gray-700/50 dark:bg-gray-800/20 dark:text-gray-300',
        INACTIVE:
          'border-gray-200 bg-gray-100 text-gray-800 dark:border-gray-700/50 dark:bg-gray-800/20 dark:text-gray-300',
      },
    },
  }
);

interface StudentStatusBadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof studentStatusBadgeVariants> {
  status: EnrollmentStatusEnum;
}

export function StudentStatusBadge({
  className,
  status,
  ...props
}: StudentStatusBadgeProps) {
  if (!status) {
    return null;
  }

  const displayName = EnrollmentStatusEnumMap[status];

  return (
    <div
      className={cn(studentStatusBadgeVariants({ status }), className)}
      {...props}
    >
      {displayName}
    </div>
  );
}

export { studentStatusBadgeVariants };
