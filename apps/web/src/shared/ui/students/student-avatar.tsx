import type { Student } from '@lilypad/db/types';
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from '@lilypad/ui/components/avatar';
import { cn } from '@lilypad/ui/lib/utils';

interface StudentAvatarProps {
  student: Pick<
    Student,
    'id' | 'firstName' | 'lastName' | 'fullName' | 'preferredName'
  >;
  className?: string;
}

export function StudentAvatar({ student, className }: StudentAvatarProps) {
  const initials = `${student.firstName?.[0] || ''}${student.lastName?.[0] || ''}`;
  return (
    <Avatar className={cn('size-40 rounded-lg', className)}>
      <AvatarImage alt={`${student.fullName} profile picture`} src="" />
      <AvatarFallback className="rounded-lg border border-primary/20 bg-primary/10 font-semibold text-lg text-primary">
        {initials}
      </AvatarFallback>
    </Avatar>
  );
}
