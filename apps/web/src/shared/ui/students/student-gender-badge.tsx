import { type GenderEnum, GenderEnumMap } from '@lilypad/db/enums';
import { cn, cva, type VariantProps } from '@lilypad/ui/lib/utils';

const studentGenderBadgeVariants = cva(
  'inline-flex items-center rounded-md border px-2 py-0.5 font-semibold text-xs transition-colors',
  {
    variants: {
      gender: {
        MALE: 'border-blue-200 bg-blue-100 text-blue-800 dark:border-blue-700/50 dark:bg-blue-800/20 dark:text-blue-300',
        FEMALE:
          'border-pink-200 bg-pink-100 text-pink-800 dark:border-pink-700/50 dark:bg-pink-800/20 dark:text-pink-300',
        NON_BINARY:
          'border-purple-200 bg-purple-100 text-purple-800 dark:border-purple-700/50 dark:bg-purple-800/20 dark:text-purple-300',
        PREFER_NOT_TO_SAY:
          'border-gray-200 bg-gray-100 text-gray-800 dark:border-gray-700/50 dark:bg-gray-800/20 dark:text-gray-300',
        OTHER:
          'border-gray-200 bg-gray-100 text-gray-800 dark:border-gray-700/50 dark:bg-gray-800/20 dark:text-gray-300',
      },
    },
  }
);

interface StudentGenderBadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof studentGenderBadgeVariants> {
  gender: GenderEnum;
}

export function StudentGenderBadge({
  className,
  gender,
  ...props
}: StudentGenderBadgeProps) {
  return (
    <div
      className={cn(studentGenderBadgeVariants({ gender }), className)}
      {...props}
    >
      {GenderEnumMap[gender]}
    </div>
  );
}
