import { routes } from '@lilypad/shared/routes';
import { Logo } from '@lilypad/ui/components/logo';
import Link from 'next/link';

interface SidebarHeaderIconProps {
  open: boolean;
  iconClassname?: string;
}

export function SidebarHeaderIcon({ open }: SidebarHeaderIconProps) {
  return (
    <Link
      className="flex h-12 items-center justify-between overflow-hidden px-1"
      href={routes.app.dashboard.Index}
    >
      <Logo hideWordmark={!open} iconClassname="size-6" />
      <span
        className={`${open ? 'block' : 'hidden'} rounded-lg bg-primary/20 px-1.5 py-0.5 font-medium text-[10px] text-primary`}
      >
        v1.0
      </span>
    </Link>
  );
}
