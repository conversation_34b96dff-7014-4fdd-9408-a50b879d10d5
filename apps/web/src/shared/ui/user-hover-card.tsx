import type { RoleEnum } from '@lilypad/db/enums';
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from '@lilypad/ui/components/avatar';
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from '@lilypad/ui/components/hover-card';
import { RolesBadge } from '@lilypad/ui/components/roles-badge';
import { Mail } from 'lucide-react';
import type React from 'react';

interface UserData {
  id: string;
  firstName: string;
  lastName: string;
  fullName: string;
  email: string;
  avatar?: string | null;
  userRoles: Array<{
    role: {
      name: string;
    };
  }>;
}

interface UserHoverCardProps {
  user: UserData;
  size?: 'sm' | 'md' | 'lg';
  children?: React.ReactNode;
  className?: string;
}

export const UserHoverCardContent: React.FC<{
  user: UserData;
  size?: 'sm' | 'md' | 'lg';
}> = ({ user, size = 'sm' }) => {
  const hoverAvatarSizeClasses = {
    sm: 'w-12 h-12',
    md: 'w-14 h-14',
    lg: 'w-16 h-16',
  };

  return (
    <div className="flex gap-4">
      <Avatar className={hoverAvatarSizeClasses[size]}>
        {user.avatar ? (
          <AvatarImage alt={user.fullName} src={user.avatar} />
        ) : (
          <AvatarFallback className="font-medium text-sm">
            {user.firstName.charAt(0)}
            {user.lastName.charAt(0)}
          </AvatarFallback>
        )}
      </Avatar>

      <div className="flex-1 space-y-2">
        <div>
          <h4 className="font-semibold text-sm">{user.fullName}</h4>
          <RolesBadge role={user.userRoles.at(0)?.role.name as RoleEnum} />
        </div>

        <div className="flex items-center gap-2 text-muted-foreground text-xs">
          <Mail className="size-3" />
          <span>{user.email}</span>
        </div>
      </div>
    </div>
  );
};

export const UserHoverCard: React.FC<UserHoverCardProps> = ({
  user,
  size = 'sm',
  children,
  className = '',
}) => {
  const sizeClasses = {
    sm: 'size-6',
    md: 'size-8',
    lg: 'size-10',
  };

  // Default avatar trigger if no children provided
  const defaultTrigger = (
    <Avatar
      className={`${sizeClasses[size]} cursor-pointer border-2 border-primary/20 ${className}`}
    >
      {user.avatar ? (
        <AvatarImage alt={user.fullName} src={user.avatar} />
      ) : (
        <AvatarFallback className="font-medium text-xxs">
          {user.firstName.charAt(0)}
          {user.lastName.charAt(0)}
        </AvatarFallback>
      )}
    </Avatar>
  );

  return (
    <HoverCard>
      <HoverCardTrigger asChild>{children || defaultTrigger}</HoverCardTrigger>
      <HoverCardContent className="w-fit" side="top">
        <UserHoverCardContent size={size} user={user} />
      </HoverCardContent>
    </HoverCard>
  );
};
