'use client';

import { useCaptureError } from '@lilypad/monitoring/hooks';
import { Button } from '@lilypad/ui/components/button';
import { Card, CardContent } from '@lilypad/ui/components/card';
import { TriangleAlertIcon } from 'lucide-react';

export type DefaultErrorProps = {
  error: Error & { digest?: string };
  reset: VoidFunction;
};

export function DefaultError({
  error: { digest, ...error },
  reset,
}: DefaultErrorProps): React.JSX.Element {
  useCaptureError(error, { digest });
  const handleReset = (): void => {
    reset();
  };
  return (
    <Card className="border-dashed shadow-none">
      <CardContent className="flex flex-col items-center justify-center gap-4 pt-6">
        <div className="flex items-center justify-center rounded-md border bg-background p-2 shadow-sm">
          <TriangleAlertIcon className="size-4 shrink-0" />
        </div>
        <p className="text-muted-foreground text-sm">
          Unexpected error occured.
        </p>
        <Button
          onClick={handleReset}
          size="default"
          type="button"
          variant="default"
        >
          Try again
        </Button>
      </CardContent>
    </Card>
  );
}
