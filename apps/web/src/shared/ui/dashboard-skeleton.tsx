import { Skeleton } from '@lilypad/ui/components/skeleton';

interface DashboardSkeletonProps {
  metricCards?: number;
  chartCards?: number;
  showMainContent?: boolean;
  mainContentHeight?: string;
}

export function DashboardSkeleton({
  metricCards = 4,
  chartCards = 2,
  showMainContent = true,
  mainContentHeight = "h-96",
}: DashboardSkeletonProps): React.JSX.Element {
  return (
    <div className="space-y-6">
      {/* Metric cards row */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: metricCards }).map((_, i) => (
          <Skeleton key={`metric-${i}`} className="h-32 w-full" />
        ))}
      </div>

      {/* Chart cards row */}
      {chartCards > 0 && (
        <div className="grid gap-4 md:grid-cols-2">
          {Array.from({ length: chartCards }).map((_, i) => (
            <Skeleton key={`chart-${i}`} className="h-64 w-full" />
          ))}
        </div>
      )}

      {/* Main content area */}
      {showMainContent && (
        <Skeleton className={`${mainContentHeight} w-full`} />
      )}
    </div>
  );
} 