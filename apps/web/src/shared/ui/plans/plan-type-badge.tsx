import { PlanTypeEnum } from '@lilypad/db/schema/enums';
import { cn, cva, type VariantProps } from '@lilypad/ui/lib/utils';

const planTypeBadgeVariants = cva(
  'inline-flex items-center rounded-md border px-2 py-0.5 font-semibold text-xs transition-colors',
  {
    variants: {
      type: {
        [PlanTypeEnum.IEP]:
          'border-blue-200 bg-blue-100 text-blue-800 dark:border-blue-700/50 dark:bg-blue-800/20 dark:text-blue-300',
        [PlanTypeEnum.PLAN_504]:
          'border-purple-200 bg-purple-100 text-purple-800 dark:border-purple-700/50 dark:bg-purple-800/20 dark:text-purple-300',
        [PlanTypeEnum.BIP]:
          'border-orange-200 bg-orange-100 text-orange-800 dark:border-orange-700/50 dark:bg-orange-800/20 dark:text-orange-300',
        [PlanTypeEnum.SST]:
          'border-teal-200 bg-teal-100 text-teal-800 dark:border-teal-700/50 dark:bg-teal-800/20 dark:text-teal-300',
      },
    },
  }
);

const planTypeDisplayMap = {
  [PlanTypeEnum.IEP]: 'IEP',
  [PlanTypeEnum.PLAN_504]: '504 Plan',
  [PlanTypeEnum.BIP]: 'BIP',
  [PlanTypeEnum.SST]: 'SST',
} as const;

interface PlanTypeBadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof planTypeBadgeVariants> {
  type: PlanTypeEnum;
}

export function PlanTypeBadge({
  type,
  className,
  ...props
}: PlanTypeBadgeProps) {
  if (!type) {
    return null;
  }

  const displayName = planTypeDisplayMap[type];

  return (
    <div className={cn(planTypeBadgeVariants({ type }), className)} {...props}>
      {displayName}
    </div>
  );
}

export { planTypeBadgeVariants };
