import { PlanStatusEnum } from '@lilypad/db/schema/enums';
import { cn, cva, type VariantProps } from '@lilypad/ui/lib/utils';

const planStatusBadgeVariants = cva(
  'inline-flex items-center rounded-md border px-2 py-0.5 font-semibold text-xs transition-colors',
  {
    variants: {
      status: {
        [PlanStatusEnum.ACTIVE]:
          'border-green-200 bg-green-100 text-green-800 dark:border-green-700/50 dark:bg-green-800/20 dark:text-green-300',
        [PlanStatusEnum.PENDING]:
          'border-yellow-200 bg-yellow-100 text-yellow-800 dark:border-yellow-700/50 dark:bg-yellow-800/20 dark:text-yellow-300',
        [PlanStatusEnum.CANCELLED]:
          'border-gray-200 bg-gray-100 text-gray-800 dark:border-gray-700/50 dark:bg-gray-800/20 dark:text-gray-300',
      },
    },
  }
);

const planStatusDisplayMap = {
  [PlanStatusEnum.ACTIVE]: 'Active',
  [PlanStatusEnum.PENDING]: 'Pending',
  [PlanStatusEnum.CANCELLED]: 'Cancelled',
} as const;

interface PlanStatusBadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof planStatusBadgeVariants> {
  status: PlanStatusEnum;
}

export function PlanStatusBadge({
  status,
  className,
  ...props
}: PlanStatusBadgeProps) {
  if (!status) {
    return null;
  }

  const displayName = planStatusDisplayMap[status];

  return (
    <div
      className={cn(planStatusBadgeVariants({ status }), className)}
      {...props}
    >
      {displayName}
    </div>
  );
}

export { planStatusBadgeVariants };
