import type { DatabaseType } from '@lilypad/db/client';
import { createDatabaseClient, eq } from '@lilypad/db/client';
import {
  districtsTable,
  permissionsTable,
  type RoleEnum,
  rolePermissionsTable,
  rolesTable,
  schoolsTable,
  userDistrictsTable,
  userRolesTable,
  userSchoolsTable,
  usersTable,
} from '@lilypad/db/schema';
import { routes } from '@lilypad/shared/routes';
import type { UserIdentity } from '@lilypad/supabase';
import {
  checkRequiresMFA,
  checkSession,
  getRedirectToTotp,
} from '@lilypad/supabase/auth';
import { getRedirectToSignIn } from '@lilypad/supabase/auth/redirect';
import { getSupabaseServerClient } from '@lilypad/supabase/server';
import type { Supabase } from '@lilypad/supabase/types';
import { redirect } from 'next/navigation';
import { cache } from 'react';
import type { AuthContext, User, UserDistrict, UserSchool } from './types';

const dedupedGetUserInfo = cache(
  async (
    db: DatabaseType,
    supabase: Supabase,
    userId: string,
    email: string,
    identities: UserIdentity[]
  ): Promise<User> => {
    const userInfo = await db.transaction(async (tx) => {
      // 1. Get basic user info
      const [user] = await tx
        .select({
          avatar: usersTable.avatar,
          fullName: usersTable.fullName,
          isOnboarded: usersTable.isOnboarded,
        })
        .from(usersTable)
        .where(eq(usersTable.id, userId))
        .limit(1);

      if (!user) {
        return null;
      }

      // 2. Get user districts with district info
      const userDistricts = await tx
        .select({
          districtId: userDistrictsTable.districtId,
          userId: userDistrictsTable.userId,
          district: {
            id: districtsTable.id,
            name: districtsTable.name,
            slug: districtsTable.slug,
          },
        })
        .from(userDistrictsTable)
        .leftJoin(
          districtsTable,
          eq(userDistrictsTable.districtId, districtsTable.id)
        )
        .where(eq(userDistrictsTable.userId, userId));

      // 3. Get user schools with school info
      const userSchools = await tx
        .select({
          schoolId: userSchoolsTable.schoolId,
          userId: userSchoolsTable.userId,
          school: {
            id: schoolsTable.id,
            name: schoolsTable.name,
            districtId: schoolsTable.districtId,
          },
        })
        .from(userSchoolsTable)
        .leftJoin(schoolsTable, eq(userSchoolsTable.schoolId, schoolsTable.id))
        .where(eq(userSchoolsTable.userId, userId));

      // 4. Get user roles and permissions in one optimized query
      const rolePermissionsData = await tx
        .select({
          roleId: rolesTable.id,
          roleName: rolesTable.name,
          permissionId: permissionsTable.id,
          permissionName: permissionsTable.name,
        })
        .from(userRolesTable)
        .innerJoin(rolesTable, eq(userRolesTable.roleId, rolesTable.id))
        .innerJoin(
          rolePermissionsTable,
          eq(rolesTable.id, rolePermissionsTable.roleId)
        )
        .innerJoin(
          permissionsTable,
          eq(rolePermissionsTable.permissionId, permissionsTable.id)
        )
        .where(eq(userRolesTable.userId, userId));

      // Process roles and permissions
      const rolesMap = new Map();
      const permissions: {
        id: string;
        name: string;
        roleId: string;
        roleName: RoleEnum;
      }[] = [];

      for (const row of rolePermissionsData) {
        // Add role if not already present
        if (!rolesMap.has(row.roleId)) {
          rolesMap.set(row.roleId, {
            id: row.roleId,
            name: row.roleName,
            userId,
          });
        }

        permissions.push({
          id: row.permissionId,
          name: row.permissionName,
          roleId: row.roleId,
          roleName: row.roleName,
        });
      }

      return {
        id: userId,
        email,
        identities,
        ...user,
        userDistricts: userDistricts as UserDistrict[],
        userSchools: userSchools as UserSchool[],
        rolePermissions: {
          userId,
          roles: Array.from(rolesMap.values()),
          permissions,
        },
      };
    });

    if (!userInfo) {
      await supabase.auth.signOut();
      return redirect(routes.app.auth.SignIn);
    }

    return userInfo;
  }
);

export async function getAuthContext(): Promise<AuthContext> {
  const adminDb = await createDatabaseClient({ admin: true });
  const db = await createDatabaseClient();

  const supabase = await getSupabaseServerClient();

  const { data: auth } = await supabase.auth.getUser();

  if (!checkSession(auth)) {
    return redirect(getRedirectToSignIn());
  }

  const requiresMfa = await checkRequiresMFA(supabase);

  if (auth.user && requiresMfa) {
    redirect(getRedirectToTotp(auth.user.id));
  }

  const user = await dedupedGetUserInfo(
    adminDb,
    supabase,
    auth.user.id,
    auth.user.email || '',
    auth.user.identities || []
  );

  return { user, db, supabase };
}
