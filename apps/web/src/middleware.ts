import {
  CsrfError,
  createCsrfProtect,
  type NextConfigOptions,
} from '@edge-csrf/nextjs';
import { RoleEnum } from '@lilypad/db/enums';
import { routes } from '@lilypad/shared/routes';
import { checkRequiresMFA } from '@lilypad/supabase/auth/user';
import { getSupabaseMiddlewareClient } from '@lilypad/supabase/middleware';
import type { Supabase } from '@lilypad/supabase/types';
import { type NextRequest, NextResponse } from 'next/server';
import { env } from '@/env';

const CSRF_SECRET_COOKIE = 'csrfSecret';
const NEXT_ACTION_HEADER = 'next-action';

const PROTECTED_ROUTES = [
  '/invite',
  '/onboarding',
  '/dashboard',
  '/districts',
  '/settings',
  '/settings/district',
  '/settings/district/schools',
  '/settings/district/members',
  '/settings/security',
  '/settings/notifications',
];
const setRequestHeaders = (request: NextRequest) => {
  const headers = new Headers(request.headers);

  const pathname = request.nextUrl.pathname;

  headers.set('x-correlation-id', crypto.randomUUID());
  headers.set('x-pathname', pathname);

  if (PROTECTED_ROUTES.some((route) => pathname.startsWith(route))) {
    headers.set('x-robots-tag', 'noindex, nofollow');
  }
  return headers;
};

const isServerAction = (request: NextRequest) => {
  const headers = new Headers(request.headers);
  return headers.has(NEXT_ACTION_HEADER);
};

const getNextConfigOptions = (
  request: NextRequest
): Partial<NextConfigOptions> => {
  return {
    cookie: {
      secure: env.ENVIRONMENT === 'production',
      name: CSRF_SECRET_COOKIE,
    },
    // ignore CSRF errors for server actions since protection is built-in
    // and always ignore GET, HEAD, and OPTIONS requests
    ignoreMethods: isServerAction(request)
      ? ['POST']
      : ['GET', 'HEAD', 'OPTIONS'],
  };
};

const withCsrfMiddleware = async (
  request: NextRequest,
  response = new NextResponse()
) => {
  const options = getNextConfigOptions(request);
  const csrfProtect = createCsrfProtect(options);

  try {
    await csrfProtect(request, response);
    return response;
  } catch (error) {
    if (error instanceof CsrfError) {
      return NextResponse.json('Invalid CSRF token', {
        status: 401,
      });
    }

    throw error;
  }
};

const getUser = async (supabase: Supabase) => {
  const userResponse = await supabase.auth.getUser();
  return userResponse.data;
};

const isOnboarded = async (
  userId: string,
  supabase: Supabase
): Promise<boolean> => {
  const user = await supabase
    .from('users')
    .select('is_onboarded')
    .eq('id', userId)
    .single();
  return user.data?.is_onboarded ?? true;
};

const hasSuperUserRole = async (
  userId: string,
  supabase: Supabase
): Promise<boolean> => {
  const role = await supabase
    .from('user_roles')
    .select('role: roles(name)')
    .eq('user_id', userId)
    .single();
  return role.data?.role.name === RoleEnum.SUPER_USER.toString();
};

// General auth check to determine if a user needs to be redirected to authenticate
async function requiresAuthentication(
  request: NextRequest,
  response: NextResponse
) {
  const supabase = getSupabaseMiddlewareClient(request, response);

  const { user } = await getUser(supabase);

  const pathname = request.nextUrl.pathname;
  const origin = request.nextUrl.origin;

  // User is not authenticated, redirect to sign in
  if (!user) {
    const signIn = routes.app.auth.SignIn;
    const redirectPath = `${signIn}?next=${pathname}`;
    return NextResponse.redirect(new URL(redirectPath, origin).href);
  }

  // Check if user requires MFA
  const requiresMFA = await checkRequiresMFA(supabase);

  if (requiresMFA) {
    return NextResponse.redirect(
      new URL(routes.app.auth.totp.Verify, origin).href
    );
  }

  // Check if user needs onboarding
  const isUserOnboarded = await isOnboarded(user.id, supabase);

  if (!(isUserOnboarded || pathname.startsWith(routes.app.onboarding.Index))) {
    return NextResponse.redirect(
      new URL(routes.app.onboarding.Index, origin).href
    );
  }

  // User is onboarded but trying to access onboarding
  if (isUserOnboarded && pathname.startsWith(routes.app.onboarding.Index)) {
    return NextResponse.redirect(
      new URL(routes.app.dashboard.Index, origin).href
    );
  }

  return null; // No redirect needed
}

// Default handler for protected routes
async function protectedRouteMiddleware(
  request: NextRequest,
  response: NextResponse
) {
  return await requiresAuthentication(request, response);
}

const onboardingRouteHandler = async (
  request: NextRequest,
  response: NextResponse
) => {
  const supabase = getSupabaseMiddlewareClient(request, response);
  const { data } = await supabase.auth.getUser();

  const user = data.user;
  const origin = request.nextUrl.origin;

  if (!user) {
    const signInPath = '/auth/sign-in';
    const redirectPath = `${signInPath}?next=${request.nextUrl.pathname}`;

    return NextResponse.redirect(
      new URL(redirectPath, request.nextUrl.origin).href
    );
  }

  const requiresMFA = await checkRequiresMFA(supabase);

  // If user requires multi-factor authentication, redirect to MFA page.
  if (requiresMFA) {
    return NextResponse.redirect(
      new URL(routes.app.auth.totp.Verify, origin).href
    );
  }

  const isUserOnboarded = await isOnboarded(user.id, supabase);

  if (isUserOnboarded) {
    return NextResponse.redirect(
      new URL(routes.app.dashboard.Index, request.nextUrl.origin).href
    );
  }
};

const districtsRouteHandler = async (
  request: NextRequest,
  response: NextResponse
) => {
  const supabase = getSupabaseMiddlewareClient(request, response);

  const { user } = await getUser(supabase);

  const pathname = request.nextUrl.pathname;
  const origin = request.nextUrl.origin;

  // User is not authenticated, redirect to sign in
  if (!user) {
    const signIn = routes.app.auth.SignIn;
    const redirectPath = `${signIn}?next=${pathname}`;
    return NextResponse.redirect(new URL(redirectPath, origin).href);
  }

  // Check if user requires MFA
  const requiresMFA = await checkRequiresMFA(supabase);
  if (requiresMFA) {
    return NextResponse.redirect(
      new URL(routes.app.auth.totp.Verify, origin).href
    );
  }

  const isSuperUser = await hasSuperUserRole(user.id, supabase);

  if (!isSuperUser) {
    return NextResponse.redirect(
      new URL(routes.app.dashboard.Index, origin).href
    );
  }
};

const authRouteHandler = async (
  request: NextRequest,
  response: NextResponse
) => {
  const supabase = getSupabaseMiddlewareClient(request, response);
  const { user } = await getUser(supabase);

  // The user is logged out, so we don't need to do anything
  if (!user) {
    return;
  }

  const origin = request.nextUrl.origin;
  const pathname = request.nextUrl.pathname;

  // Check if user requires MFA verification
  const requiresMFA = await checkRequiresMFA(supabase);

  const hasSkippedMFASetup = user.app_metadata?.mfaSetupSkipped === true;

  // Handle MFA setup flow (user doesn't require MFA verification yet)
  if (!hasSkippedMFASetup) {
    const route = new URL(routes.app.auth.totp.Index, origin).pathname;
    if (pathname !== route) {
      return NextResponse.redirect(
        new URL(routes.app.auth.totp.Verify, origin).href
      );
    }
    return;
  }

  // Handle MFA verification flow
  if (requiresMFA) {
    const recoveryCodeRoute = new URL(routes.app.auth.RecoveryCode, origin)
      .pathname;
    if (pathname === recoveryCodeRoute) {
      return;
    }

    const verifyMfaRoute = new URL(routes.app.auth.totp.Verify, origin)
      .pathname;
    if (pathname !== verifyMfaRoute) {
      return NextResponse.redirect(
        new URL(routes.app.auth.totp.Verify, origin).href
      );
    }
    return;
  }

  // If user is authenticated and doesn't need MFA, redirect to dashboard
  if (!requiresMFA && hasSkippedMFASetup) {
    return NextResponse.redirect(
      new URL(routes.app.dashboard.Index, origin).href
    );
  }
};

const joinRequestRouteHandler = async (
  request: NextRequest,
  response: NextResponse
) => {
  const supabase = getSupabaseMiddlewareClient(request, response);
  const { user } = await getUser(supabase);

  // The user is logged out, so we don't need to do anything
  if (user) {
    return NextResponse.redirect(
      new URL(routes.app.dashboard.Index, request.nextUrl.origin).href
    );
  }

  return null;
};

const getUrlPatterns = () => {
  return [
    {
      pattern: '/join-request',
      handler: joinRequestRouteHandler,
    },
    {
      pattern: '/auth',
      handler: authRouteHandler,
    },
    {
      pattern: '/onboarding',
      handler: onboardingRouteHandler,
    },
    {
      pattern: '/districts',
      handler: districtsRouteHandler,
    },
    {
      // General catch-all handler for protected routes
      pattern: '*',
      handler: protectedRouteMiddleware,
    },
  ];
};

const matchUrlPattern = (pathname: string) => {
  const urlPatterns = getUrlPatterns();

  for (const urlPattern of urlPatterns) {
    // Skip wildcard pattern (catch-all) for now
    if (urlPattern.pattern === '*') {
      continue;
    }

    // Handle exact match or prefix match
    if (pathname.startsWith(urlPattern.pattern)) {
      return urlPattern.handler;
    }
  }

  // If no specific pattern matched, use the catch-all handler
  const catchAllPattern = urlPatterns.find((p) => p.pattern === '*');
  return catchAllPattern?.handler || null;
};

export async function middleware(request: NextRequest) {
  const response = NextResponse.next();
  const headers = setRequestHeaders(request);
  const csrfResponse = await withCsrfMiddleware(request, response);

  const handlePattern = matchUrlPattern(request.nextUrl.pathname);

  // If a pattern handler exists, call it.
  if (handlePattern) {
    const patternHandlerResponse = await handlePattern(request, csrfResponse);

    // If a pattern handler returns a response, return it.
    if (patternHandlerResponse) {
      return patternHandlerResponse;
    }
  }

  // Append the action path to the request headers, which is useful for knowing the action path in server actions.
  if (isServerAction(request)) {
    csrfResponse.headers.set('x-action-path', request.nextUrl.pathname);
  }

  // Merge the new headers with the CSRF response headers
  for (const [key, value] of headers.entries()) {
    csrfResponse.headers.set(key, value);
  }

  // If no pattern handler returned a response, return the session response.
  return csrfResponse;
}
export const config = {
  matcher: [
    {
      source:
        '/((?!_next/static|_next/image|favicon.ico|assets|api/stripe/webhook|api/auth/callback|api/trpc|api/inngest|logo).*)',
      missing: [{ type: 'header', key: 'next-action' }],
    },
  ],
};
