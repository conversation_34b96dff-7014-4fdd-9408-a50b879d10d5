import { PlanStatusBadge } from '@/shared/ui/plans/plan-status-badge';
import { PlanTypeBadge } from '@/shared/ui/plans/plan-type-badge';
import type { PlanWithCase } from '@lilypad/db/repository/types/plans';
import { formatDate } from '@lilypad/ui/lib/utils';
import type React from 'react';

interface PlanInfoFieldProps {
  label: string;
  value: string | React.ReactNode;
  className?: string;
}

export const PlanInfoField: React.FC<PlanInfoFieldProps> = ({
  label,
  value,
  className,
}) => (
  <div className={`flex flex-col gap-1 ${className || ''}`}>
    <span className="font-medium text-muted-foreground">{label}</span>
    <div className="font-medium">{value}</div>
  </div>
);

interface PlanInfoGridProps {
  plan: PlanWithCase;
}

export const PlanInfoGrid: React.FC<PlanInfoGridProps> = ({ plan }) => {
  const daysUntilExpiration = Math.ceil(
    (plan.expirationDate.getTime() - new Date().getTime()) /
      (1000 * 60 * 60 * 24)
  );
  const isExpired = daysUntilExpiration < 0;
  const isExpiringSoon = daysUntilExpiration <= 30 && daysUntilExpiration >= 0;

  return (
    <div className="grid grid-cols-2 gap-4 text-xs lg:col-span-8 lg:grid-cols-3">
      <PlanInfoField
        label="Plan Type"
        value={<PlanTypeBadge type={plan.type} />}
      />
      <PlanInfoField
        label="Status"
        value={<PlanStatusBadge status={plan.status} />}
      />
      <PlanInfoField
        label="Expiration Date"
        value={
          <div className="flex flex-col gap-1">
            <span
              className={
                isExpired
                  ? 'font-medium text-red-600'
                  : isExpiringSoon
                    ? 'font-medium text-amber-600'
                    : ''
              }
            >
              {formatDate(plan.expirationDate)}
            </span>
            {isExpired && <span className="text-red-500 text-xs">Expired</span>}
            {isExpiringSoon && !isExpired && (
              <span className="text-amber-600 text-xs">
                Expires in {daysUntilExpiration} days
              </span>
            )}
          </div>
        }
      />
      <PlanInfoField
        label="Active Case"
        value={plan.case.isActive ? 'Yes' : 'No'}
      />
      <PlanInfoField
        label="IEP Period"
        value={`${plan.case.iepStartDate ? formatDate(plan.case.iepStartDate, { month: 'short' }) : 'N/A'} - ${plan.case.iepEndDate ? formatDate(plan.case.iepEndDate, { month: 'short' }) : 'N/A'}`}
      />
      <PlanInfoField label="Created On" value={formatDate(plan.createdAt)} />
    </div>
  );
};
