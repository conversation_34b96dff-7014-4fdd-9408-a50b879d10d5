'use client';

import type { Case } from '@lilypad/db/schema';
import type React from 'react';

import { CasePriorityBadge } from '@/shared/ui/cases/case-priority-badge';
import { CaseStatusBadge } from '@/shared/ui/cases/case-status-badge';
import { CaseTypeBadge } from '@/shared/ui/cases/case-type-badge';
import { Button } from '@lilypad/ui/components/button';
import { useCopy } from '@lilypad/ui/hooks/use-copy';
import { CopyIcon } from 'lucide-react';

interface CaseInfoProps {
  case: Pick<
    Case,
    | 'id'
    | 'status'
    | 'priority'
    | 'caseType'
    | 'isActive'
    | 'iepStatus'
    | 'iepStartDate'
    | 'iepEndDate'
    | 'referralDate'
    | 'evaluationDueDate'
    | 'meetingDate'
  >;
}

export const CaseInfo: React.FC<CaseInfoProps> = ({ case: caseData }) => {
  const [_, copyToClipboard] = useCopy();

  const handleCopyId = async () => {
    await copyToClipboard(caseData.id.slice(0, 8));
  };

  return (
    <div className="flex flex-col gap-2">
      <div className="flex flex-col gap-1">
        <span className="text-muted-foreground">Associated Case</span>
        <span className="flex items-center">
          <span>#{caseData.id.slice(0, 8)}</span>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleCopyId}
            className="h-6 w-6"
            title="Copy full plan ID"
          >
            <CopyIcon className="size-3" />
          </Button>
        </span>
      </div>
      <div className="flex flex-wrap gap-2">
        <CaseStatusBadge status={caseData.status} className="w-fit" />
        <CasePriorityBadge priority={caseData.priority} className="w-fit" />
        <CaseTypeBadge type={caseData.caseType} className="w-fit" />
      </div>
    </div>
  );
};
