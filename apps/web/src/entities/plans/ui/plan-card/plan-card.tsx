'use client';

import type { PlanWithCase } from '@lilypad/db/repository/types/plans';
import type React from 'react';

import { Button } from '@lilypad/ui/components/button';
import { Card, CardContent } from '@lilypad/ui/components/card';
import { useCopy } from '@lilypad/ui/hooks/use-copy';
import { CopyIcon } from 'lucide-react';
import { CaseInfo } from './case-info';
import { PlanActions } from './plan-actions';
import { PlanInfoGrid } from './plan-info-grid';

interface PlanCardProps {
  plan: PlanWithCase;
  onView?: VoidFunction;
  onEdit?: VoidFunction;
  onArchive?: VoidFunction;
}

export const PlanCard: React.FC<PlanCardProps> = ({
  plan,
  onView,
  onEdit,
  onArchive,
}) => {
  const [_, copyToClipboard] = useCopy();

  const handleCopyId = async () => {
    await copyToClipboard(plan.id);
  };

  if (!plan) {
    return null;
  }

  return (
    <Card className="w-full p-0">
      <CardContent className="flex flex-col gap-4 p-4">
        <div className="grid gap-4 text-xs lg:grid-cols-12">
          {/* Plan ID & Type Info */}
          <div className="flex flex-col gap-2 lg:col-span-4">
            <div>
              <div className="flex flex-col">
                <span className="text-muted-foreground">Plan ID</span>
                <div className="flex items-center">
                  <span>#{plan.id.slice(0, 8)}</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleCopyId}
                    className="h-6 w-6"
                    title="Copy full plan ID"
                  >
                    <CopyIcon className="size-3" />
                  </Button>
                </div>
              </div>
            </div>
            <CaseInfo case={plan.case} />
          </div>

          {/* Plan Information Grid */}
          <PlanInfoGrid plan={plan} />
        </div>
      </CardContent>

      {/* Actions */}
      <PlanActions
        createdAt={plan.createdAt}
        updatedAt={plan.updatedAt}
        expirationDate={plan.expirationDate}
        onView={onView}
        onEdit={onEdit}
        onArchive={onArchive}
      />
    </Card>
  );
};
