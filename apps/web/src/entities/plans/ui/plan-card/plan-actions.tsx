import { formatDate } from '@lilypad/shared/date';
import { Button } from '@lilypad/ui/components/button';
import { CardFooter } from '@lilypad/ui/components/card';
import type React from 'react';

interface PlanActionsProps {
  createdAt: Date;
  updatedAt: Date;
  expirationDate: Date;
  onView?: VoidFunction;
  onEdit?: VoidFunction;
  onArchive?: VoidFunction;
}

export const PlanActions: React.FC<PlanActionsProps> = ({
  createdAt,
  updatedAt,
  expirationDate,
  onView = () => {},
  onEdit = () => {},
  onArchive = () => {},
}) => {
  const daysUntilExpiration = Math.ceil(
    (expirationDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
  );
  const isExpired = daysUntilExpiration < 0;
  const isExpiringSoon = daysUntilExpiration <= 30 && daysUntilExpiration >= 0;

  return (
    <CardFooter className="flex flex-col justify-between gap-2 rounded-b-lg bg-muted px-4 py-2 sm:flex-row sm:gap-0">
      <div className="flex gap-2 space-y-1 text-muted-foreground text-xs sm:space-y-0">
        <span>Created: {formatDate(createdAt, { month: 'short' })}</span>
        <span className="hidden sm:inline">|</span>
        <span>Updated: {formatDate(updatedAt, { month: 'short' })}</span>
        <span className="hidden sm:inline">|</span>
        <span
          className={
            isExpired
              ? 'font-medium text-red-600'
              : isExpiringSoon
                ? 'font-medium text-amber-600'
                : ''
          }
        >
          Expires: {formatDate(expirationDate, { month: 'short' })}
        </span>
      </div>
      <div className="flex gap-2">
        {onArchive && (
          <Button variant="cancel" size="sm" onClick={onArchive}>
            Archive Plan
          </Button>
        )}
        {onEdit && (
          <Button variant="outline" size="sm" onClick={onEdit}>
            Edit Plan
          </Button>
        )}
        {onView && (
          <Button size="sm" onClick={onView}>
            View Plan
          </Button>
        )}
      </div>
    </CardFooter>
  );
};
