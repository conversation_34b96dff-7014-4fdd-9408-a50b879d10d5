import { AdminStatusBadge } from '@/shared/ui/assessments/admin-status-badge';
import type { AssessmentSessionSummary } from '@lilypad/db/repository/types/assessments';
import { Badge } from '@lilypad/ui/components/badge';
import type React from 'react';

interface TestAdministrationsProps {
  administrations: AssessmentSessionSummary['testAdministrations'];
}

export const TestAdministrations: React.FC<TestAdministrationsProps> = ({
  administrations,
}) => {
  if (!administrations || administrations.length === 0) {
    return (
      <div className="text-muted-foreground text-xs">
        No test administrations recorded
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <h5 className="flex items-center gap-x-1 font-medium text-muted-foreground text-xs">
        Test Administrations
        <Badge variant="secondary" className="text-xs" size="sm">
          {administrations.length}
        </Badge>
      </h5>
      <div className="space-y-2">
        {administrations.map((administration) => (
          <div
            key={administration.id}
            className="flex flex-col gap-2 rounded-md bg-muted/50 p-3 text-xs lg:flex-row lg:items-center lg:justify-between lg:gap-4"
          >
            <div className="flex flex-1 flex-col gap-1">
              <div className="font-medium">{administration.battery.name}</div>
              <div className="text-muted-foreground">
                {administration.battery.code} •{' '}
                {administration.battery.category
                  .replace(/_/g, ' ')
                  .toLowerCase()
                  .replace(/\b\w/g, (l) => l.toUpperCase())}
              </div>
            </div>
            <div className="flex items-center justify-between gap-2 lg:justify-end lg:gap-3">
              <span className="text-muted-foreground text-xs">
                Order: {administration.administrationOrder}
              </span>
              {administration.adminStatus && (
                <AdminStatusBadge status={administration.adminStatus} />
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
