'use client';

import type { AssessmentSessionSummary } from '@lilypad/db/repository/types/assessments';
import type React from 'react';

import { SessionStatusBadge } from '@/shared/ui/assessments/session-status-badge';
import { SessionTypeBadge } from '@/shared/ui/assessments/session-type-badge';
import { Button } from '@lilypad/ui/components/button';
import { Card, CardContent } from '@lilypad/ui/components/card';
import { useCopy } from '@lilypad/ui/hooks/use-copy';
import { CopyIcon } from 'lucide-react';
import { AssessmentActions } from './assessment-actions';
import { AssessmentInfoGrid } from './assessment-info-grid';
import { PsychologistInfo } from './psychologist-info';
import { TestAdministrations } from './test-administrations';

interface AssessmentCardProps {
  assessment: AssessmentSessionSummary;
  onView?: VoidFunction;
  onEdit?: VoidFunction;
}

export const AssessmentCard: React.FC<AssessmentCardProps> = ({
  assessment,
  onView,
  onEdit,
}) => {
  const [_, copyToClipboard] = useCopy();

  const handleCopyId = async () => {
    await copyToClipboard(assessment.id);
  };

  if (!assessment) {
    return null;
  }

  return (
    <Card className="w-full p-0">
      <CardContent className="flex flex-col gap-4 p-4">
        <div className="grid grid-cols-1 gap-4 text-xs lg:grid-cols-12">
          {/* Assessment ID & Psychologist */}
          <div className="flex flex-col gap-2 font-medium lg:col-span-4">
            <div className="flex items-center justify-between lg:block">
              <div className="flex flex-col gap-1">
                <span className="text-muted-foreground">Assessment ID</span>
                <div className="flex items-center gap-1">
                  <span>#{assessment.id.slice(0, 8)}</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleCopyId}
                    className="h-6 w-6 p-0 hover:bg-muted"
                    title="Copy full assessment ID"
                  >
                    <CopyIcon className="size-3" />
                  </Button>
                </div>
              </div>
              <div className="flex flex-col items-end gap-1 lg:hidden">
                {assessment.sessionStatus && (
                  <SessionStatusBadge status={assessment.sessionStatus} />
                )}
                <SessionTypeBadge type={assessment.sessionType} />
              </div>
            </div>
            <PsychologistInfo psychologist={assessment.psychologist} />
          </div>

          {/* Assessment Information Grid */}
          <AssessmentInfoGrid assessment={assessment} />
        </div>

        {/* Divider */}
        <div className="border-border border-t border-dashed" />
        {/* Test Administrations */}
        <TestAdministrations administrations={assessment.testAdministrations} />
      </CardContent>

      {/* Actions */}
      <AssessmentActions
        sessionDate={assessment.sessionDate}
        createdAt={assessment.createdAt}
        updatedAt={assessment.updatedAt}
        onView={onView}
        onEdit={onEdit}
      />
    </Card>
  );
};
