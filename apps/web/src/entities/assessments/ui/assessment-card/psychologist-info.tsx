import { UserHoverCard } from '@/shared/ui/user-hover-card';
import { type RoleEnum, RoleEnumMap } from '@lilypad/db/enums';
import type { AssessmentSessionSummary } from '@lilypad/db/repository/types/assessments';
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from '@lilypad/ui/components/avatar';
import type React from 'react';

interface PsychologistInfoProps {
  psychologist: AssessmentSessionSummary['psychologist'];
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export const PsychologistInfo: React.FC<PsychologistInfoProps> = ({
  psychologist,
  className = '',
  size = 'sm',
}) => {
  const sizeClasses = {
    sm: 'size-6',
    md: 'size-8',
    lg: 'size-10',
  };

  return (
    <div className="flex flex-col gap-2">
      <span className="text-muted-foreground">Psychologist</span>
      <UserHoverCard user={psychologist} size={size}>
        <div className="flex cursor-pointer items-center gap-2">
          <Avatar
            className={`${sizeClasses[size]} border-2 border-primary/20 ${className}`}
          >
            {psychologist.avatar ? (
              <AvatarImage
                src={psychologist.avatar}
                alt={psychologist.fullName}
              />
            ) : (
              <AvatarFallback className="font-medium text-xxs">
                {psychologist.firstName.charAt(0)}
                {psychologist.lastName.charAt(0)}
              </AvatarFallback>
            )}
          </Avatar>
          <div className="min-w-0 flex-1 text-xs">
            <div className="truncate font-medium">{psychologist.fullName}</div>
            {psychologist.userRoles[0]?.role.name && (
              <div className="truncate text-muted-foreground">
                {RoleEnumMap[psychologist.userRoles[0]?.role.name as RoleEnum]}
              </div>
            )}
          </div>
        </div>
      </UserHoverCard>
    </div>
  );
};
