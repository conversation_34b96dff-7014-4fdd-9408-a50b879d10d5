import type { AssessmentSessionSummary } from '@lilypad/db/repository/types/assessments';
import type React from 'react';

import { SessionStatusBadge } from '@/shared/ui/assessments/session-status-badge';
import { SessionTypeBadge } from '@/shared/ui/assessments/session-type-badge';
import { CasePriorityBadge } from '@/shared/ui/cases/case-priority-badge';
import { CaseStatusBadge } from '@/shared/ui/cases/case-status-badge';
import { formatDate } from '@lilypad/ui/lib/utils';

interface AssessmentInfoFieldProps {
  label: string;
  value: string | React.ReactNode;
  className?: string;
}

export const AssessmentInfoField: React.FC<AssessmentInfoFieldProps> = ({
  label,
  value,
  className,
}) => (
  <div className={`flex flex-col gap-1 ${className || ''}`}>
    <span className="font-medium text-muted-foreground">{label}</span>
    <div className="font-medium">{value}</div>
  </div>
);

interface AssessmentInfoGridProps {
  assessment: AssessmentSessionSummary;
}

export const AssessmentInfoGrid: React.FC<AssessmentInfoGridProps> = ({
  assessment,
}) => (
  <div className="grid grid-cols-2 gap-4 text-xs lg:col-span-8 lg:grid-cols-3">
    <div className="hidden lg:block">
      <AssessmentInfoField
        label="Session Status"
        value={
          assessment.sessionStatus ? (
            <SessionStatusBadge status={assessment.sessionStatus} />
          ) : (
            'Unknown'
          )
        }
      />
    </div>
    <div className="hidden lg:block">
      <AssessmentInfoField
        label="Session Type"
        value={<SessionTypeBadge type={assessment.sessionType} />}
      />
    </div>
    <AssessmentInfoField
      label="Duration"
      value={`${assessment.sessionDuration} minutes`}
    />
    <AssessmentInfoField
      label="Location"
      value={assessment.location || 'Not specified'}
    />
    <AssessmentInfoField
      label="Session Date"
      value={formatDate(assessment.sessionDate, { month: 'short' })}
    />
    <AssessmentInfoField
      label="Case Status"
      value={
        assessment.case ? (
          <CaseStatusBadge status={assessment.case.status} />
        ) : (
          'Independent Assessment'
        )
      }
    />
    <AssessmentInfoField
      label="Case Priority"
      value={
        assessment.case ? (
          <CasePriorityBadge priority={assessment.case.priority} />
        ) : (
          'N/A'
        )
      }
    />
    <AssessmentInfoField
      label="Evaluation Due"
      value={
        assessment.case?.evaluationDueDate
          ? formatDate(assessment.case.evaluationDueDate)
          : 'N/A'
      }
    />
    <AssessmentInfoField
      label="Test Count"
      value={`${assessment.testAdministrations.length} test${
        assessment.testAdministrations.length !== 1 ? 's' : ''
      }`}
    />
  </div>
);
