import { formatDate } from '@lilypad/shared/date';
import { Button } from '@lilypad/ui/components/button';
import { CardFooter } from '@lilypad/ui/components/card';

interface AssessmentActionsProps {
  sessionDate: Date;
  createdAt: Date;
  updatedAt: Date;
  onView?: VoidFunction;
  onEdit?: VoidFunction;
}

export const AssessmentActions: React.FC<AssessmentActionsProps> = ({
  sessionDate,
  createdAt,
  updatedAt,
  onView = () => {},
  onEdit = () => {},
}) => (
  <CardFooter className="flex justify-between gap-3 rounded-b-lg bg-muted px-4 py-3 lg:gap-4 lg:py-2">
    <div className="flex gap-1 text-muted-foreground text-xs sm:gap-1">
      <span>Created: {formatDate(createdAt)}</span>
      <span className="hidden text-muted-foreground/60 sm:inline">|</span>
      <span>Updated: {formatDate(updatedAt)}</span>
    </div>
    <div className="flex gap-2">
      {onEdit && (
        <Button variant="outline" size="sm" onClick={onEdit}>
          Edit
          <span className="hidden xl:inline">Assessment</span>
        </Button>
      )}
      {onView && (
        <Button variant="default" size="sm" onClick={onView}>
          View
          <span className="hidden xl:inline">Assessment</span>
        </Button>
      )}
    </div>
  </CardFooter>
);
