import { <PERSON>Enum } from '@lilypad/db/schema';
import type { User } from '@/shared/types';

export type TaskPermission =
  | 'view_tasks'
  | 'create_tasks'
  | 'edit_tasks'
  | 'delete_tasks'
  | 'assign_tasks'
  | 'approve_tasks'
  | 'reject_tasks'
  | 'complete_tasks'
  | 'view_all_tasks'
  | 'manage_system';

// Role-based permissions mapping
const ROLE_PERMISSIONS: Record<RoleEnum, TaskPermission[]> = {
  [RoleEnum.SUPER_USER]: [
    'view_tasks',
    'create_tasks',
    'edit_tasks',
    'delete_tasks',
    'assign_tasks',
    'approve_tasks',
    'reject_tasks',
    'complete_tasks',
    'view_all_tasks',
    'manage_system',
  ],
  [RoleEnum.SPECIAL_ED_DIRECTOR]: [
    'view_tasks',
    'create_tasks',
    'edit_tasks',
    'assign_tasks',
    'approve_tasks',
    'reject_tasks',
    'complete_tasks',
    'view_all_tasks',
  ],
  [RoleEnum.CLINICAL_DIRECTOR]: [
    'view_tasks',
    'create_tasks',
    'edit_tasks',
    'assign_tasks',
    'approve_tasks',
    'reject_tasks',
    'complete_tasks',
    'view_all_tasks',
  ],
  [RoleEnum.SCHOOL_COORDINATOR]: [
    'view_tasks',
    'create_tasks',
    'edit_tasks',
    'assign_tasks',
    'complete_tasks',
  ],
  [RoleEnum.SCHOOL_ADMIN]: [
    'view_tasks',
    'create_tasks',
    'edit_tasks',
    'assign_tasks',
    'complete_tasks',
  ],
  [RoleEnum.CASE_MANAGER]: ['view_tasks', 'edit_tasks', 'complete_tasks'],
  [RoleEnum.PSYCHOLOGIST]: ['view_tasks', 'edit_tasks', 'complete_tasks'],
  [RoleEnum.PROCTOR]: ['view_tasks', 'edit_tasks', 'complete_tasks'],
  [RoleEnum.ASSISTANT]: ['view_tasks', 'edit_tasks', 'complete_tasks'],
};

/**
 * Check if a user has a specific permission based on their roles
 */
export function hasTaskPermission(
  user: User | null,
  permission: TaskPermission
): boolean {
  if (!user?.rolePermissions?.roles) {
    return false;
  }

  // Check if any of the user's roles has the required permission
  return user.rolePermissions.roles.some((role) => {
    const rolePermissions = ROLE_PERMISSIONS[role.name as RoleEnum];
    return rolePermissions?.includes(permission);
  });
}

/**
 * Get all permissions for a user based on their roles
 */
export function getUserTaskPermissions(user: User | null): TaskPermission[] {
  if (!user?.rolePermissions?.roles) {
    return [];
  }

  const permissions = new Set<TaskPermission>();

  for (const role of user.rolePermissions.roles) {
    const rolePermissions = ROLE_PERMISSIONS[role.name as RoleEnum];
    if (rolePermissions) {
      for (const permission of rolePermissions) {
        permissions.add(permission);
      }
    }
  }

  return Array.from(permissions);
}

/**
 * Check if a user can perform actions on a specific task
 */
export function canEditTask(
  user: User | null,
  taskAssigneeId?: string
): boolean {
  if (!user) {
    return false;
  }

  // Admin roles can edit any task
  if (
    hasTaskPermission(user, 'edit_tasks') &&
    hasTaskPermission(user, 'view_all_tasks')
  ) {
    return true;
  }

  // Users can edit their own assigned tasks
  if (hasTaskPermission(user, 'edit_tasks') && taskAssigneeId === user.id) {
    return true;
  }

  return false;
}

/**
 * Check if a user can assign tasks to others
 */
export function canAssignTasks(user: User | null): boolean {
  return hasTaskPermission(user, 'assign_tasks');
}

/**
 * Check if a user can create new tasks
 */
export function canCreateTasks(user: User | null): boolean {
  return hasTaskPermission(user, 'create_tasks');
}

/**
 * Check if a user can approve/reject tasks
 */
export function canApproveRejectTasks(user: User | null): boolean {
  return (
    hasTaskPermission(user, 'approve_tasks') ||
    hasTaskPermission(user, 'reject_tasks')
  );
}

/**
 * Check if a user can view all tasks or only their own
 */
export function canViewAllTasks(user: User | null): boolean {
  return hasTaskPermission(user, 'view_all_tasks');
}

/**
 * Get the primary role of a user (highest priority role)
 */
export function getUserPrimaryRole(user: User | null): RoleEnum | null {
  if (!user?.rolePermissions?.roles) {
    return null;
  }

  // Define role hierarchy (highest to lowest priority)
  const roleHierarchy = [
    RoleEnum.SUPER_USER,
    RoleEnum.SPECIAL_ED_DIRECTOR,
    RoleEnum.CLINICAL_DIRECTOR,
    RoleEnum.SCHOOL_COORDINATOR,
    RoleEnum.SCHOOL_ADMIN,
    RoleEnum.CASE_MANAGER,
    RoleEnum.PSYCHOLOGIST,
    RoleEnum.PROCTOR,
    RoleEnum.ASSISTANT,
  ];

  const userRoles = user.rolePermissions.roles.map(
    (role) => role.name as RoleEnum
  );

  // Find the highest priority role for the user
  return roleHierarchy.find((role) => userRoles.includes(role)) || null;
}
