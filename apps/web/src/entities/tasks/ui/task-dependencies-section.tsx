'use client';

import { useTR<PERSON> } from '@lilypad/api/client';
import { TaskStatusEnum, TaskTypeEnumMap } from '@lilypad/db/enums';
import { Badge } from '@lilypad/ui/components/badge';
import { Button } from '@lilypad/ui/components/button';
import { If } from '@lilypad/ui/components/if';
import { Skeleton } from '@lilypad/ui/components/skeleton';
import { cn } from '@lilypad/ui/lib/utils';
import { useSuspenseQuery } from '@tanstack/react-query';
import {
  AlertTriangle,
  ArrowDownRight,
  ArrowUpLeft,
  Calendar,
  CheckCircle,
  ExternalLink,
  Flag,
  Link2,
  Lock,
} from 'lucide-react';
import type { TaskTableRow } from '@/entities/tasks/model/schema';
import { TaskPriorityEnumMap, TaskStatusEnumMap } from '@lilypad/db/enums';

interface TaskDependenciesSectionProps {
  task: TaskTableRow;
}

export function TaskDependenciesSection({ task }: TaskDependenciesSectionProps) {
  const trpc = useTRPC();

  const { data: dependencies, isError } = useSuspenseQuery(
    trpc.tasks.getTaskDependencies.queryOptions(task.id)
  );

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    }).format(date);
  };

  const getStatusColor = (status: TaskStatusEnum) => {
    switch (status) {
      case TaskStatusEnum.COMPLETED:
        return 'text-green-600 bg-green-50 border-green-200';
      case TaskStatusEnum.IN_PROGRESS:
        return 'text-blue-600 bg-blue-50 border-blue-200';
      case TaskStatusEnum.BLOCKED:
        return 'text-red-600 bg-red-50 border-red-200';
      case TaskStatusEnum.REJECTED:
        return 'text-red-600 bg-red-50 border-red-200';
      case TaskStatusEnum.CANCELLED:
        return 'text-gray-600 bg-gray-50 border-gray-200';
      case TaskStatusEnum.PENDING:
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'URGENT':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'HIGH':
        return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'MEDIUM':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'LOW':
        return 'text-green-600 bg-green-50 border-green-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getTaskTypeIcon = (taskType: string) => {
    const taskTypeInfo = TaskTypeEnumMap[taskType as keyof typeof TaskTypeEnumMap];
    const stage = taskTypeInfo?.stage;

    const stageColorMap = {
      'ONBOARDING': 'text-blue-600',
      'PRE_EVALUATION': 'text-purple-600',
      'EVALUATION': 'text-green-600',
      'POST_EVALUATION': 'text-orange-600',
    };

    return stageColorMap[stage as keyof typeof stageColorMap] || 'text-gray-600';
  };

  // Separate predecessors and successors
  const predecessorTasks = dependencies?.filter(dep => dep.successorTaskId === task.id) || [];
  const successorTasks = dependencies?.filter(dep => dep.predecessorTaskId === task.id) || [];

  // Check for blocking conditions
  const blockingTasks = predecessorTasks.filter(
    dep => dep.predecessorTask.status !== TaskStatusEnum.COMPLETED
  );

  const isBlocked = blockingTasks.length > 0;

  if (isError) {
    return (
      <div className="space-y-4">
        <h3 className="font-semibold text-lg">Dependencies</h3>
        <div className="rounded-lg border border-red-200 bg-red-50 p-4">
          <p className="text-red-600 text-sm">Failed to load task dependencies</p>
        </div>
      </div>
    );
  }

  if (!dependencies || dependencies.length === 0) {
    return (
      <div className="space-y-4">
        <h3 className="font-semibold text-lg">Dependencies</h3>
        <div className="rounded-lg border border-gray-300 border-dashed bg-gray-50 p-8 text-center">
          <Link2 className="mx-auto size-8 text-gray-400" />
          <p className="mt-2 text-gray-600 text-sm">No dependencies found</p>
          <p className="text-gray-500 text-xs">This task can be worked on independently</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <h3 className="font-semibold text-lg">Dependencies</h3>
        <If condition={isBlocked}>
          <Badge variant="destructive" className="text-xs">
            <Lock className="mr-1 size-3" />
            Blocked
          </Badge>
        </If>
      </div>

      {/* Blocking Warning */}
      <If condition={isBlocked}>
        <div className="rounded-lg border border-red-200 bg-red-50 p-4">
          <div className="flex items-start gap-3">
            <AlertTriangle className="mt-0.5 size-5 flex-shrink-0 text-red-600" />
            <div>
              <p className="font-medium text-red-900 text-sm">Task is blocked</p>
              <p className="text-red-700 text-sm">
                {blockingTasks.length} prerequisite task{blockingTasks.length === 1 ? '' : 's'} must be completed first
              </p>
            </div>
          </div>
        </div>
      </If>

      {/* Predecessor Tasks (Dependencies) */}
      <If condition={predecessorTasks.length > 0}>
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <ArrowUpLeft className="size-4 text-muted-foreground" />
            <h4 className="font-medium text-sm">Prerequisite Tasks</h4>
            <Badge variant="secondary" className="text-xs">
              {predecessorTasks.length}
            </Badge>
          </div>
          <div className="space-y-3">
            {predecessorTasks.map((dependency) => (
              <TaskDependencyCard
                key={dependency.id}
                dependency={dependency}
                type="predecessor"
                getStatusColor={getStatusColor}
                getPriorityColor={getPriorityColor}
                getTaskTypeIcon={getTaskTypeIcon}
                formatDate={formatDate}
              />
            ))}
          </div>
        </div>
      </If>

      {/* Successor Tasks (Dependent Tasks) */}
      <If condition={successorTasks.length > 0}>
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <ArrowDownRight className="size-4 text-muted-foreground" />
            <h4 className="font-medium text-sm">Dependent Tasks</h4>
            <Badge variant="secondary" className="text-xs">
              {successorTasks.length}
            </Badge>
          </div>
          <div className="space-y-3">
            {successorTasks.map((dependency) => (
              <TaskDependencyCard
                key={dependency.id}
                dependency={dependency}
                type="successor"
                getStatusColor={getStatusColor}
                getPriorityColor={getPriorityColor}
                getTaskTypeIcon={getTaskTypeIcon}
                formatDate={formatDate}
              />
            ))}
          </div>
        </div>
      </If>
    </div>
  );
}

interface TaskDependencyCardProps {
  dependency: any; // Will be typed properly when tRPC types are available
  type: 'predecessor' | 'successor';
  getStatusColor: (status: TaskStatusEnum) => string;
  getPriorityColor: (priority: string) => string;
  getTaskTypeIcon: (taskType: string) => string;
  formatDate: (date: Date) => string;
}

function TaskDependencyCard({
  dependency,
  type,
  getStatusColor,
  getPriorityColor,
  getTaskTypeIcon,
  formatDate,
}: TaskDependencyCardProps) {
  const relatedTask = type === 'predecessor' ? dependency.predecessorTask : dependency.successorTask;
  const taskTypeInfo = TaskTypeEnumMap[relatedTask.taskType as keyof typeof TaskTypeEnumMap];

  const isCompleted = relatedTask.status === TaskStatusEnum.COMPLETED;
  const isOverdue = relatedTask.dueDate && new Date(relatedTask.dueDate) < new Date() && !isCompleted;
  const isBlocking = type === 'predecessor' && !isCompleted;

  return (
    <div className={cn(
      "rounded-lg border bg-card p-4 transition-colors",
      isBlocking ? "border-red-200 bg-red-50" : "border-border bg-background",
      "hover:bg-muted/50"
    )}>
      <div className="flex items-start justify-between gap-4">
        <div className="min-w-0 flex-1 space-y-2">
          {/* Task Type and Title */}
          <div className="flex items-start gap-3">
            <div className={cn(
              "flex-shrink-0 rounded-lg border p-2",
              getTaskTypeIcon(relatedTask.taskType)
            )}>
              <Flag className="size-4" />
            </div>
            <div className="min-w-0 flex-1">
              <h5 className="font-medium text-sm leading-tight">
                {taskTypeInfo?.name || relatedTask.taskType}
              </h5>
              <p className="mt-1 text-muted-foreground text-xs">
                {taskTypeInfo?.description || 'Task description'}
              </p>
            </div>
          </div>

          {/* Status and Priority Badges */}
          <div className="flex flex-wrap items-center gap-2">
            <Badge
              variant="outline"
              className={cn("text-xs", getStatusColor(relatedTask.status))}
            >
              <If condition={isCompleted}>
                <CheckCircle className="mr-1 size-3" />
              </If>
              <If condition={isBlocking}>
                <Lock className="mr-1 size-3" />
              </If>
              {TaskStatusEnumMap[relatedTask.status]}
            </Badge>
            <Badge
              variant="outline"
              className={cn("text-xs", getPriorityColor(relatedTask.priority))}
            >
              {TaskPriorityEnumMap[relatedTask.priority]}
            </Badge>
            <If condition={taskTypeInfo?.stage}>
              <Badge variant="secondary" className="text-xs">
                {taskTypeInfo.stage}
              </Badge>
            </If>
          </div>

          {/* Due Date and Completion Status */}
          <div className="flex items-center gap-4 text-muted-foreground text-xs">
            <If condition={relatedTask.dueDate}>
              {(dueDate) => (
                <div className="flex items-center gap-1">
                  <Calendar className="size-3" />
                  <span className={cn(
                    isOverdue ? 'font-medium text-red-600' : ""
                  )}>
                    Due {formatDate(new Date(dueDate))}
                  </span>
                </div>
              )}
            </If>
            <If condition={relatedTask.completedAt}>
              {(completedAt) => (
                <div className="flex items-center gap-1">
                  <CheckCircle className="size-3 text-green-600" />
                  <span className="text-green-600">
                    Completed {formatDate(new Date(completedAt))}
                  </span>
                </div>
              )}
            </If>
          </div>

          {/* Blocking Warning */}
          <If condition={isBlocking}>
            <div className="flex items-center gap-2 text-red-600 text-xs">
              <AlertTriangle className="size-3" />
              <span>This task must be completed first</span>
            </div>
          </If>
        </div>

        {/* Action Button */}
        <Button variant="ghost" size="sm" className="flex-shrink-0">
          <ExternalLink className="size-3" />
        </Button>
      </div>
    </div>
  );
}

export function TaskDependenciesSectionSkeleton() {
  return (
    <div className="space-y-6">
      <Skeleton className="h-6 w-32" />
      <div className="space-y-3">
        <div className="flex items-center gap-2">
          <Skeleton className="h-4 w-4" />
          <Skeleton className="h-4 w-32" />
          <Skeleton className="h-5 w-8" />
        </div>
        <div className="space-y-3">
          {Array.from({ length: 2 }).map((_, index) => (
            <div key={index} className="rounded-lg border bg-card p-4">
              <div className="flex items-start justify-between gap-4">
                <div className="min-w-0 flex-1 space-y-2">
                  <div className="flex items-start gap-3">
                    <Skeleton className="h-10 w-10 rounded-lg" />
                    <div className="flex-1 space-y-1">
                      <Skeleton className="h-4 w-3/4" />
                      <Skeleton className="h-3 w-full" />
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Skeleton className="h-5 w-16" />
                    <Skeleton className="h-5 w-12" />
                    <Skeleton className="h-5 w-20" />
                  </div>
                  <div className="flex items-center gap-4">
                    <Skeleton className="h-3 w-24" />
                    <Skeleton className="h-3 w-32" />
                  </div>
                </div>
                <Skeleton className="h-8 w-8" />
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
} 