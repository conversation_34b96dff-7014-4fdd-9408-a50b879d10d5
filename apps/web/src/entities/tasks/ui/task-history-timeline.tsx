'use client';

import { useTRPC } from '@lilypad/api/client';
import { TaskHistoryActionEnum } from '@lilypad/db/enums';
import { Avatar, AvatarFallback } from '@lilypad/ui/components/avatar';
import { Badge } from '@lilypad/ui/components/badge';
import { If } from '@lilypad/ui/components/if';
import { Skeleton } from '@lilypad/ui/components/skeleton';
import { cn } from '@lilypad/ui/lib/utils';
import { useSuspenseQuery } from '@tanstack/react-query';
import {
  ArrowRight,
  Calendar,
  CheckCircle,
  Clock,
  Edit,
  Flag,
  MessageSquare,
  RotateCcw,
  UserCheck,
  UserPlus,
  UserX,
  X,
} from 'lucide-react';
import type { TaskTableRow } from '@/entities/tasks/model/schema';

interface TaskHistoryTimelineProps {
  task: TaskTableRow;
}

export function TaskHistoryTimeline({ task }: TaskHistoryTimelineProps) {
  const trpc = useTRPC();

  const { data: history, isError } = useSuspenseQuery(
    trpc.tasks.getTaskHistory.queryOptions(task.id)
  );

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase();
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const formatRelativeDate = (date: Date) => {
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
    const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

    if (diffInMinutes < 1) {
      return 'Just now';
    }
    if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`;
    }
    if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    }
    if (diffInDays < 7) {
      return `${diffInDays}d ago`;
    }

    return formatDate(date);
  };

  const getActionIcon = (action: TaskHistoryActionEnum) => {
    const iconMap = {
      [TaskHistoryActionEnum.CREATED]: <UserPlus className="size-4 text-blue-600" />,
      [TaskHistoryActionEnum.ASSIGNED]: <UserCheck className="size-4 text-green-600" />,
      [TaskHistoryActionEnum.REASSIGNED]: <UserX className="size-4 text-orange-600" />,
      [TaskHistoryActionEnum.STATUS_CHANGED]: <ArrowRight className="size-4 text-purple-600" />,
      [TaskHistoryActionEnum.PRIORITY_CHANGED]: <Flag className="size-4 text-yellow-600" />,
      [TaskHistoryActionEnum.DUE_DATE_CHANGED]: <Calendar className="size-4 text-blue-600" />,
      [TaskHistoryActionEnum.COMPLETED]: <CheckCircle className="size-4 text-green-600" />,
      [TaskHistoryActionEnum.CANCELLED]: <X className="size-4 text-red-600" />,
      [TaskHistoryActionEnum.NOTES_ADDED]: <MessageSquare className="size-4 text-gray-600" />,
      [TaskHistoryActionEnum.REOPENED]: <RotateCcw className="size-4 text-blue-600" />,
      [TaskHistoryActionEnum.REJECTED]: <X className="size-4 text-red-600" />,
    };

    return iconMap[action] || <Edit className="size-4 text-gray-600" />;
  };

  const getActionDescription = (
    action: TaskHistoryActionEnum,
    previousStatus?: string,
    newStatus?: string
  ) => {
    const descriptionMap = {
      [TaskHistoryActionEnum.CREATED]: 'Task created',
      [TaskHistoryActionEnum.ASSIGNED]: 'Task assigned',
      [TaskHistoryActionEnum.REASSIGNED]: 'Task reassigned',
      [TaskHistoryActionEnum.STATUS_CHANGED]: previousStatus && newStatus
        ? `Status changed from ${previousStatus.toLowerCase().replace('_', ' ')} to ${newStatus.toLowerCase().replace('_', ' ')}`
        : 'Status changed',
      [TaskHistoryActionEnum.PRIORITY_CHANGED]: 'Priority changed',
      [TaskHistoryActionEnum.DUE_DATE_CHANGED]: 'Due date changed',
      [TaskHistoryActionEnum.COMPLETED]: 'Task completed',
      [TaskHistoryActionEnum.CANCELLED]: 'Task cancelled',
      [TaskHistoryActionEnum.NOTES_ADDED]: 'Notes added',
      [TaskHistoryActionEnum.REOPENED]: 'Task reopened',
      [TaskHistoryActionEnum.REJECTED]: 'Task rejected',
    };

    return descriptionMap[action] || 'Task updated';
  };

  const getActionColor = (action: TaskHistoryActionEnum) => {
    const colorMap = {
      [TaskHistoryActionEnum.CREATED]: 'bg-blue-100 border-blue-200',
      [TaskHistoryActionEnum.ASSIGNED]: 'bg-green-100 border-green-200',
      [TaskHistoryActionEnum.REASSIGNED]: 'bg-orange-100 border-orange-200',
      [TaskHistoryActionEnum.STATUS_CHANGED]: 'bg-purple-100 border-purple-200',
      [TaskHistoryActionEnum.PRIORITY_CHANGED]: 'bg-yellow-100 border-yellow-200',
      [TaskHistoryActionEnum.DUE_DATE_CHANGED]: 'bg-blue-100 border-blue-200',
      [TaskHistoryActionEnum.COMPLETED]: 'bg-green-100 border-green-200',
      [TaskHistoryActionEnum.CANCELLED]: 'bg-red-100 border-red-200',
      [TaskHistoryActionEnum.NOTES_ADDED]: 'bg-gray-100 border-gray-200',
      [TaskHistoryActionEnum.REOPENED]: 'bg-blue-100 border-blue-200',
      [TaskHistoryActionEnum.REJECTED]: 'bg-red-100 border-red-200',
    };

    return colorMap[action] || 'bg-gray-100 border-gray-200';
  };

  if (isError) {
    return (
      <div className="space-y-4">
        <h3 className="font-semibold text-lg">Task History</h3>
        <div className="rounded-lg border border-red-200 bg-red-50 p-4">
          <p className="text-red-600 text-sm">Failed to load task history</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h3 className="font-semibold text-lg">Task History</h3>

      <If condition={history && history.length > 0} fallback={
        <div className="rounded-lg border border-gray-300 border-dashed bg-gray-50 p-8 text-center">
          <Clock className="mx-auto size-8 text-gray-400" />
          <p className="mt-2 text-gray-600 text-sm">No history available</p>
        </div>
      }>
        <div className="space-y-4">
          {history?.map((entry, index) => (
            <div key={entry.id} className="relative flex gap-4">
              {/* Timeline Line */}
              <If condition={index < history.length - 1}>
                <div className="absolute top-12 left-5 h-full w-0.5 bg-border" />
              </If>

              {/* Action Icon */}
              <div className={cn(
                "flex-shrink-0 rounded-full border-2 p-2",
                getActionColor(entry.action)
              )}>
                {getActionIcon(entry.action)}
              </div>

              {/* Content */}
              <div className="min-w-0 flex-1 space-y-2 pb-4">
                <div className="flex items-start justify-between gap-4">
                  <div className="min-w-0 flex-1">
                    <p className="font-medium text-sm">
                      {getActionDescription(entry.action, entry.previousStatus ?? undefined, entry.newStatus ?? undefined)}
                    </p>
                    <div className="mt-1 flex items-center gap-2">
                      <Avatar className="h-6 w-6">
                        <AvatarFallback className="text-xs">
                          {getInitials(entry.user.fullName)}
                        </AvatarFallback>
                      </Avatar>
                      <span className="text-muted-foreground text-xs">
                        {entry.user.fullName}
                      </span>
                    </div>
                  </div>

                  <div className="flex-shrink-0 text-right">
                    <p className="text-muted-foreground text-xs">
                      {formatRelativeDate(entry.createdAt)}
                    </p>
                    <p className="text-muted-foreground text-xs">
                      {formatDate(entry.createdAt)}
                    </p>
                  </div>
                </div>

                {/* Status Change Details */}
                <If condition={entry.action === TaskHistoryActionEnum.STATUS_CHANGED && entry.previousStatus && entry.newStatus}>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-xs">
                      {entry.previousStatus?.toLowerCase().replace('_', ' ')}
                    </Badge>
                    <ArrowRight className="size-3 text-muted-foreground" />
                    <Badge variant="outline" className="text-xs">
                      {entry.newStatus?.toLowerCase().replace('_', ' ')}
                    </Badge>
                  </div>
                </If>
              </div>
            </div>
          ))}
        </div>
      </If>
    </div>
  );
}

export function TaskHistoryTimelineSkeleton() {
  return (
    <div className="space-y-4">
      <Skeleton className="h-6 w-32" />
      <div className="space-y-4">
        {Array.from({ length: 3 }).map((_, index) => (
          <div key={index} className="flex gap-4">
            <Skeleton className="h-10 w-10 rounded-full" />
            <div className="flex-1 space-y-2">
              <Skeleton className="h-4 w-3/4" />
              <div className="flex items-center gap-2">
                <Skeleton className="h-6 w-6 rounded-full" />
                <Skeleton className="h-3 w-24" />
              </div>
            </div>
            <div className="space-y-1">
              <Skeleton className="h-3 w-16" />
              <Skeleton className="h-3 w-20" />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
} 