import { Avatar, AvatarFallback } from '@lilypad/ui/components/avatar';
import { Badge } from '@lilypad/ui/components/badge';
import type React from 'react';

interface TaskAssigneesProps {
  assignedToName: string;
  assignedByName?: string;
  assignedToId: string;
  assignedById: string;
}

export const TaskAssignees: React.FC<TaskAssigneesProps> = ({
  assignedToName,
  assignedByName,
  assignedToId,
  assignedById,
}) => {
  const initials = getInitials(assignedToName);
  const isSystemGenerated = assignedToId === assignedById;

  return (
    <div className="flex flex-col gap-2">
      <div className="font-semibold text-muted-foreground">Assigned to</div>
      
      <div className="flex items-center gap-2">
        <Avatar className="h-6 w-6 border-2 border-primary/20 bg-muted">
          <AvatarFallback className="text-xs">{initials}</AvatarFallback>
        </Avatar>
        
        <div className="flex flex-col">
          <span className="text-sm font-medium">{assignedToName}</span>
          
          {isSystemGenerated ? (
            <Badge variant="secondary" className="w-fit text-xs">
              System
            </Badge>
          ) : (
            assignedByName && (
              <span className="text-xs text-muted-foreground">
                Assigned by {assignedByName}
              </span>
            )
          )}
        </div>
      </div>
    </div>
  );
};

function getInitials(name: string): string {
  return name
    .split(' ')
    .map(n => n[0])
    .join('')
    .toUpperCase();
}