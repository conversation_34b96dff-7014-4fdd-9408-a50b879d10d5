import type { LucideIcon } from 'lucide-react';
import {
  CalendarIcon,
  CheckCircleIcon,
  CheckIcon,
  ClipboardListIcon,
  ClockIcon,
  Edit3Icon,
  EyeIcon,
  FileCheckIcon,
  FileTextIcon,
  SendIcon,
  SettingsIcon,
  UploadIcon,
  UserPlusIcon,
  UsersIcon,
  XIcon,
} from 'lucide-react';

export interface TaskAction {
  id: string;
  label: string;
  icon?: LucideIcon;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'destructive';
  className?: string;
  handler: (taskId: string) => void | Promise<void>;
}

export interface TaskConfig {
  icon: LucideIcon;
  iconColor: string;
  iconBgColor: string;
  badgeColor: string;
  actions?: TaskAction[];
}

export const taskTypeConfigs: Record<string, TaskConfig> = {
  // ONBOARDING STAGE
  ASSIGN_PSYCHOLOGIST: {
    icon: UserPlusIcon,
    iconColor: 'text-purple-600 dark:text-purple-400',
    iconBgColor: 'bg-purple-100 dark:bg-purple-900',
    badgeColor:
      'border-purple-200 dark:border-purple-700 bg-purple-50 dark:bg-purple-900 text-purple-600 dark:text-purple-400',
    actions: [
      {
        id: 'assign',
        label: 'Assign',
        icon: UserPlusIcon,
        variant: 'default',
        handler: (_id) => {
          //
        },
      },
      {
        id: 'view',
        label: 'View',
        icon: EyeIcon,
        variant: 'outline',
        handler: (_id) => {
          //
        },
      },
    ],
  },
  REVIEW_DISTRICT_ASSIGNMENT: {
    icon: FileTextIcon,
    iconColor: 'text-blue-600 dark:text-blue-400',
    iconBgColor: 'bg-blue-100 dark:bg-blue-900',
    badgeColor:
      'border-blue-200 dark:border-blue-700 bg-blue-50 dark:bg-blue-900 text-blue-600 dark:text-blue-400',
    actions: [
      {
        id: 'review',
        label: 'Review',
        icon: EyeIcon,
        variant: 'default',
        handler: (_id) => {
          //
        },
      },
      {
        id: 'accept',
        label: 'Accept',
        icon: CheckIcon,
        variant: 'outline',
        handler: (_id) => {
          //
        },
      },
      {
        id: 'reject',
        label: 'Reject',
        icon: XIcon,
        variant: 'outline',
        handler: (_id) => {
          //
        },
      },
    ],
  },
  SHIP_EVALUATION_MATERIALS: {
    icon: UploadIcon,
    iconColor: 'text-green-600 dark:text-green-400',
    iconBgColor: 'bg-green-100 dark:bg-green-900',
    badgeColor:
      'border-green-200 dark:border-green-700 bg-green-50 dark:bg-green-900 text-green-600 dark:text-green-400',
    actions: [
      {
        id: 'ship',
        label: 'Ship Materials',
        icon: SendIcon,
        variant: 'default',
        handler: (_id) => {
          //
        },
      },
      {
        id: 'track',
        label: 'Track',
        icon: EyeIcon,
        variant: 'outline',
        handler: (_id) => {
          //
        },
      },
    ],
  },

  // PRE_EVALUATION STAGE
  UPDATE_AVAILABILITY: {
    icon: CalendarIcon,
    iconColor: 'text-orange-600 dark:text-orange-400',
    iconBgColor: 'bg-orange-100 dark:bg-orange-900',
    badgeColor:
      'border-orange-200 dark:border-orange-700 bg-orange-50 dark:bg-orange-900 text-orange-600 dark:text-orange-400',
    actions: [
      {
        id: 'update',
        label: 'Update',
        icon: Edit3Icon,
        variant: 'default',
        handler: (_id) => {
          //
        },
      },
      {
        id: 'view',
        label: 'View Calendar',
        icon: EyeIcon,
        variant: 'outline',
        handler: (_id) => {
          //
        },
      },
    ],
  },
  COMPLETE_REFERRAL_FORM: {
    icon: FileTextIcon,
    iconColor: 'text-indigo-600 dark:text-indigo-400',
    iconBgColor: 'bg-indigo-100 dark:bg-indigo-900',
    badgeColor:
      'border-indigo-200 dark:border-indigo-700 bg-indigo-50 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-400',
    actions: [
      {
        id: 'complete',
        label: 'Complete Form',
        icon: CheckIcon,
        variant: 'default',
        handler: (_id) => {
          //
        },
      },
      {
        id: 'preview',
        label: 'Preview',
        icon: EyeIcon,
        variant: 'outline',
        handler: (_id) => {
          //
        },
      },
    ],
  },
  SCHEDULE_STUDENT_EVALUATIONS: {
    icon: CalendarIcon,
    iconColor: 'text-teal-600 dark:text-teal-400',
    iconBgColor: 'bg-teal-100 dark:bg-teal-900',
    badgeColor:
      'border-teal-200 dark:border-teal-700 bg-teal-50 dark:bg-teal-900 text-teal-600 dark:text-teal-400',
    actions: [
      {
        id: 'schedule',
        label: 'Schedule',
        icon: CalendarIcon,
        variant: 'default',
        handler: (_id) => {
          //
        },
      },
      {
        id: 'view-slots',
        label: 'View Slots',
        icon: EyeIcon,
        variant: 'outline',
        handler: (_id) => {
          //
        },
      },
    ],
  },
  CREATE_EVALUATION_PLAN: {
    icon: ClipboardListIcon,
    iconColor: 'text-cyan-600 dark:text-cyan-400',
    iconBgColor: 'bg-cyan-100 dark:bg-cyan-900',
    badgeColor:
      'border-cyan-200 dark:border-cyan-700 bg-cyan-50 dark:bg-cyan-900 text-cyan-600 dark:text-cyan-400',
    actions: [
      {
        id: 'create',
        label: 'Create Plan',
        icon: Edit3Icon,
        variant: 'default',
        handler: (_id) => {
          //
        },
      },
      {
        id: 'template',
        label: 'Use Template',
        icon: FileTextIcon,
        variant: 'outline',
        handler: (_id) => {
          //
        },
      },
    ],
  },
  REVIEW_AND_SEND_RATING_SCALES: {
    icon: FileCheckIcon,
    iconColor: 'text-pink-600 dark:text-pink-400',
    iconBgColor: 'bg-pink-100 dark:bg-pink-900',
    badgeColor:
      'border-pink-200 dark:border-pink-700 bg-pink-50 dark:bg-pink-900 text-pink-600 dark:text-pink-400',
    actions: [
      {
        id: 'review',
        label: 'Review',
        icon: EyeIcon,
        variant: 'default',
        handler: (_id) => {
          //
        },
      },
      {
        id: 'send',
        label: 'Send',
        icon: SendIcon,
        variant: 'outline',
        handler: (_id) => {
          //
        },
      },
    ],
  },

  // EVALUATION STAGE
  JOIN_EVALUATION_AS_PROCTOR: {
    icon: UsersIcon,
    iconColor: 'text-violet-600 dark:text-violet-400',
    iconBgColor: 'bg-violet-100 dark:bg-violet-900',
    badgeColor:
      'border-violet-200 dark:border-violet-700 bg-violet-50 dark:bg-violet-900 text-violet-600 dark:text-violet-400',
    actions: [
      {
        id: 'join',
        label: 'Join Session',
        icon: UsersIcon,
        variant: 'default',
        handler: (_id) => {
          //
        },
      },
      {
        id: 'status',
        label: 'Update Status',
        icon: SettingsIcon,
        variant: 'outline',
        handler: (_id) => {
          //
        },
      },
    ],
  },
  JOIN_EVALUATION_AS_PSYCHOLOGIST: {
    icon: UsersIcon,
    iconColor: 'text-purple-600 dark:text-purple-400',
    iconBgColor: 'bg-purple-100 dark:bg-purple-900',
    badgeColor:
      'border-purple-200 dark:border-purple-700 bg-purple-50 dark:bg-purple-900 text-purple-600 dark:text-purple-400',
    actions: [
      {
        id: 'join',
        label: 'Join Session',
        icon: UsersIcon,
        variant: 'default',
        handler: (_id) => {
          //
        },
      },
      {
        id: 'record',
        label: 'Record Notes',
        icon: Edit3Icon,
        variant: 'outline',
        handler: (_id) => {
          //
        },
      },
    ],
  },
  MARK_EVALUATION_COMPLETE: {
    icon: CheckCircleIcon,
    iconColor: 'text-green-600 dark:text-green-400',
    iconBgColor: 'bg-green-100 dark:bg-green-900',
    badgeColor:
      'border-green-200 dark:border-green-700 bg-green-50 dark:bg-green-900 text-green-600 dark:text-green-400',
    actions: [
      {
        id: 'complete',
        label: 'Mark Complete',
        icon: CheckCircleIcon,
        variant: 'default',
        handler: (_id) => {
          //
        },
      },
      {
        id: 'extend',
        label: 'Request Extension',
        icon: ClockIcon,
        variant: 'outline',
        handler: (_id) => {
          //
        },
      },
    ],
  },

  // POST_EVALUATION STAGE
  GENERATE_REPORT_DRAFT: {
    icon: FileTextIcon,
    iconColor: 'text-blue-600 dark:text-blue-400',
    iconBgColor: 'bg-blue-100 dark:bg-blue-900',
    badgeColor:
      'border-blue-200 dark:border-blue-700 bg-blue-50 dark:bg-blue-900 text-blue-600 dark:text-blue-400',
    actions: [
      {
        id: 'generate',
        label: 'Generate Draft',
        icon: FileTextIcon,
        variant: 'default',
        handler: (_id) => {
          //
        },
      },
      {
        id: 'template',
        label: 'Use Template',
        icon: SettingsIcon,
        variant: 'outline',
        handler: (_id) => {
          //
        },
      },
    ],
  },
  FINALIZE_EVALUATION_REPORT: {
    icon: FileCheckIcon,
    iconColor: 'text-green-600 dark:text-green-400',
    iconBgColor: 'bg-green-100 dark:bg-green-900',
    badgeColor:
      'border-green-200 dark:border-green-700 bg-green-50 dark:bg-green-900 text-green-600 dark:text-green-400',
    actions: [
      {
        id: 'finalize',
        label: 'Finalize Report',
        icon: CheckCircleIcon,
        variant: 'default',
        handler: (_id) => {
          //
        },
      },
      {
        id: 'review',
        label: 'Review Draft',
        icon: EyeIcon,
        variant: 'outline',
        handler: (_id) => {
          //
        },
      },
    ],
  },
  SCHEDULE_IEP_MEETING: {
    icon: CalendarIcon,
    iconColor: 'text-orange-600 dark:text-orange-400',
    iconBgColor: 'bg-orange-100 dark:bg-orange-900',
    badgeColor:
      'border-orange-200 dark:border-orange-700 bg-orange-50 dark:bg-orange-900 text-orange-600 dark:text-orange-400',
    actions: [
      {
        id: 'schedule',
        label: 'Schedule Meeting',
        icon: CalendarIcon,
        variant: 'default',
        handler: (_id) => {
          //
        },
      },
      {
        id: 'check-availability',
        label: 'Check Availability',
        icon: EyeIcon,
        variant: 'outline',
        handler: (_id) => {
          //
        },
      },
    ],
  },
  COMPLETE_IEP_MEETING: {
    icon: CheckCircleIcon,
    iconColor: 'text-green-600 dark:text-green-400',
    iconBgColor: 'bg-green-100 dark:bg-green-900',
    badgeColor:
      'border-green-200 dark:border-green-700 bg-green-50 dark:bg-green-900 text-green-600 dark:text-green-400',
    actions: [
      {
        id: 'complete',
        label: 'Complete Meeting',
        icon: CheckCircleIcon,
        variant: 'default',
        handler: (_id) => {
          //
        },
      },
      {
        id: 'notes',
        label: 'Add Notes',
        icon: Edit3Icon,
        variant: 'outline',
        handler: (_id) => {
          //
        },
      },
    ],
  },
};

export const defaultTaskConfig: TaskConfig = {
  icon: ClipboardListIcon,
  iconColor: 'text-gray-600 dark:text-gray-400',
  iconBgColor: 'bg-gray-100 dark:bg-gray-900',
  badgeColor:
    'border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900 text-gray-600 dark:text-gray-400',
  actions: [
    {
      id: 'view',
      label: 'View',
      icon: EyeIcon,
      variant: 'outline',
      handler: (_id) => {
        //
      },
    },
  ],
};

export const getTaskConfig = (taskType: string): TaskConfig => {
  return taskTypeConfigs[taskType] || defaultTaskConfig;
};
