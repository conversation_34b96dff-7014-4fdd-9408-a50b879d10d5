import { Button } from '@lilypad/ui/components/button';
import { CardFooter } from '@lilypad/ui/components/card';
import { formatDate } from '@lilypad/shared/date';
import type React from 'react';
import type { TaskAction } from './task-config';

interface TaskFooterActionsProps {
  createdAt: Date;
  updatedAt: Date;
  taskId: string;
  actions?: TaskAction[];
  onAction?: (actionId: string, taskId: string) => void;
}

export const TaskFooterActions: React.FC<TaskFooterActionsProps> = ({
  createdAt,
  updatedAt,
  taskId,
  actions = [],
  onAction,
}) => {
  const handleAction = (actionId: string) => (e: React.MouseEvent) => {
    e.stopPropagation();
    onAction?.(actionId, taskId);
  };

  return (
    <CardFooter className="flex justify-between rounded-b-lg bg-muted px-4 py-2">
      <div className="flex items-center gap-4 text-muted-foreground text-xs">
        <span>Created on {formatDate(createdAt)}</span>|
        <span>Last updated on {formatDate(updatedAt)}</span>
      </div>
      
      {actions && actions.length > 0 && (
        <div className="flex gap-2">
          {actions.map((action) => (
            <Button
              key={action.id}
              variant={action.variant || 'outline'}
              size="sm"
              onClick={handleAction(action.id)}
              className={action.className}
            >
              {action.icon && <action.icon className="mr-1 size-3" />}
              {action.label}
            </Button>
          ))}
        </div>
      )}
    </CardFooter>
  );
};