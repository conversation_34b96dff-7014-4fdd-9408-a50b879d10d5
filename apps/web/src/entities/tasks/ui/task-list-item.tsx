import {
  TaskPriorityEnumMap,
  type TaskStatusEnum,
  type TaskTypeEnum,
  TaskTypeEnumMap
} from '@lilypad/db/enums';
import { formatDate } from '@lilypad/shared/date';
import { Avatar, AvatarFallback, AvatarImage } from '@lilypad/ui/components/avatar';
import { Badge } from '@lilypad/ui/components/badge';
import { cn } from '@lilypad/ui/lib/utils';
import { ClockIcon, MessageSquareIcon } from 'lucide-react';
import type { MouseEvent } from 'react';
import type { TaskTableRow } from '@/entities/tasks/model/schema';
import { getTaskConfig } from './task-config';

interface TaskListItemProps {
  task: TaskTableRow;
  onTaskClick?: (task: TaskTableRow) => void;
  onStatusChange?: (
    taskId: string,
    newStatus: TaskStatusEnum
  ) => Promise<void> | void;
  className?: string;
}

// Priority badge variants
const priorityVariants = {
  LOW: 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300',
  MEDIUM: 'bg-yellow-100 text-yellow-700 dark:bg-yellow-800/20 dark:text-yellow-300',
  HIGH: 'bg-orange-100 text-orange-700 dark:bg-orange-800/20 dark:text-orange-300',
  URGENT: 'bg-red-100 text-red-700 dark:bg-red-800/20 dark:text-red-300',
};

// Status indicator variants
const statusVariants = {
  PENDING: 'bg-gray-400',
  IN_PROGRESS: 'bg-blue-500',
  COMPLETED: 'bg-green-500',
  CANCELLED: 'bg-gray-500',
  BLOCKED: 'bg-red-500',
  REJECTED: 'bg-red-600',
};

export function TaskListItem({
  task,
  onTaskClick,
  className,
}: TaskListItemProps) {
  const taskConfig = getTaskConfig(task.taskType);
  const taskDetails = TaskTypeEnumMap[task.taskType as TaskTypeEnum];

  function handleItemClick(e: MouseEvent<HTMLDivElement>) {
    if (
      e.target instanceof HTMLElement &&
      (e.target.closest('[data-dropdown-trigger]') ||
        e.target.closest('[data-dropdown-content]'))
    ) {
      return;
    }
    if (onTaskClick) {
      onTaskClick(task);
    }
  }

  const isOverdue = task.dueDate && new Date(task.dueDate) < new Date() && task.status !== 'COMPLETED';

  function handleKeyDown(e: React.KeyboardEvent<HTMLDivElement>) {
    if (e.key === 'Enter') {
      if (onTaskClick) {
        onTaskClick(task);
      }
    }
  }

  return (
    <div
      className={cn(
        'group flex cursor-pointer items-center gap-3 rounded-md px-3 py-2 transition-all duration-200 hover:bg-muted/50',
        'border-transparent border-l-2 hover:border-l-border',
        className
      )}
      onClick={handleItemClick}
      onKeyDown={handleKeyDown}
      role="button"
      tabIndex={0}
    >
      <div
        className={cn(
          'size-3 flex-shrink-0 rounded-full',
          statusVariants[task.status as keyof typeof statusVariants]
        )}
      />

      <span className="w-16 flex-shrink-0 font-mono text-muted-foreground text-xs">
        {task.id.slice(0, 8)}
      </span>

      <div className="flex-shrink-0">
        <taskConfig.icon className="size-4 text-muted-foreground" />
      </div>

      <div className="min-w-0 flex-1">
        <div className="flex items-center gap-2">
          <h4 className="truncate font-medium text-sm">
            {taskDetails.name}
          </h4>

          <Badge
            className={cn(
              'flex-shrink-0 border-0 px-1.5 py-0 text-xs',
              priorityVariants[task.priority as keyof typeof priorityVariants]
            )}
            variant="outline"
          >
            {TaskPriorityEnumMap[task.priority]}
          </Badge>
        </div>

        <div className="mt-1 flex items-center gap-4 text-muted-foreground text-xs">
          <span className="flex items-center gap-1">
            <span>{taskDetails.stage}</span>
          </span>

          {task.studentName && (
            <span className="flex items-center gap-1">
              <span>Student: {task.studentName}</span>
            </span>
          )}

          {task.schoolName && (
            <span className="flex items-center gap-1 truncate">
              <span>{task.schoolName}</span>
            </span>
          )}
        </div>
      </div>

      <div className="flex flex-shrink-0 items-center gap-4">
        <div className="flex items-center gap-1.5">
          <Avatar className="size-5">
            <AvatarImage src={task.assignedTo.avatar || ''} alt={task.assignedToName} />
            <AvatarFallback className="text-xs">
              {task.assignedToName.split(' ').map(n => n[0]).join('')}
            </AvatarFallback>
          </Avatar>
          <span className="hidden text-muted-foreground text-xs sm:block">
            {task.assignedToName.split(' ')[0]}
          </span>
        </div>

        {task.dueDate && (
          <div className={cn(
            'flex items-center gap-1 text-xs',
            isOverdue ? 'text-red-500' : 'text-muted-foreground'
          )}>
            <ClockIcon className="size-3" />
            <span className="hidden sm:block">{formatDate(task.dueDate, { month: 'short', day: 'numeric' })}</span>
          </div>
        )}

        <div className="flex items-center gap-1 text-muted-foreground text-xs">
          <MessageSquareIcon className="size-3" />
          <span className="hidden sm:block">0</span>
        </div>
      </div>
    </div>
  );
}
