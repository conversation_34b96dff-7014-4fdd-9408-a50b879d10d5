import { Badge } from '@lilypad/ui/components/badge';
import { If } from '@lilypad/ui/components/if';
import type React from 'react';
import { getTaskConfig } from './task-config';

interface TaskDetailsSectionProps {
  task: {
    taskType: string;
    notes?: string | null;
  };
}

export const TaskDetailsSection: React.FC<TaskDetailsSectionProps> = ({ task }) => {
  const taskConfig = getTaskConfig(task.taskType);
  
  return (
    <div className="flex flex-col border-border border-t border-dashed pt-4 text-sm">
      <div className="mb-1 font-medium">Task Details</div>
      
      <div className="mb-3">
        <Badge className="text-xs" variant="secondary">
          {taskConfig.stage}
        </Badge>
      </div>

      <If condition={!task.notes}>
        {() => (
          <div className="flex flex-col items-center gap-2 text-muted-foreground">
            <span>No notes available.</span>
          </div>
        )}
      </If>
      <If condition={!!task.notes}>
        {() => (
          <div className="space-y-1">
            <p className="line-clamp-3 text-muted-foreground text-xs">{task.notes}</p>
          </div>
        )}
      </If>
    </div>
  );
};