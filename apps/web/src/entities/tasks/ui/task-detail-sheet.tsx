'use client';

import { TaskTypeEnumMap } from '@lilypad/db/enums';
import { Avatar, AvatarFallback } from '@lilypad/ui/components/avatar';
import { Badge } from '@lilypad/ui/components/badge';
import { Button } from '@lilypad/ui/components/button';
import { If } from '@lilypad/ui/components/if';
import { Separator } from '@lilypad/ui/components/separator';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from '@lilypad/ui/components/sheet';
import { cn } from '@lilypad/ui/lib/utils';
import {
  Award,
  BarChart,
  Calendar,
  CalendarCheck,
  CalendarDays,
  CheckCircle,
  Clipboard,
  Clock,
  Edit,
  Eye,
  FileCheck,
  FileEdit,
  FileText,
  Flag,
  FolderOpen,
  GraduationCap,
  MessageCircle,
  Package,
  School,
  Send,
  Star,
  Upload,
  UserCheck,
  UserPlus,
  Users,
  Video,
} from 'lucide-react';
import React from 'react';
import type { TaskTableRow } from '@/entities/tasks/model/schema';
import { TaskPriorityEnumMap, TaskStatusEnumMap } from '@lilypad/db/enums';
import { TaskActions } from './task-actions';
import { TaskDependenciesSection } from './task-dependencies-section';
import { TaskEditForm } from './task-edit-form';
import { TaskHistoryTimeline } from './task-history-timeline';

interface TaskDetailSheetProps {
  task: TaskTableRow | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onEdit?: (task: TaskTableRow) => void;
  onUpdate?: (taskId: string, data: {
    title?: string;
    description?: string;
    priority?: string;
    status?: string;
    dueDate?: Date;
    notes?: string;
  }) => Promise<void> | void;
}

export function TaskDetailSheet({
  task,
  open,
  onOpenChange,
  onEdit,
  onUpdate,
}: TaskDetailSheetProps) {
  const [isEditMode, setIsEditMode] = React.useState(false);
  const [isUpdating, setIsUpdating] = React.useState(false);

  if (!task) {
    return null;
  }

  const handleEditClick = () => {
    setIsEditMode(true);
    onEdit?.(task);
  };

  const handleEditCancel = () => {
    setIsEditMode(false);
  };

  const handleEditSubmit = async (data: any) => {
    if (!onUpdate) { return; }

    setIsUpdating(true);
    try {
      await onUpdate(task.id, data);
      setIsEditMode(false);
    } catch (_error) {
    } finally {
      setIsUpdating(false);
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase();
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const formatRelativeDate = (date: Date) => {
    const now = new Date();
    const diffInDays = Math.ceil((date.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) { return 'Today'; }
    if (diffInDays === 1) { return 'Tomorrow'; }
    if (diffInDays === -1) { return 'Yesterday'; }
    if (diffInDays > 0) { return `In ${diffInDays} days`; }
    return `${Math.abs(diffInDays)} days ago`;
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'URGENT':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'HIGH':
        return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'MEDIUM':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'LOW':
        return 'text-green-600 bg-green-50 border-green-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'IN_PROGRESS':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'BLOCKED':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'REJECTED':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'CANCELLED':
        return 'text-gray-600 bg-gray-50 border-gray-200';
      case 'PENDING':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getTaskTypeIcon = (taskType: string) => {
    // Icon mapping for specific task types
    const taskIconMap: Record<string, React.ReactElement> = {
      // ONBOARDING STAGE
      'ASSIGN_PSYCHOLOGIST': <UserPlus className="size-5 text-blue-600" />,
      'REVIEW_DISTRICT_ASSIGNMENT': <Eye className="size-5 text-blue-600" />,
      'SHIP_EVALUATION_MATERIALS': <Package className="size-5 text-blue-600" />,

      // PRE_EVALUATION STAGE
      'UPDATE_AVAILABILITY': <Calendar className="size-5 text-purple-600" />,
      'COMPLETE_REFERRAL_FORM': <FileText className="size-5 text-purple-600" />,
      'SCHEDULE_STUDENT_EVALUATIONS': <CalendarCheck className="size-5 text-purple-600" />,
      'GENERATE_CALENDAR_INVITES': <CalendarDays className="size-5 text-purple-600" />,
      'CREATE_EVALUATION_PLAN': <Clipboard className="size-5 text-purple-600" />,
      'PREPARE_RATING_SCALES': <Star className="size-5 text-purple-600" />,
      'REVIEW_AND_SEND_RATING_SCALES': <Star className="size-5 text-purple-600" />,
      'MONITOR_RATING_SCALES': <Star className="size-5 text-purple-600" />,
      'PREPARE_ASSESSMENT_MATERIALS': <FolderOpen className="size-5 text-purple-600" />,
      'PREPARE_FOR_EVALUATION': <Clipboard className="size-5 text-purple-600" />,

      // EVALUATION STAGE
      'JOIN_EVALUATION_AS_PROCTOR': <Video className="size-5 text-green-600" />,
      'JOIN_EVALUATION_AS_PSYCHOLOGIST': <Video className="size-5 text-green-600" />,
      'MARK_EVALUATION_COMPLETE': <CheckCircle className="size-5 text-green-600" />,
      'COMPLETE_STUDENT_INTERVIEW': <MessageCircle className="size-5 text-green-600" />,
      'UPDLOAD_PROTOCOLS': <Upload className="size-5 text-green-600" />,
      'UPDATE_ASSESSMENT_SCORES': <BarChart className="size-5 text-green-600" />,

      // POST_EVALUATION STAGE
      'GENERATE_REPORT_DRAFT': <FileEdit className="size-5 text-orange-600" />,
      'FINALIZE_EVALUATION_REPORT': <FileCheck className="size-5 text-orange-600" />,
      'SCORE_REPORT_QUALITY': <Award className="size-5 text-orange-600" />,
      'REVIEW_FINAL_REPORT': <Eye className="size-5 text-orange-600" />,
      'MARK_REPORT_RECEIVED': <CheckCircle className="size-5 text-orange-600" />,
      'SCHEDULE_IEP_MEETING': <CalendarDays className="size-5 text-orange-600" />,
      'PREPARE_FOR_IEP_MEETING': <Clipboard className="size-5 text-orange-600" />,
      'SEND_MEETING_INVITATIONS': <Send className="size-5 text-orange-600" />,
      'COMPLETE_IEP_MEETING': <CheckCircle className="size-5 text-orange-600" />,
    };

    // Check if we have a specific icon for this task type
    if (taskIconMap[taskType]) {
      return taskIconMap[taskType];
    }

    // Fallback to stage-based icons
    const taskTypeMap = TaskTypeEnumMap[taskType as keyof typeof TaskTypeEnumMap];
    const stage = taskTypeMap?.stage;

    const stageIconMap = {
      'ONBOARDING': <Users className="size-5 text-blue-600" />,
      'PRE_EVALUATION': <Calendar className="size-5 text-purple-600" />,
      'EVALUATION': <UserCheck className="size-5 text-green-600" />,
      'POST_EVALUATION': <CheckCircle className="size-5 text-orange-600" />,
    };

    return stageIconMap[stage as keyof typeof stageIconMap] || <Flag className="size-5 text-gray-600" />;
  };

  const taskTypeInfo = TaskTypeEnumMap[task.taskType as keyof typeof TaskTypeEnumMap];
  const isOverdue = task.dueDate && new Date(task.dueDate) < new Date();

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="min-w-[600px] overflow-y-auto">
        <SheetHeader className="space-y-4">
          <div className="flex items-start justify-between">
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0 rounded-lg border bg-background p-3">
                {getTaskTypeIcon(task.taskType)}
              </div>
              <div className="min-w-0 flex-1">
                <SheetTitle className="text-xl leading-7">
                  {taskTypeInfo?.name || 'Unknown Task Type'}
                </SheetTitle>
                <SheetDescription className="mt-2 text-base">
                  {taskTypeInfo?.description || 'No description available'}
                </SheetDescription>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleEditClick}
                className="flex-shrink-0"
              >
                <Edit className="size-4" />
                Edit
              </Button>
              <TaskActions task={task} />
            </div>
          </div>

          <div className="flex items-center gap-3">
            <Badge
              variant="outline"
              className={cn("text-sm", getStatusColor(task.status))}
            >
              {TaskStatusEnumMap[task.status]}
            </Badge>
            <Badge
              variant="outline"
              className={cn("text-sm", getPriorityColor(task.priority))}
            >
              {TaskPriorityEnumMap[task.priority]}
            </Badge>
            <Badge variant="secondary" className="text-sm">
              Stage: {taskTypeInfo?.stage || 'Unknown'}
            </Badge>
          </div>
        </SheetHeader>

        <Separator className="my-6" />

        <div className="space-y-8">
          {/* Assignment Information */}
          <div className="space-y-4">
            <h3 className="font-semibold text-lg">Assignment Information</h3>
            <div className="grid grid-cols-1 gap-4">
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <UserCheck className="size-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">Assigned To</p>
                    <p className="text-muted-foreground text-sm">{task.assignedToName}</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <Avatar className="h-8 w-8">
                    <AvatarFallback className="text-sm">
                      {getInitials(task.assignedByName)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium">Assigned By</p>
                    <p className="text-muted-foreground text-sm">{task.assignedByName}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Timeline Information */}
          <div className="space-y-4">
            <h3 className="font-semibold text-lg">Timeline</h3>
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <Clock className="size-5 text-muted-foreground" />
                <div>
                  <p className="font-medium">Created</p>
                  <p className="text-muted-foreground text-sm">{formatDate(task.createdAt)}</p>
                </div>
              </div>

              <If condition={task.dueDate}>
                {(dueDate) => (
                  <div className="flex items-center gap-3">
                    <Calendar className={cn(
                      "size-5",
                      isOverdue ? "text-red-600" : "text-muted-foreground"
                    )} />
                    <div>
                      <p className="font-medium">Due Date</p>
                      <p className={cn(
                        "text-sm",
                        isOverdue ? 'font-medium text-red-600' : "text-muted-foreground"
                      )}>
                        {formatDate(new Date(dueDate))} ({formatRelativeDate(new Date(dueDate))})
                      </p>
                    </div>
                  </div>
                )}
              </If>

              <div className="flex items-center gap-3">
                <Clock className="size-5 text-muted-foreground" />
                <div>
                  <p className="font-medium">Last Updated</p>
                  <p className="text-muted-foreground text-sm">{formatDate(task.updatedAt)}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Student & School Context */}
          <If condition={task.studentName || task.schoolName}>
            <div className="space-y-4">
              <h3 className="font-semibold text-lg">Context</h3>
              <div className="space-y-3">
                <If condition={task.studentName}>
                  {(studentName) => (
                    <div className="flex items-center gap-3">
                      <GraduationCap className="size-5 text-muted-foreground" />
                      <div>
                        <p className="font-medium">Student</p>
                        <p className="text-muted-foreground text-sm">{studentName}</p>
                      </div>
                    </div>
                  )}
                </If>

                <If condition={task.schoolName}>
                  {(schoolName) => (
                    <div className="flex items-center gap-3">
                      <School className="size-5 text-muted-foreground" />
                      <div>
                        <p className="font-medium">School</p>
                        <p className="text-muted-foreground text-sm">{schoolName}</p>
                      </div>
                    </div>
                  )}
                </If>
              </div>
            </div>
          </If>

          {/* Notes Section */}
          <If condition={task.notes}>
            {(notes) => (
              <div className="space-y-4">
                <h3 className="font-semibold text-lg">Notes</h3>
                <div className="rounded-lg bg-muted/50 p-4">
                  <p className="whitespace-pre-wrap text-sm leading-relaxed">{notes}</p>
                </div>
              </div>
            )}
          </If>

          {/* Task Details */}
          <div className="space-y-4">
            <h3 className="font-semibold text-lg">Details</h3>
            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Task ID</span>
                <span className="font-mono">{task.id}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Task Type</span>
                <span>{task.taskType}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Stage</span>
                <span>{taskTypeInfo?.stage || 'Unknown'}</span>
              </div>
            </div>
          </div>

          {/* Task Dependencies */}
          <div className="space-y-4">
            <Separator />
            <TaskDependenciesSection task={task} />
          </div>

          {/* Task History Timeline */}
          <div className="space-y-4">
            <Separator />
            <TaskHistoryTimeline task={task} />
          </div>
        </div>

        {isEditMode && (
          <TaskEditForm
            task={task}
            onCancel={handleEditCancel}
            onSubmit={handleEditSubmit}
            isUpdating={isUpdating}
          />
        )}
      </SheetContent>
    </Sheet>
  );
} 