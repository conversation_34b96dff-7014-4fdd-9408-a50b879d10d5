import { type TaskStatusEnum, type TaskTypeEnum, TaskTypeEnumMap } from '@lilypad/db/enums';
import { Card, CardContent } from '@lilypad/ui/components/card';
import { cn } from '@lilypad/ui/lib/utils';
import type { MouseEvent } from 'react';
import type { TaskTableRow } from '@/entities/tasks/model/schema';
import { TaskAssignees } from './task-assignees';
import { getTaskConfig } from './task-config';
import { TaskDetailsSection } from './task-details-section';
import { TaskFooterActions } from './task-footer-actions';
import { TaskInfoGrid } from './task-info-grid';

interface TaskCardProps {
  task: TaskTableRow;
  onTaskClick?: (task: TaskTableRow) => void;
  compact?: boolean;
  onStatusChange?: (
    taskId: string,
    newStatus: TaskStatusEnum
  ) => Promise<void> | void;
  className?: string;
}

export function TaskCard({
  task,
  onTaskClick,
  // compact = true,
  onStatusChange,
  className,
}: TaskCardProps) {
  const taskConfig = getTaskConfig(task.taskType);
  const taskDetails = TaskTypeEnumMap[task.taskType as TaskTypeEnum];

  function handleCardClick(e: MouseEvent<HTMLDivElement>) {
    if (
      e.target instanceof HTMLElement &&
      (e.target.closest('[data-dropdown-trigger]') ||
        e.target.closest('[data-dropdown-content]'))
    ) {
      return;
    }
    if (onTaskClick) {
      onTaskClick(task);
    }
  }

  function handleAction(actionId: string, taskId: string) {
    // Handle different action types
    switch (actionId) {
      case 'start':
        onStatusChange?.(taskId, 'IN_PROGRESS' as TaskStatusEnum);
        break;
      case 'complete':
        onStatusChange?.(taskId, 'COMPLETED' as TaskStatusEnum);
        break;
      case 'block':
        onStatusChange?.(taskId, 'BLOCKED' as TaskStatusEnum);
        break;
      default:
    }
  }

    return (
      <Card
        className={cn('w-full cursor-pointer p-0', className)}
        onClick={handleCardClick}
      >
        <CardContent className="flex flex-col gap-4 p-4">
          <div className="flex gap-4">
              <div
                className={cn(
                  'rounded-full size-14 grid place-items-center font-semibold text-foreground text-sm',
                  taskConfig.iconBgColor,
                  taskConfig.iconColor,
                )}
              >
                <taskConfig.icon className="size-5" />
              </div>
            <div className="flex flex-col gap-1">
              <h3 className="font-medium">{taskDetails.name}</h3>
              <p className="text-muted-foreground text-sm">{taskDetails.description}</p>
            </div>
          </div>
          <div className="grid grid-cols-12 gap-4 text-xs">
            <TaskInfoGrid task={task} />
          </div>
          <TaskDetailsSection task={task} />
        </CardContent>
        <TaskFooterActions
          actions={taskConfig.actions}
          createdAt={task.createdAt}
          onAction={handleAction}
          taskId={task.id}
          updatedAt={task.updatedAt}
        />
      </Card>
    );
}
