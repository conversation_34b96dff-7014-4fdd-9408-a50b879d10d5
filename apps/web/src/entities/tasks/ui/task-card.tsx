import {
  type TaskStatusEnum,
  type TaskTypeEnum,
  TaskTypeEnumMap,
  TaskPriorityEnumMap
} from '@lilypad/db/enums';
import { formatDate } from '@lilypad/shared/date';
import { Avatar, AvatarFallback, AvatarImage } from '@lilypad/ui/components/avatar';
import { Badge } from '@lilypad/ui/components/badge';
import { cn } from '@lilypad/ui/lib/utils';
import { ClockIcon } from 'lucide-react';
import type { MouseEvent } from 'react';
import type { TaskTableRow } from '@/entities/tasks/model/schema';
import { getTaskConfig } from './task-config';

interface TaskCardProps {
  task: TaskTableRow;
  onTaskClick?: (task: TaskTableRow) => void;
  onStatusChange?: (
    taskId: string,
    newStatus: TaskStatusEnum
  ) => Promise<void> | void;
  className?: string;
}

// Priority badge variants
const priorityVariants = {
  LOW: 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300',
  MEDIUM: 'bg-yellow-100 text-yellow-700 dark:bg-yellow-800/20 dark:text-yellow-300',
  HIGH: 'bg-orange-100 text-orange-700 dark:bg-orange-800/20 dark:text-orange-300',
  URGENT: 'bg-red-100 text-red-700 dark:bg-red-800/20 dark:text-red-300',
};

// Status indicator variants
const statusVariants = {
  PENDING: 'bg-gray-400',
  IN_PROGRESS: 'bg-blue-500',
  COMPLETED: 'bg-green-500',
  CANCELLED: 'bg-gray-500',
  BLOCKED: 'bg-red-500',
  REJECTED: 'bg-red-600',
};

export function TaskCard({
  task,
  onTaskClick,
  className,
}: TaskCardProps) {
  const taskConfig = getTaskConfig(task.taskType);
  const taskDetails = TaskTypeEnumMap[task.taskType as TaskTypeEnum];

  function handleCardClick(e: MouseEvent<HTMLDivElement>) {
    if (
      e.target instanceof HTMLElement &&
      (e.target.closest('[data-dropdown-trigger]') ||
        e.target.closest('[data-dropdown-content]'))
    ) {
      return;
    }
    if (onTaskClick) {
      onTaskClick(task);
    }
  }

  const isOverdue = task.dueDate && new Date(task.dueDate) < new Date() && task.status !== 'COMPLETED';

  return (
    <div
      className={cn(
        'group relative cursor-pointer rounded-lg border bg-card p-3 transition-all duration-200 hover:border-border/60 hover:shadow-sm',
        'dark:border-border dark:bg-card',
        className
      )}
      onClick={handleCardClick}
    >
      {/* Status indicator and priority */}
      <div className="flex items-start gap-3">
        <div className="flex min-w-0 flex-1 items-center gap-2">
          <div
            className={cn(
              'size-3 rounded-full',
              statusVariants[task.status as keyof typeof statusVariants]
            )}
          />

          {/* Task ID */}
          <span className="font-mono text-muted-foreground text-xs">
            {task.id.slice(0, 8)}
          </span>
        </div>

        {/* Priority badge */}
        <Badge
          variant="outline"
          className={cn(
            'border-0 px-2 py-0.5 text-xs',
            priorityVariants[task.priority as keyof typeof priorityVariants]
          )}
        >
          {TaskPriorityEnumMap[task.priority]}
        </Badge>
      </div>

      {/* Task title */}
      <div className="mt-2 mb-3">
        <h3 className="line-clamp-2 font-medium text-sm leading-tight">
          {taskDetails.name}
        </h3>
      </div>

      {/* Task metadata */}
      <div className="flex items-center justify-between text-muted-foreground text-xs">
        <div className="flex items-center gap-3">
          {/* Assignee */}
          <div className="flex items-center gap-1.5">
            <Avatar className="size-4">
              <AvatarImage src={task.assignedTo.avatar || ''} alt={task.assignedToName} />
              <AvatarFallback className="text-xs">
                {task.assignedToName.split(' ').map(n => n[0]).join('')}
              </AvatarFallback>
            </Avatar>
            <span className="max-w-20 truncate">{task.assignedToName.split(' ')[0]}</span>
          </div>

          {/* Due date */}
          {task.dueDate && (
            <div className={cn(
              'flex items-center gap-1',
              isOverdue && 'text-red-500'
            )}>
              <ClockIcon className="size-3" />
              <span>{formatDate(task.dueDate, { month: 'short', day: 'numeric' })}</span>
            </div>
          )}
        </div>

        {/* Task type badge */}
        <div className="flex items-center gap-1">
          <taskConfig.icon className="size-3" />
          <span className="text-xs">{taskDetails.stage}</span>
        </div>
      </div>
    </div>
  );
}
