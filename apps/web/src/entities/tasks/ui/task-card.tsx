import type { TaskStatusEnum } from '@lilypad/db/enums';
import { Card, CardContent } from '@lilypad/ui/components/card';
import { cn } from '@lilypad/ui/lib/utils';
import type { MouseEvent } from 'react';
import type { TaskTableRow } from '@/entities/tasks/model/schema';
import { TaskAssignees } from './task-assignees';
import { getTaskConfig } from './task-config';
import { TaskDetailsSection } from './task-details-section';
import { TaskFooterActions } from './task-footer-actions';
import { TaskInfoGrid } from './task-info-grid';

interface TaskCardProps {
  task: TaskTableRow;
  onTaskClick?: (task: TaskTableRow) => void;
  compact?: boolean;
  onStatusChange?: (
    taskId: string,
    newStatus: TaskStatusEnum
  ) => Promise<void> | void;
  className?: string;
}

export function TaskCard({
  task,
  onTaskClick,
  compact = true,
  onStatusChange,
  className,
}: TaskCardProps) {
  console.log({ taskType: task.taskType });
  const taskConfig = getTaskConfig(task.taskType);

  function handleCardClick(e: MouseEvent<HTMLDivElement>) {
    if (
      e.target instanceof HTMLElement &&
      (e.target.closest('[data-dropdown-trigger]') ||
        e.target.closest('[data-dropdown-content]'))
    ) {
      return;
    }
    if (onTaskClick) {
      onTaskClick(task);
    }
  }

  function handleAction(actionId: string, taskId: string) {
    // Handle different action types
    switch (actionId) {
      case 'start':
        onStatusChange?.(taskId, 'IN_PROGRESS' as TaskStatusEnum);
        break;
      case 'complete':
        onStatusChange?.(taskId, 'COMPLETED' as TaskStatusEnum);
        break;
      case 'block':
        onStatusChange?.(taskId, 'BLOCKED' as TaskStatusEnum);
        break;
      default:
    }
  }

  if (compact) {
    return (
      <Card
        className={cn('w-full cursor-pointer p-0', className)}
        onClick={handleCardClick}
      >
        <CardContent className="flex flex-col gap-4 p-4">
          <div className="grid grid-cols-12 gap-4 text-xs">
            <div className="col-span-12 flex flex-col gap-2 font-medium md:col-span-2">
              <span className="font-semibold text-foreground text-sm">
                <taskConfig.icon className="h-4 w-4" />
              </span>
              <TaskAssignees
                assignedById={task.assignedBy.id}
                assignedByName={task.assignedByName}
                assignedToId={task.assignedTo.id}
                assignedToName={task.assignedToName}
              />
            </div>
            <TaskInfoGrid task={task} />
          </div>
          <TaskDetailsSection task={task} />
        </CardContent>
        <TaskFooterActions
          actions={taskConfig.actions}
          createdAt={task.createdAt}
          onAction={handleAction}
          taskId={task.id}
          updatedAt={task.updatedAt}
        />
      </Card>
    )
  }

  return (
    <Card
      className={cn('w-full cursor-pointer p-0', className)}
      onClick={handleCardClick}
    >
      <CardContent className="flex flex-col gap-4 p-4">
        <div className="grid grid-cols-12 gap-4 text-xs">
          <div className="col-span-12 flex flex-col gap-2 font-medium md:col-span-2">
            <span className="font-semibold text-foreground text-sm">
              taskConfig.stage
            </span>
            <TaskAssignees
              assignedById={task.assignedBy.id}
              assignedByName={task.assignedByName}
              assignedToId={task.assignedTo.id}
              assignedToName={task.assignedToName}
            />
          </div>
          <TaskInfoGrid task={task} />
        </div>
        <TaskDetailsSection task={task} />
      </CardContent>
      <TaskFooterActions
        actions={taskConfig.actions}
        createdAt={task.createdAt}
        onAction={handleAction}
        taskId={task.id}
        updatedAt={task.updatedAt}
      />
    </Card>
  );
}
