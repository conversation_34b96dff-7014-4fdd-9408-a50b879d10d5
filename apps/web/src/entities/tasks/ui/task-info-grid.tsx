import { Badge } from '@lilypad/ui/components/badge';
import { formatDate } from '@lilypad/shared/date';
import type React from 'react';
import type { TaskTableRow } from '@/entities/tasks/model/schema';
import { TaskStatusEnumMap, TaskPriorityEnumMap } from '@lilypad/db/enums';
import { getTaskConfig } from './task-config';

interface TaskInfoFieldProps {
  label: string;
  value: string | React.ReactNode;
  className?: string;
}

const TaskInfoField: React.FC<TaskInfoFieldProps> = ({ label, value, className }) => (
  <div className={`flex flex-col gap-1 ${className || ''}`}>
    <span className="font-medium text-muted-foreground">{label}</span>
    <div className="font-medium">{value}</div>
  </div>
);

interface TaskInfoGridProps {
  task: TaskTableRow;
}

export const TaskInfoGrid: React.FC<TaskInfoGridProps> = ({ task }) => {
  const taskConfig = getTaskConfig(task.taskType);
  const isOverdue = task.dueDate && new Date(task.dueDate) < new Date();

  return (
    <div className="col-span-10 grid grid-cols-3 gap-2 space-y-1">
      <TaskInfoField
        label="Status"
        value={
          <Badge 
            className={`text-xs ${getStatusColor(task.status)}`} 
            variant="outline"
          >
            {TaskStatusEnumMap[task.status]}
          </Badge>
        }
      />
      <TaskInfoField
        label="Priority"
        value={
          <Badge 
            className={`text-xs ${getPriorityColor(task.priority)}`} 
            variant="outline"
          >
            {TaskPriorityEnumMap[task.priority]}
          </Badge>
        }
      />
      <TaskInfoField
        label="Type"
        value={
          <Badge 
            className={`text-xs ${taskConfig.badgeColor}`} 
            variant="outline"
          >
            {taskConfig.stage}
          </Badge>
        }
      />
      <TaskInfoField 
        label="Due" 
        value={
          task.dueDate ? (
            <span className={isOverdue ? 'font-semibold text-red-600 dark:text-red-400' : 'text-muted-foreground'}>
              {formatDate(new Date(task.dueDate))}
            </span>
          ) : 'N/A'
        } 
      />
      <TaskInfoField 
        label="Student" 
        value={task.studentName || 'N/A'} 
      />
      <TaskInfoField 
        label="School" 
        value={task.schoolName || 'N/A'} 
      />
    </div>
  );
};

function getStatusColor(status: string): string {
  switch (status) {
    case 'COMPLETED':
      return 'text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900 border-green-200 dark:border-green-700';
    case 'IN_PROGRESS':
      return 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900 border-blue-200 dark:border-blue-700';
    case 'BLOCKED':
    case 'REJECTED':
      return 'text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900 border-red-200 dark:border-red-700';
    case 'CANCELLED':
      return 'text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-900 border-gray-200 dark:border-gray-700';
    case 'PENDING':
      return 'text-yellow-600 dark:text-yellow-400 bg-yellow-50 dark:bg-yellow-900 border-yellow-200 dark:border-yellow-700';
    default:
      return 'text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-900 border-gray-200 dark:border-gray-700';
  }
}

function getPriorityColor(priority: string): string {
  switch (priority) {
    case 'URGENT':
      return 'text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900 border-red-200 dark:border-red-700';
    case 'HIGH':
      return 'text-orange-600 dark:text-orange-400 bg-orange-50 dark:bg-orange-900 border-orange-200 dark:border-orange-700';
    case 'MEDIUM':
      return 'text-yellow-600 dark:text-yellow-400 bg-yellow-50 dark:bg-yellow-900 border-yellow-200 dark:border-yellow-700';
    case 'LOW':
      return 'text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900 border-green-200 dark:border-green-700';
    default:
      return 'text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-900 border-gray-200 dark:border-gray-700';
  }
}