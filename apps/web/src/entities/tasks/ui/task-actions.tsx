'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@lilypad/ui/components/alert-dialog';
import { Button } from '@lilypad/ui/components/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@lilypad/ui/components/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@lilypad/ui/components/dropdown-menu';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@lilypad/ui/components/form';
import { Input } from '@lilypad/ui/components/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@lilypad/ui/components/select';
import { Copy, Loader2, MoreHorizontal, Trash2, UserX } from 'lucide-react';
import React from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import type { TaskTableRow } from '@/entities/tasks/model/schema';

// Mock users data - in real implementation, this would come from the API
const mockUsers = [
  { id: '1', name: 'Dr. Sarah Wilson', email: '<EMAIL>' },
  { id: '2', name: 'Dr. Michael Chen', email: '<EMAIL>' },
  { id: '3', name: 'Dr. Emily Rodriguez', email: '<EMAIL>' },
  { id: '4', name: 'Dr. David Thompson', email: '<EMAIL>' },
];

// Reassign form schema
const reassignSchema = z.object({
  assignedToId: z.string().min(1, 'Please select a user to assign the task to'),
  notes: z.string().max(500, 'Notes must be less than 500 characters').optional(),
});

type ReassignFormData = z.infer<typeof reassignSchema>;

interface TaskActionsProps {
  task: TaskTableRow;
  onDelete?: (taskId: string) => Promise<void> | void;
  onDuplicate?: (task: TaskTableRow) => Promise<void> | void;
  onReassign?: (taskId: string, data: ReassignFormData) => Promise<void> | void;
  disabled?: boolean;
  className?: string;
}

export function TaskActions({
  task,
  onDelete,
  onDuplicate,
  onReassign,
  disabled = false,
  className,
}: TaskActionsProps) {
  const [showDeleteDialog, setShowDeleteDialog] = React.useState(false);
  const [showReassignDialog, setShowReassignDialog] = React.useState(false);
  const [isDeleting, setIsDeleting] = React.useState(false);
  const [isDuplicating, setIsDuplicating] = React.useState(false);
  const [isReassigning, setIsReassigning] = React.useState(false);

  const reassignForm = useForm<ReassignFormData>({
    resolver: zodResolver(reassignSchema),
    defaultValues: {
      assignedToId: '',
      notes: '',
    },
  });

  const handleDelete = async () => {
    if (!onDelete) { return; }

    setIsDeleting(true);
    try {
      await onDelete(task.id);
      setShowDeleteDialog(false);
    } catch (error) {
      console.error('Delete error:', error);
      setIsDeleting(false);
    }
  };

  const handleDuplicate = async () => {
    if (!onDuplicate) { return; }

    setIsDuplicating(true);
    try {
      await onDuplicate(task);
    } catch (error) {
      console.error('Duplicate error:', error);
    } finally {
      setIsDuplicating(false);
    }
  };

  const handleReassign = async (data: ReassignFormData) => {
    if (!onReassign) { return; }

    setIsReassigning(true);
    try {
      await onReassign(task.id, data);
      setShowReassignDialog(false);
      reassignForm.reset();
    } catch (error) {
      console.error('Reassign error:', error);
    } finally {
      setIsReassigning(false);
    };

    const handleReassignCancel = () => {
      setShowReassignDialog(false);
      reassignForm.reset();
    };

    return (
      <>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              disabled={disabled}
              className={className}
            >
              <MoreHorizontal className="h-4 w-4" />
              Actions
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuItem
              onClick={() => setShowReassignDialog(true)}
              disabled={!onReassign}
            >
              <UserX className="mr-2 h-4 w-4" />
              Reassign Task
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={handleDuplicate}
              disabled={!onDuplicate || isDuplicating}
            >
              {isDuplicating ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Copy className="mr-2 h-4 w-4" />
              )}
              Duplicate Task
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              className="text-red-600 focus:text-red-600"
              onClick={() => setShowDeleteDialog(true)}
              disabled={!onDelete}
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete Task
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Delete Confirmation Dialog */}
        <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Delete Task</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to delete this task? This action cannot be undone.
                <br />
                <br />
                <strong>Task:</strong> {task.title || 'Untitled Task'}
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDelete}
                disabled={isDeleting}
                className="bg-red-600 text-white hover:bg-red-700"
              >
                {isDeleting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Delete Task
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Reassign Dialog */}
        <Dialog open={showReassignDialog} onOpenChange={setShowReassignDialog}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Reassign Task</DialogTitle>
              <DialogDescription>
                Select a new person to assign this task to.
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              <div className="rounded-lg border bg-muted/50 p-4">
                <div className="space-y-1">
                  <p className="font-medium text-sm">{task.title || 'Untitled Task'}</p>
                  <p className="text-muted-foreground text-xs">
                    Currently assigned to: {task.assignedToName}
                  </p>
                </div>
              </div>

              <Form {...reassignForm}>
                <form onSubmit={reassignForm.handleSubmit(handleReassign)} className="space-y-4">
                  <FormField
                    control={reassignForm.control}
                    name="assignedToId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Assign To</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          disabled={isReassigning}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a person" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {mockUsers.map((user) => (
                              <SelectItem key={user.id} value={user.id}>
                                <div className="flex flex-col">
                                  <span className="font-medium">{user.name}</span>
                                  <span className="text-muted-foreground text-xs">{user.email}</span>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={reassignForm.control}
                    name="notes"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Notes (Optional)</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Add a note about the reassignment..."
                            {...field}
                            disabled={isReassigning}
                          />
                        </FormControl>
                        <FormDescription>
                          Optional note to include with the reassignment.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <DialogFooter className="gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleReassignCancel}
                      disabled={isReassigning}
                    >
                      Cancel
                    </Button>
                    <Button type="submit" disabled={isReassigning}>
                      {isReassigning && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                      Reassign Task
                    </Button>
                  </DialogFooter>
                </form>
              </Form>
            </div>
          </DialogContent>
        </Dialog>
      </>
    );
  }
}