'use client';

import type { TaskStatusEnum } from '@lilypad/db/enums';
import { TaskStatusEnum as StatusEnum } from '@lilypad/db/enums';
import { Badge } from '@lilypad/ui/components/badge';
import { Button } from '@lilypad/ui/components/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@lilypad/ui/components/dropdown-menu';
import { cn } from '@lilypad/ui/lib/utils';
import {
  CheckCircle,
  Circle,
  Clock,
  LoaderIcon,
  ShieldAlert,
  X,
  XCircle,
} from 'lucide-react';
import React from 'react';
import { useTaskActions } from '@/features/task-management/model/use-task-actions';
import { TaskStatusEnumMap } from '@lilypad/db/enums';

interface TaskStatusDropdownProps {
  taskId: string;
  currentStatus: TaskStatusEnum;
  onStatusChange?: (newStatus: TaskStatusEnum) => Promise<void> | void;
  size?: 'xs' | 'sm' | 'md' | 'lg';
  variant?: 'default' | 'outline' | 'secondary' | 'ghost';
  showIcon?: boolean;
  disabled?: boolean;
  className?: string;
}

// Status configuration for icons and colors
const statusConfig = {
  [StatusEnum.PENDING]: {
    icon: Circle,
    color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    label: TaskStatusEnumMap[StatusEnum.PENDING],
  },
  [StatusEnum.IN_PROGRESS]: {
    icon: Clock,
    color: 'bg-blue-100 text-blue-800 border-blue-200',
    label: TaskStatusEnumMap[StatusEnum.IN_PROGRESS],
  },
  [StatusEnum.COMPLETED]: {
    icon: CheckCircle,
    color: 'bg-green-100 text-green-800 border-green-200',
    label: TaskStatusEnumMap[StatusEnum.COMPLETED],
  },
  [StatusEnum.CANCELLED]: {
    icon: X,
    color: 'bg-gray-100 text-gray-800 border-gray-200',
    label: TaskStatusEnumMap[StatusEnum.CANCELLED],
  },
  [StatusEnum.BLOCKED]: {
    icon: ShieldAlert,
    color: 'bg-red-100 text-red-800 border-red-200',
    label: TaskStatusEnumMap[StatusEnum.BLOCKED],
  },
  [StatusEnum.REJECTED]: {
    icon: XCircle,
    color: 'bg-orange-100 text-orange-800 border-orange-200',
    label: TaskStatusEnumMap[StatusEnum.REJECTED],
  },
};

// Available status options (excluding current status)
const getAvailableStatuses = (currentStatus: TaskStatusEnum): TaskStatusEnum[] => {
  const allStatuses = Object.values(StatusEnum);

  // Business rules: what statuses can be changed to from current status
  switch (currentStatus) {
    case StatusEnum.PENDING:
      return [StatusEnum.IN_PROGRESS, StatusEnum.BLOCKED, StatusEnum.CANCELLED];
    case StatusEnum.IN_PROGRESS:
      return [StatusEnum.COMPLETED, StatusEnum.BLOCKED, StatusEnum.CANCELLED];
    case StatusEnum.BLOCKED:
      return [StatusEnum.PENDING, StatusEnum.IN_PROGRESS, StatusEnum.CANCELLED];
    case StatusEnum.COMPLETED:
      return []; // Completed tasks cannot be changed
    case StatusEnum.CANCELLED:
      return [StatusEnum.PENDING]; // Can reopen cancelled tasks
    case StatusEnum.REJECTED:
      return [StatusEnum.PENDING]; // Can reopen rejected tasks
    default:
      return allStatuses.filter(status => status !== currentStatus);
  }
};

export function TaskStatusDropdown({
  taskId,
  currentStatus,
  onStatusChange,
  size = 'sm',
  variant = 'outline',
  showIcon = true,
  disabled = false,
  className,
}: TaskStatusDropdownProps) {
  const { updateStatus, isUpdatingStatus, getStatusColor, canPerformAction } = useTaskActions({
    onSuccess: (_action, _taskId) => {
      // Task action completed successfully
    },
  });

  const [isOpen, setIsOpen] = React.useState(false);
  const [isUpdating, setIsUpdating] = React.useState(false);

  const currentConfig = statusConfig[currentStatus];
  const CurrentIcon = currentConfig.icon;
  const availableStatuses = React.useMemo(() =>
    getAvailableStatuses(currentStatus),
    [currentStatus]
  );

  const { canUpdate } = canPerformAction(currentStatus);

  const handleStatusChange = React.useCallback(
    async (newStatus: TaskStatusEnum) => {
      if (disabled || isUpdating || !canUpdate) {
        return;
      }

      setIsUpdating(true);
      setIsOpen(false);

      try {
        // Use custom handler if provided
        if (onStatusChange) {
          await onStatusChange(newStatus);
        } else {
          // Use the optimistic task actions hook
          await updateStatus(taskId, newStatus);
        }
      } catch (_error) {
      } finally {
        setIsUpdating(false);
      }
    },
    [taskId, disabled, isUpdating, canUpdate, onStatusChange, updateStatus]
  );

  const sizeClasses = {
    xs: 'h-6 px-2 text-xs',
    sm: 'h-7 px-2 text-xs',
    md: 'h-8 px-3 text-sm',
    lg: 'h-9 px-4 text-sm',
  };

  const iconSizes = {
    xs: 'h-3 w-3',
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-4 w-4',
  };

  const isPending = isUpdatingStatus || isUpdating;
  const isDisabled = disabled || !canUpdate || isPending;

  // If no available statuses, show as read-only badge
  if (availableStatuses.length === 0 || isDisabled) {
    return (
      <Badge
        variant="outline"
        className={cn(
          getStatusColor(currentStatus),
          sizeClasses[size],
          'flex items-center gap-1.5',
          className
        )}
      >
        {isPending ? (
          <LoaderIcon className={cn(iconSizes[size], 'animate-spin')} />
        ) : showIcon ? (
          <CurrentIcon className={iconSizes[size]} />
        ) : null}
        {currentConfig.label}
      </Badge>
    );
  }

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant={variant}
          size="sm"
          className={cn(
            getStatusColor(currentStatus),
            sizeClasses[size],
            'flex items-center justify-start gap-1.5',
            className
          )}
          disabled={isDisabled}
        >
          {isPending ? (
            <LoaderIcon className={cn(iconSizes[size], 'animate-spin')} />
          ) : showIcon ? (
            <CurrentIcon className={iconSizes[size]} />
          ) : null}
          {currentConfig.label}
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent align="start" className="w-[200px]">
        {availableStatuses.map((status) => {
          const config = statusConfig[status];
          const Icon = config.icon;

          return (
            <DropdownMenuItem
              key={status}
              className="flex cursor-pointer items-center gap-2"
              onClick={() => handleStatusChange(status)}
            >
              <Icon className="h-4 w-4" />
              <span className="flex-1">{config.label}</span>
            </DropdownMenuItem>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
} 