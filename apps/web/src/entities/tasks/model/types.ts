import type {
  TaskHistoryActionEnum,
  TaskPriorityEnum,
  TaskStatusEnum,
  TaskTypeEnum,
} from '@lilypad/db/enums';
import type {
  GetTasksParams,
  TaskWithRelations,
} from '@lilypad/db/repository/types/tasks';

// Re-export database types
export type {
  TaskHistoryActionEnum,
  TaskPriorityEnum,
  TaskStatusEnum,
  TaskTypeEnum,
} from '@lilypad/db/enums';

export type {
  Case,
  District,
  School,
  Student,
  Task,
  TaskDependency,
  TaskHistory,
  User,
} from '@lilypad/db/types';

// Task UI component props
export interface TaskCardProps {
  task: TaskWithRelations;
  onStatusChange?: (taskId: string, status: TaskStatusEnum) => void;
  onPriorityChange?: (taskId: string, priority: TaskPriorityEnum) => void;
  onReassign?: (taskId: string, newAssigneeId: string) => void;
  onComplete?: (taskId: string) => void;
  onReject?: (taskId: string, reason: string) => void;
  showActions?: boolean;
  compact?: boolean;
}

export interface TaskListProps {
  tasks: TaskWithRelations[];
  loading?: boolean;
  error?: string | null;
  onTaskAction?: (taskId: string, action: string, data?: unknown) => void;
  emptyMessage?: string;
  showFilters?: boolean;
  showPagination?: boolean;
  currentPage?: number;
  totalPages?: number;
  onPageChange?: (page: number) => void;
}

export interface TaskFiltersProps {
  filters: GetTasksParams;
  onFiltersChange: (filters: GetTasksParams) => void;
  showSearch?: boolean;
  showStatus?: boolean;
  showPriority?: boolean;
  showType?: boolean;
  showDateRange?: boolean;
  showAssignee?: boolean;
}

export interface TaskFormProps {
  task?: TaskWithRelations;
  onSubmit: (data: TaskFormData) => void;
  onCancel: () => void;
  loading?: boolean;
  errors?: Record<string, string>;
}

export interface TaskFormData {
  taskType: TaskTypeEnum;
  assignedToId: string;
  caseId?: string;
  studentId?: string;
  districtId?: string;
  schoolId?: string;
  priority: TaskPriorityEnum;
  dueDate: Date | null;
  notes?: string;
  metadata?: Record<string, unknown>;
}

// Task actions and mutations
export interface TaskActionProps {
  taskId: string;
  currentStatus: TaskStatusEnum;
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}

export interface TaskStatusUpdateProps extends TaskActionProps {
  newStatus: TaskStatusEnum;
  notes?: string;
}

export interface TaskPriorityUpdateProps extends TaskActionProps {
  newPriority: TaskPriorityEnum;
  notes?: string;
}

export interface TaskReassignProps extends TaskActionProps {
  newAssigneeId: string;
  notes?: string;
}

export interface TaskCompleteProps extends TaskActionProps {
  completionNotes?: string;
}

export interface TaskRejectProps extends TaskActionProps {
  rejectionReason: string;
}

// Task history and dependencies
export interface TaskHistoryEntry {
  id: string;
  taskId: string;
  action: TaskHistoryActionEnum;
  performedBy: {
    id: string;
    fullName: string;
    email: string;
  };
  previousValue: string | null;
  newValue: string | null;
  notes: string | null;
  createdAt: Date;
}

export interface TaskDependencyWithTasks {
  id: string;
  predecessorTaskId: string;
  successorTaskId: string;
  createdAt: Date;
  predecessorTask: {
    id: string;
    taskType: TaskTypeEnum;
    status: TaskStatusEnum;
    priority: TaskPriorityEnum;
    dueDate: Date | null;
    completedAt: Date | null;
  };
  successorTask: {
    id: string;
    taskType: TaskTypeEnum;
    status: TaskStatusEnum;
    priority: TaskPriorityEnum;
    dueDate: Date | null;
    completedAt: Date | null;
  };
}

// Task relationship types
export interface TaskWithStudent extends TaskWithRelations {
  student: NonNullable<TaskWithRelations['student']>;
}

export interface TaskWithCase extends TaskWithRelations {
  case: NonNullable<TaskWithRelations['case']>;
}

export interface TaskWithSchool extends TaskWithRelations {
  school: NonNullable<TaskWithRelations['school']>;
}

export interface TaskWithDistrict extends TaskWithRelations {
  district: NonNullable<TaskWithRelations['district']>;
}

// Task workflow types
export type TaskWorkflowStage =
  | 'referral'
  | 'evaluation'
  | 'assessment'
  | 'reporting'
  | 'meeting'
  | 'monitoring'
  | 'completion';

export interface TaskWorkflowConfig {
  stage: TaskWorkflowStage;
  taskTypes: TaskTypeEnum[];
  requiredRoles: string[];
  dependencies?: TaskTypeEnum[];
  autoAssign?: boolean;
  dueInDays?: number;
}

// Task automation types
export interface TaskAutomationRule {
  id: string;
  name: string;
  trigger: TaskAutomationTrigger;
  condition: TaskAutomationCondition;
  actions: TaskAutomationAction[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface TaskAutomationTrigger {
  type:
    | 'status_change'
    | 'due_date'
    | 'assignment'
    | 'completion'
    | 'rejection';
  taskTypes?: TaskTypeEnum[];
  statuses?: TaskStatusEnum[];
}

export interface TaskAutomationCondition {
  type: 'always' | 'if_role' | 'if_priority' | 'if_date' | 'if_metadata';
  value?: unknown;
  operator?:
    | 'equals'
    | 'not_equals'
    | 'greater_than'
    | 'less_than'
    | 'contains';
}

export interface TaskAutomationAction {
  type:
    | 'create_task'
    | 'update_status'
    | 'reassign'
    | 'send_notification'
    | 'update_priority';
  parameters: Record<string, unknown>;
}

// Task bulk operations
export interface TaskBulkOperationProps {
  taskIds: string[];
  operation:
    | 'update_status'
    | 'update_priority'
    | 'reassign'
    | 'complete'
    | 'reject';
  parameters: Record<string, unknown>;
  onSuccess?: (results: TaskBulkOperationResult[]) => void;
  onError?: (error: Error) => void;
}

export interface TaskBulkOperationResult {
  taskId: string;
  success: boolean;
  error?: string;
  data?: unknown;
}

// Task analytics and reporting
export interface TaskAnalyticsProps {
  dateRange: {
    from: Date;
    to: Date;
  };
  groupBy: 'status' | 'priority' | 'type' | 'assignee' | 'district' | 'school';
  filters?: GetTasksParams;
}

export interface TaskMetrics {
  total: number;
  completed: number;
  pending: number;
  overdue: number;
  completionRate: number;
  averageCompletionTime: number;
  byStatus: Record<TaskStatusEnum, number>;
  byPriority: Record<TaskPriorityEnum, number>;
  byType: Record<TaskTypeEnum, number>;
}

// Task hooks types
export interface UseTaskMutationOptions {
  onSuccess?: (data: unknown) => void;
  onError?: (error: Error) => void;
  onSettled?: () => void;
}

export interface UseTaskStatusUpdateOptions extends UseTaskMutationOptions {
  invalidateQueries?: boolean;
  optimisticUpdate?: boolean;
}

export interface UseTaskPriorityUpdateOptions extends UseTaskMutationOptions {
  invalidateQueries?: boolean;
  optimisticUpdate?: boolean;
}

export interface UseTaskReassignOptions extends UseTaskMutationOptions {
  invalidateQueries?: boolean;
  showNotification?: boolean;
}
