'use client';

import type { TRPCClientError } from '@lilypad/api/client';
import { useTRPC } from '@lilypad/api/client';
import { TaskStatusEnum } from '@lilypad/db/enums';
import { toast } from '@lilypad/ui/components/sonner';
import {
  useMutation,
  useQueryClient,
  useSuspenseQuery,
} from '@tanstack/react-query';
import { useCallback, useMemo } from 'react';

interface UseTaskDependenciesOptions {
  onSuccess?: () => void;
  onError?: (error: TRPCClientError) => void;
}

interface DependencyStatusInfo {
  isBlocked: boolean;
  canStart: boolean;
  blockingTasksCount: number;
  predecessorTasksCount: number;
  successorTasksCount: number;
  completedPrerequisites: number;
  pendingPrerequisites: number;
}

export function useTaskDependencies(
  taskId: string,
  options: UseTaskDependenciesOptions = {}
) {
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  // Fetch task dependencies
  const { data: dependencies, isError: dependenciesError } = useSuspenseQuery(
    trpc.tasks.getTaskDependencies.queryOptions(taskId)
  );

  // Fetch blocking tasks
  const { data: blockingTasks, isError: blockingError } = useSuspenseQuery(
    trpc.tasks.getBlockingTasks.queryOptions(taskId)
  );

  // Check if task can start
  const { data: canStart, isError: canStartError } = useSuspenseQuery(
    trpc.tasks.canStartTask.queryOptions(taskId)
  );

  // Calculate dependency status information
  const dependencyStatus: DependencyStatusInfo = useMemo(() => {
    if (!dependencies) {
      return {
        isBlocked: false,
        canStart: true,
        blockingTasksCount: 0,
        predecessorTasksCount: 0,
        successorTasksCount: 0,
        completedPrerequisites: 0,
        pendingPrerequisites: 0,
      };
    }

    const predecessorTasks = dependencies.filter(
      (dep) => dep.successorTaskId === taskId
    );
    const successorTasks = dependencies.filter(
      (dep) => dep.predecessorTaskId === taskId
    );

    const completedPrerequisites = predecessorTasks.filter(
      (dep) => dep.predecessorTask.status === TaskStatusEnum.COMPLETED
    ).length;

    const pendingPrerequisites = predecessorTasks.filter(
      (dep) => dep.predecessorTask.status !== TaskStatusEnum.COMPLETED
    ).length;

    return {
      isBlocked: pendingPrerequisites > 0,
      canStart,
      blockingTasksCount: blockingTasks?.length || 0,
      predecessorTasksCount: predecessorTasks.length,
      successorTasksCount: successorTasks.length,
      completedPrerequisites,
      pendingPrerequisites,
    };
  }, [dependencies, blockingTasks, canStart, taskId]);

  // Get predecessor and successor tasks separately
  const predecessorTasks = useMemo(() => {
    return dependencies?.filter((dep) => dep.successorTaskId === taskId) || [];
  }, [dependencies, taskId]);

  const successorTasks = useMemo(() => {
    return (
      dependencies?.filter((dep) => dep.predecessorTaskId === taskId) || []
    );
  }, [dependencies, taskId]);

  // Check for critical blocking conditions
  const criticalBlocking = useMemo(() => {
    const urgentBlockingTasks =
      blockingTasks?.filter(
        (task) =>
          task.priority === 'URGENT' && task.status !== TaskStatusEnum.COMPLETED
      ) || [];

    const overdueBlockingTasks =
      blockingTasks?.filter(
        (task) =>
          task.dueDate &&
          new Date(task.dueDate) < new Date() &&
          task.status !== TaskStatusEnum.COMPLETED
      ) || [];

    return {
      hasUrgentBlocking: urgentBlockingTasks.length > 0,
      hasOverdueBlocking: overdueBlockingTasks.length > 0,
      urgentBlockingCount: urgentBlockingTasks.length,
      overdueBlockingCount: overdueBlockingTasks.length,
      urgentBlockingTasks,
      overdueBlockingTasks,
    };
  }, [blockingTasks]);

  // Error handling
  const handleError = useCallback(
    (error: TRPCClientError) => {
      const message = error.message || 'An unexpected error occurred';
      toast.error('Dependency Error', {
        description: message,
      });
      options.onError?.(error);
    },
    [options]
  );

  // Success handling
  const handleSuccess = useCallback(async () => {
    // Invalidate related queries
    await queryClient.invalidateQueries({
      queryKey: [['tasks']],
      exact: false,
    });

    toast.success('Success', {
      description: 'Dependency operation completed successfully',
    });

    options.onSuccess?.();
  }, [queryClient, options]);

  // Create task dependency mutation
  const createDependencyMutation = useMutation({
    mutationFn: async (_data: {
      predecessorTaskId: string;
      successorTaskId: string;
    }) => {
      // This would nee_data be implemented in the tRPC router
      // For now, returning a placeholder
      return { success: true };
    },
    onSuccess: handleSuccess,
    onError: handleError,
  });

  // Remove task dependency mutation
  const removeDependencyMutation = useMutation({
    mutationFn: async (_dependencyId: string) => {
      // This would nee_dependencyIdemented in the tRPC router
      // For now, returning a placeholder
      return { success: true };
    },
    onSuccess: handleSuccess,
    onError: handleError,
  });

  // Get dependency warnings
  const getDependencyWarnings = useCallback(() => {
    const warnings: string[] = [];

    if (dependencyStatus.isBlocked) {
      warnings.push(
        `Task is blocked by ${dependencyStatus.pendingPrerequisites} incomplete prerequisite task${dependencyStatus.pendingPrerequisites === 1 ? '' : 's'}`
      );
    }

    if (criticalBlocking.hasUrgentBlocking) {
      warnings.push(
        `${criticalBlocking.urgentBlockingCount} urgent prerequisite task${criticalBlocking.urgentBlockingCount === 1 ? '' : 's'} must be completed`
      );
    }

    if (criticalBlocking.hasOverdueBlocking) {
      warnings.push(
        `${criticalBlocking.overdueBlockingCount} overdue prerequisite task${criticalBlocking.overdueBlockingCount === 1 ? '' : 's'} must be completed`
      );
    }

    return warnings;
  }, [dependencyStatus, criticalBlocking]);

  // Get dependency alerts for UI
  const getDependencyAlerts = useCallback(() => {
    const alerts: Array<{
      type: 'warning' | 'error' | 'info';
      title: string;
      message: string;
    }> = [];

    if (dependencyStatus.isBlocked) {
      alerts.push({
        type: 'warning',
        title: 'Task Blocked',
        message: `This task cannot be started until ${dependencyStatus.pendingPrerequisites} prerequisite task${dependencyStatus.pendingPrerequisites === 1 ? '' : 's'} are completed.`,
      });
    }

    if (criticalBlocking.hasUrgentBlocking) {
      alerts.push({
        type: 'error',
        title: 'Urgent Dependencies',
        message: `${criticalBlocking.urgentBlockingCount} urgent prerequisite task${criticalBlocking.urgentBlockingCount === 1 ? '' : 's'} require immediate attention.`,
      });
    }

    if (criticalBlocking.hasOverdueBlocking) {
      alerts.push({
        type: 'error',
        title: 'Overdue Dependencies',
        message: `${criticalBlocking.overdueBlockingCount} prerequisite task${criticalBlocking.overdueBlockingCount === 1 ? '' : 's'} are overdue and blocking progress.`,
      });
    }

    if (
      dependencyStatus.successorTasksCount > 0 &&
      !dependencyStatus.isBlocked
    ) {
      alerts.push({
        type: 'info',
        title: 'Dependent Tasks',
        message: `${dependencyStatus.successorTasksCount} task${dependencyStatus.successorTasksCount === 1 ? '' : 's'} depend on this one. Complete this task to unblock them.`,
      });
    }

    return alerts;
  }, [dependencyStatus, criticalBlocking]);

  return {
    // Data
    dependencies,
    blockingTasks,
    predecessorTasks,
    successorTasks,
    dependencyStatus,
    criticalBlocking,

    // State
    isLoading: false, // Since we're using Suspense queries
    isError: dependenciesError || blockingError || canStartError,

    // Actions
    createDependency: createDependencyMutation.mutate,
    removeDependency: removeDependencyMutation.mutate,
    isCreatingDependency: createDependencyMutation.isPending,
    isRemovingDependency: removeDependencyMutation.isPending,

    // Utilities
    canStartTask: dependencyStatus.canStart,
    isBlocked: dependencyStatus.isBlocked,
    getDependencyWarnings,
    getDependencyAlerts,

    // Quick access helpers
    hasPrerequisites: dependencyStatus.predecessorTasksCount > 0,
    hasDependents: dependencyStatus.successorTasksCount > 0,
    isFullyUnblocked: dependencyStatus.canStart && !dependencyStatus.isBlocked,
    hasUrgentBlocking: criticalBlocking.hasUrgentBlocking,
    hasOverdueBlocking: criticalBlocking.hasOverdueBlocking,
  };
}

// Simplified hook for just checking if a task can start
export function useCanStartTask(taskId: string) {
  const trpc = useTRPC();

  const { data: canStart, isError } = useSuspenseQuery(
    trpc.tasks.canStartTask.queryOptions(taskId)
  );

  return {
    canStart,
    isError,
  };
}

// Hook for getting dependency counts without full details
export function useTaskDependencyCounts(taskId: string) {
  const { dependencyStatus, isError } = useTaskDependencies(taskId);

  return {
    predecessorCount: dependencyStatus.predecessorTasksCount,
    successorCount: dependencyStatus.successorTasksCount,
    completedPrerequisites: dependencyStatus.completedPrerequisites,
    pendingPrerequisites: dependencyStatus.pendingPrerequisites,
    isBlocked: dependencyStatus.isBlocked,
    isError,
  };
}
