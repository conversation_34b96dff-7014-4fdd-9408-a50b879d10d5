import type { TRPCClientError } from '@lilypad/api/client';
import { useTRPC } from '@lilypad/api/client';
import type { TaskStatusEnum } from '@lilypad/db/schema/enums';
import { toast } from '@lilypad/ui/components/sonner';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useCallback } from 'react';

interface UpdateTaskStatusOptions {
  onSuccess?: () => void;
  onError?: (error: TRPCClientError) => void;
}

interface UpdateTaskStatusParams {
  taskId: string;
  previousStatus: TaskStatusEnum;
  newStatus: TaskStatusEnum;
}

interface TaskQueryData {
  id: string;
  status: TaskStatusEnum;
  [key: string]: unknown;
}

export const useTaskActions = (options: UpdateTaskStatusOptions = {}) => {
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  const handleSuccess = useCallback(() => {
    options.onSuccess?.();
  }, [options]);

  const handleError = useCallback(
    (err: TRPCClientError, _variables: unknown, _context: unknown) => {
      const message = err.message || 'An unexpected error occurred';
      toast.error(`Failed to update task: ${message}`);

      // Rollback is handled in onError automatically by TanStack Query
      options.onError?.(err);
    },
    [options]
  );

  const {
    mutate: updateTaskStatusMutation,
    mutateAsync: updateTaskStatusMutationAsync,
    isPending: isUpdating,
    error: mutationError,
  } = useMutation(
    trpc.tasks.updateTaskStatus.mutationOptions({
      // Optimistic update - update UI immediately
      onMutate: async ({ taskId, status: newStatus }) => {
        // Cancel any outgoing refetches
        await queryClient.cancelQueries({
          queryKey: [['tasks']],
        });

        // Snapshot the previous value
        const previousQueries: Array<{ queryKey: unknown[]; data: unknown }> =
          [];

        // Get all task-related queries
        const queriesData = queryClient.getQueriesData({
          queryKey: [['tasks']],
        });
        for (const [queryKey, data] of queriesData) {
          if (data) {
            previousQueries.push({ queryKey, data });
          }
        }

        // Optimistically update task status in all relevant queries
        queryClient.setQueriesData(
          { queryKey: [['tasks']] },
          (oldData: unknown) => {
            if (!oldData || typeof oldData !== 'object') {
              return oldData;
            }

            // Handle paginated responses
            if (
              'data' in oldData &&
              Array.isArray((oldData as Record<string, unknown>).data)
            ) {
              const paginatedData = oldData as { data: TaskQueryData[] };
              return {
                ...paginatedData,
                data: paginatedData.data.map((task) =>
                  task.id === taskId ? { ...task, status: newStatus } : task
                ),
              };
            }

            // Handle array responses
            if (Array.isArray(oldData)) {
              return (oldData as TaskQueryData[]).map((task) =>
                task.id === taskId ? { ...task, status: newStatus } : task
              );
            }

            // Handle single task responses
            if ('id' in oldData && (oldData as TaskQueryData).id === taskId) {
              return { ...oldData, status: newStatus };
            }

            return oldData;
          }
        );

        // Return a context object with the snapshotted value
        return { previousQueries };
      },

      onError: (err, variables, context) => {
        // On error, roll back to the previous value
        if (context?.previousQueries) {
          for (const { queryKey, data } of context.previousQueries) {
            queryClient.setQueryData(queryKey, data);
          }
        }

        handleError(err, variables, context);
      },

      onSuccess: handleSuccess,

      // Always refetch after error or success to ensure consistency
      onSettled: () => {
        queryClient.invalidateQueries({
          queryKey: [['tasks']],
          exact: false,
        });
      },
    })
  );

  const updateTaskStatus = useCallback(
    (params: UpdateTaskStatusParams) => {
      return updateTaskStatusMutation({
        taskId: params.taskId,
        status: params.newStatus,
      });
    },
    [updateTaskStatusMutation]
  );

  const updateTaskStatusAsync = useCallback(
    async (params: UpdateTaskStatusParams) => {
      return await updateTaskStatusMutationAsync({
        taskId: params.taskId,
        status: params.newStatus,
      });
    },
    [updateTaskStatusMutationAsync]
  );

  return {
    updateTaskStatus,
    updateTaskStatusAsync,
    isUpdating,
    error: mutationError,
  };
};
