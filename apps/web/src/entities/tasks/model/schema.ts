import {
  TaskPriorityEnumMap,
  TaskStatusEnumMap,
  TaskTypeEnumMap,
} from '@lilypad/db/enums';
import type { TaskWithRelations } from '@lilypad/db/repository/types/tasks';
import {
  getFiltersStateParser,
  getSortingStateParser,
} from '@lilypad/ui/data-table/lib/parsers';
import {
  createSearchParamsCache,
  parseAsString,
  parseAsStringEnum,
} from 'nuqs/server';

export const taskStatusOptions = Object.entries(TaskStatusEnumMap).map(
  ([value, label]) => ({
    value,
    label,
  })
);

export const taskPriorityOptions = Object.entries(TaskPriorityEnumMap).map(
  ([value, label]) => ({
    value,
    label,
  })
);

export const taskTypeOptions = Object.entries(TaskTypeEnumMap).map(
  ([value, label]) => ({
    value,
    label: label.name,
  })
);

export const tasksSearchParamsCache = createSearchParamsCache({
  sort: getSortingStateParser<TaskTableRow>().withDefault([
    { id: 'dueDate', desc: false },
  ]),
  search: parseAsString.withDefault(''),
  filters: getFiltersStateParser<TaskTableRow>().withDefault([]),
  joinOperator: parseAsStringEnum(['and', 'or']).withDefault('and'),
});

export type GetTasksSearchParams = Awaited<
  ReturnType<typeof tasksSearchParamsCache.parse>
>;

export interface TaskTableRow extends TaskWithRelations {
  caseId: string | null;
  studentName: string | null;
  studentIdNumber: string | null;
  schoolName: string | null;
  districtName: string | null;
  assignedToName: string;
  assignedByName: string;
}

export function formatTasksToTableRow(
  tasks: TaskWithRelations[]
): TaskTableRow[] {
  return tasks.map((task) => ({
    ...task,
    caseId: task.case?.id || null,
    studentName: task.student?.fullName || null,
    studentIdNumber: task.student?.studentIdNumber || null,
    schoolName: task.school?.name || null,
    districtName: task.district?.name || null,
    assignedToName: task.assignedTo.fullName,
    assignedByName: task.assignedBy.fullName,
  }));
}
