import { z } from 'zod';
import {
  createSearchParamsCache,
  parseAsInteger,
  parseAsString,
  parseAsStringEnum,
} from 'nuqs/server';

import { DistrictTypeEnum, DistrictTypeMap } from '@lilypad/db/enums';
import {
  getFiltersStateParser,
  getSortingStateParser,
} from '@lilypad/ui/data-table/lib/parsers';

export type { District } from '@lilypad/db';

export const districtTypeOptions = Object.entries(DistrictTypeMap).map(
  ([value, label]) => ({
    value,
    label,
  })
);

export const districtFilterSchema = z.object({
  name: z.string().optional(),
  type: z.nativeEnum(DistrictTypeEnum).optional(),
  county: z.string().optional(),
  state: z.string().optional(),
  ncesId: z.string().optional(),
  stateId: z.string().optional(),
});

export type DistrictFilter = z.infer<typeof districtFilterSchema>;

export interface DistrictTableRow {
  id: string;
  name: string;
  slug: string;
  type: DistrictTypeEnum;
  website: string;
  ncesId: string;
  stateId: string;
  county: string;
  addressId: string;
  address: string;
  city: string;
  state: string;
}

export const districtsSearchParamsCache = createSearchParamsCache({
  page: parseAsInteger.withDefault(1),
  perPage: parseAsInteger.withDefault(10),
  sort: getSortingStateParser<DistrictTableRow>().withDefault([
    { id: 'name', desc: true },
  ]),
  search: parseAsString.withDefault(''),
  filters: getFiltersStateParser<DistrictTableRow>().withDefault([]),
  joinOperator: parseAsStringEnum(['and', 'or']).withDefault('and'),
});

export type GetDistrictsSearchParams = Awaited<
  ReturnType<typeof districtsSearchParamsCache.parse>
>;
