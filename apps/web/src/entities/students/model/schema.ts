import {
  EnrollmentStatusEnum,
  EnrollmentStatusEnumMap,
  GenderEnum,
  GenderEnumMap,
  SchoolGradeEnum,
  SchoolGradeEnumMap,
} from '@lilypad/db/enums';
import type { Address, Language, Parent, Student } from '@lilypad/db/types';
import {
  getFiltersStateParser,
  getSortingStateParser,
} from '@lilypad/ui/data-table/lib/parsers';
import {
  createSearchParamsCache,
  parseAsInteger,
  parseAsString,
  parseAsStringEnum,
} from 'nuqs/server';
import { z } from 'zod';

export type { Student } from '@lilypad/db/types';

export const gradeOptions = Object.entries(SchoolGradeEnum).map(
  ([key, value]) => ({
    value, // This is the actual DB value like 'PK', 'K', '1', etc.
    label: SchoolGradeEnumMap[value as keyof typeof SchoolGradeEnumMap], // Access by enum value, not key
  })
);

export const genderOptions = Object.entries(GenderEnumMap).map(
  ([value, label]) => ({
    value,
    label,
  })
);

export const enrollmentStatusOptions = Object.entries(
  EnrollmentStatusEnumMap
).map(([value, label]) => ({
  value,
  label,
}));

export const studentFilterSchema = z.object({
  fullName: z.string().optional(),
  studentIdNumber: z.string().optional(),
  grade: z.string().optional(), // Grade is stored as string in DB
  gender: z.nativeEnum(GenderEnum).optional(),
  enrollmentStatus: z.nativeEnum(EnrollmentStatusEnum).optional(),
  specialNeedsIndicator: z.boolean().optional(),
  schoolName: z.string().optional(),
  districtName: z.string().optional(),
  emergencyContactName: z.string().optional(),
});

export type StudentFilter = z.infer<typeof studentFilterSchema>;

export const studentsSearchParamsCache = createSearchParamsCache({
  page: parseAsInteger.withDefault(1),
  perPage: parseAsInteger.withDefault(10),
  sort: getSortingStateParser<StudentTableRow>().withDefault([
    { id: 'fullName', desc: false },
  ]),
  search: parseAsString.withDefault(''),
  filters: getFiltersStateParser<StudentTableRow>().withDefault([]),
  joinOperator: parseAsStringEnum(['and', 'or']).withDefault('and'),
});

export type GetStudentsSearchParams = Awaited<
  ReturnType<typeof studentsSearchParamsCache.parse>
>;

export interface StudentTableRow {
  id: string;
  firstName: string;
  middleName: string | null;
  lastName: string;
  fullName: string | null;
  preferredName: string;
  studentIdNumber: string;
  dateOfBirth: string;
  dateOfConsent: string;
  grade: SchoolGradeEnum;
  gender: GenderEnum;
  primarySchoolId: string | null;
  enrollmentStatus: EnrollmentStatusEnum;
  specialNeedsIndicator: boolean;
  emergencyContactName: string | null;
  emergencyContactPhone: string | null;
  isDeleted: boolean;
  deletedAt: string | null;
  deletedBy: string | null;
  createdAt: string;
  updatedAt: string;
  schoolName: string;
  districtName: string;
  schoolId: string;
  districtId: string;
}

export interface StudentProfile
  extends Omit<Student, 'deletedAt' | 'deletedBy' | 'isDeleted'> {
  primarySchool: {
    id: string | null;
    name: string | null;
    district: string | null;
  } | null;
  languages: (Language & { isPrimary: boolean })[] | null;
  parents:
    | Omit<
        Parent,
        'deletedAt' | 'deletedBy' | 'isDeleted' | 'createdAt' | 'updatedAt'
      >[]
    | null;
  addresses:
    | Omit<
        Address,
        | 'createdAt'
        | 'updatedAt'
        | 'studentId'
        | 'schoolId'
        | 'districtId'
        | 'parentId'
      >[]
    | null;
}
