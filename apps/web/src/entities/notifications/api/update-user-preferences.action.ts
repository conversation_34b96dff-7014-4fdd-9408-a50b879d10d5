'use server';

import { userNotificationPreferencesTable } from '@lilypad/db/schema';
import { logger } from '@lilypad/shared/logger';
import { revalidateTag } from 'next/cache';

import { caching, UserCacheKey } from '@/shared/caching';
import { authActionClient } from '@/shared/safe-action';
import { createDatabaseClient, eq } from '@lilypad/db/client';
import { notificationPreferencesFormSchema } from '@/features/notifications/model/schema';

export const updateNotificationPreferencesAction = authActionClient
  .metadata({ actionName: 'updateNotificationPreference' })
  .schema(notificationPreferencesFormSchema)
  .action(async ({ parsedInput, ctx: { user } }) => {
    const userId = user?.id;
    if (!userId) {
      throw new Error('User not found');
    }

    let {
      isInAppNotificationsEnabled,
      isPushNotificationsEnabled,
      isEmailEnabled,
      isAllNotificationsEnabled,
    } = parsedInput;

    if (!isAllNotificationsEnabled) {
      isInAppNotificationsEnabled = false;
      isPushNotificationsEnabled = false;
      isEmailEnabled = false;
    }

    const db = await createDatabaseClient();

    const [upsertedPreference] = await db.transaction(async (tx) => {
      return await tx
        .update(userNotificationPreferencesTable)
        .set({
          isInAppNotificationsEnabled,
          isPushNotificationsEnabled,
          isEmailEnabled,
        })
        .where(eq(userNotificationPreferencesTable.userId, userId))
        .returning({
          id: userNotificationPreferencesTable.id,
        });
    });

    logger.info(
      { userId, upsertedPreferenceId: upsertedPreference.id },
      '✅ SUCCESSFULLY UPDATED USER NOTIFICATION PREFERENCE'
    );

    revalidateTag(
      caching.createUserTag(UserCacheKey.NotificationPreferences, userId)
    );

    return {
      success: true,
    };
  });
