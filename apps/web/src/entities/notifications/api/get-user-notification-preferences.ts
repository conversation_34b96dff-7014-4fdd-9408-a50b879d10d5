import 'server-only';

import type { UserNotificationPreference } from '@lilypad/db/schema/types';

import {
  UserCacheKey,
  cache,
  caching,
  defaultRevalidateTimeInSeconds,
} from '@/shared/caching';
import { createDatabaseClient, dbAdmin, eq } from '@lilypad/db/client';
import { userNotificationPreferencesTable } from '@lilypad/db/schema';
import { getSupabaseServerClient } from '@lilypad/supabase/server';

export async function getUserNotificationPreferences(): Promise<UserNotificationPreference> {
  const supabase = await getSupabaseServerClient();
  const { data: auth } = await supabase.auth.getUser();
  const db = await createDatabaseClient();

  if (!auth.user) {
    throw new Error('User not found');
  }

  return cache(
    async () => {
      const [existingPreferences] = await db.transaction(async (tx) => {
        return await tx
          .select()
          .from(userNotificationPreferencesTable)
          .where(eq(userNotificationPreferencesTable.userId, auth.user.id));
      });

      if (!existingPreferences) {
        const [newPreferences] = await dbAdmin
          .insert(userNotificationPreferencesTable)
          .values({
            userId: auth.user.id,
            isInAppNotificationsEnabled: true,
            isPushNotificationsEnabled: false,
            isEmailEnabled: false,
          })
          .returning();
        return newPreferences;
      }

      return existingPreferences;
    },
    caching.createUserKeyParts(
      UserCacheKey.NotificationPreferences,
      auth.user.id
    ),
    {
      revalidate: defaultRevalidateTimeInSeconds,
      tags: [
        caching.createUserTag(
          UserCacheKey.NotificationPreferences,
          auth.user.id
        ),
      ],
    }
  )();
}
