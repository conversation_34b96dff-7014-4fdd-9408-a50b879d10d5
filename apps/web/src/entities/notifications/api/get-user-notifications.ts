import 'server-only';

import { createDatabaseClient } from '@lilypad/db';
import { and, desc, eq, gt } from '@lilypad/db/client';
import { notificationsTable } from '@lilypad/db/schema';
import { getSupabaseServerClient } from '@lilypad/supabase/server';

import type { Notification } from '@/entities/notifications/model/types';
import { cache } from '@/shared';
import {
  UserCacheKey,
  caching,
  defaultRevalidateTimeInSeconds,
} from '@/shared/caching';

export async function getUserNotifications(): Promise<Notification[]> {
  const supabase = await getSupabaseServerClient();
  const { data: auth } = await supabase.auth.getUser();
  const db = await createDatabaseClient();

  if (!auth?.user?.id) {
    return [];
  }
  const userId = auth.user.id;

  return cache(
    async () => {
      const notifications = await db.transaction(async (tx) => {
        return await tx
          .select({
            id: notificationsTable.id,
            type: notificationsTable.type,
            content: notificationsTable.content,
            category: notificationsTable.category,
            metadata: notificationsTable.metadata,
            userId: notificationsTable.userId,
            isRead: notificationsTable.isRead,
            isArchived: notificationsTable.isArchived,
            readAt: notificationsTable.readAt,
            archivedAt: notificationsTable.archivedAt,
            expiresAt: notificationsTable.expiresAt,
            createdAt: notificationsTable.createdAt,
          })
          .from(notificationsTable)
          .where(
            and(
              eq(notificationsTable.userId, userId),
              gt(notificationsTable.expiresAt, new Date())
            )
          )
          .orderBy(desc(notificationsTable.createdAt));
      });
      return notifications as Notification[];
    },
    caching.createUserKeyParts(UserCacheKey.Notifications, userId),
    {
      revalidate: defaultRevalidateTimeInSeconds,
      tags: [caching.createUserTag(UserCacheKey.Notifications, userId)],
    }
  )();
}
