import type { NotificationTypeEnum } from '@lilypad/db/enums';
import type { Notification as NotificationDto } from '@lilypad/db/types';
import type { RealtimePostgresChangesPayload } from '@lilypad/supabase';
import type { Database } from '@lilypad/supabase/types';

export type NotificationDao =
  Database['public']['Tables']['notifications']['Insert'];

export interface Notification extends NotificationDto {
  type: NotificationTypeEnum;
}

export type NotificationPayload =
  RealtimePostgresChangesPayload<NotificationDao>;

export interface ReportReadyNotificationMetadata {
  reportUrl: string;
}
