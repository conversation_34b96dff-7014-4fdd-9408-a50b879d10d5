import { z } from 'zod';

export const verifyEmailWithOtpSchema = z.object({
  otp: z
    .string({
      required_error: 'OTP is required.',
      invalid_type_error: 'OTP must be a string.',
    })
    .trim()
    .min(1, 'OTP is required.')
    .max(6, 'Maximum 6 characters allowed.'),
  email: z
    .string({
      required_error: 'Email is required.',
      invalid_type_error: 'Email must be a string.',
    })
    .trim()
    .min(1, 'Email is required.')
    .max(255, 'Maximum 255 characters allowed.')
    .email('Use a valid email address.'),
});

export type VerifyEmailWithOtpSchema = z.infer<typeof verifyEmailWithOtpSchema>;
