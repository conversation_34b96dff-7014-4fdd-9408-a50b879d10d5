import { z } from 'zod';

export const signInOtpSchema = z.object({
  email: z
    .string({
      required_error: 'Email is required.',
      invalid_type_error: 'Email must be a string.',
    })
    .trim()
    .min(1, 'Email is required.')
    .max(255, 'Maximum 255 characters allowed.')
    .email('Enter a valid email address.'),
  otp: z
    .string({
      required_error: 'Code is required.',
      invalid_type_error: 'Code must be a string.',
    })
    .trim()
    .min(6, 'Code must be 6 characters.')
    .max(6, 'Code must be 6 characters.'),
});

export type SignInOtpSchema = z.infer<typeof signInOtpSchema>;
