'use server';

import { routes } from '@lilypad/shared/routes';
import { getSupabaseServerClient } from '@lilypad/supabase/server';
import { redirect } from 'next/navigation';

import { rateLimitedActionClient } from '@/shared/safe-action';
import { signOutSchema } from '@/entities/users/model/auth/sign-out.schema';

export const signOutAction = rateLimitedActionClient
  .metadata({ actionName: 'signOut' })
  .schema(signOutSchema)
  .action(async ({ parsedInput }) => {
    const supabase = await getSupabaseServerClient();
    await supabase.auth.signOut({ scope: 'local' });
    if (parsedInput.redirect) {
      if (parsedInput.redirectTo) {
        redirect(parsedInput.redirectTo);
      } else {
        redirect(routes.app.auth.SignIn);
      }
    }
  });
