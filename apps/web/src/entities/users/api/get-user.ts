import 'server-only';

import { unstable_cache as cache } from 'next/cache';

import {
  caching,
  defaultRevalidateTimeInSeconds,
  UserCacheKey,
} from '@/shared/caching';

import { getAuthContext } from '@/shared/context';
import type { User } from '@/shared/types';
import { notFound } from 'next/navigation';

export async function getUser(): Promise<User> {
  const ctx = await getAuthContext();

  const activeMembership = ctx.user.userDistricts.find(
    (m) => m.userId === ctx.user.id
  );

  if (!activeMembership) {
    return notFound();
  }

  return cache(
    async () => {
      // User data is already fully loaded in the context
      return await ctx.user;
    },
    caching.createUserKeyParts(UserCacheKey.Profile, ctx.user.id),
    {
      revalidate: defaultRevalidateTimeInSeconds,
      tags: [
        caching.createUserTag(UserCacheKey.Profile, ctx.user.id),
        'user-permissions',
        'user-districts',
        'user-schools',
      ],
    }
  )();
}
