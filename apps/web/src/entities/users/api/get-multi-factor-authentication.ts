import 'server-only';

import type { MultiFactorAuthenticationDto } from '@/features/auth/model/schema';

import {
  caching,
  UserCacheKey,
  cache,
  defaultRevalidateTimeInSeconds,
} from '@/shared/caching';
import { logger } from '@lilypad/shared/logger';
import { getSupabaseServerClient } from '@lilypad/supabase/server';

export async function getMultiFactorAuthentication(): Promise<MultiFactorAuthenticationDto> {
  const supabase = await getSupabaseServerClient();
  const { data: auth } = await supabase.auth.getUser();

  if (!auth.user) {
    return { factorId: undefined };
  }

  return cache(
    async () => {
      const { data: factors, error } = await supabase.auth.mfa.listFactors();
      if (error) {
        logger.error({ error }, '❌ ERROR LISTING FACTORS');
        throw error;
      }
      const factorId = factors.all?.find(
        (factor) => factor.factor_type === 'totp'
      )?.id;
      return { factorId };
    },
    caching.createUserKeyParts(
      UserCacheKey.MultiFactorAuthentication,
      auth.user.id
    ),
    {
      revalidate: defaultRevalidateTimeInSeconds,
      tags: [
        caching.createUserTag(
          UserCacheKey.MultiFactorAuthentication,
          auth.user.id
        ),
      ],
    }
  )();
}
