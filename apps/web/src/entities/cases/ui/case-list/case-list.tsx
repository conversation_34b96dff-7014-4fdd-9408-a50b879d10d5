'use client';

import { useTRPC } from '@lilypad/api/client';
import { useSuspenseQuery } from '@tanstack/react-query';
import { CaseCard } from '../case-card/case-card';

interface CaseListProps {
  studentId: string;
}

export const CaseList = ({ studentId }: CaseListProps) => {
  const trpc = useTRPC();
  const { data: cases } = useSuspenseQuery(
    trpc.cases.getCasesByStudentId.queryOptions(studentId)
  );

  return (
    <div className="flex flex-col gap-4">
      {cases?.map((caseItem) => (
        <CaseCard key={caseItem.id} studentCase={caseItem} />
      ))}
    </div>
  );
};
