import { Skeleton } from '@lilypad/ui/components/skeleton';

interface CaseListSkeletonProps {
  count?: number;
}

export const CaseListSkeleton = ({ count = 3 }: CaseListSkeletonProps) => {
  return (
    <div className="flex flex-col gap-4">
      {Array.from({ length: count }).map((_, index) => (
        <CaseCardSkeleton key={index} />
      ))}
    </div>
  );
};

const CaseCardSkeleton = () => {
  return (
    <div className="rounded-lg border bg-card p-6">
      <div className="flex flex-col gap-4">
        {/* Case title/header */}
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <Skeleton className="mb-2 h-6 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </div>
          <Skeleton className="h-6 w-20" />
        </div>

        {/* Case details */}
        <div className="space-y-2">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-5/6" />
          <Skeleton className="h-4 w-4/6" />
        </div>

        {/* Case metadata or actions */}
        <div className="flex items-center justify-between pt-2">
          <div className="flex gap-2">
            <Skeleton className="h-5 w-24" />
            <Skeleton className="h-5 w-24" />
          </div>
          <Skeleton className="h-8 w-20" />
        </div>
      </div>
    </div>
  );
};
