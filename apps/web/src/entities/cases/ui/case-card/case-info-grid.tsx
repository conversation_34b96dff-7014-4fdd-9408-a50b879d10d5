import type { CaseWithAssignmentsAndDetails } from '@lilypad/db/repository/types/cases';
import { formatDate } from '@lilypad/ui/lib/utils';
import type React from 'react';
import { CaseStatusBadge } from '@/shared/ui/cases/case-status-badge';
import { CasePriorityBadge } from '@/shared/ui/cases/case-priority-badge';
import { CaseTypeBadge } from '@/shared/ui/cases/case-type-badge';
import { IepStatusBadge } from '@/shared/ui/cases/iep-status-badge';

interface CaseInfoFieldProps {
  label: string;
  value: string | React.ReactNode;
  className?: string;
}

export const CaseInfoField: React.FC<CaseInfoFieldProps> = ({
  label,
  value,
  className,
}) => (
  <div className={`flex flex-col gap-1 ${className || ''}`}>
    <span className="font-medium text-muted-foreground">{label}</span>
    <div className="font-medium">{value}</div>
  </div>
);

interface CaseInfoGridProps {
  studentCase: CaseWithAssignmentsAndDetails;
}

export const CaseInfoGrid: React.FC<CaseInfoGridProps> = ({ studentCase }) => (
  <div className="col-span-10 grid grid-cols-3 gap-2 space-y-1">
    <CaseInfoField
      label="Status"
      value={<CaseStatusBadge status={studentCase.status} />}
    />
    <CaseInfoField
      label="Priority"
      value={<CasePriorityBadge priority={studentCase.priority} />}
    />
    <CaseInfoField
      label="Type"
      value={<CaseTypeBadge type={studentCase.caseType} />}
    />
    <CaseInfoField label="Active" value={studentCase.isActive ? 'Yes' : 'No'} />
    <CaseInfoField
      label="IEP Status"
      value={<IepStatusBadge status={studentCase.iepStatus} />}
    />
    <CaseInfoField
      label="IEP Duration"
      value={`${studentCase.iepStartDate ? formatDate(studentCase.iepStartDate, { month: 'short' }) : 'N/A'} - ${studentCase.iepEndDate ? formatDate(studentCase.iepEndDate, { month: 'short' }) : 'N/A'}`}
    />
    <CaseInfoField
      label="Referral Date"
      value={
        studentCase.referralDate ? formatDate(studentCase.referralDate) : 'N/A'
      }
    />
    <CaseInfoField
      label="Due"
      value={
        studentCase.evaluationDueDate
          ? formatDate(studentCase.evaluationDueDate)
          : 'N/A'
      }
    />
    <CaseInfoField
      label="Meeting"
      value={
        studentCase.meetingDate ? formatDate(studentCase.meetingDate) : 'N/A'
      }
    />
  </div>
);
