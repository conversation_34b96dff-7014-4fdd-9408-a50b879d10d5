import type { CaseWithAssignmentsAndDetails } from '@lilypad/db/repository/types/cases';
import { Card, CardContent } from '@lilypad/ui/components/card';
import type React from 'react';
import {
  CaseAssignees,
  CaseInfoGrid,
  CaseDetailsSection,
  CaseActions,
} from '.';

interface CaseCardProps {
  studentCase: CaseWithAssignmentsAndDetails;
  onArchive?: () => void;
  onView?: () => void;
}

export const CaseCard: React.FC<CaseCardProps> = ({
  studentCase,
  onArchive,
  onView,
}) => {
  if (!studentCase) {
    return null;
  }

  return (
    <Card className="w-full p-0">
      <CardContent className="flex flex-col gap-4 p-4">
        <div className="grid grid-cols-12 gap-4 text-xs">
          {/* Case ID & Assignees */}
          <div className="col-span-2 flex flex-col gap-2 font-medium">
            <span className="text-muted-foreground">Case ID</span>
            <span>#{studentCase.id.slice(0, 8)}</span>
            <CaseAssignees assignments={studentCase.caseAssignments} />
          </div>

          {/* Case Information Grid */}
          <CaseInfoGrid studentCase={studentCase} />
        </div>

        {/* Case Details */}
        <CaseDetailsSection details={studentCase.caseDetails} />
      </CardContent>

      {/* Actions */}
      <CaseActions
        createdAt={studentCase.createdAt}
        updatedAt={studentCase.updatedAt}
        onArchive={onArchive}
        onView={onView}
      />
    </Card>
  );
};
