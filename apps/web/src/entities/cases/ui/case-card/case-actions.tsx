import { Button } from '@lilypad/ui/components/button';
import { CardFooter } from '@lilypad/ui/components/card';
import { formatDate } from '@lilypad/shared/date';
import type React from 'react';

interface CaseActionsProps {
  createdAt: Date;
  updatedAt: Date;
  onArchive?: () => void;
  onView?: () => void;
}

export const CaseActions: React.FC<CaseActionsProps> = ({
  createdAt,
  updatedAt,
  onArchive,
  onView,
}) => (
  <CardFooter className="flex justify-between rounded-b-lg bg-muted px-4 py-2">
    <div className="flex items-center gap-4 text-muted-foreground text-xs">
      <span>Created on {formatDate(createdAt)}</span>|
      <span>Last updated on {formatDate(updatedAt)}</span>
    </div>
    <div className="flex gap-2">
      <Button variant="cancel" size="sm" onClick={onArchive}>
        Archive Case
      </Button>
      <Button variant="outline" size="sm" onClick={onView}>
        View Case
      </Button>
    </div>
  </CardFooter>
);
