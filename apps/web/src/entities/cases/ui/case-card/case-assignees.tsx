import {
  UserHoverCard,
  UserHoverCardContent,
} from '@/shared/ui/user-hover-card';
import type { CaseWithAssignmentsAndDetails } from '@lilypad/db/repository/types/cases';
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from '@lilypad/ui/components/hover-card';
import type React from 'react';

interface CaseAssigneesProps {
  assignments: CaseWithAssignmentsAndDetails['caseAssignments'];
  maxVisible?: number;
}

export const CaseAssignees: React.FC<CaseAssigneesProps> = ({
  assignments,
  maxVisible = 3,
}) => (
  <div className="flex flex-col gap-2">
    <div className="font-semibold text-muted-foreground">Assigned to</div>
    <div className="-space-x-2 flex">
      {assignments.slice(0, maxVisible).map((assignment) => (
        <UserHoverCard
          key={assignment.id}
          user={assignment.user}
          className=""
          size="sm"
        />
      ))}
      {assignments.length > maxVisible && (
        <HoverCard>
          <HoverCardTrigger asChild>
            <div className="flex size-6 cursor-pointer items-center justify-center rounded-full border-2 border-primary/20 bg-muted font-medium text-muted-foreground text-xxs">
              +{assignments.length - maxVisible}
            </div>
          </HoverCardTrigger>
          <HoverCardContent className="w-fit" side="top">
            {assignments.slice(maxVisible).map((assignment) => (
              <UserHoverCardContent
                key={assignment.id}
                user={assignment.user}
                size="sm"
              />
            ))}
          </HoverCardContent>
        </HoverCard>
      )}
    </div>
  </div>
);
