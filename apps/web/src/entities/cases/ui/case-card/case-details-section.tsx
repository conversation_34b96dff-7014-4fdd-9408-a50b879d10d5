import type { CaseWithAssignmentsAndDetails } from '@lilypad/db/repository/types/cases';
import { If } from '@lilypad/ui/components/if';
import type React from 'react';
import { Button } from '@lilypad/ui/components/button';

interface CaseDetailsSectionProps {
  details: CaseWithAssignmentsAndDetails['caseDetails'];
}

export const CaseDetailsSection: React.FC<CaseDetailsSectionProps> = ({
  details,
}) => (
  <div className="flex flex-col border-border border-t border-dashed pt-4 text-sm">
    <div className="mb-1 font-medium">Case Details</div>

    <If condition={details.length === 0}>
      <div className="flex flex-col items-center gap-2 text-muted-foreground">
        <span>No details available.</span>
        <Button variant="outline" size="xs">
          Add details
        </Button>
      </div>
    </If>

    <If condition={details.length > 0}>
      <div className="space-y-1">
        {details.map((detail) => (
          <div key={detail.key} className="flex justify-between text-xs">
            <span className="text-muted-foreground capitalize">
              {detail.key.replace(/_/g, ' ')}
            </span>
            <span className="truncate">{detail.value}</span>
          </div>
        ))}
      </div>
    </If>
  </div>
);
