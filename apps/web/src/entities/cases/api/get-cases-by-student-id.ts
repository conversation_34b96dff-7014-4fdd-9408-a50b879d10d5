import { createDatabaseClient } from '@lilypad/db';
import { CaseRepository } from '@lilypad/db/repository/cases';
import type { CaseWithAssignmentsAndDetails } from '@lilypad/db/repository/types/cases';
import {
  CaseCacheKey,
  cache,
  caching,
  defaultRevalidateTimeInSeconds,
} from '@/shared/caching';

export const getCasesByStudentId = async (
  id: string
): Promise<CaseWithAssignmentsAndDetails[] | null> => {
  const db = await createDatabaseClient();
  const caseRepository = new CaseRepository(
    await db.transaction(async (tx) => tx)
  );

  return cache(
    async () => {
      return await caseRepository.getCompleteCasesByStudentId(id);
    },
    caching.createStudentCasesKeyParts(CaseCacheKey.ByStudentId, id),
    {
      revalidate: defaultRevalidateTimeInSeconds,
      tags: [caching.createStudentCasesTag(CaseCacheKey.ByStudentId, id)],
    }
  )();
};
