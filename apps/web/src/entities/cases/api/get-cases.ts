import {
  cache,
  caching,
  CaseCacheKey,
  defaultRevalidateTimeInSeconds,
} from '@/shared/caching';
import { createDatabaseClient } from '@lilypad/db/client';
import type { RoleEnum } from '@lilypad/db/enums';
import { CaseRepository } from '@lilypad/db/repository/cases';
import type { CaseWithAssignmentsAndDetails } from '@lilypad/db/repository/types/cases';

export const getCasesByUserAccess = async (
  userId: string,
  userRoles: RoleEnum[]
): Promise<CaseWithAssignmentsAndDetails[] | null> => {
  const db = await createDatabaseClient();
  const caseRepository = new CaseRepository(
    await db.transaction(async (tx) => tx)
  );

  return cache(
    async () => {
      return await caseRepository.getCasesByUserAccess(userId, userRoles);
    },
    caching.createCasesByUserAccessKeyParts(CaseCacheKey.ByStudentId, userId),
    {
      revalidate: defaultRevalidateTimeInSeconds,
      tags: [
        caching.createCasesByUserAccessTag(CaseCacheKey.ByStudentId, userId),
      ],
    }
  )();
};
