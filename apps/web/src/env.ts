import { keys as analytics } from '@lilypad/analytics/keys';
import { keys as database } from '@lilypad/db/keys';
import { keys as email } from '@lilypad/email/keys';
import { keys as jobs } from '@lilypad/jobs/keys';
import { keys as kv } from '@lilypad/kv/keys';
import { keys as monitoring } from '@lilypad/monitoring/keys';
import { keys as payments } from '@lilypad/payments/keys';
import { keys as shared } from '@lilypad/shared/keys';
import { keys as supabase } from '@lilypad/supabase/keys';
import { createEnv } from '@t3-oss/env-nextjs';

import { z } from 'zod';

export const env = createEnv({
  extends: [
    analytics(),
    kv(),
    supabase(),
    payments(),
    database(),
    email(),
    monitoring(),
    shared(),
    kv(),
    jobs(),
  ],
  server: {
    BUNDLE_ANALYZER: z.coerce.boolean().default(false),
  },
  client: {},
  runtimeEnv: {
    BUNDLE_ANALYZER: process.env.BUNDLE_ANALYZER,
  },
});
