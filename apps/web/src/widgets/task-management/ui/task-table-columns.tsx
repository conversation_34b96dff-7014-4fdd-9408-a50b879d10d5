'use client';

import { TaskPriorityEnum } from '@lilypad/db/enums';
import type { ColumnDef } from '@tanstack/react-table';
import type { TaskTableRow } from '@/entities/tasks/model/schema';
import { taskPriorityOptions, taskStatusOptions, taskTypeOptions } from '@/entities/tasks/model/schema';

export const taskTableColumns: ColumnDef<TaskTableRow>[] = [
  {
    accessorKey: 'taskType',
    meta: {
      label: 'Task Type',
      variant: 'select',
      options: taskTypeOptions,
    },
    enableColumnFilter: true,
    enableSorting: false,
    filterFn: (row, id, value) => value.includes(row.getValue(id)),
  },
  {
    accessorKey: 'status',
    meta: {
      label: 'Status',
      variant: 'select',
      options: taskStatusOptions,
    },
    enableColumnFilter: true,
    enableSorting: false,
    filterFn: (row, id, value) => value.includes(row.getValue(id)),
  },
  {
    accessorKey: 'priority',
    meta: {
      label: 'Priority',
      variant: 'select',
      options: taskPriorityOptions,
    },
    enableColumnFilter: true,
    enableSorting: true,
    sortingFn: (rowA, rowB) => {
      const priorityMap = {
        [TaskPriorityEnum.LOW]: 0,
        [TaskPriorityEnum.MEDIUM]: 1,
        [TaskPriorityEnum.HIGH]: 2,
        [TaskPriorityEnum.URGENT]: 3,
      };
      const priorityA = priorityMap[rowA.original.priority];
      const priorityB = priorityMap[rowB.original.priority];
      return priorityA - priorityB;
    },
    filterFn: (row, id, value) => value.includes(row.getValue(id)),
  },
  {
    accessorKey: 'assignedToName',
    meta: {
      label: 'Assigned To',
      variant: 'text',
      placeholder: 'Search assignee...'
    },
    enableSorting: false,
    enableColumnFilter: true,
  },
  {
    accessorKey: 'assignedByName',
    meta: {
      label: 'Assigned By',
      variant: 'text',
      placeholder: 'Search assigner...'
    },
    enableColumnFilter: true,
    enableSorting: false,
  },
  {
    accessorKey: 'dueDate',
    meta: {
      label: 'Due Date',
      variant: 'date',
    },
    enableColumnFilter: true,
    enableSorting: true,
    sortingFn: 'datetime',
  },
  {
    accessorKey: 'studentName',
    meta: {
      label: 'Student Name',
      variant: 'text',
      placeholder: 'Search student...'
    },
    enableSorting: false,
    enableColumnFilter: true,
  },
  {
    accessorKey: 'studentIdNumber',
    meta: {
      label: 'Student ID',
      variant: 'text',
      placeholder: 'Search student ID...'
    },
    enableSorting: false,
    enableColumnFilter: true,
  },
  {
    accessorKey: 'districtName',
    meta: {
      label: 'District',
      variant: 'text',
      placeholder: 'Search district...'
    },
    enableSorting: false,
    enableColumnFilter: true,
  },
  {
    accessorKey: 'schoolName',
    meta: {
      label: 'School',
      variant: 'text',
      placeholder: 'Search school...'
    },
    enableSorting: false,
    enableColumnFilter: true,
  },
  {
    accessorKey: 'createdAt',
    meta: {
      label: 'Created',
      variant: 'date',
    },
    enableColumnFilter: true,
    enableSorting: true,
    sortingFn: 'datetime',
  },
  {
    accessorKey: 'completedAt',
    meta: {
      label: 'Completed',
      variant: 'date',
    },
    enableColumnFilter: true,
    enableSorting: true,
    sortingFn: 'datetime',
  },
  {
    accessorKey: 'rejectedAt',
    meta: {
      label: 'Rejected',
      variant: 'date',
    },
    enableColumnFilter: true,
    enableSorting: true,
    sortingFn: 'datetime',
  },
  {
    accessorKey: 'cancelledAt',
    meta: {
      label: 'Cancelled',
      variant: 'date',
    },
    enableColumnFilter: true,
    enableSorting: true,
    sortingFn: 'datetime',
  },
];

// import { TaskPriorityEnumMap, type TaskStatusEnum, TaskTypeEnumMap } from '@lilypad/db/enums';
// import { Avatar, AvatarFallback } from '@lilypad/ui/components/avatar';
// import { Badge } from '@lilypad/ui/components/badge';
// import { Button } from '@lilypad/ui/components/button';
// import {
//   DropdownMenu,
//   DropdownMenuContent,
//   DropdownMenuItem,
//   DropdownMenuLabel,
//   DropdownMenuSeparator,
//   DropdownMenuTrigger,
// } from '@lilypad/ui/components/dropdown-menu';
// import { DataTableColumnHeader } from '@lilypad/ui/data-table/data-table-column-header';
// import { cn } from '@lilypad/ui/lib/utils';
// import {
//   CalendarIcon,
//   EllipsisVerticalIcon,
//   ExternalLinkIcon,
//   UserCheck,
//   UserIcon,
//   UserPlus,
// } from 'lucide-react';
// import { TaskStatusDropdown } from '@/entities/tasks/ui/task-status-dropdown';
// import { useTaskAssignmentDialogs } from '@/features/task-management/model/use-task-assignment';


// const getInitials = (name: string) => {
//   return name
//     ?.split(' ')
//     .map((n) => n[0])
//     .join('')
//     .toUpperCase();
// };

// const formatRelativeDate = (date: Date) => {
//   const now = new Date();
//   const diffInDays = Math.ceil((date.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

//   if (diffInDays === 0) { return 'Today'; }
//   if (diffInDays === 1) { return 'Tomorrow'; }
//   if (diffInDays === -1) { return 'Yesterday'; }
//   if (diffInDays > 0) { return `In ${diffInDays} days`; }
//   return `${Math.abs(diffInDays)} days ago`;
// };

// const getPriorityColor = (priority: string) => {
//   switch (priority) {
//     case 'URGENT':
//       return 'text-red-600 bg-red-50 border-red-200';
//     case 'HIGH':
//       return 'text-orange-600 bg-orange-50 border-orange-200';
//     case 'MEDIUM':
//       return 'text-yellow-600 bg-yellow-50 border-yellow-200';
//     case 'LOW':
//       return 'text-green-600 bg-green-50 border-green-200';
//     default:
//       return 'text-gray-600 bg-gray-50 border-gray-200';
//   }
// };

// export const taskTableColumnsOld: ColumnDef<TaskTableRow>[] = [
//   {
//     accessorKey: 'taskType',
//     header: ({ column }) => (
//       <DataTableColumnHeader column={column} title="Task" />
//     ),
//     cell: ({ row }) => {
//       const taskType = row.original.taskType;
//       const taskTypeInfo = TaskTypeEnumMap[taskType as keyof typeof TaskTypeEnumMap];

//       return (
//         <div className="max-w-[300px]">
//           <div className="font-medium text-sm">
//             {taskTypeInfo?.name || 'Unknown Task Type'}
//           </div>
//           <div className="line-clamp-2 text-muted-foreground text-xs">
//             {taskTypeInfo?.description || 'No description available'}
//           </div>
//         </div>
//       );
//     },
//     enableSorting: true,
//     enableHiding: false,
//   },
//   {
//     accessorKey: 'status',
//     header: ({ column }) => (
//       <DataTableColumnHeader column={column} title="Status" />
//     ),
//     cell: ({ row }) => {
//       const status = row.original.status;

//       return (
//         <TaskStatusDropdown
//           currentStatus={status}
//           onStatusChange={async (_newStatus: TaskStatusEnum) => {
//             // Status change handled by the component
//           }}
//           showIcon={false}
//           size="sm"
//           taskId={row.original.id}
//           variant="ghost"
//         />
//       );
//     },
//     filterFn: (row, id, value) => {
//       return value.includes(row.getValue(id));
//     },
//   },
//   {
//     accessorKey: 'priority',
//     header: ({ column }) => (
//       <DataTableColumnHeader column={column} title="Priority" />
//     ),
//     cell: ({ row }) => {
//       const priority = row.original.priority;

//       return (
//         <Badge
//           className={cn("text-xs", getPriorityColor(priority))}
//           variant="outline"
//         >
//           {TaskPriorityEnumMap[priority]}
//         </Badge>
//       );
//     },
//     filterFn: (row, id, value) => {
//       return value.includes(row.getValue(id));
//     },
//   },
//   {
//     accessorKey: 'assignedToName',
//     header: ({ column }) => (
//       <DataTableColumnHeader column={column} title="Assignee" />
//     ),
//     cell: ({ row }) => {
//       const assignedToName = row.original.assignToName;

//       return (
//         <div className="flex items-center gap-2">
//           <Avatar className="h-8 w-8">
//             <AvatarFallback className="text-xs">
//               {getInitials(assignedToName)}
//             </AvatarFallback>
//           </Avatar>
//           <div>
//             <div className="font-medium text-sm">{assignedToName}</div>
//             {/* <div className="text-muted-foreground text-xs">{assignedToEmail}</div> */}
//           </div>
//         </div>
//       );
//     },
//   },
//   {
//     accessorKey: 'dueDate',
//     header: ({ column }) => (
//       <DataTableColumnHeader column={column} title="Due Date" />
//     ),
//     cell: ({ row }) => {
//       const dueDate = row.original.dueDate;

//       if (!dueDate) {
//         return (
//           <span className="text-muted-foreground text-sm">No due date</span>
//         );
//       }

//       const date = new Date(dueDate);
//       const isOverdue = date < new Date();

//       return (
//         <div className="flex items-center gap-2">
//           <CalendarIcon className={cn(
//             "size-4",
//             isOverdue ? "text-red-600" : "text-muted-foreground"
//           )} />
//           <div>
//             <div className={cn(
//               "text-sm",
//               isOverdue ? 'font-medium text-red-600' : "text-foreground"
//             )}>
//               {formatRelativeDate(date)}
//             </div>
//             <div className="text-muted-foreground text-xs">
//               {date.toLocaleDateString()}
//             </div>
//           </div>
//         </div>
//       );
//     },
//     sortingFn: 'datetime',
//   },
//   {
//     accessorKey: 'studentName',
//     header: ({ column }) => (
//       <DataTableColumnHeader column={column} title="Student" />
//     ),
//     cell: ({ row }) => {
//       const studentName = row.original.studentName;
//       const schoolName = row.original.schoolName;

//       if (!studentName) {
//         return (
//           <span className="text-muted-foreground text-sm">No student assigned</span>
//         );
//       }

//       return (
//         <div>
//           <div className="font-medium text-sm">{studentName}</div>
//           {schoolName && (
//             <div className="text-muted-foreground text-xs">{schoolName}</div>
//           )}
//         </div>
//       );
//     },
//   },
//   {
//     accessorKey: 'createdAt',
//     header: ({ column }) => (
//       <DataTableColumnHeader column={column} title="Created" />
//     ),
//     cell: ({ row }) => {
//       const createdAt = new Date(row.original.createdAt);

//       return (
//         <div className="text-sm">
//           {createdAt.toLocaleDateString()}
//         </div>
//       );
//     },
//     sortingFn: 'datetime',
//   },
//   {
//     id: 'actions',
//     cell: ({ row }) => {
//       const task = row.original;
//       const { quickAssign } = useTaskAssignmentDialogs();

//       return (
//         <DropdownMenu>
//           <DropdownMenuTrigger asChild>
//             <Button
//               className="h-8 w-8 p-0"
//               variant="ghost"
//             >
//               <span className="sr-only">Open menu</span>
//               <EllipsisVerticalIcon className="h-4 w-4" />
//             </Button>
//           </DropdownMenuTrigger>
//           <DropdownMenuContent align="end">
//             <DropdownMenuLabel>Actions</DropdownMenuLabel>
//             <DropdownMenuItem
//               onClick={() => navigator.clipboard.writeText(task.id)}
//             >
//               Copy task ID
//             </DropdownMenuItem>
//             <DropdownMenuSeparator />
//             <DropdownMenuItem onClick={() => quickAssign(task)}>
//               {task.assignByName ? (
//                 <>
//                   <UserCheck className="mr-2 size-4 text-muted-foreground" />
//                   Reassign task
//                 </>
//               ) : (
//                 <>
//                   <UserPlus className="mr-2 size-4 text-muted-foreground" />
//                   Assign task
//                 </>
//               )}
//             </DropdownMenuItem>
//             <DropdownMenuItem>
//               <UserIcon className="mr-2 size-4 text-muted-foreground" />
//               View assignee
//             </DropdownMenuItem>
//             <DropdownMenuItem>
//               <ExternalLinkIcon className="mr-2 size-4 text-muted-foreground" />
//               View details
//             </DropdownMenuItem>
//           </DropdownMenuContent>
//         </DropdownMenu>
//       );
//     },
//   },
// ]; 