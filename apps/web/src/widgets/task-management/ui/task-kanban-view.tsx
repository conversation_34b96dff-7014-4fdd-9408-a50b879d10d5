'use client';

import { useTRPC } from '@lilypad/api/client';
import { TaskStatusEnumMap } from '@lilypad/db/enums';
import { TaskStatusEnum } from '@lilypad/db/schema/enums';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@lilypad/ui/components/alert';
import { Badge } from '@lilypad/ui/components/badge';
import {
  type DragEndEvent,
  KanbanBoard,
  KanbanCard,
  KanbanCards,
  KanbanHeader,
  KanbanProvider,
} from '@lilypad/ui/components/kanban';
import { toast } from '@lilypad/ui/components/sonner';
import { getValidFilters } from '@lilypad/ui/data-table/lib/utils';
import { useSuspenseQuery } from '@tanstack/react-query';
import type { Table } from '@tanstack/react-table';
import { InfoIcon } from 'lucide-react';
import { useSearchParams } from 'next/navigation';
import React, { Suspense } from 'react';
import type { TaskTableRow } from '@/entities/tasks/model/schema';
import { tasksSearchParamsCache } from '@/entities/tasks/model/schema';
import { TaskCard } from '@/entities/tasks/ui';
import { TaskDetailSheet } from '@/features/task-management/ui/task-detail-sheet';
// import { useTaskActions } from '@/features/task-management/model/use-task-actions';
import { TaskErrorWrapper } from '@/features/task-management/ui/task-error-boundary';
import { TaskKanbanSkeleton } from '@/features/task-management/ui/task-skeleton-loaders';

// Define kanban column type
type KanbanColumn = {
  id: string;
  name: string;
  status: TaskStatusEnum;
  count: number;
  color: string;
};

// Define the kanban task item type extending TaskTableRow
type KanbanTaskItem = TaskTableRow & {
  column: string;
  name: string;
};

interface TaskKanbanViewProps {
  onTaskClick?: (task: TaskTableRow) => void;
  onStatusChange?: (
    taskId: string,
    newStatus: TaskStatusEnum
  ) => Promise<void> | void;
  table: Table<TaskTableRow>;
}

// Status column configuration
const statusColumns: Record<TaskStatusEnum, KanbanColumn> = {
  [TaskStatusEnum.PENDING]: {
    id: TaskStatusEnum.PENDING,
    name: TaskStatusEnumMap[TaskStatusEnum.PENDING],
    status: TaskStatusEnum.PENDING,
    count: 0,
    color: 'bg-gray-100 border-gray-300',
  },
  [TaskStatusEnum.IN_PROGRESS]: {
    id: TaskStatusEnum.IN_PROGRESS,
    name: TaskStatusEnumMap[TaskStatusEnum.IN_PROGRESS],
    status: TaskStatusEnum.IN_PROGRESS,
    count: 0,
    color: 'bg-blue-50 border-blue-200',
  },
  [TaskStatusEnum.BLOCKED]: {
    id: TaskStatusEnum.BLOCKED,
    name: TaskStatusEnumMap[TaskStatusEnum.BLOCKED],
    status: TaskStatusEnum.BLOCKED,
    count: 0,
    color: 'bg-orange-50 border-orange-200',
  },
  [TaskStatusEnum.COMPLETED]: {
    id: TaskStatusEnum.COMPLETED,
    name: TaskStatusEnumMap[TaskStatusEnum.COMPLETED],
    status: TaskStatusEnum.COMPLETED,
    count: 0,
    color: 'bg-green-50 border-green-200',
  },
  [TaskStatusEnum.CANCELLED]: {
    id: TaskStatusEnum.CANCELLED,
    name: TaskStatusEnumMap[TaskStatusEnum.CANCELLED],
    status: TaskStatusEnum.CANCELLED,
    count: 0,
    color: 'bg-red-50 border-red-200',
  },
  [TaskStatusEnum.REJECTED]: {
    id: TaskStatusEnum.REJECTED,
    name: TaskStatusEnumMap[TaskStatusEnum.REJECTED],
    status: TaskStatusEnum.REJECTED,
    count: 0,
    color: 'bg-red-50 border-red-200',
  },
};

export function TaskKanbanView({
  onTaskClick,
  onStatusChange,
  table,
}: TaskKanbanViewProps) {
  const searchParams = useSearchParams();
  const trpc = useTRPC();
  // const { updateTaskStatus } = useTaskActions();

  const [selectedTask, setSelectedTask] = React.useState<TaskTableRow | null>(
    null
  );
  const [isDetailSheetOpen, setIsDetailSheetOpen] = React.useState(false);

  const _rows = table.getRowModel().rows;

  const search = React.useMemo(() => {
    const params = Object.fromEntries(searchParams.entries());
    return tasksSearchParamsCache.parse(params);
  }, [searchParams]);

  const validFilters = React.useMemo(
    () => getValidFilters(search.filters),
    [search.filters]
  );

  const { data, isError } = useSuspenseQuery(
    trpc.tasks.getTasks.queryOptions(
      {
        search: search.search,
        filters: validFilters,
        joinOperator: search.joinOperator,
        sort: search.sort,
      },
      {
        staleTime: 30_000, // 30 seconds cache for tasks
      }
    )
  );

  // Transform tasks to kanban format
  const { kanbanData, columns } = React.useMemo(() => {
    if (!data?.tasks) {
      return { kanbanData: [], columns: Object.values(statusColumns) };
    }

    const tasksData: KanbanTaskItem[] = data.tasks.map((task) => ({
      ...task,
      column: task.status, // Use task status as column ID
      name: task.taskType, // Add name property for Kanban
    }));

    // Calculate counts for each column
    const columnCounts = tasksData.reduce(
      (acc, task) => {
        acc[task.status] = (acc[task.status] || 0) + 1;
        return acc;
      },
      {} as Record<TaskStatusEnum, number>
    );

    const columnsWithCounts = Object.values(statusColumns).map((column) => ({
      ...column,
      count: columnCounts[column.status] || 0,
    }));

    return {
      kanbanData: tasksData,
      columns: columnsWithCounts,
    };
  }, [data]);

  const handleTaskClick = React.useCallback(
    (task: TaskTableRow) => {
      setSelectedTask(task);
      setIsDetailSheetOpen(true);
      onTaskClick?.(task);
    },
    [onTaskClick]
  );

  const handleDetailSheetClose = React.useCallback(() => {
    setIsDetailSheetOpen(false);
    // Clear selected task after animation completes
    setTimeout(() => setSelectedTask(null), 300);
  }, []);

  const handleDragEnd = React.useCallback(
    async (event: DragEndEvent) => {
      const { active, over } = event;

      if (!over || active.id === over.id) {
        return;
      }

      const taskId = active.id as string;
      const newStatus = over.id as TaskStatusEnum;

      // Find the task being moved
      const task = kanbanData.find((t) => t.id === taskId);
      if (!task) {
        return;
      }

      // Only update if status actually changed
      if (task.status === newStatus) {
        return;
      }

      try {
        // Use the custom status change handler if provided
        if (onStatusChange) {
          await onStatusChange(taskId, newStatus);
        } else {
          // Otherwise use the default task actions
          // await updateTaskStatus({
          //   taskId,
          //   previousStatus: task.status,
          //   newStatus,
          // });
        }

        toast.success(`Task moved to ${TaskStatusEnumMap[newStatus]}`);
      } catch (_error) {
        toast.error('Failed to move task. Please try again.');
      }
    },
    [kanbanData, onStatusChange]
  );

  if (isError) {
    return (
      <div className="flex items-center justify-center p-8">
        <Alert variant="destructive">
          <InfoIcon className="h-4 w-4" />
          <AlertTitle>Error loading tasks</AlertTitle>
          <AlertDescription>
            There was an error loading the tasks. Please try again.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!data?.tasks || data.totalCount === 0) {
    return (
      <div className="flex items-center justify-center p-8">
        <Alert>
          <InfoIcon className="h-4 w-4" />
          <AlertTitle>No tasks found</AlertTitle>
          <AlertDescription>
            There are no tasks to display. Try adjusting your filters or create
            a new task.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <TaskErrorWrapper resetKeys={['task-kanban-view']}>
      <Suspense fallback={<TaskKanbanViewSkeleton />}>
        <div className="h-full w-full overflow-hidden">
          <KanbanProvider
            className="h-full gap-4 p-4"
            columns={columns}
            data={kanbanData}
            onDragEnd={handleDragEnd}
          >
            {(column) => (
              <KanbanBoard
                className={`min-w-80 ${column.color}`}
                id={column.id}
                key={column.id}
              >
                <KanbanHeader className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <span className="font-semibold">{column.name}</span>
                    <Badge className="text-xs" variant="secondary">
                      {column.count}
                    </Badge>
                  </div>
                </KanbanHeader>
                <KanbanCards className="min-h-96" id={column.id}>
                  {(task) => (
                    <KanbanCard
                      className="cursor-pointer transition-shadow hover:shadow-md"
                      column={task.column}
                      id={task.id}
                      key={task.id}
                      name={task.taskType}
                    >
                      <div className="w-full">
                        <TaskCard
                          className="border-none bg-transparent p-0 shadow-none"
                          task={task}
                          onStatusChange={onStatusChange}
                          onTaskClick={handleTaskClick}
                          showStatusDropdown={false}
                        />
                      </div>
                    </KanbanCard>
                  )}
                </KanbanCards>
              </KanbanBoard>
            )}
          </KanbanProvider>
        </div>

        {isDetailSheetOpen && selectedTask && (
          <TaskErrorWrapper resetKeys={[selectedTask?.id]}>
            <Suspense fallback={<TaskDetailSheetLoadingSkeleton />}>
              <TaskDetailSheet
                isOpen={isDetailSheetOpen}
                onClose={handleDetailSheetClose}
                taskId={selectedTask.id}
              />
            </Suspense>
          </TaskErrorWrapper>
        )}
      </Suspense>
    </TaskErrorWrapper>
  );
}

// Loading skeleton components
function TaskKanbanViewSkeleton() {
  return (
    <div className="h-full w-full overflow-hidden p-4">
      <TaskKanbanSkeleton />
    </div>
  );
}

function TaskDetailSheetLoadingSkeleton() {
  return (
    <div className="fixed inset-y-0 right-0 z-50 w-full border-l bg-background sm:max-w-lg">
      <div className="flex h-full flex-col">
        <div className="border-b p-6">
          <div className="space-y-2">
            <div className="h-6 w-3/4 animate-pulse rounded bg-muted" />
            <div className="h-4 w-1/2 animate-pulse rounded bg-muted" />
          </div>
        </div>
        <div className="flex-1 p-6">
          <div className="space-y-4">
            {Array.from({ length: 6 }, (_, i) => (
              <div
                key={i}
                className="h-4 w-full animate-pulse rounded bg-muted"
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
