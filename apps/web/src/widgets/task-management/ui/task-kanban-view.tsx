'use client';

import { TaskStatusEnumMap } from '@lilypad/db/enums';
import { TaskStatusEnum } from '@lilypad/db/schema/enums';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@lilypad/ui/components/alert';
import { Badge } from '@lilypad/ui/components/badge';
import {
  type DragEndEvent,
  KanbanBoard,
  KanbanCard,
  KanbanCards,
  KanbanHeader,
  type KanbanItemProps,
  KanbanProvider,
} from '@lilypad/ui/components/kanban';
import { toast } from '@lilypad/ui/components/sonner';
import type { Table } from '@tanstack/react-table';
import { InfoIcon, type LucideIcon } from 'lucide-react';
import React, { Suspense } from 'react';
import type { TaskTableRow } from '@/entities/tasks/model/schema';
import { TaskCard } from '@/entities/tasks/ui';
import { TaskDetailSheet } from '@/features/task-management/ui/task-detail-sheet';
// import { useTaskActions } from '@/features/task-management/model/use-task-actions';
import { TaskErrorWrapper } from '@/features/task-management/ui/task-error-boundary';

// Define the kanban task item type extending TaskTableRow
type KanbanTaskItem = TaskTableRow & {
  column: string;
  name: string;
};

interface TaskKanbanViewProps {
  onTaskClick?: (task: TaskTableRow) => void;
  onStatusChange?: (
    taskId: string,
    newStatus: TaskStatusEnum
  ) => Promise<void> | void;
  table: Table<TaskTableRow>;
}

// Status column configuration
const statusColumns: Record<TaskStatusEnum, KanbanColumn> = {
  [TaskStatusEnum.PENDING]: {
    id: TaskStatusEnum.PENDING,
    name: TaskStatusEnumMap[TaskStatusEnum.PENDING],
    status: TaskStatusEnum.PENDING,
    count: 0,
    color: '',
  },
  [TaskStatusEnum.IN_PROGRESS]: {
    id: TaskStatusEnum.IN_PROGRESS,
    name: TaskStatusEnumMap[TaskStatusEnum.IN_PROGRESS],
    status: TaskStatusEnum.IN_PROGRESS,
    count: 0,
    color: '',
  },
  [TaskStatusEnum.BLOCKED]: {
    id: TaskStatusEnum.BLOCKED,
    name: TaskStatusEnumMap[TaskStatusEnum.BLOCKED],
    status: TaskStatusEnum.BLOCKED,
    count: 0,
    color: '',
  },
  [TaskStatusEnum.COMPLETED]: {
    id: TaskStatusEnum.COMPLETED,
    name: TaskStatusEnumMap[TaskStatusEnum.COMPLETED],
    status: TaskStatusEnum.COMPLETED,
    count: 0,
    color: '',
  },
  [TaskStatusEnum.CANCELLED]: {
    id: TaskStatusEnum.CANCELLED,
    name: TaskStatusEnumMap[TaskStatusEnum.CANCELLED],
    status: TaskStatusEnum.CANCELLED,
    count: 0,
    color: '',
  },
  [TaskStatusEnum.REJECTED]: {
    id: TaskStatusEnum.REJECTED,
    name: TaskStatusEnumMap[TaskStatusEnum.REJECTED],
    status: TaskStatusEnum.REJECTED,
    count: 0,
    color: '',
  },
};

export function TaskKanbanView({
  onTaskClick,
  onStatusChange,
  table,
}: TaskKanbanViewProps) {
  // const { updateTaskStatus } = useTaskActions();

  const [selectedTask, setSelectedTask] = React.useState<TaskTableRow | null>(
    null
  );
  const [isDetailSheetOpen, setIsDetailSheetOpen] = React.useState(false);

  const rows = table.getRowModel().rows;

  // Transform tasks to kanban format
  const { kanbanData, columns } = React.useMemo(() => {
    if (!rows.length) {
      return { kanbanData: [], columns: Object.values(statusColumns) };
    }

    const tasksData: KanbanTaskItem[] = rows.map((task) => ({
      ...task.original,
      column: task.original.status,
      name: TaskStatusEnumMap[task.original.status],
    }));

    // Calculate counts for each column
    const columnCounts = tasksData.reduce(
      (acc, task) => {
        acc[task.status] = (acc[task.status] || 0) + 1;
        return acc;
      },
      {} as Record<TaskStatusEnum, number>
    );

    const columnsWithCounts = Object.values(statusColumns).map((column) => ({
      ...column,
      count: columnCounts[column.status] || 0,
    }));

    return {
      kanbanData: tasksData,
      columns: columnsWithCounts,
    };
  }, [rows]);

  const handleTaskClick = React.useCallback(
    (task: TaskTableRow) => {
      setSelectedTask(task);
      setIsDetailSheetOpen(true);
      onTaskClick?.(task);
    },
    [onTaskClick]
  );

  const handleDetailSheetClose = React.useCallback(() => {
    setIsDetailSheetOpen(false);
    // Clear selected task after animation completes
    setTimeout(() => setSelectedTask(null), 300);
  }, []);

  const handleDragEnd = React.useCallback(
    async (event: DragEndEvent) => {
      const { active, over } = event;

      if (!over || active.id === over.id) {
        return;
      }

      const taskId = active.id as string;
      const newStatus = over.id as TaskStatusEnum;

      // Find the task being moved
      const task = kanbanData.find((t) => t.id === taskId);
      if (!task) {
        return;
      }

      // Only update if status actually changed
      if (task.status === newStatus) {
        return;
      }

      try {
        // Use the custom status change handler if provided
        if (onStatusChange) {
          await onStatusChange(taskId, newStatus);
        } else {
          // Otherwise use the default task actions
          // await updateTaskStatus({
          //   taskId,
          //   previousStatus: task.status,
          //   newStatus,
          // });
        }

        toast.success(`Task moved to ${TaskStatusEnumMap[newStatus]}`);
      } catch (_error) {
        toast.error('Failed to move task. Please try again.');
      }
    },
    [kanbanData, onStatusChange]
  );

  if (!rows.length) {
    return (
      <div className="flex items-center justify-center p-8">
        <Alert>
          <InfoIcon className="h-4 w-4" />
          <AlertTitle>No tasks found</AlertTitle>
          <AlertDescription>
            There are no tasks to display. Try adjusting your filters or create
            a new task.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <TaskErrorWrapper resetKeys={['task-kanban-view']}>
      <Suspense fallback={<TaskKanbanViewSkeleton />}>
        <div className="h-dvh w-full overflow-hidden">
          <KanbanProvider
            columns={columns}
            data={kanbanData as unknown as KanbanItemProps[]}
            onDragEnd={handleDragEnd}
          >
            {(column) => (
              <KanbanBoard
                id={column.id}
                key={column.id}
              >
                <KanbanHeader className="border-border/50 border-b">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-foreground">{column.name}</span>
                      <Badge
                        variant="secondary"
                        className="h-5 px-2 font-normal text-muted-foreground text-xs"
                      >
                        {column.count}
                      </Badge>
                    </div>
                  </div>
                </KanbanHeader>
                <KanbanCards id={column.id}>
                  {(task) => (
                    <KanbanCard
                      className="cursor-pointer"
                      column={task.column}
                      id={task.id}
                      key={task.id}
                      name={task.name}
                    >
                      <TaskCard
                        className="border-none bg-transparent p-0 shadow-none"
                        onStatusChange={onStatusChange}
                        onTaskClick={handleTaskClick}
                        task={task as unknown as TaskTableRow}
                      />
                    </KanbanCard>
                  )}
                </KanbanCards>
              </KanbanBoard>
            )}
          </KanbanProvider>
        </div>

        {isDetailSheetOpen && selectedTask && (
          <TaskErrorWrapper resetKeys={[selectedTask?.id]}>
            <Suspense fallback={<TaskDetailSheetLoadingSkeleton />}>
              <TaskDetailSheet
                isOpen={isDetailSheetOpen}
                onClose={handleDetailSheetClose}
                taskId={selectedTask.id}
              />
            </Suspense>
          </TaskErrorWrapper>
        )}
      </Suspense>
    </TaskErrorWrapper>
  );
}

// Loading skeleton components
function TaskKanbanViewSkeleton() {
  return (
    <div className="h-full w-full overflow-hidden">
      <div className="flex h-full">
        {Array.from({ length: 4 }, (_, index) => (
          <div key={`skeleton-column-${index}`} className="w-80 flex-shrink-0 border-border/50 border-r last:border-r-0">
            <div className="border-border/50 border-b px-3 py-4">
              <div className="flex items-center gap-2">
                <div className="h-4 w-20 animate-pulse rounded bg-muted" />
                <div className="h-5 w-8 animate-pulse rounded bg-muted" />
              </div>
            </div>
            <div className="space-y-3 p-3">
              {Array.from({ length: 3 }, (_, cardIndex) => (
                <div key={`skeleton-card-${index}-${cardIndex}`} className="h-24 animate-pulse rounded-lg bg-muted" />
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

function TaskDetailSheetLoadingSkeleton() {
  return (
    <div className="fixed inset-y-0 right-0 z-50 w-full border-l bg-background sm:max-w-lg">
      <div className="flex h-full flex-col">
        <div className="border-b p-6">
          <div className="space-y-2">
            <div className="h-6 w-3/4 animate-pulse rounded bg-muted" />
            <div className="h-4 w-1/2 animate-pulse rounded bg-muted" />
          </div>
        </div>
        <div className="flex-1 p-6">
          <div className="space-y-4">
            {Array.from({ length: 6 }, (_, i) => (
              <div
                className="h-4 w-full animate-pulse rounded bg-muted"
                key={i}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
