'use client';

import { TaskStatusEnumMap } from '@lilypad/db/enums';
import { TaskStatusEnum } from '@lilypad/db/schema/enums';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@lilypad/ui/components/alert';
import { Badge } from '@lilypad/ui/components/badge';
import {
  type DragEndEvent,
  KanbanBoard,
  KanbanCard,
  KanbanCards,
  KanbanHeader,
  type KanbanItemProps,
  KanbanProvider,
} from '@lilypad/ui/components/kanban';
import { toast } from '@lilypad/ui/components/sonner';
import { cn } from '@lilypad/ui/lib/utils';
import type { Table } from '@tanstack/react-table';
import { InfoIcon } from 'lucide-react';
import React, { Suspense } from 'react';
import type { TaskTableRow } from '@/entities/tasks/model/schema';
import { TaskCard } from '@/entities/tasks/ui';
import { TaskDetailSheet } from '@/features/task-management/ui/task-detail-sheet';
// import { useTaskActions } from '@/features/task-management/model/use-task-actions';
import { TaskErrorWrapper } from '@/features/task-management/ui/task-error-boundary';
import { TaskKanbanSkeleton } from '@/features/task-management/ui/task-skeleton-loaders';

// Define kanban column type
type KanbanColumn = {
  id: string;
  name: string;
  status: TaskStatusEnum;
  count: number;
  color: string;
};

// Define the kanban task item type extending TaskTableRow
type KanbanTaskItem = TaskTableRow & {
  column: string;
  name: string;
};

interface TaskKanbanViewProps {
  onTaskClick?: (task: TaskTableRow) => void;
  onStatusChange?: (
    taskId: string,
    newStatus: TaskStatusEnum
  ) => Promise<void> | void;
  table: Table<TaskTableRow>;
}

// Status column configuration
const statusColumns: Record<TaskStatusEnum, KanbanColumn> = {
  [TaskStatusEnum.PENDING]: {
    id: TaskStatusEnum.PENDING,
    name: TaskStatusEnumMap[TaskStatusEnum.PENDING],
    status: TaskStatusEnum.PENDING,
    count: 0,
    color: '',
  },
  [TaskStatusEnum.IN_PROGRESS]: {
    id: TaskStatusEnum.IN_PROGRESS,
    name: TaskStatusEnumMap[TaskStatusEnum.IN_PROGRESS],
    status: TaskStatusEnum.IN_PROGRESS,
    count: 0,
    color: '',
  },
  [TaskStatusEnum.BLOCKED]: {
    id: TaskStatusEnum.BLOCKED,
    name: TaskStatusEnumMap[TaskStatusEnum.BLOCKED],
    status: TaskStatusEnum.BLOCKED,
    count: 0,
    color: '',
  },
  [TaskStatusEnum.COMPLETED]: {
    id: TaskStatusEnum.COMPLETED,
    name: TaskStatusEnumMap[TaskStatusEnum.COMPLETED],
    status: TaskStatusEnum.COMPLETED,
    count: 0,
    color: '',
  },
  [TaskStatusEnum.CANCELLED]: {
    id: TaskStatusEnum.CANCELLED,
    name: TaskStatusEnumMap[TaskStatusEnum.CANCELLED],
    status: TaskStatusEnum.CANCELLED,
    count: 0,
    color: '',
  },
  [TaskStatusEnum.REJECTED]: {
    id: TaskStatusEnum.REJECTED,
    name: TaskStatusEnumMap[TaskStatusEnum.REJECTED],
    status: TaskStatusEnum.REJECTED,
    count: 0,
    color: '',
  },
};

export function TaskKanbanView({
  onTaskClick,
  onStatusChange,
  table,
}: TaskKanbanViewProps) {
  // const { updateTaskStatus } = useTaskActions();

  const [selectedTask, setSelectedTask] = React.useState<TaskTableRow | null>(
    null
  );
  const [isDetailSheetOpen, setIsDetailSheetOpen] = React.useState(false);

  const rows = table.getRowModel().rows;

  // Transform tasks to kanban format
  const { kanbanData, columns } = React.useMemo(() => {
    if (!rows.length) {
      return { kanbanData: [], columns: Object.values(statusColumns) };
    }

    const tasksData: KanbanTaskItem[] = rows.map((task) => ({
      ...task.original,
      column: task.original.status,
      name: TaskStatusEnumMap[task.original.status],
    }));

    // Calculate counts for each column
    const columnCounts = tasksData.reduce(
      (acc, task) => {
        acc[task.status] = (acc[task.status] || 0) + 1;
        return acc;
      },
      {} as Record<TaskStatusEnum, number>
    );

    const columnsWithCounts = Object.values(statusColumns).map((column) => ({
      ...column,
      count: columnCounts[column.status] || 0,
    }));

    return {
      kanbanData: tasksData,
      columns: columnsWithCounts,
    };
  }, [rows]);

  const handleTaskClick = React.useCallback(
    (task: TaskTableRow) => {
      setSelectedTask(task);
      setIsDetailSheetOpen(true);
      onTaskClick?.(task);
    },
    [onTaskClick]
  );

  const handleDetailSheetClose = React.useCallback(() => {
    setIsDetailSheetOpen(false);
    // Clear selected task after animation completes
    setTimeout(() => setSelectedTask(null), 300);
  }, []);

  const handleDragEnd = React.useCallback(
    async (event: DragEndEvent) => {
      const { active, over } = event;

      if (!over || active.id === over.id) {
        return;
      }

      const taskId = active.id as string;
      const newStatus = over.id as TaskStatusEnum;

      // Find the task being moved
      const task = kanbanData.find((t) => t.id === taskId);
      if (!task) {
        return;
      }

      // Only update if status actually changed
      if (task.status === newStatus) {
        return;
      }

      try {
        // Use the custom status change handler if provided
        if (onStatusChange) {
          await onStatusChange(taskId, newStatus);
        } else {
          // Otherwise use the default task actions
          // await updateTaskStatus({
          //   taskId,
          //   previousStatus: task.status,
          //   newStatus,
          // });
        }

        toast.success(`Task moved to ${TaskStatusEnumMap[newStatus]}`);
      } catch (_error) {
        toast.error('Failed to move task. Please try again.');
      }
    },
    [kanbanData, onStatusChange]
  );

  if (!rows.length) {
    return (
      <div className="flex items-center justify-center p-8">
        <Alert>
          <InfoIcon className="h-4 w-4" />
          <AlertTitle>No tasks found</AlertTitle>
          <AlertDescription>
            There are no tasks to display. Try adjusting your filters or create
            a new task.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <TaskErrorWrapper resetKeys={['task-kanban-view']}>
      <Suspense fallback={<TaskKanbanViewSkeleton />}>
        <KanbanProvider
          columns={columns}
          data={kanbanData as unknown as KanbanItemProps[]}
          onDragEnd={handleDragEnd}
        >
          {(column) => (
            <KanbanBoard
              className={cn(
                column.color
              )}
              id={column.id}
              key={column.id}
            >
              <KanbanHeader className="flex items-center justify-between border-none">
                <div className="flex items-center gap-2">
                  <span className="font-semibold">{column.name}</span>
                  <Badge className="text-xs" variant="secondary">
                    {column.count}
                  </Badge>
                </div>
              </KanbanHeader>
              <KanbanCards className="min-h-96" id={column.id}>
                {(task) => (
                  <KanbanCard
                    className="cursor-pointer transition-shadow hover:shadow-md"
                    column={task.column}
                    id={task.id}
                    key={task.id}
                    name={task.name}
                  >
                    <div className="w-full">
                      <TaskCard
                        className="border-none bg-transparent p-0 shadow-none"
                        onStatusChange={onStatusChange}
                        onTaskClick={handleTaskClick}
                        task={task as unknown as TaskTableRow}
                      />
                    </div>
                  </KanbanCard>
                )}
              </KanbanCards>
            </KanbanBoard>
          )}
        </KanbanProvider>

        {isDetailSheetOpen && selectedTask && (
          <TaskErrorWrapper resetKeys={[selectedTask?.id]}>
            <Suspense fallback={<TaskDetailSheetLoadingSkeleton />}>
              <TaskDetailSheet
                isOpen={isDetailSheetOpen}
                onClose={handleDetailSheetClose}
                taskId={selectedTask.id}
              />
            </Suspense>
          </TaskErrorWrapper>
        )}
      </Suspense>
    </TaskErrorWrapper>
  );
}

// Loading skeleton components
function TaskKanbanViewSkeleton() {
  return (
    <div className="h-full w-full overflow-hidden p-4">
      <TaskKanbanSkeleton />
    </div>
  );
}

function TaskDetailSheetLoadingSkeleton() {
  return (
    <div className="fixed inset-y-0 right-0 z-50 w-full border-l bg-background sm:max-w-lg">
      <div className="flex h-full flex-col">
        <div className="border-b p-6">
          <div className="space-y-2">
            <div className="h-6 w-3/4 animate-pulse rounded bg-muted" />
            <div className="h-4 w-1/2 animate-pulse rounded bg-muted" />
          </div>
        </div>
        <div className="flex-1 p-6">
          <div className="space-y-4">
            {Array.from({ length: 6 }, (_, i) => (
              <div
                className="h-4 w-full animate-pulse rounded bg-muted"
                key={i}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
