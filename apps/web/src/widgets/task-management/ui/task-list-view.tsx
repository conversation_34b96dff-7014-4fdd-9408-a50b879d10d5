'use client';

import type { Table } from '@tanstack/react-table';
import type { TaskTableRow } from '@/entities/tasks/model/schema';
import { TaskCard } from '@/entities/tasks/ui';

// import { TaskCard } from '@/entities/tasks/ui';

interface TaskListViewProps {
  table: Table<TaskTableRow>;
  onTaskClick?: (row: TaskTableRow) => void;
}

export function TaskListView({ table, onTaskClick }: TaskListViewProps) {
  const rows = table.getRowModel().rows;

  if (!rows?.length) {
    return (
      <div className="flex h-24 items-center justify-center text-center">
        No results.
      </div>
    );
  }

  return (
    <div className="grid gap-4 overflow-scroll px-4 py-2">
      {rows.map((row) => (
        <TaskCard
          key={row.id}
          // onStatusChange={onStatusChange}
          onTaskClick={onTaskClick}
          task={row.original}
        />
      ))}
    </div>
  );
}
