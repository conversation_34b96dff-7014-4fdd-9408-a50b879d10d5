'use client';

import type { Table } from '@tanstack/react-table';
import type { TaskTableRow } from '@/entities/tasks/model/schema';
import { TaskListItem } from '@/entities/tasks/ui/task-list-item';

interface TaskListViewProps {
  table: Table<TaskTableRow>;
  onTaskClick?: (row: TaskTableRow) => void;
  onStatusChange?: (
    taskId: string,
    newStatus: string
  ) => void;
}

export function TaskListView({ table, onTaskClick, onStatusChange }: TaskListViewProps) {
  const rows = table.getRowModel().rows;

  if (!rows?.length) {
    return (
      <div className="flex h-24 items-center justify-center text-center">
        No results.
      </div>
    );
  }

  return (
    <div className="grid">
      {rows.map((row) => (
        <TaskListItem
          key={row.id}
          onStatusChange={onStatusChange}
          onTaskClick={onTaskClick}
          task={row.original}
        />
      ))}
    </div>
  );
}
