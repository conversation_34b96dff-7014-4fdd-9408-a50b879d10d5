'use client';

import { useTR<PERSON> } from '@lilypad/api/client';
import { type RoleEnum, RoleEnumMap } from '@lilypad/db/enums';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@lilypad/ui/components/alert';
import { But<PERSON> } from '@lilypad/ui/components/button';
import { ButtonGroup } from '@lilypad/ui/components/button-group';
import { If } from '@lilypad/ui/components/if';
import { DataTableAdvancedToolbar } from '@lilypad/ui/data-table/data-table-advanced-toolbar';
import { DataTableFilterMenu } from '@lilypad/ui/data-table/data-table-filter-menu';
import { DataTableSortList } from '@lilypad/ui/data-table/data-table-sort-list';
import { getValidFilters } from '@lilypad/ui/data-table/lib/utils';
import { useSuspenseQuery } from '@tanstack/react-query';
import { AlignJustifyIcon, InfoIcon, KanbanSquareIcon } from 'lucide-react';
import { useSearchParams } from 'next/navigation';
import React, { Suspense } from 'react';
import type { TaskTableRow } from '@/entities/tasks/model/schema';
import { formatTasksToTableRow, tasksSearchParamsCache } from '@/entities/tasks/model/schema';
import { TaskDetailSheet } from '@/features/task-management/ui/task-detail-sheet';
import { TaskErrorWrapper } from '@/features/task-management/ui/task-error-boundary';
import { TaskKanbanSkeleton, TaskListSkeleton, TaskManagementSkeleton } from '@/features/task-management/ui/task-skeleton-loaders';
import { useUser } from '@/shared/contexts/user-context';
import { useDataTable } from '@/shared/hooks/use-data-table';
import { TaskKanbanView } from './task-kanban-view';
import { TaskListView } from './task-list-view';
import { TaskStatsContainer } from './task-stats-container';
import { taskTableColumns } from './task-table-columns';


export function TaskManagementContainer() {
  const { user } = useUser();
  const trpc = useTRPC();
  const searchParams = useSearchParams();

  const [selectedTask, setSelectedTask] = React.useState<TaskTableRow | null>(null);
  const [isDetailSheetOpen, setIsDetailSheetOpen] = React.useState(false);
  const [viewMode, setViewMode] = React.useState<'list' | 'kanban'>('list');

  const search = React.useMemo(() => {
    const params = Object.fromEntries(searchParams.entries());
    return tasksSearchParamsCache.parse(params);
  }, [searchParams]);

  const validFilters = React.useMemo(
    () => getValidFilters(search.filters),
    [search.filters]
  );

  const { data, isError } = useSuspenseQuery(
    trpc.tasks.getTasks.queryOptions(
      {
        search: search.search,
        filters: validFilters,
        joinOperator: search.joinOperator,
        sort: search.sort,
      },
      {
        staleTime: 30_000,
      }
    )
  );

  const { table, debounceMs, advancedFilters, setAdvancedFilters } =
    useDataTable({
      data: formatTasksToTableRow(data?.tasks ?? []),
      columns: taskTableColumns,
      pageCount: data?.totalCount ?? 1,
      shallow: false,
      clearOnDefault: true,
      enableAdvancedFilter: true,
      initialState: {
        pagination: {
          pageIndex: 0,
          pageSize: data?.totalCount,
        },
        sorting: search.sort ?? [{ id: 'dueDate', desc: false }],
      },
    });

  const handleTaskClick = React.useCallback((task: TaskTableRow) => {
    setSelectedTask(task);
    setIsDetailSheetOpen(true);
  }, []);

  const handleDetailSheetClose = React.useCallback(() => {
    setIsDetailSheetOpen(false);
    setTimeout(() => setSelectedTask(null), 300);
  }, []);

  if (isError) {
    return (
      <div className="flex items-center justify-center p-8">
        <Alert variant="destructive">
          <InfoIcon className="h-4 w-4" />
          <AlertTitle>Error loading tasks</AlertTitle>
          <AlertDescription>
            There was an error loading the tasks. Please try again.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  const primaryRole = user?.rolePermissions?.roles?.[0]?.name || 'User';

  return (
    <TaskErrorWrapper resetKeys={['task-management-container']}>
      <Suspense fallback={<TaskManagementContainerSkeleton />}>
        <div className="flex h-full flex-col space-y-4">
          <TaskStatsContainer />

          <div className="flex items-center justify-between px-4">
            <div className="flex flex-col gap-2">
              <div className="flex items-center gap-2">
                <h2 className="font-semibold text-lg">Task Management</h2>
                <span className="text-muted-foreground text-sm">
                  {data?.totalCount || 0} tasks
                </span>
                <span className="rounded bg-secondary px-2 py-1 text-muted-foreground text-xs">
                  {RoleEnumMap[primaryRole as RoleEnum]}
                </span>
              </div>
              <div className="text-muted-foreground text-sm">
                Manage and track all your tasks efficiently.
              </div>
            </div>

            {/* <ButtonGroup>
          <Button
            onClick={() => setViewMode('list')}
            size="sm"
            variant={viewMode === 'list' ? 'default' : 'outline'}
          >
            <AlignJustifyIcon className="mr-2 h-4 w-4" />
            List
          </Button>
          <Button
            onClick={() => setViewMode('kanban')}
            size="sm"
            variant={viewMode === 'kanban' ? 'default' : 'outline'}
          >
            <LayoutGridIcon className="mr-2 h-4 w-4" />
            Kanban
          </Button>
        </ButtonGroup> */}
          </div>

          <div
            className='scrollarea flex w-full flex-col overflow-scroll'
          >
            <DataTableAdvancedToolbar className="border-b px-4 py-2" showViewOptions={false} table={table}>
              <div className="flex items-center gap-2">
                <DataTableSortList align="start" table={table} />
                <DataTableFilterMenu
                  debounceMs={debounceMs}
                  filters={advancedFilters}
                  onFiltersChange={setAdvancedFilters}
                  table={table}
                />
              </div>
              <div className="flex items-center gap-2">
                <ButtonGroup>
                  <Button
                    className={viewMode === 'list' ? 'bg-accent' : ''}
                    onClick={() => setViewMode('list')}
                    size="sm"
                    variant="outline"
                  >
                    <AlignJustifyIcon className="size-4" />
                  </Button>
                  <Button
                    className={viewMode === 'kanban' ? 'bg-accent' : ''}
                    onClick={() => setViewMode('kanban')}
                    size="sm"
                    variant="outline"
                  >
                    <KanbanSquareIcon className="size-4" />
                  </Button>
                </ButtonGroup>
              </div>
            </DataTableAdvancedToolbar>

            <If condition={viewMode === 'list'}>
              <Suspense fallback={<TaskListSkeleton />}>
                <TaskListView
                  onTaskClick={handleTaskClick}
                  table={table}
                />
              </Suspense>
            </If>

            <If condition={viewMode === 'kanban'}>
              <Suspense fallback={<TaskKanbanSkeleton />}>
                <TaskKanbanView
                  onTaskClick={handleTaskClick}
                  table={table}
                 />
              </Suspense>
            </If>

            {isDetailSheetOpen && selectedTask && (
              <TaskDetailSheet
                isOpen={isDetailSheetOpen}
                onClose={handleDetailSheetClose}
                taskId={selectedTask.id}
              />
            )}
          </div>
        </div>
      </Suspense>
    </TaskErrorWrapper>
  );
}

function TaskManagementContainerSkeleton() {
  return (
    <div className="flex h-full flex-col space-y-4">
      {/* Header skeleton */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className="h-6 w-32 animate-pulse rounded bg-muted" />
          <div className="h-4 w-16 animate-pulse rounded bg-muted" />
          <div className="h-5 w-12 animate-pulse rounded bg-muted" />
        </div>
        <div className="flex gap-1">
          <div className="h-8 w-16 animate-pulse rounded bg-muted" />
          <div className="h-8 w-20 animate-pulse rounded bg-muted" />
        </div>
      </div>

      {/* Content skeleton */}
      <div className="min-h-0 flex-1">
        <TaskManagementSkeleton />
      </div>
    </div>
  );
} 