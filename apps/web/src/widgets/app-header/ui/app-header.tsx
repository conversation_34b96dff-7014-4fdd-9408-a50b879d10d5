import {
  PagePrimaryBar,
  PagePrimaryBarActions,
} from '@lilypad/ui/components/page';
import { Separator } from '@lilypad/ui/components/separator';
import React from 'react';
import { NotificationsPopover } from '@/features/notifications/ui/notifications-popover';

interface AppHeaderProps extends React.PropsWithChildren {
  showNotifications?: boolean;
}

export type AppHeaderActionsProps = React.HTMLAttributes<HTMLDivElement>;

export function AppHeaderActions({ children }: AppHeaderActionsProps) {
  return <>{children}</>;
}

export function AppHeader({
  children,
  showNotifications = true,
}: AppHeaderProps) {
  const childrenArray = React.Children.toArray(children);
  let actionsSlot: React.ReactNode = null;
  const otherChildren: React.ReactNode[] = [];

  for (const child of childrenArray) {
    if (React.isValidElement(child) && child.type === AppHeaderActions) {
      const typedChild = child as React.ReactElement<AppHeaderActionsProps>;
      actionsSlot = typedChild.props.children;
    } else {
      otherChildren.push(child);
    }
  }

  return (
    <PagePrimaryBar>
      {otherChildren}
      <PagePrimaryBarActions>
        {actionsSlot}
        <Separator className="ml-2 h-4!" orientation="vertical" />
        {showNotifications && <NotificationsPopover />}
      </PagePrimaryBarActions>
    </PagePrimaryBar>
  );
}
