'use client';

import { routes } from '@lilypad/shared/routes';
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from '@lilypad/ui/components/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from '@lilypad/ui/components/dropdown-menu';
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from '@lilypad/ui/components/sidebar';
import { toast } from '@lilypad/ui/components/sonner';
import { ThemeSwitcher } from '@lilypad/ui/components/theme-switcher';
import { BadgeCheck, LogOut, SettingsIcon, Sparkles } from 'lucide-react';
import Link from 'next/link';
import { signOutAction } from '@/entities/users/api/auth/sign-out.action';
import type { User } from '@/shared/types';

interface NavUserProps {
  user: User;
}

export function NavUser({ user }: NavUserProps) {
  const { open } = useSidebar();

  const onSignOut = async (): Promise<void> => {
    const result = await signOutAction({ redirect: true });
    if (result?.serverError || result?.validationErrors) {
      toast.error("Couldn't sign out. Please try again.");
    }
  };

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
              size="lg"
            >
              <Avatar className="h-8 w-8 rounded-lg">
                <AvatarImage alt={user.fullName} src={user.avatar || ''} />
                <AvatarFallback className="rounded-lg">
                  {user.fullName.charAt(0)}
                </AvatarFallback>
              </Avatar>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-semibold">{user.fullName}</span>
                <span className="truncate text-xs">{user.email}</span>
              </div>
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            align="end"
            className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
            side={open ? 'bottom' : 'right'}
            sideOffset={4}
          >
            <DropdownMenuLabel className="p-0 font-normal">
              <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                <Avatar className="h-8 w-8 rounded-lg">
                  <AvatarImage alt={user.fullName} src={user.avatar || ''} />
                  <AvatarFallback className="rounded-lg">
                    {user.fullName.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold">
                    {user.fullName}
                  </span>
                  <span className="truncate text-xs">{user.email}</span>
                </div>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              <DropdownMenuItem>
                <Sparkles className="size-4" />
                Theme
                <DropdownMenuShortcut>
                  <ThemeSwitcher size="sm" />
                </DropdownMenuShortcut>
              </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              <Link href={routes.app.settings.Index}>
                <DropdownMenuItem>
                  <BadgeCheck className="size-4" />
                  Account
                </DropdownMenuItem>
              </Link>
              <Link href={routes.app.settings.Index}>
                <DropdownMenuItem>
                  <SettingsIcon className="size-4" />
                  Settings
                </DropdownMenuItem>
              </Link>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={onSignOut}>
              <LogOut className="size-4" />
              Sign Out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
