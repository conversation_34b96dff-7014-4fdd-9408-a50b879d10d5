'use client';

import { Kbd, Kbd<PERSON>ey } from '@lilypad/ui/components/kbd';
import {
  SidebarGroup,
  type SidebarGroupProps,
  SidebarMenu,
  SidebarMenuBadge,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from '@lilypad/ui/components/sidebar';
import { AnimatedMessageCircleIcon, AnimatedPlusIcon } from '@lilypad/ui/icons';
import React from 'react';
import { useHotkeys } from 'react-hotkeys-hook';
import { FeedbackDialog } from '@/features/feedback';

export type NavSupportProps = SidebarGroupProps & {};

const SUPPORT_NAV_SHORTCUTS = {
  inviteMember: 'i',
  feedback: 'u',
};

export function NavSupport({ ...props }: NavSupportProps): React.JSX.Element {
  const { setFeedbackOpen } = useSidebar();
  const handleShowInviteMemberModal = React.useCallback((): void => {
    return;
  }, []);

  useHotkeys(`mod+${SUPPORT_NAV_SHORTCUTS.inviteMember}`, (event) => {
    event.preventDefault();
    handleShowInviteMemberModal();
  });

  useHotkeys(`mod+${SUPPORT_NAV_SHORTCUTS.feedback}`, (event) => {
    event.preventDefault();
    setFeedbackOpen(true);
  });

  return (
    <SidebarGroup {...props}>
      <SidebarMenu>
        <FeedbackDialog>
          <SidebarMenuItem>
            <SidebarMenuButton
              className="group/menu-button cursor-pointer text-muted-foreground"
              tooltip="Feedback"
              type="button"
            >
              <AnimatedMessageCircleIcon />
              <span>Share feedback</span>
              <SidebarMenuBadge className="hidden group-hover/menu-button:block">
                <Kbd size="sm" variant="ghost">
                  <KbdKey className="mt-0.25 text-sm">⌘</KbdKey>
                  <KbdKey>
                    {SUPPORT_NAV_SHORTCUTS.feedback.toUpperCase()}
                  </KbdKey>
                </Kbd>
              </SidebarMenuBadge>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </FeedbackDialog>

        <SidebarMenuItem>
          <SidebarMenuButton
            className="group/menu-button cursor-pointer text-muted-foreground"
            tooltip="Invite Members"
            type="button"
          >
            <AnimatedPlusIcon />
            <span>Invite members</span>
            <SidebarMenuBadge className="hidden group-hover/menu-button:block">
              <Kbd size="sm" variant="ghost">
                <KbdKey className="mt-0.25 text-sm">⌘</KbdKey>
                <KbdKey>
                  {SUPPORT_NAV_SHORTCUTS.inviteMember.toUpperCase()}
                </KbdKey>
              </Kbd>
            </SidebarMenuBadge>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    </SidebarGroup>
  );
}
