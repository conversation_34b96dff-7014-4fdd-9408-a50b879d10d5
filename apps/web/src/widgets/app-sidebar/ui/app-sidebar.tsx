'use client';

import { RoleEnum } from '@lilypad/db/enums';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
  useSidebar,
} from '@lilypad/ui/components/sidebar';
import type * as React from 'react';
import type { User } from '@/shared/types';
import { SidebarHeaderIcon } from '@/shared/ui';
import { RoleGuard } from '@/shared/ui/auth-guards';
import { NavMain } from '@/widgets/app-sidebar/ui/nav-main';
import { NavSupport } from '@/widgets/app-sidebar/ui/nav-support';
import { NavUser } from '@/widgets/app-sidebar/ui/nav-user';
import { NavSuperUser } from './nav-super-user';

interface AppSidebarProps extends React.ComponentProps<typeof Sidebar> {
  user: User;
}

export function AppSidebar({ user, ...props }: AppSidebarProps) {
  const { open } = useSidebar();
  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <SidebarHeaderIcon open={open} />
      </SidebarHeader>
      <SidebarContent>
        <NavMain />
        <RoleGuard fallback={<div />} userRole={RoleEnum.SUPER_USER}>
          <NavSuperUser />
        </RoleGuard>
        <NavSupport className="mt-auto pb-0" />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
