'use client';

import { baseUrl, getPathname, routes } from '@lilypad/shared/routes';

import { Kbd, KbdKey } from '@lilypad/ui/components/kbd';
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuBadge,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@lilypad/ui/components/sidebar';
import {
  AnimatedBellIcon,
  AnimatedShieldCheckIcon,
  AnimatedUserIcon,
} from '@lilypad/ui/icons';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import React from 'react';

const SETTINGS_NAV_SHORTCUTS = {
  profile: 'z',
  security: 'x',
  notifications: 'c',
};

const ACCOUNT_SETTINGS_NAV_ITEMS = [
  {
    title: 'Profile',
    url: routes.app.settings.Index,
    icon: <AnimatedUserIcon />,
    shortcut: (
      <Kbd size="sm">
        <KbdKey>⌘</KbdKey>
        <KbdKey>{SETTINGS_NAV_SHORTCUTS.profile.toUpperCase()}</KbdKey>
      </Kbd>
    ),
  },
  {
    title: 'Security',
    url: routes.app.settings.security.Index,
    icon: <AnimatedShieldCheckIcon />,
    shortcut: (
      <Kbd size="sm">
        <KbdKey>⌘</KbdKey>
        <KbdKey>{SETTINGS_NAV_SHORTCUTS.security.toUpperCase()}</KbdKey>
      </Kbd>
    ),
  },
  {
    title: 'Notifications',
    url: routes.app.settings.notifications.Index,
    icon: <AnimatedBellIcon />,
    shortcut: (
      <Kbd size="sm">
        <KbdKey>⌘</KbdKey>
        <KbdKey>{SETTINGS_NAV_SHORTCUTS.notifications.toUpperCase()}</KbdKey>
      </Kbd>
    ),
  },
];
export function AccountSettingsNav() {
  const router = useRouter();
  const pathname = usePathname();

  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.metaKey || event.ctrlKey) {
        switch (event.key) {
          case SETTINGS_NAV_SHORTCUTS.profile:
            event.preventDefault();
            router.push(routes.app.settings.Index);
            break;
          case SETTINGS_NAV_SHORTCUTS.security:
            event.preventDefault();
            router.push(routes.app.settings.security.Index);
            break;
          case SETTINGS_NAV_SHORTCUTS.notifications:
            event.preventDefault();
            router.push(routes.app.settings.notifications.Index);
            break;
          default:
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [router]);

  return (
    <SidebarGroup>
      <SidebarGroupLabel>Account</SidebarGroupLabel>
      <SidebarMenu className="text-muted-foreground">
        {ACCOUNT_SETTINGS_NAV_ITEMS.map((item) => {
          const isActive = pathname === getPathname(item.url, baseUrl.App);

          return (
            <SidebarMenuItem key={item.title}>
              <Link href={item.url}>
                <SidebarMenuButton
                  className="cursor-pointer"
                  isActive={isActive}
                  tooltip={item.title}
                >
                  {item.icon}
                  <span>{item.title}</span>
                  <SidebarMenuBadge>{item.shortcut}</SidebarMenuBadge>
                </SidebarMenuButton>
              </Link>
            </SidebarMenuItem>
          );
        })}
      </SidebarMenu>
    </SidebarGroup>
  );
}
