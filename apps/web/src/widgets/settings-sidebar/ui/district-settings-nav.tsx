'use client';

import { baseUrl, getPathname, routes } from '@lilypad/shared/routes';

import { Kbd, KbdKey } from '@lilypad/ui/components/kbd';
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuBadge,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@lilypad/ui/components/sidebar';
import { AnimatedHomeIcon, AnimatedUsersIcon } from '@lilypad/ui/icons';
import { SchoolIcon } from 'lucide-react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import React from 'react';

const SETTINGS_NAV_SHORTCUTS = {
  general: 'z',
  schools: 'x',
  members: 'c',
};

const DISTRICT_SETTINGS_NAV_ITEMS = [
  {
    title: 'General',
    url: routes.app.settings.district.Index,
    icon: <AnimatedHomeIcon />,
    shortcut: (
      <Kbd size="sm">
        <KbdKey>⌘</KbdKey>
        <KbdKey>{SETTINGS_NAV_SHORTCUTS.general.toUpperCase()}</KbdKey>
      </Kbd>
    ),
  },
  {
    title: 'Schools',
    url: routes.app.settings.district.schools.Index,
    icon: <SchoolIcon />,
    shortcut: (
      <Kbd size="sm">
        <KbdKey>⌘</KbdKey>
        <KbdKey>{SETTINGS_NAV_SHORTCUTS.schools.toUpperCase()}</KbdKey>
      </Kbd>
    ),
  },
  {
    title: 'Members',
    url: routes.app.settings.district.members.Index,
    icon: <AnimatedUsersIcon />,
    shortcut: (
      <Kbd size="sm">
        <KbdKey>⌘</KbdKey>
        <KbdKey>{SETTINGS_NAV_SHORTCUTS.members.toUpperCase()}</KbdKey>
      </Kbd>
    ),
  },
];

export function DistrictSettingsNav() {
  const router = useRouter();
  const pathname = usePathname();

  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.metaKey || event.ctrlKey) {
        switch (event.key) {
          case SETTINGS_NAV_SHORTCUTS.general:
            event.preventDefault();
            router.push(routes.app.settings.district.Index);
            break;
          case SETTINGS_NAV_SHORTCUTS.schools:
            event.preventDefault();
            router.push(routes.app.settings.district.schools.Index);
            break;
          case SETTINGS_NAV_SHORTCUTS.members:
            event.preventDefault();
            router.push(routes.app.settings.district.members.Index);
            break;
          default:
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [router]);

  return (
    <SidebarGroup>
      <SidebarGroupLabel>District</SidebarGroupLabel>
      <SidebarMenu className="text-muted-foreground">
        {DISTRICT_SETTINGS_NAV_ITEMS.map((item) => {
          const isActive = pathname === getPathname(item.url, baseUrl.App);

          return (
            <SidebarMenuItem key={item.title}>
              <Link href={item.url}>
                <SidebarMenuButton
                  className="cursor-pointer"
                  isActive={isActive}
                  tooltip={item.title}
                >
                  {item.icon}
                  <span>{item.title}</span>
                  <SidebarMenuBadge>{item.shortcut}</SidebarMenuBadge>
                </SidebarMenuButton>
              </Link>
            </SidebarMenuItem>
          );
        })}
      </SidebarMenu>
    </SidebarGroup>
  );
}
