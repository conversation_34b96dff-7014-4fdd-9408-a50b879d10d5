'use client';

import { SidebarHeaderIcon } from '@/shared/ui';
import { routes } from '@lilypad/shared/routes';
import { Kbd, KbdKey } from '@lilypad/ui/components/kbd';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuBadge,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@lilypad/ui/components/sidebar';
import { AnimatedChevronLeftIcon } from '@lilypad/ui/icons';
import Link from 'next/link';
import { AccountSettingsNav } from './account-settings-nav';
import { DistrictSettingsNav } from './district-settings-nav';

export function SettingsSidebar() {
  return (
    <Sidebar className="border-r-0" collapsible="icon">
      <SidebarHeader>
        <SidebarHeaderIcon open={true} />
        {/* <SidebarMenu className="text-muted-foreground">
					<SidebarMenuItem>
						<Link href={routes.app.dashboard.Index}>
							<SidebarMenuButton tooltip="Dashboard" className="cursor-pointer">
								<AnimatedChevronLeftIcon />
								<span>Back to app</span>
								<SidebarMenuBadge>
									<Kbd size="sm">
										<KbdKey>⌘</KbdKey>
										<KbdKey>H</KbdKey>
									</Kbd>
								</SidebarMenuBadge>
							</SidebarMenuButton>
						</Link>
					</SidebarMenuItem>
				</SidebarMenu> */}
      </SidebarHeader>
      <SidebarContent>
        <AccountSettingsNav />
        <DistrictSettingsNav />
      </SidebarContent>
      <SidebarFooter className="flex items-center justify-center">
        <SidebarMenu className="pb-8 text-muted-foreground">
          <SidebarMenuItem>
            <Link href={routes.app.dashboard.Index}>
              <SidebarMenuButton
                className="cursor-pointer"
                tooltip="Back to app"
              >
                <AnimatedChevronLeftIcon />
                <span>Back to app</span>
                <SidebarMenuBadge>
                  <Kbd size="sm">
                    <KbdKey>⌘</KbdKey>
                    <KbdKey>H</KbdKey>
                  </Kbd>
                </SidebarMenuBadge>
              </SidebarMenuButton>
            </Link>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  );
}
