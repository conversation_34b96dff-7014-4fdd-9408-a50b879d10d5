'use client';

import type { ColumnDef } from '@tanstack/react-table';
import { Badge } from '@lilypad/ui/components/badge';
import { DataTableColumnHeader } from '@lilypad/ui/data-table/data-table-column-header';
import type {
  GenderEnumMap,
  SchoolGradeEnum,
  EnrollmentStatusEnum,
} from '@lilypad/db/enums';
import {
  gradeOptions,
  genderOptions,
  enrollmentStatusOptions,
  type StudentTableRow,
} from '@/entities/students/model/schema';
import { Phone, Building2, Calendar } from 'lucide-react';
import { StudentStatusBadge } from '../../../shared/ui/students/student-status-badge';
import { StudentGenderBadge } from '../../../shared/ui/students/student-gender-badge';
import { SchoolGradeBadge } from '@/shared/ui/students/school-grade-badge';

export const studentsTableColumns: ColumnDef<StudentTableRow>[] = [
  {
    accessorKey: 'fullName',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Student Name" />
    ),
    cell: ({ row }) => {
      const student = row.original;
      return (
        <div className="flex flex-col">
          <span className="font-medium">{student.fullName}</span>
          <span className="text-muted-foreground text-sm">
            ID: {student.studentIdNumber}
          </span>
          {student.preferredName !== student.fullName && student.fullName && (
            <span className="text-muted-foreground text-xs">
              Preferred: {student.preferredName}
            </span>
          )}
        </div>
      );
    },
    meta: {
      label: 'Student Name',
      variant: 'text',
      placeholder: 'Search students...',
    },
    enableColumnFilter: true,
    enableSorting: true,
  },
  {
    accessorKey: 'grade',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Grade" />
    ),
    cell: ({ row }) => {
      const grade = row.getValue('grade') as SchoolGradeEnum;
      return <SchoolGradeBadge grade={grade} />;
    },
    meta: {
      label: 'Grade',
      variant: 'select',
      options: gradeOptions,
    },
    enableColumnFilter: true,
    enableSorting: true,
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
  {
    accessorKey: 'gender',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Gender" />
    ),
    cell: ({ row }) => {
      const gender = row.getValue('gender') as keyof typeof GenderEnumMap;
      return <StudentGenderBadge gender={gender} />;
    },
    meta: {
      label: 'Gender',
      variant: 'select',
      options: genderOptions,
    },
    enableColumnFilter: true,
    enableSorting: true,
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
  {
    accessorKey: 'schoolName',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="School" />
    ),
    cell: ({ row }) => {
      const student = row.original;
      return (
        <div className="flex items-start gap-2">
          <Building2 className="mt-1 size-3 text-muted-foreground" />
          <div className="flex flex-col">
            <span className="font-medium text-sm">
              {student.schoolName || 'No School'}
            </span>
            <span className="text-muted-foreground text-xs">
              {student.districtName || 'No District'}
            </span>
          </div>
        </div>
      );
    },
    meta: {
      label: 'School',
      variant: 'text',
      placeholder: 'Search schools...',
    },
    enableColumnFilter: true,
    enableSorting: true,
  },
  {
    accessorKey: 'enrollmentStatus',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    cell: ({ row }) => {
      const status = row.getValue('enrollmentStatus') as EnrollmentStatusEnum;

      return <StudentStatusBadge status={status} />;
    },
    meta: {
      label: 'Enrollment Status',
      variant: 'select',
      options: enrollmentStatusOptions,
    },
    enableColumnFilter: true,
    enableSorting: true,
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
  {
    accessorKey: 'specialNeedsIndicator',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Special Needs" />
    ),
    cell: ({ row }) => {
      const hasSpecialNeeds = row.getValue('specialNeedsIndicator') as boolean;
      return (
        <Badge
          className={
            hasSpecialNeeds ? 'border-blue-200 bg-blue-100 text-blue-800' : ''
          }
          variant={hasSpecialNeeds ? 'default' : 'outline'}
        >
          {hasSpecialNeeds ? 'Yes' : 'No'}
        </Badge>
      );
    },
    meta: {
      label: 'Special Needs',
      variant: 'select',
      options: [
        { label: 'Yes', value: 'true' },
        { label: 'No', value: 'false' },
      ],
    },
    enableColumnFilter: true,
    enableSorting: true,
    filterFn: (row, id, value) => {
      const cellValue = row.getValue(id) as boolean;
      return value.includes(cellValue.toString());
    },
  },
  {
    accessorKey: 'emergencyContactName',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Emergency Contact" />
    ),
    cell: ({ row }) => {
      const student = row.original;
      if (!student.emergencyContactName) {
        return (
          <div className="flex w-full items-center justify-center">
            <span className="w-full rounded-md border border-dashed bg-muted p-1 text-center text-muted-foreground text-xs">
              No contact
            </span>
          </div>
        );
      }

      return (
        <div className="flex items-start gap-2">
          <Phone className="mt-1 size-3 text-muted-foreground" />
          <div className="flex flex-col">
            <span className="font-medium text-sm">
              {student.emergencyContactName}
            </span>
            {student.emergencyContactPhone && (
              <span className="text-muted-foreground text-xs">
                {student.emergencyContactPhone}
              </span>
            )}
          </div>
        </div>
      );
    },
    meta: {
      label: 'Emergency Contact',
      variant: 'text',
      placeholder: 'Search contacts...',
    },
    enableColumnFilter: true,
    enableSorting: true,
  },
  {
    accessorKey: 'dateOfBirth',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Date of Birth" />
    ),
    cell: ({ row }) => {
      const dateOfBirth = row.getValue('dateOfBirth') as string;
      const date = new Date(dateOfBirth);
      const age = Math.floor(
        (Date.now() - date.getTime()) / (365.25 * 24 * 60 * 60 * 1000)
      );

      return (
        <div className="flex items-start gap-2">
          <Calendar className="mt-1 size-3 text-muted-foreground" />
          <div className="flex flex-col">
            <span className="text-sm">{date.toLocaleDateString()}</span>
            <span className="text-muted-foreground text-xs">Age: {age}</span>
          </div>
        </div>
      );
    },
    meta: {
      label: 'Date of Birth',
      variant: 'date',
    },
    enableColumnFilter: true,
    enableSorting: true,
  },
  {
    accessorKey: 'dateOfConsent',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Date of Consent" />
    ),
    cell: ({ row }) => {
      const dateOfConsent = row.getValue('dateOfConsent') as string;
      const date = new Date(dateOfConsent);

      return (
        <div className="flex items-start gap-2">
          <Calendar className="mt-1 size-3 text-muted-foreground" />
          <span className="text-sm">{date.toLocaleDateString()}</span>
        </div>
      );
    },
    meta: {
      label: 'Date of Consent',
      variant: 'date',
    },
    enableColumnFilter: true,
    enableSorting: true,
  },
];
