import type { StudentTableRow } from '@/entities/students/model/schema';
import { StudentGenderBadge } from '@/shared/ui/students/student-gender-badge';
import { StudentStatusBadge } from '@/shared/ui/students/student-status-badge';
import { Avatar, AvatarFallback } from '@lilypad/ui/components/avatar';
import { Badge } from '@lilypad/ui/components/badge';
import { Card, CardContent } from '@lilypad/ui/components/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@lilypad/ui/components/dropdown-menu';
import { If } from '@lilypad/ui/components/if';
import {
  AlertCircle,
  BadgeCheckIcon,
  CakeIcon,
  CalendarClockIcon,
  EllipsisVerticalIcon,
  FileUpIcon,
  GraduationCap,
  School,
  SquareUserRoundIcon,
  Trash2Icon,
  UserPenIcon,
} from 'lucide-react';

interface StudentCardProps {
  data: StudentTableRow;
  index: number;
}

export function StudentCard({ data }: StudentCardProps) {
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase();
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }).format(date);
  };

  const formatGrade = (grade: string) => {
    if (grade === 'K') {
      return 'Kindergarten';
    }
    if (grade === 'PK') {
      return 'Pre-K';
    }

    const gradeNum = Number.parseInt(grade, 10);
    if (!Number.isNaN(gradeNum)) {
      if (gradeNum === 1) {
        return '1st Grade';
      }
      if (gradeNum === 2) {
        return '2nd Grade';
      }
      if (gradeNum === 3) {
        return '3rd Grade';
      }
      return `${gradeNum}th Grade`;
    }

    return grade;
  };

  const hasEmergencyContact =
    data.emergencyContactName || data.emergencyContactPhone;

  return (
    <Card className="overflow-hidden bg-muted/40 shadow-none">
      <CardContent className="space-y-4">
        <div className="flex items-start gap-3">
          <Avatar className="h-16 w-16 border border-primary">
            <AvatarFallback className="text-lg">
              {getInitials(data.fullName || '')}
            </AvatarFallback>
          </Avatar>

          <div className="flex-1">
            <div className="flex items-center justify-between">
              <h2 className="font-bold text-lg">
                {data.fullName?.split(' ')[0]} {data.fullName?.split(' ')[1]}
              </h2>
              <div className="flex items-center gap-2">
                <StudentStatusBadge status={data.enrollmentStatus} />
                <DropdownMenu>
                  <DropdownMenuTrigger className="cursor-pointer">
                    <EllipsisVerticalIcon className="size-4 text-muted-foreground" />
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuGroup>
                      <DropdownMenuLabel className="text-muted-foreground text-xs">
                        Case Management
                      </DropdownMenuLabel>
                      <DropdownMenuItem>
                        <CalendarClockIcon className="size-3.5 text-muted-foreground" />
                        Schedule Evaluation
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <FileUpIcon className="size-3.5 text-muted-foreground" />
                        Upload Files
                      </DropdownMenuItem>
                    </DropdownMenuGroup>
                    <DropdownMenuSeparator />
                    <DropdownMenuGroup>
                      <DropdownMenuLabel className="text-muted-foreground text-xs">
                        Manage Student
                      </DropdownMenuLabel>
                      <DropdownMenuItem>
                        <SquareUserRoundIcon className="size-3.5 text-muted-foreground" />
                        View Profile
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <UserPenIcon className="size-3.5 text-muted-foreground" />
                        Edit
                      </DropdownMenuItem>
                    </DropdownMenuGroup>
                    <DropdownMenuSeparator />
                    <DropdownMenuGroup>
                      <DropdownMenuItem>
                        <Trash2Icon className="size-3.5 text-muted-foreground" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuGroup>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>

            {data.preferredName && (
              <p className="text-muted-foreground text-xs">
                Preferred: {data.preferredName}
              </p>
            )}

            <div className="mt-1 flex flex-wrap items-center gap-1">
              <Badge className="text-xs" variant="outline">
                ID: {data.studentIdNumber}
              </Badge>
              <StudentGenderBadge gender={data.gender} />
            </div>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-2 text-sm">
          <div className="flex items-center gap-1">
            <GraduationCap className="size-4 text-muted-foreground" />
            <span>{formatGrade(data.grade)}</span>
          </div>

          <div className="flex items-center gap-1">
            <CakeIcon className="size-4 text-muted-foreground" />
            <span>{formatDate(data.dateOfBirth)}</span>
          </div>

          <div className="col-span-2 flex items-start gap-1">
            <School className="mt-0.5 size-4 text-muted-foreground" />
            <div>
              <span>{data.schoolName}</span>
              <span className="block text-muted-foreground text-xs">
                {data.districtName}
              </span>
            </div>
          </div>
        </div>

        <div className="mt-4 rounded-md bg-primary/10 p-3">
          <div className="flex items-center gap-1 font-medium text-xs">
            {/* <Phone className="size-3 text-muted-foreground" /> */}
            <span>Emergency Contact</span>
          </div>
          <div className="mt-1 text-xs">
            <If condition={hasEmergencyContact}>
              <p>{data.emergencyContactName}</p>
              <p>{data.emergencyContactPhone}</p>
            </If>
            <If condition={!hasEmergencyContact}>
              <p>No emergency contact provided</p>
              <p className="text-muted-foreground">
                Please add an emergency contact
              </p>
            </If>
          </div>
        </div>

        <div className="flex items-center justify-between border-t border-dashed pt-4 text-muted-foreground text-xs">
          <div className="flex items-center gap-1">
            <BadgeCheckIcon className="size-4 text-primary/70" />
            <span>Consented on: {formatDate(data.dateOfConsent)}</span>
          </div>
          <If condition={data.specialNeedsIndicator}>
            <Badge
              className="bg-amber-50 text-amber-700 text-xs"
              variant="outline"
            >
              <AlertCircle className="mr-1 h-3 w-3" />
              Special Needs
            </Badge>
          </If>
        </div>
      </CardContent>
    </Card>
  );
}
