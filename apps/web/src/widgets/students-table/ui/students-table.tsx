'use client';

import { useTRPC } from '@lilypad/api/client';
import { routes } from '@lilypad/shared/routes';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@lilypad/ui/components/alert';
import { Button } from '@lilypad/ui/components/button';
import { ButtonGroup } from '@lilypad/ui/components/button-group';
import { DataTable } from '@lilypad/ui/data-table/data-table';
import { DataTableAdvancedToolbar } from '@lilypad/ui/data-table/data-table-advanced-toolbar';
import { DataTableFilterMenu } from '@lilypad/ui/data-table/data-table-filter-menu';
import { DataTableSortList } from '@lilypad/ui/data-table/data-table-sort-list';
import { getValidFilters } from '@lilypad/ui/data-table/lib/utils';
import { useSuspenseQuery } from '@tanstack/react-query';
import { AlignJustifyIcon, InfoIcon, LayoutGridIcon, Plus } from 'lucide-react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import React from 'react';
import type { StudentTableRow } from '@/entities/students/model/schema';
import { studentsSearchParamsCache } from '@/entities/students/model/schema';
import StudentProfileSheet from '@/features/student-sheet/ui/student-profile-sheet';
import { useDataTable } from '@/shared/hooks/use-data-table';
import { StudentCard } from './student-card';
import { studentsTableColumns } from './students-table-columns';

export function StudentsTable() {
  const searchParams = useSearchParams();
  const trpc = useTRPC();

  const search = React.useMemo(() => {
    const params = Object.fromEntries(searchParams.entries());
    return studentsSearchParamsCache.parse(params);
  }, [searchParams]);

  const validFilters = React.useMemo(
    () => getValidFilters(search.filters),
    [search.filters]
  );

  const { data, isError } = useSuspenseQuery(
    trpc.students.getStudents.queryOptions(
      {
        page: search.page,
        perPage: search.perPage,
        search: search.search,
        filters: validFilters,
        joinOperator: search.joinOperator,
        sort: search.sort,
      },
      {
        staleTime: 1000,
      }
    )
  );

  const [showGrid, setShowGrid] = React.useState(false);
  const [selectedStudent, setSelectedStudent] =
    React.useState<StudentTableRow | null>(null);
  const [isSheetOpen, setIsSheetOpen] = React.useState(false);

  const { table, debounceMs, advancedFilters, setAdvancedFilters } =
    useDataTable({
      data: data?.data ?? [],
      columns: studentsTableColumns,
      pageCount: data?.pagination.pages ?? 1,
      shallow: false,
      clearOnDefault: true,
      enableAdvancedFilter: true,
      initialState: {
        pagination: {
          pageIndex: (search.page ?? 1) - 1,
          pageSize: search.perPage ?? 10,
        },
        sorting: search.sort ?? [{ id: 'fullName', desc: false }],
      },
    });

  const handleRowClick = React.useCallback((student: StudentTableRow) => {
    setSelectedStudent(student);
    setIsSheetOpen(true);
  }, []);

  const handleSheetClose = React.useCallback((open: boolean) => {
    setIsSheetOpen(open);
    if (!open) {
      setTimeout(() => setSelectedStudent(null), 300);
    }
  }, []);

  const handleGridToggle = React.useCallback((_showGrid: boolean) => {
    setShowGrid(_showGrid);
  }, []);

  if (isError) {
    return (
      <div className="flex items-center justify-center p-8">
        <Alert variant="destructive">
          <InfoIcon className="h-4 w-4" />
          <AlertTitle>Error loading students</AlertTitle>
          <AlertDescription>
            There was an error loading the students. Please try again.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <>
      <DataTable
        gridConfig={{
          cardComponent: StudentCard,
          cardProps: (data, index) => ({
            data,
            index,
          }),
        }}
        onRowClick={handleRowClick}
        paginationClassName="px-4 py-2"
        rowClickable={true}
        table={table}
        view={showGrid ? 'grid' : 'table'}
      >
        <DataTableAdvancedToolbar className="border-b px-4 py-2" table={table}>
          <div className="flex items-center gap-2">
            <DataTableSortList align="start" table={table} />
            <DataTableFilterMenu
              debounceMs={debounceMs}
              filters={advancedFilters}
              onFiltersChange={setAdvancedFilters}
              table={table}
            />
          </div>
          <div className="flex items-center gap-2">
            <Link href={routes.app.students.add.Index}>
              <Button
                className="flex items-center gap-2"
                size="sm"
                variant="outline"
              >
                <Plus className="size-4" />
                Add Student
              </Button>
            </Link>
            <ButtonGroup>
              <Button
                className={showGrid ? '' : 'bg-accent'}
                onClick={() => handleGridToggle(false)}
                size="sm"
                variant="outline"
              >
                <AlignJustifyIcon className="size-4" />
              </Button>
              <Button
                className={showGrid ? 'bg-accent' : ''}
                onClick={() => handleGridToggle(true)}
                size="sm"
                variant="outline"
              >
                <LayoutGridIcon className="size-4" />
              </Button>
            </ButtonGroup>
          </div>
        </DataTableAdvancedToolbar>
      </DataTable>

      <StudentProfileSheet
        onOpenChange={handleSheetClose}
        open={isSheetOpen}
        student={selectedStudent}
      />
    </>
  );
}
