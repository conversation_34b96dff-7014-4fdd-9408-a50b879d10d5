'use client';

import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@lilypad/ui/components/alert';
import { Badge } from '@lilypad/ui/components/badge';
import { Button } from '@lilypad/ui/components/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@lilypad/ui/components/dialog';
import { If } from '@lilypad/ui/components/if';
import { ScrollArea } from '@lilypad/ui/components/scroll-area';
import { AlertCircleIcon, CheckCircleIcon, XCircleIcon } from 'lucide-react';
import type {
  StudentRowData,
  StudentSaveResult,
} from '@/features/create-students/model/schema';

interface SaveSummaryDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  results: StudentSaveResult[];
  students: StudentRowData[];
  onReset: () => void;
}

export function SaveSummaryDialog({
  open,
  onOpenChange,
  results,
  students,
  onReset,
}: SaveSummaryDialogProps) {
  const successCount = results.filter((r) => r.success).length;
  const failureCount = results.filter((r) => !r.success).length;
  const totalCount = results.length;

  const getStudentInfo = (tempId: string) => {
    const student = students.find((s) => s.id === tempId);
    if (!student) {
      return 'Unknown Student';
    }
    return `${student.firstName} ${student.lastName} (${student.studentIdNumber})`;
  };

  const handleClose = () => {
    if (failureCount === 0) {
      onReset();
    }
    onOpenChange(false);
  };

  return (
    <Dialog onOpenChange={onOpenChange} open={open}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Summary</DialogTitle>
          <DialogDescription>
            Review the results of the bulk student creation operation.
          </DialogDescription>
        </DialogHeader>

        <div className="w-full space-y-4">
          {/* Summary Stats */}
          <div className="grid grid-cols-3 gap-4">
            <div className="rounded-lg border p-4 text-center">
              <p className="font-bold text-2xl">{totalCount}</p>
              <p className="text-muted-foreground text-sm">Total Students</p>
            </div>
            <div className="rounded-lg border border-primary bg-primary/10 p-4 text-center">
              <p className="font-bold text-2xl text-primary/80">
                {successCount}
              </p>
              <p className="text-muted-foreground text-sm">Successful</p>
            </div>
            <div className="rounded-lg border border-red-200 bg-red-50 p-4 text-center dark:border-red-800 dark:bg-red-950">
              <p className="font-bold text-2xl text-red-600">{failureCount}</p>
              <p className="text-muted-foreground text-sm">Failed</p>
            </div>
          </div>

          {/* Success Message */}
          <If condition={successCount > 0 && failureCount === 0}>
            <Alert className="w-full border-green-200 bg-green-50 dark:bg-green-950">
              <CheckCircleIcon className="h-4 w-4 text-green-600" />
              <AlertTitle>Success!</AlertTitle>
              <AlertDescription className="w-full">
                All {successCount} students were created successfully.
              </AlertDescription>
            </Alert>
          </If>

          {/* Partial Success Message */}
          <If condition={successCount > 0 && failureCount > 0}>
            <Alert className="border-yellow-200 bg-yellow-50 dark:bg-yellow-950">
              <AlertCircleIcon className="h-4 w-4 text-yellow-600" />
              <AlertTitle>Partial Success</AlertTitle>
              <AlertDescription>
                {successCount} students were created successfully, but{' '}
                {failureCount} failed.
              </AlertDescription>
            </Alert>
          </If>

          {/* All Failed Message */}
          <If condition={failureCount > 0 && successCount === 0}>
            <Alert variant="destructive">
              <XCircleIcon className="h-4 w-4" />
              <AlertTitle>Save Failed</AlertTitle>
              <AlertDescription>
                No students were created. Please review the errors and try
                again.
              </AlertDescription>
            </Alert>
          </If>

          {/* Detailed Results */}
          <If condition={results.length > 0}>
            <div className="space-y-2">
              <h3 className="font-medium text-sm">Detailed Results</h3>
              <ScrollArea className="h-[300px] pr-4">
                <div className="space-y-2">
                  {results.map((result, index) => (
                    <div
                      className={`rounded-lg border p-3 ${
                        result.success
                          ? 'border-primary bg-primary/10'
                          : 'border-destructive bg-destructive/10 dark:border-destructive/20 dark:bg-destructive/20'
                      }`}
                      key={index}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <p className="font-medium text-sm">
                            {getStudentInfo(result.tempId)}
                          </p>
                          <If condition={result.success && result.studentId}>
                            <p className="mt-1 text-muted-foreground text-xs">
                              Student ID: {result.studentId}
                            </p>
                          </If>
                          <If condition={!result.success && result.error}>
                            <p className="mt-1 text-red-600 text-xs">
                              Error: {result.error}
                            </p>
                          </If>
                        </div>
                        <Badge
                          className="text-xs"
                          variant={result.success ? 'success' : 'destructive'}
                        >
                          {result.success ? 'Success' : 'Failed'}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </div>
          </If>
        </div>

        <DialogFooter>
          <Button onClick={handleClose}>
            {failureCount === 0 ? 'Close & Reset' : 'Close'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
