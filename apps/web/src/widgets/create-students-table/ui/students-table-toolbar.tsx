import { Button } from '@lilypad/ui/components/button';
import { If } from '@lilypad/ui/components/if';
import { Kbd, KbdKey } from '@lilypad/ui/components/kbd';
import {
  CloudUploadIcon,
  CopyIcon,
  Loader2Icon,
  PlusIcon,
  Trash2Icon,
  UploadIcon,
} from 'lucide-react';

interface StudentsTableToolbarProps {
  onAddRow: () => void;
  selectedRowsCount: number;
  onDuplicateSelected: () => void;
  onDeleteSelected: () => void;
  onSaveAll: () => void;
  onClearAll?: () => void;
  onImportCSV?: () => void;
  isSaving: boolean;
}

export function StudentsTableToolbar({
  onAddRow,
  selectedRowsCount,
  onDuplicateSelected,
  onDeleteSelected,
  onSaveAll,
  onClearAll,
  onImportCSV,
  isSaving,
}: StudentsTableToolbarProps) {
  return (
    <div className="flex items-center justify-between border-b p-4">
      <div className="flex items-center gap-2">
        <Button
          className="flex items-center gap-2"
          onClick={onAddRow}
          size="sm"
          variant="outline"
        >
          <PlusIcon className="size-4" />
          <span>Add Row</span>
          <Kbd size="sm">
            <KbdKey>⌘</KbdKey>
            <KbdKey>K</KbdKey>
          </Kbd>
        </Button>

        <If condition={onImportCSV}>
          <Button
            className="flex items-center gap-2"
            onClick={onImportCSV}
            size="sm"
            variant="outline"
          >
            <UploadIcon className="size-4" />
            <span>Import CSV</span>
          </Button>
        </If>

        <If condition={selectedRowsCount > 0}>
          <Button onClick={onDuplicateSelected} size="sm" variant="outline">
            <CopyIcon className="mr-2 size-4" />
            Duplicate Selected{' '}
            <span className="rounded-sm bg-primary/10 px-1.5 py-0.25 text-muted-foreground text-xs">
              {selectedRowsCount}
            </span>
          </Button>
          <Button onClick={onDeleteSelected} size="sm" variant="outline">
            <Trash2Icon className="mr-2 size-4" />
            Delete Selected
          </Button>
        </If>

        <Button onClick={onClearAll} size="sm" variant="cancel">
          <Trash2Icon className="mr-2 size-4" />
          Clear All
        </Button>
      </div>

      <Button disabled={isSaving} onClick={onSaveAll} size="sm">
        <If
          condition={isSaving}
          fallback={
            <>
              <CloudUploadIcon className="mr-1 size-4" />
              Save All
            </>
          }
        >
          <Loader2Icon className="mr-1 size-4 animate-spin" />
          Saving...
        </If>
      </Button>
    </div>
  );
}
