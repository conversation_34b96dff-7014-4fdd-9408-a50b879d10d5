'use client';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@lilypad/ui/components/alert-dialog';
import { Badge } from '@lilypad/ui/components/badge';
import { ScrollArea } from '@lilypad/ui/components/scroll-area';
import { Separator } from '@lilypad/ui/components/separator';
import { AlertTriangleIcon } from 'lucide-react';
import type { StudentRowData } from '@/features/create-students/model/schema';
import type { DuplicateGroup } from '../model/types';

interface DuplicateStudentsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  duplicateGroups: DuplicateGroup[];
  onRemoveDuplicatesAndSave: () => void;
}

export function DuplicateStudentsDialog({
  open,
  onOpenChange,
  duplicateGroups,
  onRemoveDuplicatesAndSave,
}: DuplicateStudentsDialogProps) {
  const totalDuplicates = duplicateGroups.reduce(
    (sum, group) => sum + (group.count - 1),
    0
  );

  const formatStudentInfo = (student: StudentRowData) => {
    return `${student.firstName} ${student.lastName} (ID: ${student.studentIdNumber})`;
  };

  const formatDateOfBirth = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(date);
  };

  return (
    <AlertDialog onOpenChange={onOpenChange} open={open}>
      <AlertDialogContent className="max-w-2xl">
        <AlertDialogHeader>
          <div className="flex items-center gap-2">
            <AlertTriangleIcon className="size-5 text-yellow-600" />
            <AlertDialogTitle>Duplicate Students Detected</AlertDialogTitle>
          </div>
          <AlertDialogDescription>
            We found {totalDuplicates} duplicate student
            {totalDuplicates !== 1 ? 's ' : ' '}
            based on matching names and ID numbers. Review the duplicates below
            and choose how to proceed.
          </AlertDialogDescription>
        </AlertDialogHeader>

        <div className="space-y-4">
          {/* Summary */}
          <div className="flex items-center justify-between rounded-lg border border-yellow-200 bg-yellow-50 p-3 dark:border-yellow-800 dark:bg-yellow-950/20">
            <div className="flex items-center gap-2">
              <AlertTriangleIcon className="size-4 text-yellow-600" />
              <span className="font-medium text-sm text-yellow-800 dark:text-yellow-200">
                {duplicateGroups.length} duplicate group
                {duplicateGroups.length !== 1 ? 's' : ''} found
              </span>
            </div>
            <Badge
              className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
              variant="secondary"
            >
              {totalDuplicates} duplicate{totalDuplicates !== 1 ? 's' : ''}
            </Badge>
          </div>

          {/* Duplicate Groups */}
          <div className="space-y-3">
            <h4 className="font-medium text-muted-foreground text-sm">
              Duplicate Groups
            </h4>
            <ScrollArea className="h-[300px] pr-4">
              <div className="space-y-4">
                {duplicateGroups.map((group, groupIndex) => (
                  <div
                    className="space-y-2 rounded-lg border p-3"
                    key={group.key}
                  >
                    <div className="flex items-center justify-between">
                      <h5 className="font-medium text-sm">
                        Group {groupIndex + 1}
                      </h5>
                      <Badge className="text-xs" variant="outline">
                        {group.count} students
                      </Badge>
                    </div>

                    <div className="space-y-2">
                      {group.students.map((student, studentIndex) => (
                        <div key={student.id}>
                          <div className="flex items-center justify-between text-sm">
                            <div className="flex-1">
                              <p className="font-medium">
                                {formatStudentInfo(student)}
                              </p>
                              <p className="text-muted-foreground text-xs">
                                DOB: {formatDateOfBirth(student.dateOfBirth)} •
                                Grade: {student.grade}
                              </p>
                            </div>
                            {studentIndex === 0 ? (
                              <Badge className="text-xs" variant="default">
                                Keep
                              </Badge>
                            ) : (
                              <Badge className="text-xs" variant="destructive">
                                Remove
                              </Badge>
                            )}
                          </div>
                          {studentIndex < group.students.length - 1 && (
                            <Separator className="my-2" />
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>

          {/* Explanation */}
          <div className="rounded-lg border bg-muted p-3 text-muted-foreground text-xs">
            <p className="mb-1 font-medium">How duplicates are handled:</p>
            <ul className="list-inside list-disc space-y-1">
              <li>The first student in each group will be kept</li>
              <li>All other students in the group will be removed</li>
              <li>This action cannot be undone</li>
            </ul>
          </div>
        </div>

        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction onClick={onRemoveDuplicatesAndSave}>
            Remove Duplicates and Save
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
