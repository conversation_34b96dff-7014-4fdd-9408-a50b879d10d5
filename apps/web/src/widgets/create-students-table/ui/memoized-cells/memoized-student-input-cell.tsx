import React from 'react';
import {
  useStudentField,
  useStudentOperations,
} from '@/features/create-students/model/hooks/use-student-data';
import type { StudentRowData } from '@/features/create-students/model/schema';
import { useValidationStore } from '@/features/create-students/model/stores/students-validation.store';
import { StudentInputCell } from '../cells/student-input-cell';

interface MemoizedStudentInputCellProps {
  studentId: string;
  field: keyof StudentRowData;
  onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void;
  placeholder?: string;
  dataRow?: number;
  dataCol?: string;
  className?: string;
}

export const MemoizedStudentInputCell = React.memo(
  function MemoizedStudentInputCellComponent({
    studentId,
    field,
    onKeyDown,
    placeholder,
    dataRow,
    dataCol,
    className,
  }: MemoizedStudentInputCellProps) {
    const value = (useStudentField(studentId, field) as string) || '';
    const { updateStudent } = useStudentOperations();
    const errors = useValidationStore((state) => state.getErrors(studentId));
    const hasError = !!errors?.[field];

    const handleChange = React.useCallback(
      (newValue: string) => {
        updateStudent(studentId, { [field]: newValue });
      },
      [studentId, field, updateStudent]
    );

    return (
      <StudentInputCell
        className={className}
        dataCol={dataCol}
        dataRow={dataRow}
        hasError={hasError}
        onChange={handleChange}
        onKeyDown={onKeyDown}
        placeholder={placeholder}
        value={value}
      />
    );
  }
);
