import React from 'react';
import {
  useStudentField,
  useStudentOperations,
} from '@/features/create-students/model/hooks/use-student-data';
import type { StudentRowData } from '@/features/create-students/model/schema';
import { useValidationStore } from '@/features/create-students/model/stores/students-validation.store';

interface MemoizedSelectCellProps<T> {
  studentId: string;
  field: string;
  renderCell: (props: {
    value: T;
    onChange: (value: T) => void;
    hasError: boolean;
  }) => React.ReactNode;
}

export function MemoizedSelectCell<T>({
  studentId,
  field,
  renderCell,
}: MemoizedSelectCellProps<T>) {
  const value = useStudentField(studentId, field as keyof StudentRowData) as T;
  const { updateStudent } = useStudentOperations();
  const errors = useValidationStore((state) => state.getErrors(studentId));
  const hasError = !!errors?.[field];

  const handleChange = React.useCallback(
    (newValue: T) => {
      updateStudent(studentId, { [field]: newValue });
    },
    [studentId, field, updateStudent]
  );

  return <>{renderCell({ value, onChange: handleChange, hasError })}</>;
}
