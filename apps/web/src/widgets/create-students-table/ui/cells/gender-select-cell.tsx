import type { GenderEnum } from '@lilypad/db/enums';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@lilypad/ui/components/select';
import { cn } from '@lilypad/ui/lib/utils';
import React from 'react';
import { genderOptions } from '@/entities/students';
import { StudentGenderBadge } from '@/shared/ui/students/student-gender-badge';

interface GenderSelectCellProps {
  value: GenderEnum;
  onChange: (value: GenderEnum) => void;
  hasError?: boolean;
}

export const GenderSelectCell = React.memo(function GenderSelectCell({
  value,
  onChange,
  hasError,
}: GenderSelectCellProps) {
  return (
    <Select onValueChange={onChange} value={value}>
      <SelectTrigger
        className={cn('h-8 w-fit', hasError && 'border-destructive')}
      >
        <SelectValue placeholder="Select" />
      </SelectTrigger>
      <SelectContent>
        {genderOptions.map(({ value }) => (
          <SelectItem key={value} value={value}>
            <StudentGenderBadge gender={value as GenderEnum} />
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
});
