'use client';
import type { Language } from '@lilypad/db/types';
import { Badge } from '@lilypad/ui/components/badge';
import { Button } from '@lilypad/ui/components/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@lilypad/ui/components/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@lilypad/ui/components/popover';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@lilypad/ui/components/tooltip';
import { cn } from '@lilypad/ui/lib/utils';
import { CheckIcon, ChevronsUpDownIcon, StarIcon } from 'lucide-react';
import React from 'react';

interface LanguageSelectCellProps {
  value: string[];
  primaryLanguageId: string | null;
  onChange: (languageIds: string[], primaryLanguageId: string | null) => void;
  languages: Pick<Language, 'id' | 'name' | 'emoji'>[];
  hasError?: boolean;
}

export const LanguageSelectCell = React.memo(
  function LanguageSelectCellComponent({
    value,
    primaryLanguageId,
    onChange,
    languages,
    hasError,
  }: LanguageSelectCellProps) {
    const [open, setOpen] = React.useState(false);

    const selectedLanguages = React.useMemo(
      () => languages.filter((lang) => value.includes(lang.id)),
      [value, languages]
    );

    const primaryLanguage = React.useMemo(
      () => languages.find((lang) => lang.id === primaryLanguageId),
      [primaryLanguageId, languages]
    );

    const handleLanguageToggle = (languageId: string) => {
      if (value.includes(languageId)) {
        // Removing a language
        const newLanguages = value.filter((id) => id !== languageId);
        // If we're removing the primary language, set new primary
        if (primaryLanguageId === languageId) {
          const newPrimary = newLanguages.length > 0 ? newLanguages[0] : null;
          onChange(newLanguages, newPrimary);
        } else {
          onChange(newLanguages, primaryLanguageId);
        }
      } else {
        // Adding a language
        const newLanguages = [...value, languageId];
        // If it's the first language, make it primary
        if (newLanguages.length === 1) {
          onChange(newLanguages, languageId);
        } else {
          onChange(newLanguages, primaryLanguageId);
        }
      }
    };

    const handleSetPrimary = (languageId: string) => {
      if (value.includes(languageId)) {
        onChange(value, languageId);
      }
    };

    return (
      <Popover modal={true} onOpenChange={setOpen} open={open}>
        <PopoverTrigger asChild>
          <Button
            aria-expanded={open}
            aria-label="Select languages"
            className={cn(
              'w-fit justify-between',
              hasError && 'border-destructive'
            )}
            role="combobox"
            size="sm"
            variant="outline"
          >
            {selectedLanguages.length > 0 ? (
              <div className="flex items-center gap-1 overflow-hidden">
                {primaryLanguage && (
                  <>
                    <StarIcon className="h-3 w-3 fill-primary text-primary" />
                    <span className="truncate text-sm">
                      {primaryLanguage.emoji} {primaryLanguage.name}
                    </span>
                  </>
                )}
                {selectedLanguages.length > 1 && (
                  <Badge className="ml-1 h-5 px-1 text-xs" variant="secondary">
                    +{selectedLanguages.length - 1}
                  </Badge>
                )}
              </div>
            ) : (
              <span className="font-normal text-muted-foreground">
                Select languages
              </span>
            )}
            <ChevronsUpDownIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent align="start" className="w-80 p-0">
          <Command>
            <CommandInput placeholder="Search languages..." />
            <CommandList>
              <CommandEmpty>No language found.</CommandEmpty>
              <CommandGroup>
                {languages.map((language) => {
                  const isSelected = value.includes(language.id);
                  const isPrimary = primaryLanguageId === language.id;

                  return (
                    <CommandItem
                      className="flex items-center justify-between"
                      key={language.id}
                      onSelect={() => handleLanguageToggle(language.id)}
                      value={language.name}
                    >
                      <div className="flex items-center">
                        <CheckIcon
                          className={cn(
                            'mr-2 h-4 w-4',
                            isSelected ? 'opacity-100' : 'opacity-0'
                          )}
                        />
                        <span className="mr-2">{language.emoji}</span>
                        <span>{language.name}</span>
                      </div>
                      {isSelected && (
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              className="h-6 w-6 p-0"
                              onClick={(e) => {
                                e.stopPropagation();
                                e.preventDefault();
                                handleSetPrimary(language.id);
                              }}
                              size="icon"
                              variant="ghost"
                            >
                              <StarIcon
                                className={cn(
                                  'h-4 w-4',
                                  isPrimary
                                    ? 'fill-primary text-primary'
                                    : 'text-muted-foreground hover:text-primary'
                                )}
                              />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            {isPrimary ? 'Primary language' : 'Set as primary'}
                          </TooltipContent>
                        </Tooltip>
                      )}
                    </CommandItem>
                  );
                })}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    );
  }
);
