import { toast } from '@lilypad/ui/components/sonner';
import React from 'react';

import type { StudentRowData } from '@/features/create-students/model/schema';
import type { DuplicateGroup } from './types';

interface UseStudentsTableHandlersProps {
  students: StudentRowData[];
  validateAllRows: () => boolean;
  clearSaveResults: () => void;
  executeBulkCreate: (args: { students: StudentRowData[] }) => void;
  addRow: () => void;
  columnsLength: number;
  onShowDuplicateDialog: (duplicateGroups: DuplicateGroup[]) => void;
}

export function useStudentsTableHandlers({
  students,
  validateAllRows,
  clearSaveResults,
  executeBulkCreate,
  addRow,
  columnsLength,
  onShowDuplicateDialog,
}: UseStudentsTableHandlersProps) {
  const handleKeyDown = React.useCallback(
    (e: React.KeyboardEvent, rowIndex: number, columnIndex: number) => {
      if (e.key === 'Tab' && !e.shiftKey) {
        const isLastColumn = columnIndex === columnsLength - 2; // -1 for actions column
        const isLastRow = rowIndex === students.length - 1;

        if (isLastColumn && isLastRow) {
          e.preventDefault();
          addRow();
          // Focus on first cell of new row after a short delay
          setTimeout(() => {
            const firstInput = document.querySelector(
              `[data-row="${students.length}"][data-col="0"] input`
            ) as HTMLInputElement;
            firstInput?.focus();
          }, 100);
        }
      }
    },
    [students.length, addRow, columnsLength]
  );

  const detectDuplicates = React.useCallback(
    (studentList: StudentRowData[]): DuplicateGroup[] => {
      const duplicateMap = new Map<string, StudentRowData[]>();

      for (const student of studentList) {
        const key = `${student.firstName}-${student.lastName}-${student.studentIdNumber}`;
        if (!duplicateMap.has(key)) {
          duplicateMap.set(key, []);
        }
        duplicateMap.get(key)?.push(student);
      }

      // Filter out groups with only one student and create duplicate groups
      const duplicateGroups: DuplicateGroup[] = [];
      for (const [key, groupStudents] of duplicateMap.entries()) {
        if (groupStudents.length > 1) {
          duplicateGroups.push({
            key,
            students: groupStudents,
            count: groupStudents.length,
          });
        }
      }

      return duplicateGroups;
    },
    []
  );

  const removeDuplicates = React.useCallback(
    (studentList: StudentRowData[]): StudentRowData[] => {
      const seen = new Set<string>();
      const uniqueStudents: StudentRowData[] = [];

      for (const student of studentList) {
        const key = `${student.firstName}-${student.lastName}-${student.studentIdNumber}`;
        if (!seen.has(key)) {
          seen.add(key);
          uniqueStudents.push(student);
        }
      }

      return uniqueStudents;
    },
    []
  );

  const handleSaveAll = React.useCallback(() => {
    const isValid = validateAllRows();

    if (!isValid) {
      toast.error('Please fix all errors before saving');
      return;
    }

    const duplicateGroups = detectDuplicates(students);

    if (duplicateGroups.length > 0) {
      onShowDuplicateDialog(duplicateGroups);
      return;
    }

    clearSaveResults();
    executeBulkCreate({ students });
  }, [
    students,
    validateAllRows,
    clearSaveResults,
    executeBulkCreate,
    detectDuplicates,
    onShowDuplicateDialog,
  ]);

  const handleRemoveDuplicatesAndSave = React.useCallback(() => {
    const uniqueStudents = removeDuplicates(students);
    clearSaveResults();
    executeBulkCreate({ students: uniqueStudents });
  }, [students, removeDuplicates, clearSaveResults, executeBulkCreate]);

  return {
    handleKeyDown,
    handleSaveAll,
    handleRemoveDuplicatesAndSave,
  };
}
