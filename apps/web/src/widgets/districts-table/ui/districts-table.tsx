'use client';

import { useTRPC } from '@lilypad/api/client';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@lilypad/ui/components/alert';
import { DataTable } from '@lilypad/ui/data-table/data-table';
import { DataTableAdvancedToolbar } from '@lilypad/ui/data-table/data-table-advanced-toolbar';
import { DataTableFilterMenu } from '@lilypad/ui/data-table/data-table-filter-menu';
import { DataTableSortList } from '@lilypad/ui/data-table/data-table-sort-list';
import { getValidFilters } from '@lilypad/ui/data-table/lib/utils';
import { useSuspenseQuery } from '@tanstack/react-query';
import { InfoIcon } from 'lucide-react';
import { useSearchParams } from 'next/navigation';
import React from 'react';

import { districtsSearchParamsCache } from '@/entities/districts/model/schema';
import { AddDistrictDialog } from '@/features/district-onboarding/ui/add-district-dialog';
import { useDataTable } from '@/shared/hooks/use-data-table';

import { districtsTableColumns } from './districts-table-columns';

export function DistrictsTable() {
  const searchParams = useSearchParams();
  const trpc = useTRPC();

  const search = React.useMemo(() => {
    const params = Object.fromEntries(searchParams.entries());
    return districtsSearchParamsCache.parse(params);
  }, [searchParams]);

  const validFilters = React.useMemo(
    () => getValidFilters(search.filters),
    [search.filters]
  );

  const { data, isError } = useSuspenseQuery(
    trpc.districts.getDistricts.queryOptions(
      {
        page: search.page,
        perPage: search.perPage,
        search: search.search,
        filters: validFilters,
        joinOperator: search.joinOperator,
        sort: search.sort,
      },
      {
        staleTime: 1000,
      }
    )
  );

  const { table, debounceMs, advancedFilters, setAdvancedFilters } =
    useDataTable({
      data: data?.data ?? [],
      columns: districtsTableColumns,
      pageCount: data?.pagination.pages ?? 1,
      shallow: false,
      clearOnDefault: true,
      enableAdvancedFilter: true,
      initialState: {
        pagination: {
          pageIndex: (search.page ?? 1) - 1,
          pageSize: search.perPage ?? 10,
        },
        sorting: search.sort ?? [{ id: 'name', desc: true }],
      },
    });

  if (isError) {
    return (
      <div className="flex items-center justify-center p-8">
        <Alert variant="destructive">
          <InfoIcon className="h-4 w-4" />
          <AlertTitle>Error loading districts</AlertTitle>
          <AlertDescription>
            There was an error loading the districts. Please try again.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <DataTable paginationClassName="px-4 py-2" table={table}>
      <DataTableAdvancedToolbar className="border-b px-4 py-2" table={table}>
        <div className="flex items-center gap-2">
          <DataTableSortList align="start" table={table} />
          <DataTableFilterMenu
            debounceMs={debounceMs}
            filters={advancedFilters}
            onFiltersChange={setAdvancedFilters}
            table={table}
          />
        </div>
        <div className="flex items-center">
          <AddDistrictDialog />
        </div>
      </DataTableAdvancedToolbar>
    </DataTable>
  );
}
