'use client';

import type { ColumnDef } from '@tanstack/react-table';
import { Badge } from '@lilypad/ui/components/badge';
import { Button } from '@lilypad/ui/components/button';
import { DataTableColumnHeader } from '@lilypad/ui/data-table/data-table-column-header';
import { DistrictTypeMap } from '@lilypad/db/enums';
import {
  districtTypeOptions,
  type DistrictTableRow,
} from '@/entities/districts/model/schema';
import { ExternalLink, MapPin } from 'lucide-react';

export const districtsTableColumns: ColumnDef<DistrictTableRow>[] = [
  {
    accessorKey: 'name',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="District Name" />
    ),
    cell: ({ row }) => {
      const district = row.original;
      return (
        <div className="flex flex-col">
          <span className="font-medium">{district.name}</span>
          <span className="text-muted-foreground text-sm">
            NCES ID: {district.ncesId}
          </span>
        </div>
      );
    },
    meta: {
      label: 'District Name',
      variant: 'text',
      placeholder: 'Search districts...',
    },
    enableColumnFilter: true,
    enableSorting: true,
  },
  {
    accessorKey: 'type',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Type" />
    ),
    cell: ({ row }) => {
      const type = row.getValue('type') as keyof typeof DistrictTypeMap;
      return <Badge variant="secondary">{DistrictTypeMap[type]}</Badge>;
    },
    meta: {
      label: 'District Type',
      variant: 'select',
      options: districtTypeOptions,
    },
    enableColumnFilter: true,
    enableSorting: true,
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
  {
    accessorKey: 'county',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="County" />
    ),
    cell: ({ row }) => row.getValue('county'),
    meta: {
      label: 'County',
      variant: 'text',
      placeholder: 'Search counties...',
    },
    enableColumnFilter: true,
    enableSorting: true,
  },
  {
    accessorKey: 'state',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="State" />
    ),
    cell: ({ row }) => row.getValue('state'),
    meta: {
      label: 'State',
      variant: 'text',
      placeholder: 'Search states...',
    },
    enableColumnFilter: true,
    enableSorting: true,
  },
  {
    accessorKey: 'city',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Location" />
    ),
    cell: ({ row }) => {
      const district = row.original;
      return (
        <div className="flex items-center gap-2">
          <MapPin className="size-4 text-muted-foreground" />
          <div className="flex flex-col">
            <span className="text-sm">
              {district.city}, {district.state}
            </span>
            <span className="text-muted-foreground text-xs">
              {district.address}
            </span>
          </div>
        </div>
      );
    },
    meta: {
      label: 'City',
      variant: 'text',
      placeholder: 'Search cities...',
    },
    enableColumnFilter: true,
    enableSorting: true,
  },
  {
    accessorKey: 'website',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Website" />
    ),
    cell: ({ row }) => {
      const website = row.getValue('website') as string;
      if (!website) {
        return null;
      }

      return (
        <Button variant="ghost" size="sm" asChild>
          <a
            className="flex items-center gap-2"
            href={website}
            rel="noopener noreferrer"
            target="_blank"
          >
            <ExternalLink className="size-4" />
            Visit
          </a>
        </Button>
      );
    },
    enableSorting: false,
    enableColumnFilter: false,
  },
  {
    accessorKey: 'stateId',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="State ID" />
    ),
    cell: ({ row }) => (
      <code className="rounded bg-muted px-2 py-1 text-xs">
        {row.getValue('stateId')}
      </code>
    ),
    meta: {
      label: 'State ID',
      variant: 'text',
      placeholder: 'Search state IDs...',
    },
    enableColumnFilter: true,
    enableSorting: true,
  },
];
