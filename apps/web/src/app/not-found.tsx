'use client';

import type * as React from 'react';
import { useRouter } from 'next/navigation';

import { routes } from '@lilypad/shared/routes';
import { Button } from '@lilypad/ui/components/button';

export default function NotFound(): React.JSX.Element {
  const router = useRouter();
  const handleGoBack = (): void => {
    router.back();
  };
  const handleBackToHome = (): void => {
    router.push(routes.site.Index);
  };
  return (
    <div className="flex h-screen flex-col items-center justify-center text-center">
      <span className="font-semibold text-[10rem] leading-none">404</span>
      <h2 className="my-2 font-bold font-heading text-2xl">
        Oops! Page not found.
      </h2>
      <p>We couldn&apos;t find the page you are looking for.</p>
      <div className="mt-8 flex justify-center gap-2">
        <Button type="button" variant="default" onClick={handleGoBack}>
          Go back
        </Button>
        <Button type="button" variant="ghost" onClick={handleBackToHome}>
          Back to Home
        </Button>
      </div>
    </div>
  );
}
