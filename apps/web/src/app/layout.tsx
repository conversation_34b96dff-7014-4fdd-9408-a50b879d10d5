import './globals.css';

import { Toaster } from '@lilypad/ui/components/sonner';
import type { Metadata } from 'next';
import React from 'react';

import { APP_DESCRIPTION } from '@lilypad/shared';
import { APP_NAME } from '@lilypad/shared';
import { baseUrl } from '@lilypad/shared/routes';
import localFont from 'next/font/local';
import { RootProviders } from './providers';

const geistSans = localFont({
  src: './fonts/GeistVF.woff',
  variable: '--font-geist-sans',
});
const geistMono = localFont({
  src: './fonts/GeistMonoVF.woff',
  variable: '--font-geist-mono',
});

export const metadata: Metadata = {
  metadataBase: new URL(baseUrl.App),
  title: APP_NAME,
  description: APP_DESCRIPTION,
  icons: {
    icon: '/favicon/favicon.ico',
    shortcut: '/favicon/favicon-32x32.png',
    apple: '/favicon/apple-touch-icon.png',
  },
  manifest: `${baseUrl.App}/manifest`,
  openGraph: {
    type: 'website',
    locale: 'en_US',
    siteName: APP_NAME,
    title: APP_NAME,
    description: APP_DESCRIPTION,
    url: baseUrl.App,
    images: {
      url: `${baseUrl.App}/og-image`,
      width: 1200,
      height: 630,
      alt: APP_NAME,
    },
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable}`}
        suppressHydrationWarning
      >
        <RootProviders>
          {children}
          <React.Suspense>
            <Toaster />
          </React.Suspense>
        </RootProviders>
      </body>
    </html>
  );
}
