import { JoinRequestCard } from '@/features/join-request/ui/join-request-card';
import { JOIN_REQUEST_METADATA } from '../auth/metadata';
import { ThemeSwitcher } from '@lilypad/ui/components/theme-switcher';

export const metadata = JOIN_REQUEST_METADATA;

export default function JoinRequestPage() {
  return (
    <main className="flex h-screen items-center justify-center bg-gray-50 px-4 dark:bg-background">
      <div className="mx-auto w-full min-w-[320px] max-w-sm space-y-6">
        <JoinRequestCard />
      </div>
      <ThemeSwitcher className="absolute right-6 bottom-6" />
    </main>
  );
}
