import { routes } from '@lilypad/shared/routes';
import { DynamicBreadcrumb } from '@lilypad/ui/components/dynamic-breadcrumb';
import {
  Page,
  PageBody,
  PageHeader,
  PagePrimaryBar,
} from '@lilypad/ui/components/page';
import Link from 'next/link';
import { SETTINGS_PAGE_METADATA } from './_metadata';

export const metadata = SETTINGS_PAGE_METADATA;

export default function SettingsPage() {
  const breadcrumbItems = [
    {
      label: 'Settings',
      href: routes.app.settings.Index,
      asChild: true,
    },
    {
      label: 'Profile',
    },
  ];
  return (
    <Page>
      <PageHeader>
        <PagePrimaryBar>
          <DynamicBreadcrumb
            items={breadcrumbItems}
            linkComponent={({ href, children }) => (
              <Link href={href}>{children}</Link>
            )}
          />
        </PagePrimaryBar>
      </PageHeader>
      <PageBody>
        <div className="mx-auto max-w-6xl space-y-2 p-2 sm:space-y-8 sm:p-6">
          <div className="grid grid-cols-1 gap-2 sm:gap-8 md:grid-cols-2">
            <div className="aspect-video rounded-xl bg-muted/50" />
            <div className="aspect-video rounded-xl bg-muted/50" />
          </div>
        </div>
      </PageBody>
    </Page>
  );
}
