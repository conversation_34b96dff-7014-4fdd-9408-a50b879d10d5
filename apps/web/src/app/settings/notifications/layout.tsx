import { routes } from '@lilypad/shared/routes';
import { AnnotatedLayout } from '@lilypad/ui/components/annotated';
import { DynamicBreadcrumb } from '@lilypad/ui/components/dynamic-breadcrumb';
import {
  Page,
  PageBody,
  PageHeader,
  PagePrimaryBar,
  PageTitle,
} from '@lilypad/ui/components/page';
import Link from 'next/link';

const breadcrumbItems = [
  {
    label: 'Settings',
    href: routes.app.settings.Index,
    asChild: true,
  },
  {
    label: 'Notifications',
  },
];

export type NotificationsSettingsLayoutProps = {
  notificationPreferences: React.ReactNode;
};

export default function NotificationsSettingsLayout({
  notificationPreferences,
}: NotificationsSettingsLayoutProps) {
  return (
    <Page>
      <PageHeader>
        <PagePrimaryBar>
          <PageTitle>
            <DynamicBreadcrumb
              items={breadcrumbItems}
              linkComponent={({ href, children }) => (
                <Link href={href}>{children}</Link>
              )}
            />
          </PageTitle>
        </PagePrimaryBar>
      </PageHeader>
      <PageBody>
        <AnnotatedLayout>{notificationPreferences}</AnnotatedLayout>
      </PageBody>
    </Page>
  );
}
