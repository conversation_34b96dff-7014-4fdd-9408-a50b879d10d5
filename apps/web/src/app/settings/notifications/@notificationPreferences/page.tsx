import { getUserNotificationPreferences } from '@/entities/notifications/api/get-user-notification-preferences';
import { NotificationPreferencesCard } from '@/features/notifications/ui/notification-preferences-card';

export default async function NotificationPreferencesPage(): Promise<React.JSX.Element> {
  const userNotificationPreferences = await getUserNotificationPreferences();
  return (
    <NotificationPreferencesCard
      notificationPreferences={userNotificationPreferences}
    />
  );
}
