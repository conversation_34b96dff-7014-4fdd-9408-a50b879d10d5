import { routes } from '@lilypad/shared/routes';
import { DynamicBreadcrumb } from '@lilypad/ui/components/dynamic-breadcrumb';
import {
  Page,
  PageHeader,
  PagePrimaryBar,
  PageTitle,
} from '@lilypad/ui/components/page';
import Link from 'next/link';

export default function DistrictSettingsPage() {
  const breadcrumbItems = [
    {
      label: 'Settings',
      href: routes.app.settings.Index,
      asChild: true,
    },
    {
      label: 'District',
    },
  ];

  return (
    <Page>
      <PageHeader>
        <PagePrimaryBar>
          <PageTitle>
            <DynamicBreadcrumb
              items={breadcrumbItems}
              linkComponent={({ href, children }) => (
                <Link href={href}>{children}</Link>
              )}
            />
          </PageTitle>
        </PagePrimaryBar>
      </PageHeader>
    </Page>
  );
}
