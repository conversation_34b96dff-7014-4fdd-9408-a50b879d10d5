import { routes } from '@lilypad/shared/routes';
import { AnnotatedLayout } from '@lilypad/ui/components/annotated';
import { DynamicBreadcrumb } from '@lilypad/ui/components/dynamic-breadcrumb';
import {
  Page,
  PageBody,
  PageHeader,
  PagePrimaryBar,
  PageTitle,
} from '@lilypad/ui/components/page';
import Link from 'next/link';

import { Separator } from '@lilypad/ui/components/separator';
import { SECURITY_LAYOUT_METADATA } from '../_metadata';

const breadcrumbItems = [
  {
    label: 'Settings',
    href: routes.app.settings.Index,
    asChild: true,
  },
  {
    label: 'Security',
  },
];

export const metadata = SECURITY_LAYOUT_METADATA;

export type SecurityLayoutProps = {
  multiFactorAuthentication: React.ReactNode;
};

export default function SecurityLayout({
  multiFactorAuthentication,
}: SecurityLayoutProps): React.JSX.Element {
  return (
    <Page>
      <PageHeader>
        <PagePrimaryBar>
          <PageTitle>
            <DynamicBreadcrumb
              items={breadcrumbItems}
              linkComponent={({ href, children }) => (
                <Link href={href}>{children}</Link>
              )}
            />
          </PageTitle>
        </PagePrimaryBar>
      </PageHeader>
      <PageBody>
        <AnnotatedLayout>
          {multiFactorAuthentication}
          <Separator />
        </AnnotatedLayout>
      </PageBody>
    </Page>
  );
}
