import { AnnotatedSection } from '@lilypad/ui/components/annotated';

export default function MultiFactorAuthenticationLayout({
  children,
}: React.PropsWithChildren): React.JSX.Element {
  return (
    <AnnotatedSection
      title="Multi-factor authentication"
      description="Add an extra layer of security to your account by requiring an additional factor to sign in."
    >
      {children}
    </AnnotatedSection>
  );
}
