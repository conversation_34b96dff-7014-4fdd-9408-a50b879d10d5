import { getMultiFactorAuthentication } from '@/entities/users/api/get-multi-factor-authentication';
import { MultiFactorAuthenticationCardSimple } from '@/features/auth/ui/multi-factor-authentication-card-simple';

export default async function MultiFactorAuthenticationPage(): Promise<React.JSX.Element> {
  const multiFactorAuthentication = await getMultiFactorAuthentication();
  return <MultiFactorAuthenticationCardSimple {...multiFactorAuthentication} />;
}
