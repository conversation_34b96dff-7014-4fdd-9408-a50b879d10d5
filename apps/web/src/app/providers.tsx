'use client';

import NiceModal from '@ebay/nice-modal-react';
import { NuqsAdapter } from 'nuqs/adapters/next/app';
import type * as React from 'react';

import { AnalyticsProvider } from '@lilypad/analytics/hooks';
import { MonitoringProvider } from '@lilypad/monitoring/hooks';
import { TooltipProvider } from '@lilypad/ui/components/tooltip';
import { ThemeProvider } from '@lilypad/ui/hooks/use-theme';
import { TRPCReactProvider } from '@lilypad/api/client';

export function RootProviders({
  children,
}: React.PropsWithChildren): React.JSX.Element {
  return (
    <MonitoringProvider>
      <AnalyticsProvider>
        <TRPCReactProvider>
          <NuqsAdapter>
            <ThemeProvider
              attribute="class"
              defaultTheme="system"
              enableSystem
              disableTransitionOnChange
            >
              <TooltipProvider>
                <NiceModal.Provider>{children}</NiceModal.Provider>
              </TooltipProvider>
            </ThemeProvider>
          </NuqsAdapter>
        </TRPCReactProvider>
      </AnalyticsProvider>
    </MonitoringProvider>
  );
}
