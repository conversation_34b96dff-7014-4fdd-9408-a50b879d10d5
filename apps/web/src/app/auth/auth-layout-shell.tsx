import type * as React from 'react';
import { ThemeSwitcher } from '@lilypad/ui/components/theme-switcher';

export async function AuthLayoutShell({
  children,
}: React.PropsWithChildren): Promise<React.JSX.Element> {
  return (
    <main className="flex h-screen items-center justify-center bg-gray-50 px-4 dark:bg-background">
      <div className="mx-auto w-full min-w-[320px] max-w-sm space-y-6">
        {children}
      </div>
      <ThemeSwitcher className="absolute bottom-6" />
    </main>
  );
}
