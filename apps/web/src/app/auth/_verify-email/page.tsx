import type * as React from 'react';

import { createSearchParamsCache, parseAsString } from 'nuqs/server';

import { VerifyEmailCard } from '@/features/auth/ui/verify-email-card';
import { VERIFY_EMAIL_METADATA } from '../metadata';

const searchParamsCache = createSearchParamsCache({
  email: parseAsString.withDefault(''),
});

export const metadata = VERIFY_EMAIL_METADATA;

export default async function VerifyEmailPage({
  searchParams,
}: NextPageProps): Promise<React.JSX.Element> {
  const { email } = await searchParamsCache.parse(searchParams);
  return <VerifyEmailCard email={email} />;
}
