import { notFound } from 'next/navigation';
import { createSearchParamsCache, parseAsString } from 'nuqs/server';

import { RecoveryCodeCard } from '@/features/auth/ui/recovery-code-card';
import { isValidEncryptedToken } from '@lilypad/supabase/auth/encryption';
import { RECOVERY_CODE_METADATA } from '../metadata';

const searchParamsCache = createSearchParamsCache({
  token: parseAsString.withDefault(''),
  expiry: parseAsString.withDefault(''),
});

function isValidExpiry(expiry: string): boolean {
  try {
    // Check if expiry is a valid timestamp (Unix timestamp in seconds or milliseconds)
    const timestamp = Number(expiry);
    if (Number.isNaN(timestamp) || timestamp <= 0) return false;

    // Check if it's a reasonable timestamp (between 2020 and 2100)
    const date = new Date(timestamp > 1e10 ? timestamp : timestamp * 1000);
    const year = date.getFullYear();
    return year >= 2020 && year <= 2100;
  } catch {
    return false;
  }
}

export const metadata = RECOVERY_CODE_METADATA;

export default async function RecoveryCodePage({
  searchParams,
}: NextPageProps): Promise<React.JSX.Element> {
  const { token, expiry } = await searchParamsCache.parse(searchParams);

  // Validate presence
  if (!(token && expiry)) {
    return notFound();
  }

  // Validate token format (encrypted string should match expected pattern)
  if (!isValidEncryptedToken(token)) {
    return notFound();
  }

  // Validate expiry format (should be a valid timestamp)
  if (!isValidExpiry(expiry)) {
    return notFound();
  }

  return <RecoveryCodeCard token={token} expiry={expiry} />;
}
