import { SIGN_IN_METADATA } from '../metadata';
import { SignInCard } from '@/features/auth/ui/sign-in-card';
import { createSearchParamsCache, parseAsString } from 'nuqs/server';

const searchParamsCache = createSearchParamsCache({
  step: parseAsString.withDefault('email'),
  email: parseAsString.withDefault(''),
});

export const metadata = SIGN_IN_METADATA;

export default async function SignInPage({
  searchParams,
}: NextPageProps): Promise<React.JSX.Element> {
  const { step, email } = await searchParamsCache.parse(searchParams);
  return <SignInCard step={step as 'email' | 'verify'} email={email} />;
}
