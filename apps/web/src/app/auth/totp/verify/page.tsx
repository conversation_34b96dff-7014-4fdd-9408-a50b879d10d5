import { notFound, redirect } from 'next/navigation';
import { createSearchParamsCache, parseAsString } from 'nuqs/server';
import type * as React from 'react';

import { TotpCodeCard } from '@/features/auth/ui/totp-code-card';
import { getRedirectToTotp } from '@lilypad/supabase/auth/redirect';
import { getSupabaseServerClient } from '@lilypad/supabase/server';
import { TOTP_VERIFY_METADATA } from '../../metadata';

const searchParamsCache = createSearchParamsCache({
  token: parseAsString.withDefault(''),
  expiry: parseAsString.withDefault(''),
});

export const metadata = TOTP_VERIFY_METADATA;

export default async function TotpPage({
  searchParams,
}: NextPageProps): Promise<React.JSX.Element> {
  const { token, expiry } = await searchParamsCache.parse(searchParams);

  if (!(token && expiry)) {
    const supabase = await getSupabaseServerClient();
    const { data: auth } = await supabase.auth.getUser();
    if (!auth.user) {
      return notFound();
    }
    redirect(getRedirectToTotp(auth.user.id));
  }

  return <TotpCodeCard token={token} expiry={expiry} />;
}
