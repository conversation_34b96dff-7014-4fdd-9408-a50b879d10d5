import { getMultiFactorAuthentication } from '@/entities/users/api/get-multi-factor-authentication';
import { MultiFactorAuthenticationCard } from '@/features/auth/ui/multi-factor-authentication-card';
import { TOTP_METADATA } from '../metadata';

export const metadata = TOTP_METADATA;

export default async function TotpPage(): Promise<React.JSX.Element> {
  const multiFactorAuthentication = await getMultiFactorAuthentication();
  return <MultiFactorAuthenticationCard {...multiFactorAuthentication} />;
}
