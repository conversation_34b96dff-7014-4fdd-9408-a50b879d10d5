'use client';

import type * as React from 'react';
import NextError from 'next/error';

import { useCaptureError } from '@lilypad/monitoring/hooks';

export type GlobalErrorProps = {
  error: Error & { digest?: string };
  reset: () => void;
};

export default function GlobalError({
  error: { digest, ...error },
}: GlobalErrorProps): React.JSX.Element {
  useCaptureError(error, { digest });
  return (
    <html lang="en">
      <body>
        {/* This is the default Next.js error component but it doesn't allow omitting the statusCode property yet. */}
        <NextError statusCode={undefined as never} />
      </body>
    </html>
  );
}
