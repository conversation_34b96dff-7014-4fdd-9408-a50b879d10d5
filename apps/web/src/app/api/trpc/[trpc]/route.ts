import { fetchRequestHandler } from '@trpc/server/adapters/fetch';
import type { NextRequest } from 'next/server';
import { createTRPCContext } from 'node_modules/@lilypad/api/src/core/init';
import { appRouter } from 'node_modules/@lilypad/api/src/routers/_app';

const handler = (req: NextRequest) =>
  fetchRequestHandler({
    endpoint: '/api/trpc',
    req,
    router: appRouter,
    createContext: createTRPCContext,
    onError({ path, error }) {
      console.error(
        `❌ tRPC failed on ${path ?? '<no-path>'}: ${error.message}`
      );
    },
  });

export { handler as GET, handler as POST };
