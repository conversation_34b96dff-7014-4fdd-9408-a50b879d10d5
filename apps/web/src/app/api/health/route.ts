import { NextResponse } from 'next/server';

import { createDatabaseClient, sql } from '@lilypad/db/client';

export async function GET(): Promise<Response> {
  try {
    const db = await createDatabaseClient();
    await db.transaction(async (tx) => {
      await tx.execute(sql`SELECT 1`);
    });
    return NextResponse.json({ status: 'ok' });
  } catch (err) {
    const { statusCode = 503 } = err as { statusCode?: number };
    return new NextResponse(undefined, {
      status: statusCode,
      headers: {
        'Cache-Control': 'no-store',
      },
    });
  }
}
