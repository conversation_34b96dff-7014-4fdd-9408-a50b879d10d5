'use server';

import { dbAdmin, eq } from '@lilypad/db/client';
import { NotificationCategoryTypeEnum } from '@lilypad/db/schema';
import { logger } from '@lilypad/shared/logger';

import { authActionClient } from '@/shared/safe-action';
import { NotificationsService } from '@lilypad/core/services/notifications';
import { authUsersTable } from '@lilypad/db/auth-schema';
import { z } from 'zod';

export const createNotificationAction = authActionClient
  .schema(
    z.object({
      email: z.string().email(),
      type: z.string(),
      content: z.string(),
      category: z.nativeEnum(NotificationCategoryTypeEnum),
      metadata: z.record(z.any()).optional(),
    })
  )
  .metadata({ actionName: 'createNotification' })
  .action(async ({ parsedInput }) => {
    const { email, type, content, category, metadata } = parsedInput;

    // Get user by email
    const [user] = await dbAdmin
      .select({
        id: authUsersTable.id,
      })
      .from(authUsersTable)
      .where(eq(authUsersTable.email, email));

    if (!user) {
      throw new Error('User not found');
    }

    const userId = user.id;
    const notifications = new NotificationsService(dbAdmin);

    const notification = await notifications.send({
      userId,
      type,
      content,
      category,
      metadata,
    });

    logger.info(
      { userId, category, type },
      'Successfully created notification'
    );

    return {
      success: true,
      notification,
    };
  });
