import { RoleEnum } from '@lilypad/db/schema';
import { logger } from '@lilypad/shared/logger';
import { notFound } from 'next/navigation';
import { getAuthContext } from '@/shared/context';

interface DashboardLayoutProps {
  superUser: React.ReactNode;
  psychologist: React.ReactNode;
  clinicalDirector: React.ReactNode;
  schoolCoordinator: React.ReactNode;
  schoolAdmin: React.ReactNode;
  caseManager: React.ReactNode;
  proctor: React.ReactNode;
  assistant: React.ReactNode;
  specialEdDirector: React.ReactNode;
}

export default async function DashboardLayout({
  superUser,
  psychologist,
  clinicalDirector,
  schoolCoordinator,
  schoolAdmin,
  caseManager,
  proctor,
  assistant,
  specialEdDirector,
}: DashboardLayoutProps) {
  const userRole = await getUserRole();

  const roleSlotMap = {
    [RoleEnum.SUPER_USER]: superUser,
    [RoleEnum.SPECIAL_ED_DIRECTOR]: specialEdDirector,
    [RoleEnum.CLINICAL_DIRECTOR]: clinicalDirector,
    [RoleEnum.SCHOOL_COORDINATOR]: schoolCoordinator,
    [RoleEnum.SCHOOL_ADMIN]: schoolAdmin,
    [RoleEnum.CASE_MANAGER]: caseManager,
    [RoleEnum.PSYCHOLOGIST]: psychologist,
    [RoleEnum.PROCTOR]: proctor,
    [RoleEnum.ASSISTANT]: assistant,
  };

  const selectedSlot = roleSlotMap[userRole];
  if (!selectedSlot) {
    logger.warn(
      { userRole },
      'Unauthorized dashboard access attempt - no matching role found'
    );
    return notFound();
  }

  return selectedSlot;
}

async function getUserRole() {
  const { user } = await getAuthContext();

  if (!user?.rolePermissions) {
    logger.warn(
      { userId: user?.id },
      'Dashboard access attempt without role permissions structure'
    );
    notFound();
  }

  if (!user.rolePermissions.roles?.length) {
    logger.warn(
      { userId: user.id, email: user.email },
      'Dashboard access attempt by user with no assigned roles'
    );
    notFound();
  }

  const userRole = user.rolePermissions.roles.map(role => role.name).at(0) as RoleEnum;
  return userRole;
}