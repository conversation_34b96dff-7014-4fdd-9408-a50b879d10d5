import { HydrateClient, prefetch, trpc } from '@lilypad/api/server'
import type { SearchParams } from '@lilypad/shared';
import { getValidFilters } from '@lilypad/ui/data-table/lib/utils';
import { Suspense } from 'react'
import { tasksSearchParamsCache } from '@/entities/tasks/model/schema';
import { DashboardSkeleton } from '@/shared/ui/dashboard-skeleton'
import { TaskManagementContainer } from '@/widgets/task-management/ui/task-management-container'

function SpecialEdDirectorDashboardContent() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="font-bold text-2xl tracking-tight">Special Education Director Dashboard</h1>
          <p className="text-muted-foreground">
            Oversee special education programs, manage compliance, and coordinate services
          </p>
        </div>
      </div>

      {/* Task Management Section */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div className="rounded-lg border p-4">
          <h3 className="mb-2 font-semibold text-muted-foreground text-sm">Program Oversight</h3>
          <p className="font-bold text-2xl">--</p>
        </div>
        <div className="rounded-lg border p-4">
          <h3 className="mb-2 font-semibold text-muted-foreground text-sm">Compliance Items</h3>
          <p className="font-bold text-2xl">--</p>
        </div>
        <div className="rounded-lg border p-4">
          <h3 className="mb-2 font-semibold text-muted-foreground text-sm">Staff Supervision</h3>
          <p className="font-bold text-2xl">--</p>
        </div>
        <div className="rounded-lg border p-4">
          <h3 className="mb-2 font-semibold text-muted-foreground text-sm">Completed This Week</h3>
          <p className="font-bold text-2xl">--</p>
        </div>
      </div>

      {/* Task Management Widget */}
      <div className="rounded-lg border p-6">
        <TaskManagementContainer />
      </div>
    </div>
  )
}

interface SpecialEdDirectorDashboardPageProps {
  searchParams: Promise<SearchParams>;
}

export default async function SpecialEdDirectorDashboardPage(props: SpecialEdDirectorDashboardPageProps) {
  const searchParams = await props.searchParams;
  const search = tasksSearchParamsCache.parse(searchParams);
  const validFilters = getValidFilters(search.filters);

  prefetch(trpc.tasks.getTasks.queryOptions({
    search: search.search,
    filters: validFilters,
    joinOperator: search.joinOperator,
    sort: search.sort,
  }));

  return (
    <HydrateClient>
      <Suspense fallback={<DashboardSkeleton />}>
        <SpecialEdDirectorDashboardContent />
      </Suspense>
    </HydrateClient>
  )
} 