import { HydrateClient, prefetch, trpc } from '@lilypad/api/server'
import { Suspense } from 'react'
import { DashboardSkeleton } from '@/shared/ui/dashboard-skeleton'
import { TaskManagementContainer } from '@/widgets/task-management'

function SchoolCoordinatorDashboardContent() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="font-bold text-2xl tracking-tight">School Coordinator Dashboard</h1>
          <p className="text-muted-foreground">
            Coordinate student services, manage referrals, and track progress
          </p>
        </div>
      </div>

      {/* Task Management Section */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div className="rounded-lg border p-4">
          <h3 className="mb-2 font-semibold text-muted-foreground text-sm">Active Referrals</h3>
          <p className="font-bold text-2xl">--</p>
        </div>
        <div className="rounded-lg border p-4">
          <h3 className="mb-2 font-semibold text-muted-foreground text-sm">Pending Reviews</h3>
          <p className="font-bold text-2xl">--</p>
        </div>
        <div className="rounded-lg border p-4">
          <h3 className="mb-2 font-semibold text-muted-foreground text-sm">Scheduled Meetings</h3>
          <p className="font-bold text-2xl">--</p>
        </div>
        <div className="rounded-lg border p-4">
          <h3 className="mb-2 font-semibold text-muted-foreground text-sm">Completed This Week</h3>
          <p className="font-bold text-2xl">--</p>
        </div>
      </div>

      {/* Task Management Widget */}
      <div className="rounded-lg border p-6">
        <TaskManagementContainer />
      </div>
    </div>
  )
}

export default function SchoolCoordinatorDashboardPage() {
  // Prefetch school coordinator-specific task data
  prefetch(trpc.tasks.getTasks.queryOptions({
    roleName: 'SCHOOL_COORDINATOR',
    page: 1,
    perPage: 10,
    urgent: true
  }));

  // prefetch(trpc.tasks.getTasks.queryOptions({
  //   taskType: 'COORDINATION',
  //   page: 1,
  //   perPage: 15,
  //   includeCompleted: false
  // }));

  return (
    <HydrateClient>
      <Suspense fallback={<DashboardSkeleton />}>
        <SchoolCoordinatorDashboardContent />
      </Suspense>
    </HydrateClient>
  )
} 