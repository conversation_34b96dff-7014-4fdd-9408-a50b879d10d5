import { HydrateClient, prefetch, trpc } from '@lilypad/api/server'
import { Suspense } from 'react'
import { DashboardSkeleton } from '@/shared/ui/dashboard-skeleton'
import { TaskManagementContainer } from '@/widgets/task-management'

function PsychologistDashboardContent() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="font-bold text-2xl tracking-tight">Psychologist Dashboard</h1>
          <p className="text-muted-foreground">
            Manage assessments, evaluations, and psychological services
          </p>
        </div>
      </div>

      {/* Task Management Section */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div className="rounded-lg border p-4">
          <h3 className="mb-2 font-semibold text-muted-foreground text-sm">Active Evaluations</h3>
          <p className="font-bold text-2xl">--</p>
        </div>
        <div className="rounded-lg border p-4">
          <h3 className="mb-2 font-semibold text-muted-foreground text-sm">Pending Reports</h3>
          <p className="font-bold text-2xl">--</p>
        </div>
        <div className="rounded-lg border p-4">
          <h3 className="mb-2 font-semibold text-muted-foreground text-sm">Assessments Due</h3>
          <p className="font-bold text-2xl">--</p>
        </div>
        <div className="rounded-lg border p-4">
          <h3 className="mb-2 font-semibold text-muted-foreground text-sm">Completed This Week</h3>
          <p className="font-bold text-2xl">--</p>
        </div>
      </div>

      {/* Task Management Widget */}
      <div className="rounded-lg border p-6">
        <TaskManagementContainer />
      </div>
    </div>
  )
}

export default function PsychologistDashboardPage() {
  // Prefetch psychologist-specific task data
  prefetch(trpc.tasks.getTasks.queryOptions({
    roleName: 'PSYCHOLOGIST',
    page: 1,
    perPage: 10,
    urgent: true
  }));

  // prefetch(trpc.tasks.getTasks.queryOptions({
  //   taskType: 'PSYCHOLOGICAL_EVALUATION',
  //   page: 1,
  //   perPage: 15,
  //   includeCompleted: false
  // }));

  return (
    <HydrateClient>
      <Suspense fallback={<DashboardSkeleton />}>
        <PsychologistDashboardContent />
      </Suspense>
    </HydrateClient>
  )
} 