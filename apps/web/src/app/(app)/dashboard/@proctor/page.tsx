import { HydrateClient, prefetch, trpc } from '@lilypad/api/server'
import { Suspense } from 'react'
import { DashboardSkeleton } from '@/shared/ui/dashboard-skeleton'
import { TaskManagementContainer } from '@/widgets/task-management'

function ProctorDashboardContent() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="font-bold text-2xl tracking-tight">Proctor Dashboard</h1>
          <p className="text-muted-foreground">
            Monitor assessments, coordinate testing, and manage proctoring tasks
          </p>
        </div>
      </div>

      {/* Task Management Section */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div className="rounded-lg border p-4">
          <h3 className="mb-2 font-semibold text-muted-foreground text-sm">Active Sessions</h3>
          <p className="font-bold text-2xl">--</p>
        </div>
        <div className="rounded-lg border p-4">
          <h3 className="mb-2 font-semibold text-muted-foreground text-sm">Scheduled Tests</h3>
          <p className="font-bold text-2xl">--</p>
        </div>
        <div className="rounded-lg border p-4">
          <h3 className="mb-2 font-semibold text-muted-foreground text-sm">Completed Today</h3>
          <p className="font-bold text-2xl">--</p>
        </div>
        <div className="rounded-lg border p-4">
          <h3 className="mb-2 font-semibold text-muted-foreground text-sm">Pending Reports</h3>
          <p className="font-bold text-2xl">--</p>
        </div>
      </div>

      {/* Task Management Widget */}
      <div className="rounded-lg border p-6">
        <TaskManagementContainer />
      </div>
    </div>
  )
}

export default function ProctorDashboardPage() {
  // Prefetch proctor-specific task data
  prefetch(trpc.tasks.getTasks.queryOptions({
    roleName: 'PROCTOR',
    page: 1,
    perPage: 10,
    urgent: true
  }));

  // prefetch(trpc.tasks.getTasks.queryOptions({
  //   taskType: 'PROCTORING',
  //   page: 1,
  //   perPage: 15,
  //   includeCompleted: false
  // }));

  return (
    <HydrateClient>
      <Suspense fallback={<DashboardSkeleton />}>
        <ProctorDashboardContent />
      </Suspense>
    </HydrateClient>
  )
} 