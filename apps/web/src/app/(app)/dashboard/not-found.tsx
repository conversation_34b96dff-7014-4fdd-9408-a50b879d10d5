'use client';


import { routes } from '@lilypad/shared/routes';
import { Button } from '@lilypad/ui/components/button';
import { ShieldAlert } from 'lucide-react';
import { useRouter } from 'next/navigation';
import type * as React from 'react';

export default function DashboardNotFound(): React.JSX.Element {
  const router = useRouter();

  const handleGoBack = (): void => {
    router.back();
  };

  const handleBackToHome = (): void => {
    router.push(routes.app.students.Index);
  };

  return (
    <div className="flex h-screen flex-col items-center justify-center px-4 text-center">
      <div className="mb-6 flex items-center justify-center">
        <ShieldAlert className="size-24 text-muted-foreground" />
      </div>

      <span className="mb-4 font-semibold text-6xl text-muted-foreground leading-none">
        403
      </span>

      <h2 className="my-2 font-bold font-heading text-2xl">
        Access Denied
      </h2>

      <p className="mb-2 max-w-md text-muted-foreground">
        You don&apos;t have the required permissions to access this dashboard.
      </p>

      <p className="mb-8 max-w-md text-muted-foreground text-sm">
        Please contact your administrator if you believe this is an error.
      </p>

      <div className="flex justify-center gap-2">
        <Button type="button" variant="default" onClick={handleGoBack}>
          Go Back
        </Button>
        <Button type="button" variant="ghost" onClick={handleBackToHome}>
          Students
        </Button>
      </div>
    </div>
  );
} 