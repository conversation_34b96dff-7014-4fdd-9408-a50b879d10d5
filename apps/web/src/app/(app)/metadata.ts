import type { Metadata } from 'next';
import { createMetadataTitle } from '@lilypad/shared';

export const CASES_PAGE_METADATA: Metadata = {
  title: createMetadataTitle('Cases'),
};

export const DASHBOARD_PAGE_METADATA: Metadata = {
  title: createMetadataTitle('Dashboard'),
};

export const STUDENTS_PAGE_METADATA: Metadata = {
  title: createMetadataTitle('Students'),
};

export const CALENDAR_PAGE_METADATA: Metadata = {
  title: createMetadataTitle('Calendar'),
};

export const TASKS_PAGE_METADATA: Metadata = {
  title: createMetadataTitle('Tasks'),
};

export const DISTRICTS_PAGE_METADATA: Metadata = {
  title: createMetadataTitle('Districts'),
};
