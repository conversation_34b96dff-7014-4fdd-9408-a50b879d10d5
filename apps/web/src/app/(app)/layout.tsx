import { SidebarInset, SidebarProvider } from '@lilypad/ui/components/sidebar';

import { getUserNotifications } from '@/entities/notifications/api/get-user-notifications';
import { getUser } from '@/entities/users/api/get-user';
import { AuthChangeListener } from '@/features/auth/lib/auth-change-listener';
import { NotificationsProvider } from '@/shared/contexts/notifications-context';
import { TransitionProvider } from '@/shared/contexts/use-transition-context';
import { UserProvider } from '@/shared/contexts/user-context';
import { AppSidebar } from '@/widgets/app-sidebar';

export default async function AppLayout({ children }: React.PropsWithChildren) {
  const [user, notifications] = await Promise.all([
    getUser(),
    getUserNotifications(),
  ]);

  return (
    <UserProvider initialUser={user}>
      <NotificationsProvider
        notifications={notifications}
        userId={user.id}
      >
        <AuthChangeListener>
          <SidebarProvider>
            <AppSidebar user={user} />
            <SidebarInset>
              <TransitionProvider>{children}</TransitionProvider>
            </SidebarInset>
          </SidebarProvider>
        </AuthChangeListener>
      </NotificationsProvider>
    </UserProvider>
  );
}
