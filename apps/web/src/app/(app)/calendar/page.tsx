import {
  <PERSON>,
  <PERSON>B<PERSON>,
  <PERSON>Header,
  PageTitle,
} from '@lilypad/ui/components/page';
import { CALENDAR_PAGE_METADATA } from '../metadata';
import { AppHeader } from '@/widgets/app-header/ui/app-header';

export const metadata = CALENDAR_PAGE_METADATA;

export default function CalendarPage() {
  return (
    <Page>
      <PageHeader>
        <AppHeader>
          <PageTitle>Calendar</PageTitle>
        </AppHeader>
      </PageHeader>
      <PageBody>
        <div className="mx-auto max-w-6xl space-y-2 p-2 sm:space-y-8 sm:p-6">
          <div className="grid grid-cols-1 gap-2 sm:gap-8 md:grid-cols-2">
            <div className="aspect-video rounded-xl bg-muted/50" />
            <div className="aspect-video rounded-xl bg-muted/50" />
          </div>
        </div>
      </PageBody>
    </Page>
  );
}
