'use client';

import { But<PERSON> } from '@lilypad/ui/components/button';
import { CollapseButton } from '@lilypad/ui/components/collapse-button';
import { ArrowLeftIcon, ArrowRightIcon } from 'lucide-react';

interface CasesLayoutHeaderProps {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
}

export const CasesLayoutHeader = ({
  isOpen,
  setIsOpen,
}: CasesLayoutHeaderProps) => {
  return (
    <div className="flex items-center gap-2 px-4 py-2">
      <CollapseButton onClick={() => setIsOpen(!isOpen)} open={!isOpen} />
      <Button size="sm" variant="ghost">
        <ArrowLeftIcon className="size-3.5 stroke-1" />
      </Button>
      <Button size="sm" variant="ghost">
        <ArrowRightIcon className="size-3.5 stroke-1" />
      </Button>
    </div>
  );
};
