import { getCasesByUserAccess } from '@/entities/cases/api/get-cases';
import { getAuthContext } from '@/shared/context';
import { AppHeader } from '@/widgets/app-header/ui/app-header';

import {
  Page,
  PageBody,
  PageHeader,
  PageTitle,
} from '@lilypad/ui/components/page';
import { CasesContainer } from './cases-container';

export default async function CasesLayout({
  children,
}: React.PropsWithChildren) {
  const user = await getAuthContext();

  const userRoles = user.user?.rolePermissions?.roles?.map((role) => role.name);

  const cases = await getCasesByUserAccess(user.user.id, userRoles);

  return (
    <Page>
      <PageHeader>
        <AppHeader>
          <PageTitle>Cases</PageTitle>
        </AppHeader>
      </PageHeader>
      <PageBody className="flex flex-row p-0" disableScroll>
        <CasesContainer cases={cases || []}>{children}</CasesContainer>
      </PageBody>
    </Page>
  );
}
