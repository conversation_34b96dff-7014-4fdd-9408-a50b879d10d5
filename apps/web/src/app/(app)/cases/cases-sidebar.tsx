'use client';

import type { CaseWithAssignmentsAndDetails } from '@lilypad/db/repository/types/cases';
import { Button } from '@lilypad/ui/components/button';
import { InputSearch } from '@lilypad/ui/components/input-search';
import { Label } from '@lilypad/ui/components/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@lilypad/ui/components/popover';
import { cn } from '@lilypad/ui/lib/utils';
import { PlusIcon, SearchIcon, SlidersHorizontalIcon } from 'lucide-react';
import { AnimatePresence, motion } from 'motion/react';
import { useState } from 'react';
import { CasePreviewCard } from './case-card-mini';

interface CasesSidebarProps {
  isOpen: boolean;
  cases: CaseWithAssignmentsAndDetails[] | null;
}

export default function CasesSidebar({ isOpen, cases }: CasesSidebarProps) {
  const [isSearchMode, setIsSearchMode] = useState(false);

  return (
    <aside
      className={cn(
        'h-full overflow-hidden bg-background transition-all duration-300',
        isOpen ? 'w-0 overflow-hidden' : 'w-80 border-r'
      )}
    >
      <div className="flex h-full flex-col">
        <div className="flex-shrink-0 items-center justify-between overflow-hidden border-b px-4 py-2">
          <AnimatePresence mode="wait">
            {isSearchMode ? (
              <motion.div
                animate={{ y: 0 }}
                className="flex w-full items-center gap-2"
                exit={{ y: 50 }}
                initial={{ y: 50 }}
                key="search"
                transition={{ duration: 0.2, ease: 'easeInOut' }}
              >
                <Popover>
                  <PopoverTrigger asChild>
                    <Button className="shrink-0" size="sm" variant="ghost">
                      <SlidersHorizontalIcon className="size-4" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent align="start" className="w-56">
                    <div className="space-y-4">
                      <h4 className="font-medium text-sm">Filters</h4>
                      <div className="space-y-2">
                        <div className="space-y-1">
                          <Label className="text-muted-foreground text-xs">
                            Status
                          </Label>
                          <div className="space-y-1">
                            <Label className="flex items-center gap-2 text-sm">
                              <input className="size-4" type="checkbox" />
                              Open
                            </Label>
                            <Label className="flex items-center gap-2 text-sm">
                              <input className="size-4" type="checkbox" />
                              Closed
                            </Label>
                          </div>
                        </div>
                        <div className="space-y-1">
                          <Label className="text-muted-foreground text-xs">
                            Priority
                          </Label>
                          <div className="space-y-1">
                            <Label className="flex items-center gap-2 text-sm">
                              <input className="size-4" type="checkbox" />
                              High
                            </Label>
                            <Label className="flex items-center gap-2 text-sm">
                              <input className="size-4" type="checkbox" />
                              Medium
                            </Label>
                            <Label className="flex items-center gap-2 text-sm">
                              <input className="size-4" type="checkbox" />
                              Low
                            </Label>
                          </div>
                        </div>
                      </div>
                    </div>
                  </PopoverContent>
                </Popover>
                <InputSearch
                  alwaysShowClearButton
                  className="flex-1 shadow-none "
                  clearButtonProps={{
                    onClick: () => setIsSearchMode(false),
                  }}
                  onClear={() => setIsSearchMode(false)}
                  placeholder="Search cases..."
                />
              </motion.div>
            ) : (
              <motion.div
                className="flex w-full justify-between gap-2"
                exit={{ y: 50 }}
                initial={{ y: 0 }}
                key="buttons"
                transition={{ duration: 0.2, ease: 'easeInOut' }}
              >
                <Button
                  onClick={() => setIsSearchMode(true)}
                  size="sm"
                  variant="ghost"
                >
                  <SearchIcon className="size-4" />
                </Button>
                <Button size="sm" variant="ghost">
                  <PlusIcon className="size-4" />
                </Button>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        <nav className="space-y-2 overflow-y-auto [&>div]:border-b">
          {cases?.map((caseItem, index) => (
            <CasePreviewCard caseItem={caseItem} key={index} />
          ))}
        </nav>
      </div>
    </aside>
  );
}
