'use client';

import type { CaseWithAssignmentsAndDetails } from '@lilypad/db/repository/types/cases';
import { useState } from 'react';
import { CasesLayoutHeader } from './cases-layout-header';
import CasesSidebar from './cases-sidebar';

interface CasesContainerProps extends React.PropsWithChildren {
  cases: CaseWithAssignmentsAndDetails[] | null;
}

export const CasesContainer = ({ children, cases }: CasesContainerProps) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <CasesSidebar cases={cases} isOpen={isOpen} />

      <main className="flex-1 overflow-auto">
        <CasesLayoutHeader isOpen={isOpen} setIsOpen={setIsOpen} />
        {children}
      </main>
    </>
  );
};
