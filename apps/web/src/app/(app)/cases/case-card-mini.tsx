import type { CaseWithAssignmentsAndDetails } from '@lilypad/db/repository/types/cases';
import { Badge } from '@lilypad/ui/components/badge';
import { Card, CardContent } from '@lilypad/ui/components/card';
import Link from 'next/link';

interface CasePreviewCardProps {
  caseItem: CaseWithAssignmentsAndDetails;
}

export function CasePreviewCard({ caseItem }: CasePreviewCardProps) {
  return (
    <Card className="rounded-none border-0 shadow-none">
      <CardContent>
        <Link className="group flex h-full" href={`/cases/${caseItem.id}`}>
          {/* Content */}
          <div className="flex flex-1 flex-col justify-center gap-0.5 px-2">
            {/* Top row: ID + badges */}
            <div className="flex items-center justify-between">
              <span className="truncate font-medium text-sm">
                #{caseItem.id.slice(0, 8)}
              </span>
              <div className="flex items-center space-x-1">
                <Badge size="sm" variant="outline">
                  {caseItem.status}
                </Badge>
                <Badge size="sm" variant="outline">
                  {caseItem.priority}
                </Badge>
              </div>
            </div>

            {/* Bottom row: type, IEP, next date */}
            <div className="flex items-center space-x-3 truncate text-muted-foreground text-xs">
              <span className="truncate">{caseItem.caseType}</span>
              <span>IEP: {caseItem.iepStatus}</span>
            </div>
          </div>
        </Link>
      </CardContent>
    </Card>
  );
}
