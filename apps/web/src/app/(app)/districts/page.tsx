import { districtsSearchParamsCache } from '@/entities/districts/model/schema';
import { AppHeader } from '@/widgets/app-header/ui/app-header';
import { DistrictsTable } from '@/widgets/districts-table/ui/districts-table';
import { HydrateClient, prefetch, trpc } from '@lilypad/api/server';
import type { SearchParams } from '@lilypad/shared';
import {
  Page,
  PageBody,
  PageHeader,
  PageTitle,
} from '@lilypad/ui/components/page';
import { DataTableSkeleton } from '@lilypad/ui/data-table/data-table-skeleton';
import { getValidFilters } from '@lilypad/ui/data-table/lib/utils';
import { Suspense } from 'react';
import { DISTRICTS_PAGE_METADATA } from '../metadata';

export const metadata = DISTRICTS_PAGE_METADATA;

interface DistrictsPageProps {
  searchParams: Promise<SearchParams>;
}

export default async function DistrictsPage(props: DistrictsPageProps) {
  const searchParams = await props.searchParams;
  const search = districtsSearchParamsCache.parse(searchParams);
  const validFilters = getValidFilters(search.filters);

  prefetch(
    trpc.districts.getDistricts.queryOptions({
      page: search.page,
      perPage: search.perPage,
      search: search.search,
      filters: validFilters,
      joinOperator: search.joinOperator,
      sort: search.sort,
    })
  );

  return (
    <Page>
      <PageHeader>
        <AppHeader>
          <PageTitle>Districts</PageTitle>
        </AppHeader>
      </PageHeader>
      <PageBody disableScroll>
        <HydrateClient>
          <Suspense
            fallback={<DataTableSkeleton columnCount={6} rowCount={10} />}
          >
            <DistrictsTable />
          </Suspense>
        </HydrateClient>
      </PageBody>
    </Page>
  );
}
