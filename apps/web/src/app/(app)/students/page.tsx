import { Suspense } from 'react';

import { HydrateClient, prefetch, trpc } from '@lilypad/api/server';
import type { SearchParams } from '@lilypad/shared';
import {
  Page,
  PageBody,
  PageHeader,
  PageTitle,
} from '@lilypad/ui/components/page';
import { DataTableSkeleton } from '@lilypad/ui/data-table/data-table-skeleton';
import { getValidFilters } from '@lilypad/ui/data-table/lib/utils';

import { studentsSearchParamsCache } from '@/entities/students/model/schema';
import { AppHeader } from '@/widgets/app-header/ui/app-header';
import { StudentsTable } from '@/widgets/students-table/ui/students-table';

import { STUDENTS_PAGE_METADATA } from '../metadata';

export const metadata = STUDENTS_PAGE_METADATA;

interface StudentsPageProps {
  searchParams: Promise<SearchParams>;
}

export default async function StudentsPage(props: StudentsPageProps) {
  const searchParams = await props.searchParams;
  const search = studentsSearchParamsCache.parse(searchParams);
  const validFilters = getValidFilters(search.filters);

  prefetch(
    trpc.students.getStudents.queryOptions({
      page: search.page,
      perPage: search.perPage,
      search: search.search,
      filters: validFilters,
      joinOperator: search.joinOperator,
      sort: search.sort,
    })
  );

  return (
    <Page>
      <PageHeader>
        <AppHeader>
          <PageTitle>Students</PageTitle>
        </AppHeader>
      </PageHeader>
      <PageBody disableScroll>
        <HydrateClient>
          <Suspense
            fallback={<DataTableSkeleton columnCount={9} rowCount={10} />}
          >
            <StudentsTable />
          </Suspense>
        </HydrateClient>
      </PageBody>
    </Page>
  );
}
