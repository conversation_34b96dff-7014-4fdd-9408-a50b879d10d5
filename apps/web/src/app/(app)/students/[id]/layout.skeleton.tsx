'use client';

import { But<PERSON> } from '@lilypad/ui/components/button';
import { Page, PageBody, PageHeader } from '@lilypad/ui/components/page';
import { Skeleton } from '@lilypad/ui/components/skeleton';
import { ChevronDown } from 'lucide-react';

export default function StudentLayoutSkeleton() {
  return (
    <Page className="flex h-screen flex-col">
      <PageHeader className="flex-none">
        <div className="flex h-14 items-center justify-between border-b px-4">
          {/* Breadcrumb skeleton */}
          <div className="flex items-center gap-2">
            <Skeleton className="h-4 w-16" />
            <span className="text-muted-foreground">/</span>
            <Skeleton className="h-4 w-32" />
          </div>

          {/* Actions skeleton */}
          <Button variant="outline" size="sm" disabled>
            <ChevronDown className="mr-2 h-4 w-4" />
            <Skeleton className="h-4 w-12" />
          </Button>
        </div>
      </PageHeader>

      <PageBody
        disableScroll
        className="grid flex-1 grid-cols-1 overflow-hidden md:grid-cols-7"
      >
        {/* Left sidebar - Student profile details */}
        <div className="space-y-4 overflow-y-auto border-r p-4 md:col-span-2">
          {/* Avatar and name */}
          <div className="flex flex-col items-center space-y-2">
            <Skeleton className="h-24 w-24 rounded-full" />
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-4 w-24" />
          </div>

          {/* Contact info sections */}
          <div className="space-y-3">
            <Skeleton className="h-4 w-20" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
            </div>
          </div>

          <div className="space-y-3">
            <Skeleton className="h-4 w-24" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-5/6" />
            </div>
          </div>

          <div className="space-y-3">
            <Skeleton className="h-4 w-16" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-2/3" />
            </div>
          </div>
        </div>

        {/* Right content area */}
        <div className="flex flex-col overflow-hidden md:col-span-5">
          {/* Tabs skeleton */}
          <div className="flex-none items-center border-b">
            <div className="flex space-x-8 px-4">
              <div className="pt-4 pb-2">
                <Skeleton className="h-5 w-16" />
              </div>
              <div className="pt-4 pb-2">
                <Skeleton className="h-5 w-20" />
              </div>
              <div className="pt-4 pb-2">
                <Skeleton className="h-5 w-24" />
              </div>
              <div className="pt-4 pb-2">
                <Skeleton className="h-5 w-16" />
              </div>
            </div>
          </div>

          {/* Content area skeleton */}
          <div className="flex-1 overflow-y-auto p-4">
            <div className="space-y-4">
              <Skeleton className="h-8 w-48" />
              <div className="space-y-3">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-5/6" />
                <Skeleton className="h-4 w-4/6" />
              </div>
              <div className="mt-6 grid gap-4">
                <Skeleton className="h-32 w-full rounded-lg" />
                <Skeleton className="h-32 w-full rounded-lg" />
              </div>
            </div>
          </div>
        </div>
      </PageBody>
    </Page>
  );
}
