import { Suspense } from 'react';

import { HydrateClient, prefetch, trpc } from '@lilypad/api/server';
import StudentLayoutContainer from './layout.container';
import StudentLayoutSkeleton from './layout.skeleton';

interface StudentLayoutProps {
  children: React.ReactNode;
  params: Promise<{ id: string }>;
}

export default async function StudentLayout({
  children,
  params,
}: StudentLayoutProps) {
  const { id } = await params;

  prefetch(trpc.students.getStudentProfileById.queryOptions(id));

  return (
    <HydrateClient>
      <Suspense fallback={<StudentLayoutSkeleton />}>
        <StudentLayoutContainer studentId={id}>
          {children}
        </StudentLayoutContainer>
      </Suspense>
    </HydrateClient>
  );
}
