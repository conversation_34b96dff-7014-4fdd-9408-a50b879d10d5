import { CaseList } from '@/entities/cases/ui/case-list/case-list';
import { CaseListSkeleton } from '@/entities/cases/ui/case-list/case-list.skeleton';
import { prefetch, trpc } from '@lilypad/api/server';
import { Suspense } from 'react';

interface CasesPageProps {
  params: {
    id: string;
  };
}

export default function CasesPage({ params }: CasesPageProps) {
  const studentId = params.id;

  prefetch(trpc.cases.getCasesByStudentId.queryOptions(studentId));

  return (
    <div className="p-4">
      <Suspense fallback={<CaseListSkeleton />}>
        <CaseList studentId={studentId} />
      </Suspense>
    </div>
  );
}
