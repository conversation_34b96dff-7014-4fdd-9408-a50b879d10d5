'use client';

import type { DocumentWithUser } from '@lilypad/db/repository/types/documents';
import { Badge } from '@lilypad/ui/components/badge';
import { Button } from '@lilypad/ui/components/button';
import { Separator } from '@lilypad/ui/components/separator';
import {
  AlertCircleIcon,
  DownloadIcon,
  ExternalLinkIcon,
  FileIcon,
  FileTextIcon,
  ImageIcon,
  VideoIcon,
} from 'lucide-react';
import { useState } from 'react';

interface DocumentPreviewProps {
  document: DocumentWithUser;
}

function getDocumentIcon(url: string) {
  const extension = url.split('.').pop()?.toLowerCase();

  switch (extension) {
    case 'pdf':
      return <FileTextIcon className="size-5 text-red-500" />;
    case 'doc':
    case 'docx':
      return <FileTextIcon className="size-5 text-blue-500" />;
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
      return <ImageIcon className="size-5 text-green-500" />;
    case 'mp4':
    case 'mov':
    case 'avi':
      return <VideoIcon className="size-5 text-purple-500" />;
    default:
      return <FileIcon className="size-5 text-muted-foreground" />;
  }
}

function getFileExtension(url: string) {
  return url.split('.').pop()?.toLowerCase() || '';
}

function formatDate(date: Date) {
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
}

// function formatFileSize(bytes: number) {
//   const sizes = ['Bytes', 'KB', 'MB', 'GB'];
//   if (bytes === 0) {
//     return '0 Bytes';
//   }
//   const i = Math.floor(Math.log(bytes) / Math.log(1024));
//   return `${Math.round((bytes / 1024 ** i) * 100) / 100} ${sizes[i]}`;
// }

function DocumentViewer({ document }: { document: DocumentWithUser }) {
  const extension = getFileExtension(document.url);
  const [imageError, setImageError] = useState(false);
  const [loadError, setLoadError] = useState(false);

  switch (extension) {
    case 'pdf':
      return (
        <div className="h-full w-full">
          {loadError ? (
            <div className="flex h-full flex-col items-center justify-center p-8 text-center">
              <AlertCircleIcon className="mb-4 size-12 text-muted-foreground" />
              <h3 className="mb-2 font-medium text-lg">Cannot preview PDF</h3>
              <p className="mb-4 text-muted-foreground text-sm">
                This PDF cannot be displayed in the browser. You can download it
                to view.
              </p>
              <Button asChild>
                <a download={document.name} href={document.url}>
                  <DownloadIcon className="mr-2 size-4" />
                  Download PDF
                </a>
              </Button>
            </div>
          ) : (
            // biome-ignore lint/nursery/noNoninteractiveElementInteractions: Fix later
            <iframe
              className="h-full w-full rounded-lg border-0"
              onError={() => setLoadError(true)}
              src={`${document.url}#toolbar=0`}
              title={document.name}
            />
          )}
        </div>
      );

    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
      return (
        <div className="flex h-full w-full items-center justify-center p-4">
          {imageError ? (
            <div className="flex flex-col items-center justify-center text-center">
              <AlertCircleIcon className="mb-4 size-12 text-muted-foreground" />
              <h3 className="mb-2 font-medium text-lg">Cannot load image</h3>
              <p className="text-muted-foreground text-sm">
                This image cannot be displayed.
              </p>
            </div>
          ) : (
            // biome-ignore lint/nursery/noNoninteractiveElementInteractions: Fix later
            <img
              alt={document.name}
              className="max-h-full max-w-full rounded-lg object-contain shadow-lg"
              onError={() => setImageError(true)}
              src={document.url}
            />
          )}
        </div>
      );

    case 'mp4':
    case 'mov':
    case 'avi':
      return (
        <div className="flex h-full w-full items-center justify-center p-4">
          <video
            className="max-h-full max-w-full rounded-lg shadow-lg"
            controls
            preload="metadata"
          >
            <source src={document.url} type={`video/${extension}`} />
            Your browser does not support the video tag.
            <track kind="captions" />
          </video>
        </div>
      );

    default:
      return (
        <div className="flex h-full flex-col items-center justify-center p-8 text-center">
          {getDocumentIcon(document.url)}
          <h3 className="mt-4 mb-2 font-medium text-lg">
            Preview not available
          </h3>
          <p className="mb-4 text-muted-foreground text-sm">
            This file type cannot be previewed in the browser.
          </p>
          <div className="flex gap-2">
            <Button asChild>
              <a href={document.url} download={document.name}>
                <DownloadIcon className="mr-2 size-4" />
                Download
              </a>
            </Button>
            <Button variant="outline" asChild>
              <a href={document.url} target="_blank" rel="noopener noreferrer">
                <ExternalLinkIcon className="mr-2 size-4" />
                Open in new tab
              </a>
            </Button>
          </div>
        </div>
      );
  }
}

export function DocumentPreview({ document }: DocumentPreviewProps) {
  return (
    <div className="flex h-full flex-col bg-background">
      {/* Header */}
      <div className="flex-shrink-0 border-b bg-muted/30 p-4">
        <div className="flex items-start justify-between gap-4">
          <div className="flex items-start gap-3">
            {getDocumentIcon(document.url)}
            <div className="space-y-1">
              <h2 className="font-semibold text-lg">{document.name}</h2>
              <div className="flex items-center gap-2 text-muted-foreground text-sm">
                <Badge variant="secondary" className="capitalize">
                  {document.category.toLowerCase()}
                </Badge>
                <span>{getFileExtension(document.url).toUpperCase()}</span>
              </div>
            </div>
          </div>

          <div className="flex gap-2">
            <Button variant="outline" size="sm" asChild>
              <a href={document.url} download={document.name}>
                <DownloadIcon className="size-4" />
              </a>
            </Button>
            <Button variant="outline" size="sm" asChild>
              <a href={document.url} target="_blank" rel="noopener noreferrer">
                <ExternalLinkIcon className="size-4" />
              </a>
            </Button>
          </div>
        </div>

        <Separator className="my-4" />

        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <p className="text-muted-foreground">Uploaded by</p>
            <p className="font-medium">{document.uploadedUser.fullName}</p>
          </div>
          <div>
            <p className="text-muted-foreground">Upload date</p>
            <p className="font-medium">{formatDate(document.createdAt)}</p>
          </div>
        </div>
      </div>

      {/* Preview Content */}
      <div className="flex-1 overflow-hidden">
        <DocumentViewer document={document} />
      </div>
    </div>
  );
}
