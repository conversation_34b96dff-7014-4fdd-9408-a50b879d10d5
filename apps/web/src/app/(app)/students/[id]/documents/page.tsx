import type { DocumentWithUser } from '@lilypad/db/repository/types/documents';
import { DocumentCategoryEnum } from '@lilypad/db/schema/enums';
import { FileTextIcon } from 'lucide-react';
import { DocumentPreview } from './document-preview';

interface DocumentsPageProps {
  params: Promise<{ id: string }>;
  searchParams: Promise<{ doc?: string }>;
}

export default async function DocumentsPage({
  params,
  searchParams,
}: DocumentsPageProps) {
  const { id } = await params;
  const { doc } = await searchParams;

  if (!doc) {
    return (
      <div className="flex h-[calc(100vh-10rem)] flex-col items-center justify-center p-8 text-center text-muted-foreground">
        <FileTextIcon className="mb-4 size-12" />
        <h2 className="mb-2 font-semibold text-base">
          Select a document to preview
        </h2>
        <p className="text-sm">
          Choose a document from the sidebar to view its contents.
        </p>
      </div>
    );
  }

  const documents = await getStudentDocuments(id);

  if (!documents) {
    return (
      <div className="flex h-full flex-col items-center justify-center p-8 text-center">
        <FileTextIcon className="mb-4 size-16 text-muted-foreground" />
        <h2 className="mb-2 font-semibold text-xl">Documents not found</h2>
      </div>
    );
  }

  const selectedDocument = documents.find((d) => d.id === doc);

  if (!selectedDocument) {
    return (
      <div className="flex h-full flex-col items-center justify-center p-8 text-center">
        <FileTextIcon className="mb-4 size-16 text-muted-foreground" />
        <h2 className="mb-2 font-semibold text-xl">Document not found</h2>
        <p className="text-muted-foreground">
          The selected document could not be found.
        </p>
      </div>
    );
  }

  return <DocumentPreview document={selectedDocument} />;
}

async function getStudentDocuments(
  studentId: string
): Promise<DocumentWithUser[]> {
  return [
    {
      id: '550e8400-e29b-41d4-a716-446655440001',
      studentId,
      assessmentSessionId: '550e8400-e29b-41d4-a716-4466554400e1',
      testAdministrationId: null,
      category: DocumentCategoryEnum.ASSESSMENT,
      name: 'WISC-V Cognitive Assessment Report',
      url: '/sample-reort.pdf',
      uploadedUserId: 'user-1',
      createdAt: new Date('2024-01-15T10:30:00Z'),
      updatedAt: new Date('2024-01-15T10:30:00Z'),
      uploadedUser: {
        id: 'user-1',
        firstName: 'Dr. Sarah',
        lastName: 'Johnson',
        fullName: 'Dr. Sarah Johnson',
        email: '<EMAIL>',
        avatar:
          'https://images.unsplash.com/photo-1494790108755-2616b612b1f3?w=150&h=150&fit=crop&crop=face',
        userRoles: [
          {
            role: {
              name: 'PSYCHOLOGIST',
            },
          },
        ],
      },
    },
    {
      id: '550e8400-e29b-41d4-a716-556655440001',
      studentId,
      assessmentSessionId: null,
      testAdministrationId: 'admin-1',
      category: DocumentCategoryEnum.BACKGROUND,
      name: 'Individualized Education Program',
      url: '/sample-reort.pdf',
      uploadedUserId: 'user-2',
      createdAt: new Date('2024-01-20T14:15:00Z'),
      updatedAt: new Date('2024-01-20T14:15:00Z'),
      uploadedUser: {
        id: 'user-2',
        firstName: 'Maria',
        lastName: 'Rodriguez',
        fullName: 'Maria Rodriguez',
        email: '<EMAIL>',
        avatar:
          'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
        userRoles: [
          {
            role: {
              name: 'SPECIAL_EDUCATION_TEACHER',
            },
          },
        ],
      },
    },
    {
      id: '550e8400-e29b-41d4-a716-336655440001',
      studentId,
      assessmentSessionId: '550e8400-e29b-41d4-a716-4466554400e2',
      testAdministrationId: null,
      category: DocumentCategoryEnum.BACKGROUND,
      name: 'Behavior Support Plan',
      url: '/sample-reort.pdf',
      uploadedUserId: 'user-3',
      createdAt: new Date('2024-02-01T09:00:00Z'),
      updatedAt: new Date('2024-02-01T09:00:00Z'),
      uploadedUser: {
        id: 'user-3',
        firstName: 'James',
        lastName: 'Chen',
        fullName: 'James Chen',
        email: '<EMAIL>',
        avatar:
          'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
        userRoles: [
          {
            role: {
              name: 'BEHAVIOR_SPECIALIST',
            },
          },
        ],
      },
    },
    {
      id: '550e8400-e29b-41d4-a716-446655440004',
      studentId,
      assessmentSessionId: null,
      testAdministrationId: null,
      category: DocumentCategoryEnum.BACKGROUND,
      name: 'Parent Consent Forms',
      url: '/sample-reort.pdf',
      uploadedUserId: 'user-2',
      createdAt: new Date('2024-01-10T09:00:00Z'),
      updatedAt: new Date('2024-01-10T09:00:00Z'),
      uploadedUser: {
        id: 'user-2',
        firstName: 'Maria',
        lastName: 'Rodriguez',
        fullName: 'Maria Rodriguez',
        email: '<EMAIL>',
        avatar:
          'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
        userRoles: [
          {
            role: {
              name: 'SPECIAL_EDUCATION_TEACHER',
            },
          },
        ],
      },
    },
    {
      id: '550e8400-e29b-41d4-a716-446655440005',
      studentId,
      assessmentSessionId: '550e8400-e29b-41d4-a716-4466554400e1',
      testAdministrationId: null,
      category: DocumentCategoryEnum.ASSESSMENT,
      name: 'Achievement Test Results',
      url: '/sample-reort.pdf',
      uploadedUserId: 'user-1',
      createdAt: new Date('2024-01-18T14:20:00Z'),
      updatedAt: new Date('2024-01-18T14:20:00Z'),
      uploadedUser: {
        id: 'user-1',
        firstName: 'Dr. Sarah',
        lastName: 'Johnson',
        fullName: 'Dr. Sarah Johnson',
        email: '<EMAIL>',
        avatar:
          'https://images.unsplash.com/photo-1494790108755-2616b612b1f3?w=150&h=150&fit=crop&crop=face',
        userRoles: [
          {
            role: {
              name: 'PSYCHOLOGIST',
            },
          },
        ],
      },
    },
  ];
}
