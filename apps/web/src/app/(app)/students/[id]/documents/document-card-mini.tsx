'use client';

import type { DocumentWithUser } from '@lilypad/db/repository/types/documents';
import { cn } from '@lilypad/ui/lib/utils';
import { FileIcon, FileTextIcon, ImageIcon, VideoIcon } from 'lucide-react';
import Link from 'next/link';
import { useParams, useSearchParams } from 'next/navigation';

interface DocumentCardMiniProps {
  document: DocumentWithUser;
}

function getDocumentIcon(url: string) {
  const extension = url.split('.').pop()?.toLowerCase();

  switch (extension) {
    case 'pdf':
      return <FileTextIcon className="size-4 text-red-500" />;
    case 'doc':
    case 'docx':
      return <FileTextIcon className="size-4 text-blue-500" />;
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
      return <ImageIcon className="size-4 text-green-500" />;
    case 'mp4':
    case 'mov':
    case 'avi':
      return <VideoIcon className="size-4 text-purple-500" />;
    default:
      return <FileIcon className="size-4 text-muted-foreground" />;
  }
}

function formatDate(date: Date) {
  return date.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
  });
}

export function DocumentCardMini({ document }: DocumentCardMiniProps) {
  const params = useParams();
  const searchParams = useSearchParams();
  const selectedDocId = searchParams.get('doc');
  const isSelected = selectedDocId === document.id;

  return (
    <Link
      href={`/students/${params.id}/documents?doc=${document.id}`}
      className={cn(
        'flex items-center gap-2 rounded-md px-2 py-1.5 transition-colors hover:bg-muted/50',
        isSelected && 'bg-muted'
      )}
    >
      <div className="flex-shrink-0">{getDocumentIcon(document.url)}</div>

      <p className="flex-1 truncate text-sm">{document.name}</p>
    </Link>
  );
}
