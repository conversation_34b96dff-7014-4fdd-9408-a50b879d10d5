'use client';

import type {
  DocumentTreeNode,
  DocumentWithUser,
} from '@lilypad/db/repository/types/documents';
import { useState } from 'react';
import { DocumentsLayoutHeader } from './documents-layout-header';
import DocumentsSidebar from './documents-sidebar';

interface DocumentsContainerProps extends React.PropsWithChildren {
  documents: DocumentWithUser[] | null;
  documentTree?: DocumentTreeNode[] | null;
}

export const DocumentsContainer = ({
  children,
  documents,
  documentTree,
}: DocumentsContainerProps) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <DocumentsSidebar
        documents={documents}
        documentTree={documentTree}
        isOpen={isOpen}
      />

      <main className="relative flex-1 overflow-auto">
        <DocumentsLayoutHeader isOpen={isOpen} setIsOpen={setIsOpen} />
        {children}
      </main>
    </>
  );
};
