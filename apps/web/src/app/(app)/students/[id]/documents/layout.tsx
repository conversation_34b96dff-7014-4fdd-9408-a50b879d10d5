import type {
  DocumentTreeNode,
  DocumentWithUser,
} from '@lilypad/db/repository/types/documents';
import { DocumentCategoryEnum } from '@lilypad/db/schema/enums';
import { DocumentsContainer } from './documents-container';

interface DocumentsLayoutProps extends React.PropsWithChildren {
  params: Promise<{ id: string }>;
}

export default async function DocumentsLayout({
  children,
  params,
}: DocumentsLayoutProps) {
  const { id } = await params;
  const documents = await getStudentDocuments(id);
  const documentTree = await getDocumentTree(id);

  return (
    <div className="flex h-full">
      <DocumentsContainer documents={documents} documentTree={documentTree}>
        {children}
      </DocumentsContainer>
    </div>
  );
}
async function getStudentDocuments(
  studentId: string
): Promise<DocumentWithUser[]> {
  return [
    {
      id: '550e8400-e29b-41d4-a716-446655440001',
      studentId,
      assessmentSessionId: '550e8400-e29b-41d4-a716-4466554400e1',
      testAdministrationId: null,
      category: DocumentCategoryEnum.ASSESSMENT,
      name: 'WISC-V Cognitive Assessment Report',
      url: '/sample-reort.pdf',
      uploadedUserId: 'user-1',
      createdAt: new Date('2024-01-15T10:30:00Z'),
      updatedAt: new Date('2024-01-15T10:30:00Z'),
      uploadedUser: {
        id: 'user-1',
        firstName: 'Dr. Sarah',
        lastName: 'Johnson',
        fullName: 'Dr. Sarah Johnson',
        email: '<EMAIL>',
        avatar:
          'https://images.unsplash.com/photo-1494790108755-2616b612b1f3?w=150&h=150&fit=crop&crop=face',
        userRoles: [
          {
            role: {
              name: 'PSYCHOLOGIST',
            },
          },
        ],
      },
    },
    {
      id: '550e8400-e29b-41d4-a716-556655440001',
      studentId,
      assessmentSessionId: null,
      testAdministrationId: 'admin-1',
      category: DocumentCategoryEnum.BACKGROUND,
      name: 'Individualized Education Program',
      url: '/sample-reort.pdf',
      uploadedUserId: 'user-2',
      createdAt: new Date('2024-01-20T14:15:00Z'),
      updatedAt: new Date('2024-01-20T14:15:00Z'),
      uploadedUser: {
        id: 'user-2',
        firstName: 'Maria',
        lastName: 'Rodriguez',
        fullName: 'Maria Rodriguez',
        email: '<EMAIL>',
        avatar:
          'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
        userRoles: [
          {
            role: {
              name: 'SPECIAL_EDUCATION_TEACHER',
            },
          },
        ],
      },
    },
    {
      id: '550e8400-e29b-41d4-a716-336655440001',
      studentId,
      assessmentSessionId: '550e8400-e29b-41d4-a716-4466554400e2',
      testAdministrationId: null,
      category: DocumentCategoryEnum.BACKGROUND,
      name: 'Behavior Support Plan',
      url: '/sample-reort.pdf',
      uploadedUserId: 'user-3',
      createdAt: new Date('2024-02-01T09:00:00Z'),
      updatedAt: new Date('2024-02-01T09:00:00Z'),
      uploadedUser: {
        id: 'user-3',
        firstName: 'James',
        lastName: 'Chen',
        fullName: 'James Chen',
        email: '<EMAIL>',
        avatar:
          'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
        userRoles: [
          {
            role: {
              name: 'BEHAVIOR_SPECIALIST',
            },
          },
        ],
      },
    },
    {
      id: '550e8400-e29b-41d4-a716-446655440004',
      studentId,
      assessmentSessionId: null,
      testAdministrationId: null,
      category: DocumentCategoryEnum.BACKGROUND,
      name: 'Parent Consent Forms',
      url: '/sample-reort.pdf',
      uploadedUserId: 'user-2',
      createdAt: new Date('2024-01-10T09:00:00Z'),
      updatedAt: new Date('2024-01-10T09:00:00Z'),
      uploadedUser: {
        id: 'user-2',
        firstName: 'Maria',
        lastName: 'Rodriguez',
        fullName: 'Maria Rodriguez',
        email: '<EMAIL>',
        avatar:
          'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
        userRoles: [
          {
            role: {
              name: 'SPECIAL_EDUCATION_TEACHER',
            },
          },
        ],
      },
    },
    {
      id: '550e8400-e29b-41d4-a716-446655440005',
      studentId,
      assessmentSessionId: '550e8400-e29b-41d4-a716-4466554400e1',
      testAdministrationId: null,
      category: DocumentCategoryEnum.ASSESSMENT,
      name: 'Achievement Test Results',
      url: '/sample-reort.pdf',
      uploadedUserId: 'user-1',
      createdAt: new Date('2024-01-18T14:20:00Z'),
      updatedAt: new Date('2024-01-18T14:20:00Z'),
      uploadedUser: {
        id: 'user-1',
        firstName: 'Dr. Sarah',
        lastName: 'Johnson',
        fullName: 'Dr. Sarah Johnson',
        email: '<EMAIL>',
        avatar:
          'https://images.unsplash.com/photo-1494790108755-2616b612b1f3?w=150&h=150&fit=crop&crop=face',
        userRoles: [
          {
            role: {
              name: 'PSYCHOLOGIST',
            },
          },
        ],
      },
    },
  ];
}

async function getDocumentTree(studentId: string): Promise<DocumentTreeNode[]> {
  const documents = await getStudentDocuments(studentId);

  // Create folder structure
  const assessmentFolder: DocumentTreeNode = {
    id: 'folder-assessments',
    name: 'Assessment Reports',
    type: 'folder',
    category: DocumentCategoryEnum.ASSESSMENT,
    isSystemFolder: true,
    documentsCount: 2,
    isExpanded: true,
    children: documents
      .filter((doc) => doc.category === DocumentCategoryEnum.ASSESSMENT)
      .map((doc) => ({
        id: `doc-${doc.id}`,
        name: doc.name,
        type: 'document' as const,
        document: doc,
        createdAt: doc.createdAt,
      })),
  };

  const backgroundFolder: DocumentTreeNode = {
    id: 'folder-background',
    name: 'Background Documents',
    type: 'folder',
    category: DocumentCategoryEnum.BACKGROUND,
    isSystemFolder: true,
    documentsCount: 3,
    isExpanded: true,
    children: [
      {
        id: 'folder-iep',
        name: 'IEP Documents',
        type: 'folder',
        parentId: 'folder-background',
        documentsCount: 1,
        isExpanded: false,
        children: documents
          .filter((doc) => doc.name.includes('Education Program'))
          .map((doc) => ({
            id: `doc-${doc.id}`,
            name: doc.name,
            type: 'document' as const,
            document: doc,
            createdAt: doc.createdAt,
          })),
      },
      {
        id: 'folder-behavior',
        name: 'Behavior Plans',
        type: 'folder',
        parentId: 'folder-background',
        documentsCount: 1,
        isExpanded: false,
        children: documents
          .filter((doc) => doc.name.includes('Behavior'))
          .map((doc) => ({
            id: `doc-${doc.id}`,
            name: doc.name,
            type: 'document' as const,
            document: doc,
            createdAt: doc.createdAt,
          })),
      },
      {
        id: `doc-${documents.find((d) => d.name.includes('Consent'))?.id}`,
        name: 'Parent Consent Forms',
        type: 'document',
        document: documents.find((d) => d.name.includes('Consent')),
        createdAt: documents.find((d) => d.name.includes('Consent'))?.createdAt,
      },
    ],
  };

  return [assessmentFolder, backgroundFolder];
}
