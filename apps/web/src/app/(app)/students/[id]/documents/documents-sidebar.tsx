'use client';

import type {
  DocumentTreeNode,
  DocumentWithUser,
} from '@lilypad/db/repository/types/documents';
import { Button } from '@lilypad/ui/components/button';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@lilypad/ui/components/collapsible';
import { InputSearch } from '@lilypad/ui/components/input-search';
import { Label } from '@lilypad/ui/components/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@lilypad/ui/components/popover';
import { cn } from '@lilypad/ui/lib/utils';
import {
  ChevronRight,
  Folder,
  FolderOpen,
  SearchIcon,
  SlidersHorizontalIcon,
} from 'lucide-react';
import { useState } from 'react';
import { DocumentCardMini } from './document-card-mini';

interface DocumentsSidebarProps {
  isOpen: boolean;
  documents: DocumentWithUser[] | null;
  documentTree?: DocumentTreeNode[] | null;
}

const DocumentTreeItem = ({ item }: { item: DocumentTreeNode }) => {
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(
    new Set(['root'])
  );
  const isExpanded = expandedFolders.has(item.id);

  const toggleFolder = (folderId: string) => {
    setExpandedFolders((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(folderId)) {
        newSet.delete(folderId);
      } else {
        newSet.add(folderId);
      }
      return newSet;
    });
  };

  if (item.type === 'folder') {
    return (
      <div className="select-none">
        <Collapsible
          onOpenChange={() => toggleFolder(item.id)}
          open={isExpanded}
        >
          <CollapsibleTrigger asChild>
            <Button
              className={cn(
                'h-8 w-full justify-start gap-2 px-2 hover:bg-muted/50',
                'font-normal text-sm'
              )}
              size="sm"
              variant="ghost"
            >
              <ChevronRight
                className={cn(
                  'size-4 transition-transform',
                  isExpanded && 'rotate-90'
                )}
              />
              {isExpanded ? (
                <FolderOpen className="size-4 text-blue-600" />
              ) : (
                <Folder className="size-4 text-blue-600" />
              )}
              <span className="truncate">{item.name}</span>
              {item.documentsCount !== undefined && item.documentsCount > 0 && (
                <span className="ml-auto text-muted-foreground text-xs">
                  {item.documentsCount}
                </span>
              )}
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <div className="ml-4 space-y-1">
              {item.children?.map((child) => (
                <DocumentTreeItem key={child.id} item={child} />
              ))}
            </div>
          </CollapsibleContent>
        </Collapsible>
      </div>
    );
  }

  // Document item
  return (
    <div className="ml-4">
      {item.document && <DocumentCardMini document={item.document} />}
    </div>
  );
};

export default function DocumentsSidebar({
  isOpen,
  documents,
  documentTree,
}: DocumentsSidebarProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);

  const filteredDocuments = documents?.filter((doc) => {
    const matchesSearch =
      searchQuery === '' ||
      doc.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      doc.uploadedUser.fullName
        ?.toLowerCase()
        .includes(searchQuery.toLowerCase());

    const matchesCategory =
      selectedCategories.length === 0 ||
      selectedCategories.includes(doc.category);

    return matchesSearch && matchesCategory;
  });

  const categories = Array.from(
    new Set(documents?.map((doc) => doc.category) || [])
  );

  const handleCategoryChange = (category: string) => {
    setSelectedCategories((prev) =>
      prev.includes(category)
        ? prev.filter((c) => c !== category)
        : [...prev, category]
    );
  };

  const renderDocumentTree = () => {
    if (!documentTree || documentTree.length === 0) {
      return (
        <div className="space-y-1 p-2">
          {filteredDocuments?.map((document) => (
            <DocumentCardMini document={document} key={document.id} />
          ))}
        </div>
      );
    }

    return (
      <div className="space-y-1 p-2">
        {documentTree.map((item) => (
          <DocumentTreeItem item={item} key={item.id} />
        ))}
      </div>
    );
  };

  return (
    <aside
      className={cn(
        'h-full overflow-hidden bg-background transition-all duration-300',
        isOpen ? 'w-0 overflow-hidden' : 'w-80 border-r'
      )}
    >
      <div className="flex h-full flex-col">
        <div className="border-b p-2">
          <div className="flex items-center gap-x-2">
            <InputSearch
              className="flex-1"
              onChange={(e) => setSearchQuery(e.target.value)}
              onClear={() => setSearchQuery('')}
              placeholder="Search documents..."
              value={searchQuery}
            />
            <Popover>
              <PopoverTrigger asChild>
                <Button size="icon" variant="outline">
                  <SlidersHorizontalIcon className="size-4" />
                </Button>
              </PopoverTrigger>
              <PopoverContent align="end" className="w-56">
                <div className="space-y-4">
                  <h4 className="font-medium text-sm">Filters</h4>
                  <div className="space-y-2">
                    <div className="space-y-1">
                      <Label className="text-muted-foreground text-xs">
                        Category
                      </Label>
                      <div className="space-y-1">
                        {categories.map((category) => (
                          <Label
                            className="flex items-center gap-2 text-sm"
                            key={category}
                          >
                            <input
                              checked={selectedCategories.includes(category)}
                              className="size-4"
                              onChange={() => handleCategoryChange(category)}
                              type="checkbox"
                            />
                            <span className="capitalize">
                              {category.toLowerCase()}
                            </span>
                          </Label>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </div>
        </div>

        <nav className="flex-1 overflow-y-auto p-2">
          {filteredDocuments?.length === 0 ? (
            <div className="flex flex-col items-center justify-center p-8 text-center">
              <div className="mb-2 text-muted-foreground">
                <SearchIcon className="size-8" />
              </div>
              <h3 className="mb-1 font-medium text-sm">No documents found</h3>
              <p className="text-muted-foreground text-xs">
                {searchQuery || selectedCategories.length > 0
                  ? 'Try adjusting your search or filters'
                  : 'No documents have been uploaded yet'}
              </p>
            </div>
          ) : (
            <div className="space-y-1">{renderDocumentTree()}</div>
          )}
        </nav>
      </div>
    </aside>
  );
}
