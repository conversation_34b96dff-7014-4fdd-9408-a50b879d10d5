'use client';

import { But<PERSON> } from '@lilypad/ui/components/button';
import { CollapseButton } from '@lilypad/ui/components/collapse-button';
import { ArrowLeftIcon, ArrowRightIcon } from 'lucide-react';

interface DocumentsLayoutHeaderProps {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
}

export const DocumentsLayoutHeader = ({
  isOpen,
  setIsOpen,
}: DocumentsLayoutHeaderProps) => {
  return (
    <div className="sticky top-0 z-10 flex items-center gap-2 border-b bg-background px-4 py-2.5">
      <CollapseButton onClick={() => setIsOpen(!isOpen)} open={!isOpen} />
      <Button size="sm" variant="ghost">
        <ArrowLeftIcon className="size-3.5 stroke-1" />
      </Button>
      <Button size="sm" variant="ghost">
        <ArrowRightIcon className="size-3.5 stroke-1" />
      </Button>
      <div className="ml-auto">
        <span className="text-muted-foreground text-sm">Documents</span>
      </div>
    </div>
  );
};
