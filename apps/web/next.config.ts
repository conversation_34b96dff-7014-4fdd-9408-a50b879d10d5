import { MonitoringProvider } from '@lilypad/monitoring/provider';
import withBundleAnalyzer from '@next/bundle-analyzer';
import type { Header } from 'next/dist/lib/load-custom-routes';
import type { RemotePattern } from 'next/dist/shared/lib/image-config';
import type { NextConfig } from 'next/types';
import { createSecureHeaders } from 'next-secure-headers';
import { env } from './src/env';

type Protocol = 'http' | 'https';

const INTERNAL_PACKAGES = [
  '@lilypad/analytics',
  '@lilypad/db',
  '@lilypad/editor',
  '@lilypad/email',
  '@lilypad/jobs',
  '@lilypad/kv',
  '@lilypad/monitoring',
  '@lilypad/payments',
  '@lilypad/shared',
  '@lilypad/supabase',
  '@lilypad/ui',
];

const SVG_REGEX = /\.svg$/i;

const svgLoader = {
  loader: '@svgr/webpack',
  options: {
    svgoConfig: {
      plugins: [
        {
          name: 'preset-default',
          params: {
            overrides: {
              removeViewBox: false, // Preserve the viewBox attribute
            },
          },
        },
      ],
    },
  },
};

/** @type {import('next').NextConfig} */
const nextConfig = {
  /** Enables hot reloading for local packages without a build step */
  transpilePackages: INTERNAL_PACKAGES,
  serverExternalPackages: [],
  reactStrictMode: true,
  poweredByHeader: false,
  turbopack: {
    rules: {
      '*.svg': {
        loaders: [svgLoader],
      },
    },
  },
  experimental: {
    optimizePackageImports: [
      'recharts',
      'lucide-react',
      '@radix-ui/react-icons',
      '@radix-ui/react-avatar',
      '@radix-ui/react-select',
      'date-fns',
      ...INTERNAL_PACKAGES,
    ],
  },
  images: {
    remotePatterns: getRemotePatterns(),
  },
  eslint: { ignoreDuringBuilds: true },
  async headers() {
    return [
      {
        locale: false,
        source: '/(.*)',
        headers: createSecureHeaders({
          frameGuard: 'deny',
          noopen: 'noopen',
          nosniff: 'nosniff',
          xssProtection: 'sanitize',
          forceHTTPSRedirect: [
            true,
            { maxAge: 60 * 60 * 24 * 360, includeSubDomains: true },
          ],
          referrerPolicy: 'same-origin',
        }),
      },
    ] as Header[];
  },
  async redirects() {
    return [
      {
        source: '/',
        destination: '/auth',
        permanent: false,
      },
      {
        source: '/auth',
        destination: '/auth/sign-in',
        permanent: false,
      },
    ];
  },
  webpack(config: NextConfig) {
    config.module.rules.push({
      test: SVG_REGEX,
      use: [svgLoader],
    });
    return config;
  },
};

function getRemotePatterns() {
  const remotePatterns: RemotePattern[] = [];
  const supabaseURL = new URL(env.NEXT_PUBLIC_SUPABASE_URL);

  remotePatterns.push({
    protocol: supabaseURL.protocol.replace(':', '') as Protocol,
    hostname: supabaseURL.hostname,
  });
  remotePatterns.push({
    protocol: 'https',
    hostname: 'lh3.googleusercontent.com',
  });
  return remotePatterns;
}

const bundleAnalyzerConfig = withBundleAnalyzer({
  enabled: env.BUNDLE_ANALYZER,
});

export default MonitoringProvider.withConfig(bundleAnalyzerConfig(nextConfig));
