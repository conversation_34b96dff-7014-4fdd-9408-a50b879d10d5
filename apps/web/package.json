{"name": "web", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack --port 3000 | pino-pretty", "build": "next build", "start": "next start", "lint": "biome lint --write", "format": "biome format --write .", "clean": "git clean -xdf .next node_modules", "typecheck": "tsc --noEmit"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@ebay/nice-modal-react": "^1.2.13", "@edge-csrf/nextjs": "2.5.3-cloudflare-rc1", "@hookform/resolvers": "^5.0.1", "@lilypad/analytics": "workspace:*", "@lilypad/api": "workspace:*", "@lilypad/core": "workspace:*", "@lilypad/db": "workspace:*", "@lilypad/editor": "workspace:*", "@lilypad/email": "workspace:*", "@lilypad/jobs": "workspace:*", "@lilypad/kv": "workspace:*", "@lilypad/monitoring": "workspace:*", "@lilypad/payments": "workspace:*", "@lilypad/shared": "workspace:*", "@lilypad/supabase": "workspace:*", "@lilypad/ui": "workspace:*", "@t3-oss/env-nextjs": "^0.13.4", "@tanstack/react-query": "^5.80.10", "@tanstack/react-query-devtools": "^5.80.10", "@tanstack/react-query-next-experimental": "^5.80.10", "@tanstack/react-table": "^8.21.3", "@tanstack/react-virtual": "^3.13.12", "@trpc/server": "^11.4.2", "@types/papaparse": "^5.3.16", "@uidotdev/usehooks": "^2.4.1", "date-fns": "^4.1.0", "drizzle-orm": "^0.43.1", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "idb-keyval": "^6.2.2", "immer": "^10.1.1", "lucide-react": "^0.511.0", "markdown-it": "^14.1.0", "motion": "^12.12.1", "next": "^15.3.3", "next-safe-action": "^7.10.8", "next-secure-headers": "^2.2.0", "nuqs": "^2.4.3", "otplib": "^12.0.1", "papaparse": "^5.5.3", "pg": "^8.16.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.4", "react-hotkeys-hook": "^5.1.0", "react-remove-scroll": "^2.7.0", "recharts": "^2.15.3", "sanitize-html": "^2.17.0", "server-only": "^0.0.1", "stripe": "^18.1.1", "turndown": "^7.2.0", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^3.25.7", "zod-form-data": "^2.0.7", "zustand": "^5.0.5"}, "devDependencies": {"@lilypad/tailwindcss": "workspace:*", "@lilypad/typescript": "workspace:*", "@next/bundle-analyzer": "^15.3.2", "@svgr/webpack": "^8.1.0", "@tailwindcss/postcss": "^4.1.7", "@types/file-saver": "^2.0.7", "@types/markdown-it": "^14.1.2", "@types/node": "^22.15.3", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "@types/sanitize-html": "^2.16.0", "@types/turndown": "^5.0.5", "@types/uuid": "^10.0.0", "pino-pretty": "^13.0.0", "tailwindcss": "^4.1.7", "typescript": "5.8.2"}}