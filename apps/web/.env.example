# -------------------------- ANALYTICS --------------------------

# Provider: PostHog
NEXT_PUBLIC_ANALYTICS_POSTHOG_KEY=phc_1234
NEXT_PUBLIC_ANALYTICS_POSTHOG_HOST=https://us.i.posthog.com

# -------------------------- SUPABASE --------------------------

NEXT_PUBLIC_SUPABASE_URL=http://127.0.0.1:54321
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU
SUPABASE_RECOVERY_CODE_SECRET='N4zI1suUdpvZSrMnp7bjvLB4Gip63MhmJrwXBperTTQ=' # run 'openssl rand -base64 32' to generate a new secret

# -------------------------- BILLING --------------------------

NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_51LcSktBpRHcb850fwteCE25Zdxa1BpjUKsBazt0kwwl4FzjBlmUd6hy3gtSUrJwhkctZ12rEvuTTZ2WSLjMwWcP500GToGpJrX
STRIPE_SECRET_KEY=sk_test_51LcSktBpRHcb850fPrgW7stHjepIaI62iTOdrpkYN5x9rhvjK0rgtc3jzJ7gv5ni4OK1RtBUxlpnKHutq77Moy7N00RK1pofdt
STRIPE_WEBHOOK_SECRET=whsec_ce83b4b88a6b5ed89cc662af76f13549e7e8eb52f3e5ca1fe9b3ac941f6d1a3e
BILLING_PRO_PRODUCT_ID=price_00000000000000000000000
BILLING_PRO_PRODUCT_PRICE_ID=price_00000000000000000000000
BILLING_UNIT=per_seat # per_seat | per_organization

# -------------------------- DATABASE --------------------------

DATABASE_URL=postgresql://protected:postgres@127.0.0.1:54322/postgres
ADMIN_DATABASE_URL=postgresql://postgres:postgres@127.0.0.1:54322/postgres

# -------------------------- EMAIL --------------------------

EMAIL_FROM=<EMAIL>
EMAIL_FEEDBACK_INBOX=<EMAIL>
# Provider: NodeMailer (Local Development)
EMAIL_NODEMAILER_URL=smtp://:@0.0.0.0:54325
# Provider: Resend (Production)
EMAIL_RESEND_API_KEY=demo_resend_api_key

# -------------------------- JOBS --------------------------

INNGEST_APP_ID=lilypad-background-jobs
INNGEST_ENCRYPTION_KEY=cRy8/aXK5d8d/jbeOxOeIzyI653GI+AQ4glL2qCH0e4= # run 'openssl rand -base64 32' to generate a new secret
FALLBACK_INNGEST_ENCRYPTION_KEY=UrXIu+rnw9i7qv6s07rzNld5kDRiT7+w9U3EpJj3yr8= # run 'openssl rand -base64 32' to generate a new secret

# -------------------------- KV --------------------------------

# UPSTASH REDIS
UPSTASH_REDIS_REST_URL=https://demo-url-123.upstash.io
UPSTASH_REDIS_REST_TOKEN=abc00000000000000000000000000

# -------------------------- MONITORING --------------------------

# Provider: Sentry
NEXT_RUNTIME=edge
SENTRY_ORG=lilypad-learning
SENTRY_PROJECT=lilypad-learning
SENTRY_AUTH_TOKEN=sntrys_ey00000000000
NEXT_PUBLIC_SENTRY_DSN=https://123456789.ingest.de.sentry.io/123456789
SENTRY_SUPPRESS_TURBOPACK_WARNING=1 # https://github.com/getsentry/sentry-javascript/blob/develop/packages/nextjs/src/config/withSentryConfig.ts#L156

# -------------------------- ROUTES --------------------------

NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_SITE_URL=http://localhost:3001
ENVIRONMENT=local

BUNDLE_ANALYZER=true