{"name": "docs", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev:docs": "next dev --turbopack --port 3001", "build": "next build", "start": "next start", "lint": "biome lint --write .", "format": "biome format --write .", "typecheck": "tsc --noEmit", "clean": "git clean -xdf .next node_modules"}, "dependencies": {"@lilypad/ui": "workspace:*", "@lilypad/tailwindcss": "workspace:*", "next": "^15.3.0", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@lilypad/typescript": "workspace:*", "@types/node": "^22.15.3", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "typescript": "5.8.2"}}