import type * as React from 'react';

import {
  <PERSON>,
  Container,
  Head,
  Html,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Text,
} from '@react-email/components';

export function EmailStructure(
  props: React.PropsWithChildren<{ previewText: string }>
): React.JSX.Element {
  return (
    <Html>
      <Head />
      <div className="hidden">{props.previewText}</div>

      <Tailwind>
        <Body
          style={
            {
              '--primary': '#1c5551',
              '--primary-foreground': '#ffffff',
              '--secondary': '#f1f5f9',
              '--secondary-foreground': '#334155',
              '--accent': '#0f766e',
              '--accent-foreground': '#ffffff',
              '--muted': '#f8fafc',
              '--muted-foreground': '#64748b',
              '--border': '#e2e8f0',
              '--ring': '#0f766e',
              fontFamily:
                "-apple-system,BlinkMacSystemFont,'<PERSON><PERSON><PERSON> UI',<PERSON>o,Oxygen-<PERSON>s,<PERSON>buntu,Can<PERSON>ell,'Helvetica Neue',sans-serif",
            } as React.CSSProperties
          }
          className="m-auto bg-slate-50 text-slate-900 antialiased"
        >
          {props.children}
        </Body>
      </Tailwind>
    </Html>
  );
}

export function EmailContainer({
  children,
}: React.PropsWithChildren): React.JSX.Element {
  return (
    <Container className="mx-auto my-12 w-full max-w-[580px] rounded-xl border border-slate-200 border-solid bg-white p-8 shadow-sm">
      {children}
    </Container>
  );
}

export function EmailParagraph({
  children,
  className,
}: React.PropsWithChildren<{ className?: string }>): React.JSX.Element {
  return (
    <Text
      className={`mb-4 text-base text-slate-700 leading-relaxed ${className}`}
    >
      {children}
    </Text>
  );
}

export function EmailLink({
  href,
  children,
}: React.PropsWithChildren<{ href: string }>): React.JSX.Element {
  return (
    <Link
      href={href}
      className="font-medium text-[var(--primary)] underline underline-offset-2 transition-colors hover:text-[var(--accent)]"
    >
      {children}
    </Link>
  );
}

export function EmailButton({
  href,
  children,
}: React.PropsWithChildren<{ href: string }>): React.JSX.Element {
  return (
    <Link
      href={href}
      target="_blank"
      rel="noopener noreferrer"
      className="inline-flex shrink-0 cursor-pointer items-center justify-center gap-2 whitespace-nowrap rounded-lg border-2 border-[var(--primary)] border-solid bg-[var(--primary)] px-4 py-2 font-semibold text-[var(--primary-foreground)] text-base shadow-sm outline-none transition-all hover:border-[var(--accent)] hover:bg-[var(--accent)] hover:shadow-md focus-visible:ring-2 focus-visible:ring-[var(--ring)] focus-visible:ring-offset-2 disabled:pointer-events-none"
    >
      {children}
    </Link>
  );
}
