import type * as React from 'react';

import { APP_NAME } from '@lilypad/shared';
import { baseUrl, routes } from '@lilypad/shared/routes';
import { Column, Img, Link, Row, Section } from '@react-email/components';

interface EmailHeaderProps {
  siteURL?: string;
}

export function EmailHeader({
  siteURL = baseUrl.Site,
}: EmailHeaderProps): React.JSX.Element {
  return (
    <Section className="border-slate-200 border-b border-solid pb-6">
      <Row>
        <Column className="">
          <Link href={siteURL}>
            <Img
              src={routes.logo.LogoFull}
              alt={APP_NAME}
              width={140}
              height={36}
            />
          </Link>
        </Column>
      </Row>
    </Section>
  );
}
