import type * as React from 'react';

import { APP_NAME } from '@lilypad/shared';
import { baseUrl, routes } from '@lilypad/shared/routes';
import {
  Column,
  Hr,
  Img,
  Link,
  Row,
  Section,
  Text,
} from '@react-email/components';

const EMAIL_MESSAGE =
  "If you didn't request this, just ignore and delete this message. To keep your account secure, please don't forward this email to anyone.";

interface EmailFooterProps {
  siteURL?: string;
}

export function EmailFooter({
  siteURL = baseUrl.Site,
}: EmailFooterProps): React.JSX.Element {
  const companyLine = `Copyright © ${APP_NAME}`;

  return (
    <Section className="mt-6 border-slate-200 border-t border-solid">
      <Row>
        <Column className="text-center">
          <Text className="mt-6 mb-4 text-slate-500 text-sm leading-relaxed">
            {EMAIL_MESSAGE}
          </Text>
        </Column>
      </Row>

      <Row>
        <Column className="mb-0 text-center">
          <Link href={siteURL} className="inline-block">
            <Img
              src={routes.logo.LogoFull}
              alt={APP_NAME}
              width={120}
              height={30}
              className="mx-auto mt-2"
            />
          </Link>
        </Column>
      </Row>

      <Row>
        <Column className="text-center">
          <Text className="my-2 text-slate-500 text-sm">{companyLine}</Text>
        </Column>
      </Row>

      <Row>
        <Column className="text-center">
          <Text className="text-slate-500 text-xs">
            <Link href={routes.site.PrivacyPolicy} className="text-slate-500">
              Privacy Policy
            </Link>
            {' • '}
            <Link href={routes.site.TermsOfUse} className="text-slate-500">
              Terms of Service
            </Link>
            {' • '}
            <Link href={routes.site.Contact} className="text-slate-500">
              Contact Us
            </Link>
          </Text>
        </Column>
      </Row>
    </Section>
  );
}
