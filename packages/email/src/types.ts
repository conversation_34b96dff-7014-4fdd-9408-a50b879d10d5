export type EmailPayload = {
  recipient: string;
  subject: string;
  text: string;
  html: string;
  replyTo?: string;
};

export type EmailProvider = {
  sendEmail(payload: EmailPayload): Promise<unknown>;
};

export type BaseEmailProps = {
  recipient: string;
};

export type ConnectedAccountSecurityAlertEmailProps = {
  name: string;
  provider: string;
  action: 'connected' | 'disconnected';
};

export type DistrictOnboardingEmailProps = {
  recipientName: string;
  invitedByName: string;
  invitedByEmail: string;
  districtName: string;
  role: string;
  schoolNames: string[];
  inviteLink: string;
};

export type PsychologistAssignmentEmailProps = {
  recipientName: string;
  psychologistName: string;
  psychologistEmail: string;
  psychologistAvatar: string;
  psychologistBio: string;
  districtName: string;
};

export type FinalReportEmailProps = {
  recipientName: string;
  studentName: string;
  districtName: string;
  schoolName: string;
  finalReportLink: string;
};

export type FeedbackEmailProps = {
  organizationName: string;
  name: string;
  email: string;
  category: string;
  message: string;
};

export type RevokedInvitationEmailProps = {
  organizationName: string;
};

export type WelcomeEmailProps = {
  name: string;
  getStartedLink: string;
};

export type RatingScalesEmailProps = {
  recipientName: string;
  studentName: string;
  studentGrade: string;
  schoolName: string;
  districtName: string;
  psychologistName: string;
  psychologistEmail: string;
  ratingScales: Array<{
    name: string;
    description: string;
    estimatedTime: string;
    link: string;
  }>;
};
