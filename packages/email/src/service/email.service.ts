import { APP_NAME } from '@lilypad/shared';
import { render } from '@react-email/render';
import { EmailProvider } from '../provider';
import type { EmailProvider as EmailProviderType } from '../provider/types';
import {
  ConnectedAccountSecurityAlertEmail,
  DistrictOnboardingEmail,
  FeedbackEmail,
  FinalReportEmail,
  PsychologistAssignmentEmail,
  RatingScalesEmail,
  RevokedInvitationEmail,
  WelcomeEmail,
} from '../templates';
import type {
  BaseEmailProps,
  ConnectedAccountSecurityAlertEmailProps,
  DistrictOnboardingEmailProps,
  FeedbackEmailProps,
  FinalReportEmailProps,
  PsychologistAssignmentEmailProps,
  RatingScalesEmailProps,
  RevokedInvitationEmailProps,
  WelcomeEmailProps,
} from '../types';

class EmailService {
  private readonly emailProvider: EmailProviderType;

  constructor(emailProvider: EmailProviderType) {
    this.emailProvider = emailProvider;
  }

  async sendConnectedAccountSecurityAlertEmail(
    payload: ConnectedAccountSecurityAlertEmailProps & BaseEmailProps
  ): Promise<void> {
    const component = ConnectedAccountSecurityAlertEmail(payload);
    const html = await render(component);
    const text = await render(component, { plainText: true });

    await this.emailProvider.sendEmail({
      recipient: payload.recipient,
      subject: 'Security Alert!',
      html,
      text,
    });
  }

  async sendFeedbackEmail(
    payload: FeedbackEmailProps & BaseEmailProps
  ): Promise<void> {
    const component = FeedbackEmail(payload);
    const html = await render(component);
    const text = await render(component, { plainText: true });

    await this.emailProvider.sendEmail({
      recipient: payload.recipient,
      subject: 'User Feedback',
      html,
      text,
      replyTo: payload.email,
    });
  }

  async sendDistrictOnboardingEmail(
    payload: DistrictOnboardingEmailProps & BaseEmailProps
  ): Promise<void> {
    const component = DistrictOnboardingEmail(payload);
    const html = await render(component);
    const text = await render(component, { plainText: true });

    await this.emailProvider.sendEmail({
      recipient: payload.recipient,
      subject: `You have been added to ${payload.districtName} as a ${payload.role}`,
      html,
      text,
    });
  }

  async sendRevokedInvitationEmail(
    payload: RevokedInvitationEmailProps & BaseEmailProps
  ): Promise<void> {
    const component = RevokedInvitationEmail(payload);
    const html = await render(component);
    const text = await render(component, { plainText: true });

    await this.emailProvider.sendEmail({
      recipient: payload.recipient,
      subject: 'Invitation Revoked',
      html,
      text,
    });
  }

  async sendWelcomeEmail(
    payload: WelcomeEmailProps & BaseEmailProps
  ): Promise<void> {
    const component = WelcomeEmail(payload);
    const html = await render(component);
    const text = await render(component, { plainText: true });

    await this.emailProvider.sendEmail({
      recipient: payload.recipient,
      subject: `Welcome to ${APP_NAME}`,
      html,
      text,
    });
  }

  async sendPsychologistAssignmentEmail(
    payload: PsychologistAssignmentEmailProps & BaseEmailProps
  ): Promise<void> {
    const component = PsychologistAssignmentEmail(payload);
    const html = await render(component);
    const text = await render(component, { plainText: true });

    await this.emailProvider.sendEmail({
      recipient: payload.recipient,
      subject: 'Meet Your New Psychologist',
      html,
      text,
    });
  }

  async sendFinalReportEmail(
    payload: FinalReportEmailProps & BaseEmailProps
  ): Promise<void> {
    const component = FinalReportEmail(payload);
    const html = await render(component);
    const text = await render(component, { plainText: true });

    await this.emailProvider.sendEmail({
      recipient: payload.recipient,
      subject: 'Final Report Ready for Review',
      html,
      text,
    });
  }

  async sendRatingScalesEmail(
    payload: RatingScalesEmailProps & BaseEmailProps
  ): Promise<void> {
    const component = RatingScalesEmail(payload);
    const html = await render(component);
    const text = await render(component, { plainText: true });

    await this.emailProvider.sendEmail({
      recipient: payload.recipient,
      subject: `Rating Scales Required - ${payload.studentName}`,
      html,
      text,
    });
  }
}

export default new EmailService(EmailProvider);
