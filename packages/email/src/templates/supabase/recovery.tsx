import type * as React from 'react';

import { APP_NAME } from '@lilypad/shared';
import { Section, Text } from '@react-email/components';
import {
  EmailButton,
  EmailContainer,
  EmailLink,
  EmailParagraph,
  EmailStructure,
} from '../../components/email-components';
import { EmailFooter } from '../../components/email-footer';
import { EmailHeader } from '../../components/email-header';
import { getEmailData } from './utils';

export default function RecoveryEmail(): React.JSX.Element {
  const previewText = 'Reset your password to regain access to your account.';

  const {
    siteURL,
    receiverEmail,
    url: resetPasswordURL,
  } = getEmailData('recovery');

  return (
    <EmailStructure previewText={previewText}>
      <EmailContainer>
        <EmailHeader siteURL={siteURL} />

        <Text className="mb-6 font-bold text-2xl text-gray-950">
          Reset Your Password
        </Text>

        <EmailParagraph>
          Hi{' '}
          <EmailLink href={`mailto:${receiverEmail}`}>
            {receiverEmail}
          </EmailLink>
          {','}
        </EmailParagraph>

        <EmailParagraph>
          We received a request to reset your password for your {APP_NAME}{' '}
          account. To proceed with resetting your password, please click the
          button below:
        </EmailParagraph>

        <Section className="my-4 text-center">
          <EmailButton href={resetPasswordURL}>Reset Password</EmailButton>
        </Section>

        <Section className="mb-4 rounded-lg border border-red-200 border-solid bg-red-50 px-4">
          <Text className="mb-2 font-semibold text-red-800 text-sm">
            Security Notice
          </Text>
          <Text className="mt-2 text-red-700 text-sm">
            If you did not request this password reset or believe it to be
            unauthorized, please ignore this message. Your account's security is
            our priority, and we recommend reviewing your account activity
            regularly for any suspicious activity.
          </Text>
        </Section>

        <EmailParagraph>
          For your security, this link will expire in 1 hour. If you need to
          reset your password after that, you'll need to request a new reset
          link.
        </EmailParagraph>

        <EmailParagraph>
          Thank you for keeping your account secure!
        </EmailParagraph>

        <EmailParagraph>
          Best regards,
          <br />
          The {APP_NAME} Team
        </EmailParagraph>

        <EmailFooter siteURL={siteURL} />
      </EmailContainer>
    </EmailStructure>
  );
}
