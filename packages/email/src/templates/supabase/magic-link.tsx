import type * as React from 'react';

import { APP_NAME } from '@lilypad/shared';
import { Section, Text } from '@react-email/components';

import {
  EmailButton,
  EmailContainer,
  EmailLink,
  EmailParagraph,
  EmailStructure,
} from '../../components/email-components';
import { EmailFooter } from '../../components/email-footer';
import { EmailHeader } from '../../components/email-header';
import { getEmailData } from './utils';

export default function MagicLinkEmail(): React.JSX.Element {
  const previewText = 'Click on the magic link to sign in instantly!';

  const {
    token,
    siteURL,
    receiverEmail,
    url: confirmEmailURL,
  } = getEmailData('magiclink');

  return (
    <EmailStructure previewText={previewText}>
      <EmailContainer>
        <EmailHeader siteURL={siteURL} />

        <Text className="mb-6 font-bold text-2xl text-gray-950">
          Your Magic Link is Ready
        </Text>

        <EmailParagraph>
          Hi{' '}
          <EmailLink href={`mailto:${receiverEmail}`}>
            {receiverEmail}
          </EmailLink>
          {','}
        </EmailParagraph>

        <EmailParagraph>
          Click the button below to sign in instantly to your {APP_NAME}{' '}
          account:
        </EmailParagraph>

        <Section className="my-4 text-center">
          <EmailButton href={confirmEmailURL}>Sign In Now</EmailButton>
        </Section>

        <Section className="mb-4 rounded-lg border border-purple-200 border-solid bg-purple-50 px-4">
          <Text className="mb-2 font-semibold text-purple-800 text-sm">
            Alternative Method
          </Text>
          <Text className="my-2 text-purple-700 text-sm">
            You can also use this one-time code to sign in. This code will only
            be valid for the next 5 minutes.
          </Text>

          <Section className="text-center">
            <Text className="mt-2 inline-block rounded-lg border border-purple-300 border-solid bg-purple-100 px-4 py-2 font-bold text-purple-900 text-xl tracking-wider">
              {token}
            </Text>
          </Section>
        </Section>

        <EmailParagraph>
          Thank you for using {APP_NAME}! We're here to help you get things
          done.
        </EmailParagraph>

        <EmailParagraph>
          Best regards,
          <br />
          The {APP_NAME} Team
        </EmailParagraph>

        <EmailFooter siteURL={siteURL} />
      </EmailContainer>
    </EmailStructure>
  );
}
