import type * as React from 'react';

import { APP_NAME } from '@lilypad/shared';
import { Section, Text } from '@react-email/components';
import {
  EmailButton,
  EmailContainer,
  EmailLink,
  EmailParagraph,
  EmailStructure,
} from '../../components/email-components';
import { EmailFooter } from '../../components/email-footer';
import { EmailHeader } from '../../components/email-header';
import { getEmailData } from './utils';

export default function EmailChange(): React.JSX.Element {
  const previewText = 'Confirm your new email address to complete the change.';

  const {
    siteURL,
    receiverEmail,
    newEmail,
    url: confirmEmailURL,
  } = getEmailData('email_change');

  return (
    <EmailStructure previewText={previewText}>
      <EmailContainer>
        <EmailHeader siteURL={siteURL} />

        <Text className="mb-6 font-bold text-2xl text-gray-950">
          Confirm Your New Email
        </Text>

        <EmailParagraph>
          Hi{' '}
          <EmailLink href={`mailto:${receiverEmail}`}>
            {receiverEmail}
          </EmailLink>
          {','}
        </EmailParagraph>

        <EmailParagraph>
          We have received a request to update your email address from your
          current address to{' '}
          <EmailLink href={`mailto:${newEmail}`}>{newEmail}</EmailLink>
          {'.'}
        </EmailParagraph>

        <Section className="my-4 rounded-lg border border-blue-200 border-solid bg-blue-50 px-4">
          <Text className="mb-2 font-semibold text-blue-800 text-sm">
            Email Change Request
          </Text>
          <Text className="mt-2 text-blue-700 text-sm">
            <span className="font-medium">From:</span> {receiverEmail}
            <br />
            <span className="font-medium">To:</span> {newEmail}
          </Text>
        </Section>

        <EmailParagraph>
          To confirm this change and complete your email update, please click
          the button below:
        </EmailParagraph>

        <Section className="my-4 text-center">
          <EmailButton href={confirmEmailURL}>Confirm Email Change</EmailButton>
        </Section>

        <Section className="mb-4 rounded-lg border border-amber-200 border-solid bg-amber-50 px-4">
          <Text className="mb-2 font-semibold text-amber-800 text-sm">
            Important Security Information
          </Text>
          <Text className="mt-2 text-amber-700 text-sm">
            If you did not initiate this request or believe it to be in error,
            please disregard this email. Your account's security is important to
            us, and we recommend reviewing your account activity regularly to
            ensure its safety.
          </Text>
        </Section>

        <EmailParagraph>
          After confirming, you'll receive all future communications at your new
          email address.
        </EmailParagraph>

        <EmailParagraph>
          Thank you for keeping your account information up to date!
        </EmailParagraph>

        <EmailParagraph>
          Best regards,
          <br />
          The {APP_NAME} Team
        </EmailParagraph>

        <EmailFooter siteURL={siteURL} />
      </EmailContainer>
    </EmailStructure>
  );
}
