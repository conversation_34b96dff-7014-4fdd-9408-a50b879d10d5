import { baseUrl } from '@lilypad/shared/routes';

export function getEmailData(
  type: 'signup' | 'magiclink' | 'recovery' | 'email_change'
) {
  return process.env.IS_PREVIEW === 'true'
    ? {
        token: '935023',
        siteURL: baseUrl.Site,
        receiverEmail: '<EMAIL>',
        newEmail: '<EMAIL>',
        url: `${baseUrl.App}/auth/callback?tokenHash=935023&type=${type}&next=/auth/sign-in`,
      }
    : {
        token: '{{ .Token }}',
        siteURL: '{{ .SiteURL }}',
        receiverEmail: '{{ .Email }}',
        newEmail: '{{ .NewEmail }}',
        url: `{{ .SiteURL }}/auth/callback?tokenHash={{ .TokenHash }}&type=${type}&next={{ .RedirectTo }}`,
      };
}
