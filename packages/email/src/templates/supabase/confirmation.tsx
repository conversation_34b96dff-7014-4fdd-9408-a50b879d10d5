import type * as React from 'react';

import { APP_NAME } from '@lilypad/shared';
import { Section, Text } from '@react-email/components';
import {
  EmailButton,
  EmailContainer,
  EmailLink,
  EmailParagraph,
  EmailStructure,
} from '../../components/email-components';
import { EmailFooter } from '../../components/email-footer';
import { EmailHeader } from '../../components/email-header';
import { getEmailData } from './utils';

export default function ConfirmationEmail(): React.JSX.Element {
  const previewText = 'Your one-time passcode: {{ .Token }}';

  const {
    token,
    siteURL,
    receiverEmail,
    url: confirmEmailURL,
  } = getEmailData('signup');

  return (
    <EmailStructure previewText={previewText}>
      <EmailContainer>
        <EmailHeader siteURL={siteURL} />

        <Text className="mb-6 font-bold text-2xl text-gray-950">
          Confirm Your Email
        </Text>

        <EmailParagraph>
          Hi{' '}
          <EmailLink href={`mailto:${receiverEmail}`}>
            {receiverEmail}
          </EmailLink>
          {','}
        </EmailParagraph>

        <EmailParagraph>
          Welcome to {APP_NAME}! To complete your account setup, please use the
          confirmation code below:
        </EmailParagraph>

        <Section className="my-4 text-center">
          <div className="inline-block rounded-lg border border-teal-200 border-solid bg-teal-50 px-4">
            <Text className="mb-2 font-medium text-teal-700 text-xs uppercase tracking-wide">
              Your Confirmation Code
            </Text>
            <Text className="font-bold text-3xl text-teal-800 tracking-wider">
              {token}
            </Text>
          </div>
        </Section>

        <Section className="mb-4 rounded-lg border border-amber-200 border-solid bg-amber-50 px-4">
          <Text className="mb-2 font-semibold text-amber-800 text-sm">
            Time Sensitive
          </Text>
          <Text className="mt-2 text-amber-700 text-sm">
            This code will only be valid for the next 5 minutes. If the code
            doesn't work, you can use the button below instead.
          </Text>
        </Section>

        <Section className="text-center">
          <EmailButton href={confirmEmailURL}>
            Confirm Email Address
          </EmailButton>
        </Section>

        <EmailParagraph>
          Thank you for joining {APP_NAME}! We're excited to have you as part of
          our community.
        </EmailParagraph>

        <EmailParagraph>
          Best regards,
          <br />
          The {APP_NAME} Team
        </EmailParagraph>

        <EmailFooter siteURL={siteURL} />
      </EmailContainer>
    </EmailStructure>
  );
}
