import type * as React from 'react';
import type { ConnectedAccountSecurityAlertEmailProps } from '../types';

import { Section, Text } from '@react-email/components';
import {
  EmailContainer,
  EmailParagraph,
  EmailStructure,
} from '../components/email-components';
import { EmailFooter } from '../components/email-footer';
import { EmailHeader } from '../components/email-header';

export default function ConnectedAccountSecurityAlertEmail(
  props: ConnectedAccountSecurityAlertEmailProps
): React.JSX.Element {
  const { name, provider, action } = getPreviewValues(props);

  return (
    <EmailStructure previewText="Security Alert!">
      <EmailContainer>
        <EmailHeader />

        <Text className="mb-6 font-bold text-2xl">Security Alert!</Text>

        <EmailParagraph>Hello {name},</EmailParagraph>

        <EmailParagraph>
          The login method '<strong>{provider}</strong>' has been{' '}
          <strong>{action}</strong> {action === 'disconnected' ? 'from' : 'to'}{' '}
          your account.
        </EmailParagraph>

        <Section className="my-4 rounded-lg border border-amber-200 border-solid bg-amber-50 px-4">
          <Text className="mb-2 font-semibold text-amber-800 text-sm">
            Important Security Information
          </Text>
          <Text className="mt-2 text-amber-700 text-sm">
            If you did not authorize this change, please contact our support
            team immediately and review your account security settings.
          </Text>
        </Section>

        <EmailParagraph>
          This notification was sent to help protect your account. If you have
          any concerns about your account security, please don't hesitate to
          reach out to our support team.
        </EmailParagraph>

        <EmailFooter />
      </EmailContainer>
    </EmailStructure>
  );
}

function getPreviewValues(
  props: ConnectedAccountSecurityAlertEmailProps
): ConnectedAccountSecurityAlertEmailProps {
  return process.env.IS_PREVIEW === 'true'
    ? {
        name: 'John Doe',
        provider: 'Google',
        action: 'connected',
      }
    : props;
}
