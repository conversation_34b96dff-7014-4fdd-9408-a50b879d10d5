import type * as React from 'react';
import type { RevokedInvitationEmailProps } from '../types';

import { APP_NAME } from '@lilypad/shared';
import { Section, Text } from '@react-email/components';
import {
  EmailContainer,
  EmailParagraph,
  EmailStructure,
} from '../components/email-components';
import { EmailFooter } from '../components/email-footer';
import { EmailHeader } from '../components/email-header';

export default function RevokedInvitationEmail(
  props: RevokedInvitationEmailProps
): React.JSX.Element {
  const { organizationName } = getPreviewValues(props);
  const previewText = `Invitation for ${organizationName} on ${APP_NAME} revoked`;

  return (
    <EmailStructure previewText={previewText}>
      <EmailContainer>
        <EmailHeader />

        <Text className="mb-6 font-bold text-2xl text-gray-950">
          Invitation for{' '}
          <span className="text-[var(--primary)]">{organizationName}</span> on{' '}
          <span className="text-[var(--primary)]">{APP_NAME}</span> revoked
        </Text>

        <EmailParagraph>Hello,</EmailParagraph>

        <EmailParagraph>
          Your invitation to join <strong>{organizationName}</strong> has been
          revoked.
        </EmailParagraph>

        <Section className="my-4 rounded-lg border border-gray-200 border-solid bg-gray-50 px-4">
          <Text className="mb-2 font-semibold text-gray-950 text-sm">
            What this means
          </Text>
          <Text className="mt-2 text-gray-700 text-sm">
            You no longer have access to join this organization. If this was
            unexpected, please contact an administrator from the organization
            for assistance.
          </Text>
        </Section>

        <EmailParagraph>
          If the revocation was unexpected, ask an admin in the organization to
          send you a new invitation link.
        </EmailParagraph>

        <EmailFooter />
      </EmailContainer>
    </EmailStructure>
  );
}

function getPreviewValues(
  props: RevokedInvitationEmailProps
): RevokedInvitationEmailProps {
  return process.env.IS_PREVIEW === 'true'
    ? {
        organizationName: 'Acme Inc.',
      }
    : props;
}
