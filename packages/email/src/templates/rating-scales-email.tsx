import type * as React from 'react';
import type { RatingScalesEmailProps } from '../types';

import { Section, Text } from '@react-email/components';
import {
  EmailButton,
  EmailContainer,
  EmailLink,
  EmailParagraph,
  EmailStructure,
} from '../components/email-components';
import { EmailFooter } from '../components/email-footer';
import { EmailHeader } from '../components/email-header';

export default function RatingScalesEmail(
  props: RatingScalesEmailProps
): React.JSX.Element {
  const {
    recipientName,
    studentName,
    studentGrade,
    schoolName,
    districtName,
    psychologistName,
    psychologistEmail,
    ratingScales,
  } = getPreviewValues(props);

  const previewText = `Please complete rating scales for ${studentName}`;

  return (
    <EmailStructure previewText={previewText}>
      <EmailContainer>
        <EmailHeader />

        <Text className="mb-6 font-bold text-2xl text-gray-950">
          Rating Scales Request for{' '}
          <span className="text-[var(--primary)]">{studentName}</span>
        </Text>

        <EmailParagraph>Dear {recipientName},</EmailParagraph>

        <EmailParagraph>
          As part of {studentName}'s psychological evaluation, we need your
          valuable input through standardized rating scales. Your observations
          and insights are essential for providing the most accurate assessment
          and recommendations.
        </EmailParagraph>

        <Section className="my-4 rounded-lg border border-blue-200 border-solid bg-blue-50 px-4">
          <Text className="mb-3 font-semibold text-blue-800 text-sm">
            Student Information
          </Text>
          <Text className="mb-1 text-blue-700 text-sm">
            <strong>Student:</strong> {studentName}
          </Text>
          <Text className="mb-1 text-blue-700 text-sm">
            <strong>Grade:</strong> {studentGrade}
          </Text>
          <Text className="mb-1 text-blue-700 text-sm">
            <strong>School:</strong> {schoolName}
          </Text>
          <Text className="text-blue-700 text-sm">
            <strong>District:</strong> {districtName}
          </Text>
        </Section>

        <EmailParagraph>
          The following rating scales have been selected specifically for{' '}
          {studentName}'s evaluation. Each scale focuses on different aspects of
          behavior and development:
        </EmailParagraph>

        {ratingScales.map((scale, index) => (
          <Section
            key={index}
            className="my-4 rounded-lg border border-teal-200 border-solid bg-teal-50 px-4"
          >
            <Text className="mb-3 font-semibold text-base text-teal-900">
              {scale.name}
            </Text>
            <Text className="my-2 text-sm text-teal-700 leading-relaxed">
              {scale.description}
            </Text>
            <Text className="my-2 text-sm text-teal-600">
              <strong>Estimated Time:</strong> {scale.estimatedTime}
            </Text>
            <div className="mb-4 text-center">
              <EmailButton href={scale.link}>Complete {scale.name}</EmailButton>
            </div>
          </Section>
        ))}

        <Section className="my-4 rounded-lg border border-gray-200 border-solid bg-gray-50 px-4">
          <Text className="mb-2 font-semibold text-gray-800 text-sm">
            Instructions for Completion
          </Text>
          <Text className="my-2 text-gray-700 text-sm">
            • Click the button for each rating scale to access the secure form
          </Text>
          <Text className="my-2 text-gray-700 text-sm">
            • Answer all questions based on your observations of {studentName}
          </Text>
          <Text className="my-2 text-gray-700 text-sm">
            • Save your progress - you can return to complete later if needed
          </Text>
          <Text className="mt-2 text-gray-700 text-sm">
            • Submit each form when complete - you'll receive a confirmation
          </Text>
        </Section>

        <EmailParagraph>
          Your honest and detailed responses help us understand {studentName}'s
          strengths and areas where additional support may be beneficial. This
          information directly contributes to developing appropriate educational
          strategies and interventions.
        </EmailParagraph>

        <Section className="my-4 rounded-lg border border-purple-200 border-solid bg-purple-50 px-4">
          <Text className="mb-2 font-semibold text-purple-800 text-sm">
            Questions or Need Help?
          </Text>
          <Text className="mt-2 text-purple-700 text-sm">
            <strong>Psychologist:</strong>{' '}
            <EmailLink href={`mailto:${psychologistEmail}`}>
              {psychologistName}
            </EmailLink>
          </Text>
        </Section>

        <EmailParagraph>
          Thank you for taking the time to complete these important assessments.
          Your input is invaluable in helping us provide the best possible
          support for {studentName}'s educational journey.
        </EmailParagraph>

        <EmailFooter />
      </EmailContainer>
    </EmailStructure>
  );
}

function getPreviewValues(
  props: RatingScalesEmailProps
): RatingScalesEmailProps {
  return process.env.IS_PREVIEW === 'true'
    ? {
        recipientName: 'Sarah Johnson',
        studentName: 'Emma Davis',
        studentGrade: '3rd Grade',
        schoolName: 'Lincoln Elementary School',
        districtName: 'Springfield Unified School District',
        psychologistName: 'Dr. Jane Smith',
        psychologistEmail: '<EMAIL>',
        ratingScales: [
          {
            name: 'BASC-3 Teacher Rating Scale',
            description:
              'Assesses behavioral and emotional strengths and difficulties in school settings, including attention, learning problems, and social skills.',
            estimatedTime: '15-20 minutes',
            link: 'https://app.lilypadlearning.com/rating-scales/basc3-teacher',
          },
          {
            name: 'Conners 4 Teacher Form',
            description:
              'Evaluates ADHD symptoms and related behavioral concerns, including hyperactivity, inattention, and executive functioning.',
            estimatedTime: '10-15 minutes',
            link: 'https://app.lilypadlearning.com/rating-scales/conners4-teacher',
          },
          {
            name: 'Adaptive Behavior Assessment System',
            description:
              'Measures practical life skills and adaptive behaviors necessary for independence and daily functioning.',
            estimatedTime: '20-25 minutes',
            link: 'https://app.lilypadlearning.com/rating-scales/abas-teacher',
          },
        ],
      }
    : props;
}
