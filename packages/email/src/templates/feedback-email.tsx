import type * as React from 'react';
import type { FeedbackEmailProps } from '../types';

import { Section, Text } from '@react-email/components';
import { EmailContainer, EmailStructure } from '../components/email-components';
import { EmailFooter } from '../components/email-footer';
import { EmailHeader } from '../components/email-header';

export default function FeedbackEmail(
  props: FeedbackEmailProps
): React.JSX.Element {
  const { organizationName, name, email, category, message } =
    getPreviewValues(props);

  const previewText = `Feedback from ${name} for ${organizationName}`;

  return (
    <EmailStructure previewText={previewText}>
      <EmailContainer>
        <EmailHeader />

        <Text className="mb-6 font-bold text-2xl text-gray-950">
          New Feedback Received
        </Text>

        <Section className="rounded-lg border border-blue-200 border-solid bg-blue-50 px-4">
          <Text className="font-semibold text-blue-800 text-sm">
            Feedback Details
          </Text>

          <Section>
            <div className="flex">
              <Text className="mt-0 mr-2 w-24 font-medium text-blue-800 text-sm">
                Organization:
              </Text>
              <Text className="mt-0 flex-1 text-blue-700 text-sm">
                {organizationName}
              </Text>
            </div>

            <div className="flex">
              <Text className="mt-0 mr-2 w-24 font-medium text-blue-800 text-sm">
                Name:
              </Text>
              <Text className="mt-0 flex-1 text-blue-700 text-sm">{name}</Text>
            </div>

            <div className="flex">
              <Text className="mt-0 mr-2 w-24 font-medium text-blue-800 text-sm">
                Email:
              </Text>
              <Text className="mt-0 flex-1 text-blue-700 text-sm">{email}</Text>
            </div>

            <div className="flex">
              <Text className="mt-0 mr-2 w-24 font-medium text-blue-800 text-sm">
                Category:
              </Text>
              <Text className="mt-0 flex-1 text-blue-700 text-sm">
                {category}
              </Text>
            </div>
          </Section>
        </Section>

        <Section>
          <Text className="mb-2 font-semibold text-base text-gray-950">
            Message:
          </Text>
          <Text className="mt-2 rounded-lg border border-gray-200 border-solid bg-gray-50 p-4 text-base text-gray-700 leading-relaxed">
            {message}
          </Text>
        </Section>

        <EmailFooter />
      </EmailContainer>
    </EmailStructure>
  );
}

function getPreviewValues(props: FeedbackEmailProps): FeedbackEmailProps {
  return process.env.IS_PREVIEW === 'true'
    ? {
        organizationName: 'Acme Inc.',
        name: 'John Doe',
        email: '<EMAIL>',
        category: 'Feature Request',
        message:
          'I would like to request a feature that allows me to change my email address. This would be very helpful for users who need to update their contact information.',
      }
    : props;
}
