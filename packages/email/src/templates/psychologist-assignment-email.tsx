import type * as React from 'react';
import type { PsychologistAssignmentEmailProps } from '../types';

import { Img, Section, Text } from '@react-email/components';
import {
  EmailButton,
  EmailContainer,
  EmailLink,
  EmailParagraph,
  EmailStructure,
} from '../components/email-components';
import { EmailFooter } from '../components/email-footer';
import { EmailHeader } from '../components/email-header';

export default function PsychologistAssignmentEmail(
  props: PsychologistAssignmentEmailProps
): React.JSX.Element {
  const {
    recipientName,
    psychologistN<PERSON>,
    psychologistE<PERSON>,
    psychologistAvatar,
    psychologistBio,
    districtName,
  } = getPreviewValues(props);

  const previewText = `Meet ${psychologistName}, your new assigned psychologist for ${districtName}`;

  return (
    <EmailStructure previewText={previewText}>
      <EmailContainer>
        <EmailHeader />

        <Text className="mb-6 font-bold text-2xl text-gray-950">
          New Psychologist Assignment for{' '}
          <span className="text-[var(--primary)]">{districtName}</span>
        </Text>

        <EmailParagraph>Dear {recipientName},</EmailParagraph>

        <EmailParagraph>
          We're pleased to introduce your newly assigned psychologist who will
          be supporting special education services in your district. Please join
          us in welcoming them to the team.
        </EmailParagraph>

        <Section className="my-4 rounded-lg border border-teal-200 border-solid bg-teal-50 px-4">
          <Text className="mb-2 font-semibold text-sm text-teal-800">
            Psychologist Profile
          </Text>

          <div className="mt-2 flex flex-col items-start">
            <div className="flex items-start gap-4">
              <Img
                src={psychologistAvatar}
                alt={`${psychologistName} profile picture`}
                width={80}
                height={80}
                className="rounded-lg border border-teal-200 border-solid"
              />

              <div className="flex-1">
                <Text className="mb-1 font-semibold text-gray-950 text-lg">
                  {psychologistName}
                </Text>
                <Text className="mb-2 text-sm text-teal-700">
                  Licensed School Psychologist
                </Text>
              </div>
            </div>
            <Text className="text-gray-700 text-sm leading-relaxed">
              {psychologistBio}
            </Text>
          </div>
        </Section>

        <Section className="my-4 rounded-lg border border-blue-200 border-solid bg-blue-50 px-4">
          <Text className="mb-2 font-semibold text-blue-800 text-sm">
            Contact Information
          </Text>
          <Text className="mt-2 text-blue-700 text-sm">
            <strong>Email:</strong>{' '}
            <EmailLink href={`mailto:${psychologistEmail}`}>
              {psychologistEmail}
            </EmailLink>
          </Text>
          <Text className="mt-1 text-blue-700 text-sm">
            <strong>District:</strong> {districtName}
          </Text>
        </Section>

        <EmailParagraph>
          {psychologistName} will be working closely with your team to provide
          comprehensive psychological assessments, consultation services, and
          support for students with special needs. They are available to
          collaborate on IEP planning, behavioral interventions, and educational
          strategies.
        </EmailParagraph>

        <Section className="my-4 text-center">
          <EmailButton
            href={`mailto:${psychologistEmail}?subject=Welcome to ${districtName}`}
          >
            Send Welcome Message
          </EmailButton>
        </Section>

        <EmailParagraph>
          Please feel free to reach out to {psychologistName} to schedule
          initial meetings or discuss any ongoing cases that would benefit from
          psychological services.
        </EmailParagraph>

        <EmailParagraph>
          If you have any questions about this assignment or need additional
          support, please don't hesitate to contact our team.
        </EmailParagraph>

        <EmailFooter />
      </EmailContainer>
    </EmailStructure>
  );
}

function getPreviewValues(
  props: PsychologistAssignmentEmailProps
): PsychologistAssignmentEmailProps {
  return process.env.IS_PREVIEW === 'true'
    ? {
        recipientName: 'John Doe',
        psychologistName: 'Dr. Jane Smith',
        psychologistEmail: '<EMAIL>',
        psychologistAvatar: 'https://i.pravatar.cc/300',
        psychologistBio:
          'Dr. Smith is a licensed school psychologist with over 10 years of experience in special education assessment and intervention. She specializes in autism spectrum disorders, learning disabilities, and behavioral support planning.',
        districtName: 'Springfield Unified School District',
      }
    : props;
}
