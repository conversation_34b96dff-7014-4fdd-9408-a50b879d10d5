import type * as React from 'react';
import type { FinalReportEmailProps } from '../types';

import { Section, Text } from '@react-email/components';
import {
  EmailButton,
  EmailContainer,
  EmailParagraph,
  EmailStructure,
} from '../components/email-components';
import { EmailFooter } from '../components/email-footer';
import { EmailHeader } from '../components/email-header';

export default function FinalReportEmail(
  props: FinalReportEmailProps
): React.JSX.Element {
  const {
    recipientName,
    studentName,
    districtName,
    schoolName,
    finalReportLink,
  } = getPreviewValues(props);

  const previewText = `Final evaluation report for ${studentName} is ready for review`;

  return (
    <EmailStructure previewText={previewText}>
      <EmailContainer>
        <EmailHeader />

        <Text className="mb-6 font-bold text-2xl text-gray-950">
          Final Evaluation Report Ready for Review
        </Text>

        <EmailParagraph>Dear {recipientName},</EmailParagraph>

        <EmailParagraph>
          The comprehensive psychological evaluation for{' '}
          <strong>{studentName}</strong> has been completed and is now available
          for your review prior to the upcoming IEP meeting.
        </EmailParagraph>

        <Section className="my-4 rounded-lg border border-green-200 border-solid bg-green-50 px-4">
          <Text className="mb-2 font-semibold text-green-800 text-sm">
            Student Information
          </Text>
          <div className="mt-2">
            <Text className="text-green-700 text-sm">
              <strong>Student:</strong> {studentName}
            </Text>
            <Text className="mt-1 text-green-700 text-sm">
              <strong>School:</strong> {schoolName}
            </Text>
            <Text className="mt-1 text-green-700 text-sm">
              <strong>District:</strong> {districtName}
            </Text>
          </div>
        </Section>

        <Section className="my-4 rounded-lg border border-amber-200 border-solid bg-amber-50 px-4">
          <Text className="mb-2 font-semibold text-amber-800 text-sm">
            Important Notice
          </Text>
          <Text className="mt-2 text-amber-700 text-sm">
            Please review this report thoroughly before the IEP meeting. This
            comprehensive evaluation contains assessment results,
            recommendations, and proposed educational strategies that will be
            essential for developing an effective individualized education
            program.
          </Text>
        </Section>

        <EmailParagraph>
          The report includes detailed findings from psychological assessments,
          cognitive evaluations, behavioral observations, and educational
          recommendations. Your input and review are crucial for ensuring the
          best possible outcomes for {studentName}.
        </EmailParagraph>

        <Section className="my-4 text-center">
          <EmailButton href={finalReportLink}>View Final Report</EmailButton>
        </Section>

        <Section className="my-4 rounded-lg border border-blue-200 border-solid bg-blue-50 px-4">
          <Text className="mb-2 font-semibold text-blue-800 text-sm">
            What's Included in This Report
          </Text>
          <div className="mt-2">
            <Text className="text-blue-700 text-sm">
              • Comprehensive psychological assessment results
            </Text>
            <Text className="mt-1 text-blue-700 text-sm">
              • Cognitive and academic performance analysis
            </Text>
            <Text className="mt-1 text-blue-700 text-sm">
              • Behavioral observations and social-emotional assessment
            </Text>
            <Text className="mt-1 text-blue-700 text-sm">
              • Educational recommendations and intervention strategies
            </Text>
            <Text className="mt-1 text-blue-700 text-sm">
              • Proposed IEP goals and accommodations
            </Text>
          </div>
        </Section>

        <EmailParagraph>
          If you have any questions about the report or need clarification on
          any recommendations before the IEP meeting, please don't hesitate to
          reach out to the evaluation team.
        </EmailParagraph>

        <EmailParagraph>
          Thank you for your continued collaboration in supporting {studentName}
          's educational journey.
        </EmailParagraph>

        <EmailFooter />
      </EmailContainer>
    </EmailStructure>
  );
}

function getPreviewValues(props: FinalReportEmailProps): FinalReportEmailProps {
  return process.env.IS_PREVIEW === 'true'
    ? {
        recipientName: 'Sarah Johnson',
        studentName: 'Alex Martinez',
        districtName: 'Springfield Unified School District',
        schoolName: 'Roosevelt Elementary School',
        finalReportLink:
          'https://lilypadlearning.com/reports/final-evaluation-alex-martinez',
      }
    : props;
}
