import type * as React from 'react';
import type { WelcomeEmailProps } from '../types';

import { APP_NAME } from '@lilypad/shared';
import { Section, Text } from '@react-email/components';
import {
  EmailButton,
  EmailContainer,
  EmailParagraph,
  EmailStructure,
} from '../components/email-components';
import { EmailFooter } from '../components/email-footer';
import { EmailHeader } from '../components/email-header';

export default function WelcomeEmail(
  props: WelcomeEmailProps
): React.JSX.Element {
  const { name, getStartedLink } = getPreviewValues(props);

  return (
    <EmailStructure previewText={`Welcome to ${APP_NAME}!`}>
      <EmailContainer>
        <EmailHeader />

        <Text className="mb-6 font-bold text-2xl text-gray-950">
          Welcome to {APP_NAME}!
        </Text>

        <EmailParagraph>Hello {name},</EmailParagraph>

        <EmailParagraph>
          Thank you for signing up! We're excited to have you on board. Your
          account has been successfully created, and you're ready to start
          exploring our platform.
        </EmailParagraph>

        <Section className="my-4 text-center">
          <EmailButton href={getStartedLink}>Get Started</EmailButton>
        </Section>

        <EmailParagraph>
          If you have any questions or need assistance, please don't hesitate to
          reach out to our support team.
        </EmailParagraph>

        <EmailFooter />
      </EmailContainer>
    </EmailStructure>
  );
}

function getPreviewValues(props: WelcomeEmailProps): WelcomeEmailProps {
  return process.env.IS_PREVIEW === 'true'
    ? {
        name: 'John Doe',
        getStartedLink: 'http://localhost:3000/auth/sign-in',
      }
    : props;
}
