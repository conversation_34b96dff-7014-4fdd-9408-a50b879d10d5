import type * as React from 'react';
import type { DistrictOnboardingEmailProps } from '../types';

import { APP_NAME } from '@lilypad/shared';
import { Section, Text } from '@react-email/components';
import {
  EmailButton,
  EmailContainer,
  EmailLink,
  EmailParagraph,
  EmailStructure,
} from '../components/email-components';
import { EmailFooter } from '../components/email-footer';
import { EmailHeader } from '../components/email-header';

export default function DistrictOnboardingEmail(
  props: DistrictOnboardingEmailProps
): React.JSX.Element {
  const {
    recipientName,
    invitedByName,
    invitedByEmail,
    districtName,
    role,
    schoolNames,
    inviteLink,
  } = getPreviewValues(props);

  return (
    <EmailStructure
      previewText={`You have been added to ${districtName} as ${role} on ${APP_NAME}`}
    >
      <EmailContainer>
        <EmailHeader />

        <Text className="mb-6 font-bold text-2xl text-gray-950">
          You have been added to{' '}
          <span className="text-[var(--primary)]">{districtName}</span> on{' '}
          <span className="text-[var(--primary)]">{APP_NAME}</span>
        </Text>

        <EmailParagraph>Hi {recipientName},</EmailParagraph>

        <EmailParagraph>
          <strong>{invitedByName}</strong> (
          <EmailLink href={`mailto:${invitedByEmail}`}>
            {invitedByEmail}
          </EmailLink>
          ) has added you to <strong>{districtName}</strong> as a{' '}
          <strong>{role}</strong> on <strong>{APP_NAME}</strong>.
        </EmailParagraph>

        {schoolNames.length > 0 && (
          <Section className="my-4 rounded-lg border border-gray-200 border-solid bg-gray-50 px-4">
            <Text className="mb-2 font-semibold text-gray-950 text-sm">
              School Assignments
            </Text>
            <Text className="mt-2 text-gray-700 text-sm">
              You will have access to:{' '}
              <span
                // biome-ignore lint/security/noDangerouslySetInnerHtml: <explanation>
                dangerouslySetInnerHTML={{
                  __html: formatSchoolList(schoolNames),
                }}
              />
            </Text>
          </Section>
        )}

        <Section className="my-4 text-center">
          <EmailButton href={inviteLink}>Sign In to Get Started</EmailButton>
        </Section>

        <EmailParagraph>
          or copy and paste this URL into your browser:{' '}
          <EmailLink href={inviteLink}>{inviteLink}</EmailLink>
        </EmailParagraph>

        <EmailFooter />
      </EmailContainer>
    </EmailStructure>
  );
}

function formatSchoolList(schools: string[]) {
  if (schools.length === 0) return 'No specific schools assigned';
  if (schools.length === 1) return `<strong>${schools[0]}</strong>`;
  if (schools.length === 2)
    return `<strong>${schools[0]}</strong> and <strong>${schools[1]}</strong>`;
  return `${schools
    .slice(0, -1)
    .map((school) => `<strong>${school}</strong>`)
    .join(', ')}, and <strong>${schools[schools.length - 1]}</strong>`;
}

function getPreviewValues(
  props: DistrictOnboardingEmailProps
): DistrictOnboardingEmailProps {
  return process.env.IS_PREVIEW === 'true'
    ? {
        recipientName: 'John Doe',
        invitedByName: 'Jane Smith',
        invitedByEmail: '<EMAIL>',
        districtName: 'Springfield Unified School District',
        role: 'Special Education Coordinator',
        schoolNames: [
          'Lincoln Elementary',
          'Roosevelt Middle School',
          'Washington High School',
        ],
        inviteLink: 'http://localhost:3000/auth/sign-in',
      }
    : props;
}
