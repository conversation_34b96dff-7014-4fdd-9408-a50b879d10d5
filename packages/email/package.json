{"name": "@lilypad/email", "private": true, "version": "0.0.0", "type": "module", "scripts": {"clean": "git clean -xdf node_modules", "format": "biome format --write .", "lint": "biome lint --write .", "typecheck": "tsc --noEmit", "preview": "IS_PREVIEW=true dotenv -e ./.env -- email dev -d ./src/templates --port 5555", "export": "dotenv -e ./.env -- email export -d ./src/templates/supabase --outDir ../../supabase/templates --pretty"}, "devDependencies": {"@lilypad/typescript": "workspace:*", "@types/node": "^22.15.3", "@types/nodemailer": "^6.4.17", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "dotenv-cli": "^8.0.0"}, "dependencies": {"@lilypad/shared": "workspace:*", "@react-email/components": "^0.0.41", "@react-email/render": "^1.1.2", "@react-email/tailwind": "^1.0.5", "@t3-oss/env-nextjs": "^0.13.4", "nodemailer": "^7.0.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-email": "^3.0.7", "resend": "^4.5.1", "zod": "^3.25.7"}, "exports": {"./provider": "./src/provider/index.ts", "./keys": "./src/keys.ts", "./service": "./src/service/index.ts", "./types": "./src/types.ts"}}