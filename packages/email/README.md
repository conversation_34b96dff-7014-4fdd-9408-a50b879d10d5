# `@lilypad/email`

This package contains the email sending logic. It provides flexibility by allowing us to choose between different mailers to suit our needs.

## Providers

- Nodemailer: A popular Node.js library for sending emails via SMTP. Great for self-hosted or custom SMTP setups.
- Resend: A developer-friendly email API that uses AWS SES under the hood, simplifying email setup while maintaining reliability.
