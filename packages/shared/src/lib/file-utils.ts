export interface SerializableFile {
  name: string;
  size: number;
  type: string;
  data: string; // Base64 encoded
}

/**
 * Converts a File object to serializable data for API transmission
 */
export async function fileToSerializable(
  file: File
): Promise<SerializableFile> {
  return await new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      const result = reader.result as string;
      resolve({
        name: file.name,
        size: file.size,
        type: file.type,
        data: result.split(',')[1], // Remove data:mime;base64, prefix
      });
    };
    reader.onerror = () => reject(new Error('Failed to read file'));
    reader.readAsDataURL(file);
  });
}

/**
 * Converts serializable file data back to a File object
 */
export function serializableToFile(serializable: SerializableFile): File {
  const byteCharacters = atob(serializable.data);
  const byteNumbers = new Array(byteCharacters.length);

  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
  }

  const byteArray = new Uint8Array(byteNumbers);
  const blob = new Blob([byteArray], { type: serializable.type });

  return new File([blob], serializable.name, { type: serializable.type });
}

// Validation utilities
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const ALLOWED_FILE_TYPES = [
  'application/pdf',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'text/plain',
];

/**
 * Validates a file for upload based on size and type restrictions
 */
export function validateFileForUpload(file: File): {
  valid: boolean;
  error?: string;
} {
  if (file.size > MAX_FILE_SIZE) {
    return { valid: false, error: 'File size exceeds 5MB limit' };
  }

  if (!ALLOWED_FILE_TYPES.includes(file.type)) {
    return { valid: false, error: 'Invalid file type' };
  }

  return { valid: true };
}

/**
 * Type guard to check if a file is a SerializableFile
 */
export function isSerializableFile(
  file: File | SerializableFile
): file is SerializableFile {
  return (
    typeof file === 'object' && 'data' in file && typeof file.data === 'string'
  );
}

/**
 * Type guard to check if a file is a File object
 */
export function isFileObject(file: File | SerializableFile): file is File {
  return file instanceof File;
}
