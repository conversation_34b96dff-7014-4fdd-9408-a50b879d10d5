export const APP_NAME = 'Lilypad';
export const APP_DESCRIPTION = 'Supporting students made easy.';

export type EmptyProps<T extends React.ElementType> = Omit<
  React.ComponentProps<T>,
  keyof React.ComponentProps<T>
>;

export type Maybe<TType> = TType | undefined | null;

export type IsDefinedGuard<T> = Exclude<T, undefined | null>;

export interface SearchParams {
  [key: string]: string | string[] | undefined;
}

export function isDefined<T>(val: Maybe<T>): val is IsDefinedGuard<T> {
  return (
    typeof val !== 'undefined' &&
    val !== undefined &&
    val !== null &&
    val !== Number.POSITIVE_INFINITY
  );
}

export function isString<T>(obj: T): boolean {
  return (
    obj !== null &&
    typeof obj !== 'undefined' &&
    Object.prototype.toString.call(obj) === '[object String]'
  );
}

export function createMetadataTitle(title: string, addSuffix = true): string {
  if (!addSuffix) {
    return title;
  }
  if (!title) {
    return APP_NAME;
  }

  return `${title} | ${APP_NAME}`;
}

export function capitalize(str: string): string {
  if (!str) {
    return str;
  }

  if (str.length === 1) {
    return str.charAt(0).toUpperCase();
  }

  return str.charAt(0).toUpperCase() + str.slice(1);
}

const INITIALS_REGEX = /\s+/;

export function getInitials(name: string): string {
  if (!name) {
    return '';
  }
  return name
    .replace(INITIALS_REGEX, ' ')
    .split(' ')
    .slice(0, 2)
    .map((v) => v?.[0].toUpperCase())
    .join('');
}

export function getTimeSlot(hours: number, minutes: number): Date {
  const date = new Date(0);

  date.setMilliseconds(0);
  date.setSeconds(0);
  date.setMinutes(0);
  date.setHours(0);

  date.setHours(hours);
  date.setMinutes(minutes);

  return date;
}

const SLUG_REGEX = /[^\w\s-]/g;
const SLUG_REPLACE_REGEX = /\s+/g;
const SLUG_REPLACE_REGEX_2 = /-+/g;

export const generateSlug = (name: string) => {
  return name
    .toLowerCase()
    .replace(SLUG_REGEX, '') // Remove special characters
    .replace(SLUG_REPLACE_REGEX, '-') // Replace spaces with hyphens
    .replace(SLUG_REPLACE_REGEX_2, '-') // Replace multiple hyphens with single
    .trim();
};

export * from './lib/file-utils';
