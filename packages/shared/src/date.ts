export function calculateDueDate(params: {
  type: 'business_days' | 'calendar_days' | 'hours';
  amount: number;
  fromDate?: Date;
}): Date {
  const { type, amount, fromDate = new Date() } = params;

  switch (type) {
    case 'business_days':
      return addBusinessDays(fromDate, amount);
    case 'calendar_days':
      return addCalendarDays(fromDate, amount);
    case 'hours':
      return addHours(fromDate, amount);
    default:
      return fromDate;
  }
}

function addBusinessDays(fromDate: Date, amount: number): Date {
  const currentDate = new Date(fromDate);
  let remainingDays = amount;
  const isWeekend = currentDate.getDay() === 0 || currentDate.getDay() === 6;

  while (remainingDays > 0) {
    currentDate.setDate(currentDate.getDate() + 1);
    if (!isWeekend) {
      remainingDays--;
    }
  }
  return currentDate;
}

function addCalendarDays(fromDate: Date, amount: number): Date {
  const date = new Date(fromDate);
  date.setDate(date.getDate() + amount);
  return date;
}

function addHours(fromDate: Date, amount: number): Date {
  const date = new Date(fromDate);
  date.setHours(date.getHours() + amount);
  return date;
}

export function getRelativeTimeAgo(dateString: string): string {
  const date = new Date(dateString);

  // Validate the date
  if (Number.isNaN(date.getTime())) {
    throw new Error(`Invalid date string: ${dateString}`);
  }

  const now = new Date();
  const timeDifferenceInSeconds = Math.floor(
    (now.getTime() - date.getTime()) / 1000
  );

  // Handle future dates
  if (timeDifferenceInSeconds < 0) {
    return 'just now';
  }

  const TIME_UNITS = [
    { unit: 'year', seconds: 365 * 24 * 60 * 60, abbreviation: 'y' },
    { unit: 'month', seconds: 30 * 24 * 60 * 60, abbreviation: 'mo' },
    { unit: 'day', seconds: 24 * 60 * 60, abbreviation: 'd' },
    { unit: 'hour', seconds: 60 * 60, abbreviation: 'h' },
    { unit: 'minute', seconds: 60, abbreviation: 'm' },
    { unit: 'second', seconds: 1, abbreviation: 's' },
  ];

  for (const { seconds, abbreviation } of TIME_UNITS) {
    if (timeDifferenceInSeconds >= seconds) {
      const value = Math.floor(timeDifferenceInSeconds / seconds);
      return `${value}${abbreviation} ago`;
    }
  }

  return 'just now';
}

export function getCurrentTimestamp(): string {
  return new Date().toISOString();
}

export function formatDate(
  date: Date | string | number | undefined,
  opts: Intl.DateTimeFormatOptions = {}
) {
  if (!date) {
    return '';
  }

  try {
    return new Intl.DateTimeFormat('en-US', {
      month: opts.month ?? 'long',
      day: opts.day ?? 'numeric',
      year: opts.year ?? 'numeric',
      ...opts,
    }).format(new Date(date));
  } catch (_err) {
    return '';
  }
}

/**
 * Get the ordinal suffix for a day number (1st, 2nd, 3rd, etc.)
 */
function getOrdinalSuffix(day: number): string {
  if (day > 3 && day < 21) {
    return 'th';
  }
  switch (day % 10) {
    case 1:
      return 'st';
    case 2:
      return 'nd';
    case 3:
      return 'rd';
    default:
      return 'th';
  }
}

// Cache for date formatters to improve performance
const dateFormattersCache = {
  time: new Intl.DateTimeFormat('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
  }),
  weekday: new Intl.DateTimeFormat('en-US', {
    weekday: 'long',
  }),
  month: new Intl.DateTimeFormat('en-US', {
    month: 'long',
  }),
};

/**
 * Format a date with contextual information based on proximity to today
 * @param date - The date to format
 * @returns A formatted string like "Today at 9:30 AM", "On Monday at 11:00 AM", etc.
 */
export function getRelativeDate(
  date: Date | string | number | undefined
): string {
  if (!date) {
    return 'Unknown date';
  }

  try {
    const inputDate =
      typeof date === 'object' && date instanceof Date ? date : new Date(date);
    const inputTime = inputDate.getTime();

    // Validate the date
    if (Number.isNaN(inputTime)) {
      return 'Unknown date';
    }

    const todayStart = new Date().setHours(0, 0, 0, 0);
    const inputDateStart = new Date(inputTime).setHours(0, 0, 0, 0);

    // Calculate days difference
    const daysDiff = Math.floor(
      (inputDateStart - todayStart) / (1000 * 60 * 60 * 24)
    );
    const timeString = dateFormattersCache.time.format(inputDate);

    // Today
    if (daysDiff === 0) {
      return `Today at ${timeString}`;
    }

    // Get day of week for week calculations
    const today = new Date(todayStart);
    const currentDayOfWeek = today.getDay();
    const mondayOffset = currentDayOfWeek === 0 ? -6 : 1 - currentDayOfWeek;

    // This week (future days)
    if (daysDiff > 0 && daysDiff <= 7 + mondayOffset) {
      const dayName = dateFormattersCache.weekday.format(inputDate);
      return `On ${dayName} at ${timeString}`;
    }

    // Last week
    if (daysDiff >= mondayOffset - 7 && daysDiff < mondayOffset) {
      const dayName = dateFormattersCache.weekday.format(inputDate);
      return `Last ${dayName} at ${timeString}`;
    }

    // Otherwise, full date format
    const monthName = dateFormattersCache.month.format(inputDate);
    const day = inputDate.getDate();
    const year = inputDate.getFullYear();
    const ordinalDay = `${day}${getOrdinalSuffix(day)}`;

    return `On ${monthName} ${ordinalDay}, ${year} at ${timeString}`;
  } catch (_err) {
    return 'Unknown date';
  }
}
