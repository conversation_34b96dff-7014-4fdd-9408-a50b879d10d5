// Convention:
// - Everything lowercase is an object
// - Everything uppercase is a string (the route)

import { keys } from './keys';

export const baseUrl = {
  App: keys().NEXT_PUBLIC_APP_URL,
  Site: keys().NEXT_PUBLIC_SITE_URL,
} as const;

export const routes = {
  app: {
    Index: `${baseUrl.App}/`,
    Api: `${baseUrl.App}/api`,
    auth: {
      Callback: `${baseUrl.App}/auth/callback`,
      changeEmail: {
        Expired: `${baseUrl.App}/auth/change-email/expired`,
        Index: `${baseUrl.App}/auth/change-email`,
        Success: `${baseUrl.App}/auth/change-email/success`,
      },
      Error: `${baseUrl.App}/auth/error`,
      forgetPassword: {
        Index: `${baseUrl.App}/auth/forgot-password`,
        Success: `${baseUrl.App}/auth/forgot-password/success`,
      },
      Index: `${baseUrl.App}/auth`,
      RecoveryCode: `${baseUrl.App}/auth/recovery-code`,
      resetPassword: {
        Expired: `${baseUrl.App}/auth/reset-password/expired`,
        Index: `${baseUrl.App}/auth/reset-password`,
        Request: `${baseUrl.App}/auth/reset-password/request`,
        Success: `${baseUrl.App}/auth/reset-password/success`,
      },
      SignIn: `${baseUrl.App}/auth/sign-in`,
      SignUp: `${baseUrl.App}/auth/sign-up`,
      totp: {
        Index: `${baseUrl.App}/auth/totp`,
        Verify: `${baseUrl.App}/auth/totp/verify`,
      },
      verifyEmail: {
        Expired: `${baseUrl.App}/auth/verify-email/expired`,
        Index: `${baseUrl.App}/auth/verify-email`,
        Request: `${baseUrl.App}/auth/verify-email/request`,
        Success: `${baseUrl.App}/auth/verify-email/success`,
      },
    },
    joinRequest: {
      Index: `${baseUrl.App}/join-request`,
    },
    dashboard: {
      Index: `${baseUrl.App}/dashboard`,
    },
    districts: {
      Index: `${baseUrl.App}/districts`,
    },
    settings: {
      Index: `${baseUrl.App}/settings`,
      security: {
        Index: `${baseUrl.App}/settings/security`,
      },
      notifications: {
        Index: `${baseUrl.App}/settings/notifications`,
      },
      district: {
        Index: `${baseUrl.App}/settings/district`,
        schools: {
          Index: `${baseUrl.App}/settings/district/schools`,
        },
        members: {
          Index: `${baseUrl.App}/settings/district/members`,
        },
      },
    },
    cases: {
      Index: `${baseUrl.App}/cases`,
    },
    calendar: {
      Index: `${baseUrl.App}/calendar`,
    },
    students: {
      Index: `${baseUrl.App}/students`,
      add: {
        Index: `${baseUrl.App}/students/add`,
      },
    },
    invitations: {
      AlreadyAccepted: `${baseUrl.App}/invitations/already-accepted`,
      Index: `${baseUrl.App}/invitations`,
      Request: `${baseUrl.App}/invitations/request`,
      Revoked: `${baseUrl.App}/invitations/revoked`,
    },
    onboarding: {
      Index: `${baseUrl.App}/onboarding`,
      Organization: `${baseUrl.App}/onboarding/organization`,
      User: `${baseUrl.App}/onboarding/user`,
    },
    organizations: {
      Index: `${baseUrl.App}/organizations`,
      slug: {
        Contacts: `${baseUrl.App}/organizations/[slug]/contacts`,
        Home: `${baseUrl.App}/organizations/[slug]/home`,
        Index: `${baseUrl.App}/organizations/[slug]`,
        settings: {
          account: {
            Index: `${baseUrl.App}/organizations/[slug]/settings/account`,
            Notifications: `${baseUrl.App}/organizations/[slug]/settings/account/notifications`,
            Profile: `${baseUrl.App}/organizations/[slug]/settings/account/profile`,
            Security: `${baseUrl.App}/organizations/[slug]/settings/account/security`,
          },
          Index: `${baseUrl.App}/organizations/[slug]/settings`,
          organization: {
            Billing: `${baseUrl.App}/organizations/[slug]/settings/organization/billing`,
            Developers: `${baseUrl.App}/organizations/[slug]/settings/organization/developers`,
            General: `${baseUrl.App}/organizations/[slug]/settings/organization/general`,
            Index: `${baseUrl.App}/organizations/[slug]/settings/organization`,
            Members: `${baseUrl.App}/organizations/[slug]/settings/organization/members`,
          },
        },
      },
    },
  },
  site: {
    Api: `${baseUrl.Site}/api`,
    Blog: `${baseUrl.Site}/blog`,
    Careers: `${baseUrl.Site}/careers`,
    Contact: `${baseUrl.Site}/contact`,
    CookiePolicy: `${baseUrl.Site}/cookie-policy`,
    Docs: `${baseUrl.Site}/docs`,
    Index: `${baseUrl.Site}/`,
    Pricing: `${baseUrl.Site}/pricing`,
    PrivacyPolicy: `${baseUrl.Site}/privacy-policy`,
    Roadmap: 'https://achromatic.canny.io',
    Story: `${baseUrl.Site}/story`,
    TermsOfUse: `${baseUrl.Site}/terms-of-use`,
  },
  logo: {
    LogoFull: `${baseUrl.App}/logo/logo-full.png`,
    LogoIcon: `${baseUrl.App}/logo/logo-icon.png`,
  },
} as const;

type ExtractSlugRoutes<T> = T extends Record<string, unknown>
  ? {
      [K in keyof T]: T[K] extends string
        ? T[K] extends `${string}[slug]${string}`
          ? T[K]
          : never
        : ExtractSlugRoutes<T[K]>;
    }[keyof T]
  : never;

type OrganizationsSlugRoutes = ExtractSlugRoutes<
  typeof routes.app.organizations.slug
>;

export function replaceOrgSlug(
  route: OrganizationsSlugRoutes,
  slug: string
): string {
  if (route.indexOf('[slug]') === -1) {
    throw new Error(
      `Invalid route: ${route}. Route must contain the placeholder [slug].`
    );
  }

  return route.replace('[slug]', slug);
}

export function getPathname(route: string, url: string): string {
  return new URL(route, url).pathname;
}
