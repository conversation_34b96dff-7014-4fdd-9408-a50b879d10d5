export const TIMEZONES = [
  {
    value: 'America/New_York',
    prefix: 'GMT -4:00',
    label: 'Eastern (Eastern Daylight Time)',
    offset: -4,
    abbrev: 'EDT',
    altName: 'Eastern Daylight Time',
  },
  {
    value: 'America/Chicago',
    prefix: 'GMT -5:00',
    label: 'Central (Central Daylight Time)',
    offset: -5,
    abbrev: 'CDT',
    altName: 'Central Daylight Time',
  },
  {
    value: 'America/Denver',
    prefix: 'GMT -6:00',
    label: 'Mountain (Mountain Daylight Time)',
    offset: -6,
    abbrev: 'MDT',
    altName: 'Mountain Daylight Time',
  },
  {
    value: 'America/Los_Angeles',
    prefix: 'GMT -7:00',
    label: 'Pacific (Pacific Daylight Time)',
    offset: -7,
    abbrev: 'PDT',
    altName: 'Pacific Daylight Time',
  },
  {
    value: 'America/Anchorage',
    prefix: 'GMT -8:00',
    label: 'Alaska (Alaska Daylight Time)',
    offset: -8,
    abbrev: 'AKDT',
    altName: 'Alaska Daylight Time',
  },
  {
    value: 'Pacific/Honolulu',
    prefix: 'GMT -10:00',
    label: 'Hawaii (Hawaii Standard Time)',
    offset: -10,
    abbrev: 'HST',
    altName: 'Hawaii Standard Time',
  },
  {
    value: 'America/Phoenix',
    prefix: 'GMT -7:00',
    label: 'Arizona (Mountain Standard Time)',
    offset: -7,
    abbrev: 'MST',
    altName: 'Mountain Standard Time',
  },
  {
    value: 'America/Indiana/Indianapolis',
    prefix: 'GMT -4:00',
    label: 'Indiana (Eastern Daylight Time)',
    offset: -4,
    abbrev: 'EDT',
    altName: 'Eastern Daylight Time',
  },
  {
    value: 'America/Kentucky/Louisville',
    prefix: 'GMT -4:00',
    label: 'Kentucky (Eastern Daylight Time)',
    offset: -4,
    abbrev: 'EDT',
    altName: 'Eastern Daylight Time',
  },
  {
    value: 'America/Detroit',
    prefix: 'GMT -4:00',
    label: 'Michigan (Eastern Daylight Time)',
    offset: -4,
    abbrev: 'EDT',
    altName: 'Eastern Daylight Time',
  },
  {
    value: 'America/Menominee',
    prefix: 'GMT -5:00',
    label: 'Michigan (Central Daylight Time)',
    offset: -5,
    abbrev: 'CDT',
    altName: 'Central Daylight Time',
  },
  {
    value: 'America/North_Dakota/Center',
    prefix: 'GMT -5:00',
    label: 'North Dakota (Central Daylight Time)',
    offset: -5,
    abbrev: 'CDT',
    altName: 'Central Daylight Time',
  },
  {
    value: 'America/North_Dakota/New_Salem',
    prefix: 'GMT -5:00',
    label: 'North Dakota (Central Daylight Time)',
    offset: -5,
    abbrev: 'CDT',
    altName: 'Central Daylight Time',
  },
  {
    value: 'America/North_Dakota/Beulah',
    prefix: 'GMT -5:00',
    label: 'North Dakota (Central Daylight Time)',
    offset: -5,
    abbrev: 'CDT',
    altName: 'Central Daylight Time',
  },
  {
    value: 'America/Boise',
    prefix: 'GMT -6:00',
    label: 'Idaho (Mountain Daylight Time)',
    offset: -6,
    abbrev: 'MDT',
    altName: 'Mountain Daylight Time',
  },
  {
    value: 'America/Juneau',
    prefix: 'GMT -8:00',
    label: 'Alaska (Alaska Daylight Time)',
    offset: -8,
    abbrev: 'AKDT',
    altName: 'Alaska Daylight Time',
  },
  {
    value: 'America/Sitka',
    prefix: 'GMT -8:00',
    label: 'Alaska (Alaska Daylight Time)',
    offset: -8,
    abbrev: 'AKDT',
    altName: 'Alaska Daylight Time',
  },
  {
    value: 'America/Metlakatla',
    prefix: 'GMT -8:00',
    label: 'Alaska (Alaska Daylight Time)',
    offset: -8,
    abbrev: 'AKDT',
    altName: 'Alaska Daylight Time',
  },
  {
    value: 'America/Yakutat',
    prefix: 'GMT -8:00',
    label: 'Alaska (Alaska Daylight Time)',
    offset: -8,
    abbrev: 'AKDT',
    altName: 'Alaska Daylight Time',
  },
  {
    value: 'America/Nome',
    prefix: 'GMT -8:00',
    label: 'Alaska (Alaska Daylight Time)',
    offset: -8,
    abbrev: 'AKDT',
    altName: 'Alaska Daylight Time',
  },
  {
    value: 'America/Adak',
    prefix: 'GMT -9:00',
    label: 'Hawaii-Aleutian (Hawaiian Daylight Time)',
    offset: -9,
    abbrev: 'HDT',
    altName: 'Hawaii-Aleutian Daylight Time',
  },
  {
    value: 'America/Puerto_Rico',
    prefix: 'GMT -4:00',
    label: 'Puerto Rico (Atlantic Standard Time)',
    offset: -4,
    abbrev: 'AST',
    altName: 'Atlantic Standard Time',
  },
];
