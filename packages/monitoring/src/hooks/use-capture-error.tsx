'use client';
import React from 'react';
import { useMonitoring } from './use-monitoring';
export function useCaptureError<Extra extends object>(
  error: unknown,
  extra?: Extra
): void {
  const provider = useMonitoring();
  const errorStringRef = React.useRef<string>('');

  React.useEffect(() => {
    if (!error) {
      return;
    }

    // Create a stable string representation of the error to prevent infinite loops
    const errorString =
      error instanceof Error
        ? `${error.name}:${error.message}:${error.stack}`
        : String(error);

    // Don't log the same error twice
    if (errorStringRef.current === errorString) {
      return;
    }

    try {
      errorStringRef.current = errorString;
      provider.captureError(error, extra);
    } catch (captureError) {
      console.error('Error capturing error', captureError);
    }
  }, [error, extra, provider]);
}
