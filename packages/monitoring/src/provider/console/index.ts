import type { ErrorContext, ErrorRequest, MonitoringProvider } from '../types';

// Safe JSON stringify that prevents circular references
function safeStringify(obj: unknown): string {
  try {
    return JSON.stringify(obj, (key, value) => {
      if (typeof value === 'object' && value !== null && key.length > 50) {
        // Avoid circular references by limiting depth
        return '[Circular]';
      }
      return value;
    });
  } catch {
    return '[Unable to stringify]';
  }
}

class ConsoleMonitoringProvider implements MonitoringProvider {
  withConfig<C>(nextConfig: C): C {
    return nextConfig;
  }

  async register(): Promise<void> {
    console.info('[Console Monitoring] Registered.');
    return await Promise.resolve();
  }

  captureRequestError(
    error: unknown,
    errorRequest: Readonly<ErrorRequest>,
    errorContext: Readonly<ErrorContext>
  ): void {
    console.info('[Console Monitoring] Request error occurred.');

    // Log error details if available
    this.captureError(error);

    // Log request details if available
    if (errorRequest) {
      console.info('[Console Monitoring] Request Details:', {
        path: errorRequest.path,
        method: errorRequest.method,
        headers: errorRequest.headers,
      });
    }

    // Log context details if available
    if (errorContext) {
      console.info('[Console Monitoring] Context Details:', {
        routerKind: errorContext.routerKind,
        routePath: errorContext.routePath,
        routeType: errorContext.routeType,
        renderSource: errorContext.renderSource || 'N/A',
        revalidateReason: errorContext.revalidateReason ?? 'N/A',
      });
    }
  }

  captureError<Extra extends object>(error: unknown, extra?: Extra): void {
    try {
      if (error instanceof Error) {
        console.error('[Console Monitoring] Error:', {
          name: error.name,
          message: error.message,
          stack: error.stack,
          extra,
        });
      } else if (typeof error === 'string') {
        console.error('[Console Monitoring] Error:', error, extra);
      } else if (error) {
        console.error(
          '[Console Monitoring] Unknown error:',
          safeStringify(error),
          extra
        );
      }
    } catch (logError) {
      // Fallback to basic console.error to prevent infinite loops
      console.error('[Console Monitoring] Failed to log error:', logError);
    }
  }

  captureEvent<Extra extends object>(event: string, extra?: Extra): void {
    try {
      console.info('[Console Monitoring] Event captured:', { event, extra });
    } catch {
      console.info('[Console Monitoring] Failed to log event:', event);
    }
  }

  setUser<Info extends { id: string }>(user: Info): void {
    try {
      console.info('[Console Monitoring] User tracked:', user);
    } catch {
      console.info('[Console Monitoring] Failed to log user');
    }
  }
}

export default new ConsoleMonitoringProvider();
