import { keys as sharedKeys } from '@lilypad/shared/keys';
import {
  captureEvent,
  captureException,
  captureRequestError,
  type Event as SentryEvent,
  type User as SentryUser,
  setUser,
  withSentryConfig,
} from '@sentry/nextjs';
import { keys } from '../../../keys';
import type { ErrorContext, ErrorRequest, MonitoringProvider } from '../types';

class SentryMonitoringProvider implements MonitoringProvider {
  withConfig<C>(nextConfig: C): C {
    return withSentryConfig(nextConfig, {
      org: keys().SENTRY_ORG,
      project: keys().SENTRY_PROJECT,
      authToken: keys().SENTRY_AUTH_TOKEN, // Required for uploading source maps
      silent: sharedKeys().ENVIRONMENT !== 'production', // Suppressing sdk build logs
      autoInstrumentServerFunctions: false,
      widenClientFileUpload: true,
      telemetry: false,
    });
  }

  async register(): Promise<void> {
    try {
      if (typeof window !== 'undefined') {
        const { initializeSentryBrowserClient } = await import(
          './sentry.client.config'
        );
        initializeSentryBrowserClient();
      } else if (keys().NEXT_RUNTIME === 'edge') {
        const { initializeSentryEdgeClient } = await import(
          './sentry.edge.config'
        );
        initializeSentryEdgeClient();
      } else {
        const { initializeSentryServerClient } = await import(
          './sentry.server.config'
        );
        initializeSentryServerClient();
      }
    } catch (error) {
      console.error('[Sentry Monitoring] Registration failed:', error);
    }
  }

  captureRequestError(
    error: unknown,
    errorRequest: Readonly<ErrorRequest>,
    errorContext: Readonly<ErrorContext>
  ) {
    return captureRequestError(error, errorRequest, errorContext);
  }

  captureError(error: unknown): string {
    return captureException(error);
  }

  captureEvent<Extra extends SentryEvent>(
    event: string,
    extra?: Extra
  ): string {
    return captureEvent({
      message: event,
      ...(extra ?? {}),
    });
  }

  setUser(user: SentryUser): void {
    setUser(user);
  }
}

export default new SentryMonitoringProvider();
