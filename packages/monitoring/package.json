{"name": "@lilypad/monitoring", "private": true, "version": "0.0.0", "type": "module", "scripts": {"clean": "git clean -xdf node_modules", "format": "biome format --write .", "lint": "biome lint --write .", "typecheck": "tsc --noEmit"}, "dependencies": {"@sentry/nextjs": "^9.21.0", "@t3-oss/env-nextjs": "^0.13.4", "@lilypad/shared": "workspace:*", "react": "^19.1.0", "react-dom": "^19.1.0", "zod": "^3.25.7"}, "devDependencies": {"@lilypad/typescript": "workspace:*", "@types/node": "^22.15.3", "@types/react": "19.1.0", "@types/react-dom": "19.1.1"}, "exports": {"./keys": "./keys.ts", "./hooks": "./src/hooks/index.ts", "./provider": "./src/provider/index.ts"}}