{"name": "@lilypad/core", "private": true, "version": "0.0.0", "type": "module", "scripts": {"clean": "git clean -xdf node_modules", "format": "biome format --write .", "lint": "biome lint --write .", "typecheck": "tsc --noEmit"}, "dependencies": {"@lilypad/db": "workspace:*", "@lilypad/email": "workspace:*", "@lilypad/shared": "workspace:*", "@lilypad/supabase": "workspace:*"}, "devDependencies": {"@lilypad/typescript": "workspace:*"}, "exports": {"./services/district-onboarding": "./src/services/district-onboarding/index.ts", "./services/students": "./src/services/students/index.ts", "./services/notifications": "./src/services/notifications/index.ts", "./services/tasks": "./src/services/tasks/index.ts"}}