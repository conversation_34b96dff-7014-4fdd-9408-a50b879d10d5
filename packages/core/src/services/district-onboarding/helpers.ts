import type {
  NewDistrictAvailability,
  NewDistrictPreference,
} from '@lilypad/db/schema';
import {
  AvailabilityTypeEnum,
  DayOfWeekEnum,
  PreferenceCategoryEnum,
  PreferenceTypeEnum,
} from '@lilypad/db/schema/enums';
import type { CreateDistrictOnboardingInput } from './types';

const DAY_MAPPING: Record<string, DayOfWeekEnum> = {
  monday: DayOfWeekEnum.MONDAY,
  tuesday: DayOfWeekEnum.TUESDAY,
  wednesday: DayOfWeekEnum.WEDNESDAY,
  thursday: DayOfWeekEnum.THURSDAY,
  friday: DayOfWeekEnum.FRIDAY,
  saturday: DayOfWeekEnum.SATURDAY,
  sunday: DayOfWeekEnum.SUNDAY,
};

const PREFERENCE_MAPPINGS = {
  chromebookSetup: {
    category: PreferenceCategoryEnum.EVALUATION,
    key: 'chromebook_setup',
    type: PreferenceTypeEnum.BOOLEAN,
  },
  quietSpaceForEvaluation: {
    category: PreferenceCategoryEnum.EVALUATION,
    key: 'quiet_space_for_evaluation',
    type: PreferenceTypeEnum.BOOLEAN,
  },
  internetStability: {
    category: PreferenceCategoryEnum.EVALUATION,
    key: 'internet_stability',
    type: PreferenceTypeEnum.BOOLEAN,
  },
};

export function transformAvailabilitySchedule(
  availabilitySchedule: CreateDistrictOnboardingInput['district']['availabilities']['availabilitySchedule'],
  districtId: string,
  timezone: string,
  currentUserId: string
): NewDistrictAvailability[] {
  const availabilityRecords: NewDistrictAvailability[] = [];

  for (const [dayName, dayConfig] of Object.entries(availabilitySchedule)) {
    if (dayConfig.enabled && dayConfig.slots.length > 0) {
      for (const slot of dayConfig.slots) {
        availabilityRecords.push({
          districtId,
          type: AvailabilityTypeEnum.EVALUATION,
          day: DAY_MAPPING[dayName],
          startTime: new Date(`1970-01-01T${slot.startTime}:00`),
          endTime: new Date(`1970-01-01T${slot.endTime}:00`),
          timeZone: timezone,
          isActive: true,
          createdBy: currentUserId,
        });
      }
    }
  }

  return availabilityRecords;
}

export function transformPreferences(
  preferences: CreateDistrictOnboardingInput['district']['preferences'],
  districtId: string,
  currentUserId: string
): NewDistrictPreference[] {
  const preferenceRecords: NewDistrictPreference[] = [];

  for (const [prefKey, prefValue] of Object.entries(preferences)) {
    const mapping =
      PREFERENCE_MAPPINGS[prefKey as keyof typeof PREFERENCE_MAPPINGS];
    if (mapping) {
      preferenceRecords.push({
        districtId,
        category: mapping.category,
        key: mapping.key,
        type: mapping.type,
        value: String(prefValue),
        lastModifiedBy: currentUserId,
      });
    }
  }

  return preferenceRecords;
}

export function generateSecureTemporaryPassword(): string {
  const charset =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
  let password = '';

  // Ensure at least one of each character type
  password += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'[Math.floor(Math.random() * 26)]; // Uppercase
  password += 'abcdefghijklmnopqrstuvwxyz'[Math.floor(Math.random() * 26)]; // Lowercase
  password += '0123456789'[Math.floor(Math.random() * 10)]; // Number
  password += '!@#$%^&*'[Math.floor(Math.random() * 8)]; // Symbol

  // Fill the rest randomly (total 10 characters)
  for (let i = 4; i < 10; i++) {
    password += charset[Math.floor(Math.random() * charset.length)];
  }

  // Shuffle the password
  return password
    .split('')
    .sort(() => Math.random() - 0.5)
    .join('');
}
