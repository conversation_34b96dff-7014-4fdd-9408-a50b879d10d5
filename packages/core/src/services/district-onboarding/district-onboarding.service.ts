import type { TransactionType } from '@lilypad/db/client';
import type { Address, District } from '@lilypad/db/schema/types';
import type {
  BaseEmailProps,
  DistrictOnboardingEmailProps,
} from '@lilypad/email/types';
import type {
  CreateDistrictOnboardingInput,
  CreatedSchool,
  DistrictCreationResult,
} from './types';

import { dbAdmin } from '@lilypad/db/client';
import {
  AddressError,
  AddressesRepository,
  DistrictAvailabilitiesRepository,
  DistrictPreferencesRepository,
  DistrictsRepository,
  RolesRepository,
  SchoolsRepository,
  UserDistrictsRepository,
  UserRolesRepository,
  UserSchoolsRepository,
  UsersRepository,
} from '@lilypad/db/repository';
import { usersTable } from '@lilypad/db/schema';
import {
  AddressTypeEnum,
  type RoleEnum,
  RoleEnumMap,
} from '@lilypad/db/schema/enums';
import { logger } from '@lilypad/shared/logger';
import { routes } from '@lilypad/shared/routes';
import { getSupabaseServerClient } from '@lilypad/supabase/server';

import { DistrictOnboardingError } from './errors';
import {
  generateSecureTemporaryPassword,
  transformAvailabilitySchedule,
  transformPreferences,
} from './helpers';

export class DistrictOnboardingService {
  private readonly tx: TransactionType;
  private readonly addressesRepo: AddressesRepository;
  private readonly districtsRepo: DistrictsRepository;
  private readonly schoolsRepo: SchoolsRepository;
  private readonly availabilitiesRepo: DistrictAvailabilitiesRepository;
  private readonly preferencesRepo: DistrictPreferencesRepository;
  private readonly usersRepo: UsersRepository;
  private readonly rolesRepo: RolesRepository;
  private readonly userDistrictsRepo: UserDistrictsRepository;
  private readonly userRolesRepo: UserRolesRepository;
  private readonly userSchoolsRepo: UserSchoolsRepository;

  constructor(tx: TransactionType) {
    this.tx = tx;
    this.addressesRepo = new AddressesRepository(tx);
    this.districtsRepo = new DistrictsRepository(tx);
    this.schoolsRepo = new SchoolsRepository(tx);
    this.availabilitiesRepo = new DistrictAvailabilitiesRepository(tx);
    this.preferencesRepo = new DistrictPreferencesRepository(tx);
    this.usersRepo = new UsersRepository(tx);
    this.rolesRepo = new RolesRepository(tx);
    this.userDistrictsRepo = new UserDistrictsRepository(tx);
    this.userRolesRepo = new UserRolesRepository(tx);
    this.userSchoolsRepo = new UserSchoolsRepository(tx);
  }

  private async validateDistrictSlug(slug: string): Promise<void> {
    const exists = await this.districtsRepo.exists(slug);
    if (exists) {
      throw new DistrictOnboardingError(
        `District with slug '${slug}' already exists`,
        'DUPLICATE_SLUG'
      );
    }
  }

  private async createAddress(addressData: {
    type: AddressTypeEnum;
    address: string;
    address2?: string;
    city: string;
    state: string;
    zipcode: string;
  }): Promise<Address> {
    try {
      return await this.addressesRepo.createAddress(addressData);
    } catch (error) {
      if (error instanceof AddressError) {
        throw new DistrictOnboardingError(error.message, error.code);
      }
      throw error;
    }
  }

  /**
   * Creates the district record with its address
   */
  private async createDistrictWithAddress(
    districtInfo: CreateDistrictOnboardingInput['district']
  ): Promise<{
    district: District;
    address: Address;
  }> {
    await this.validateDistrictSlug(districtInfo.generalInfo.slug);

    const address = await this.addressesRepo.createAddress(
      districtInfo.address
    );
    const district = await this.districtsRepo.createDistrict({
      name: districtInfo.generalInfo.name,
      slug: districtInfo.generalInfo.slug,
      type: districtInfo.generalInfo.type,
      website: districtInfo.generalInfo.website,
      ncesId: districtInfo.generalInfo.ncesId,
      stateId: districtInfo.generalInfo.stateId,
      county: districtInfo.generalInfo.county,
      numSchools: districtInfo.generalInfo.numSchools,
      numStudents: districtInfo.generalInfo.numStudents,
      invoiceEmail: districtInfo.generalInfo.invoiceEmail,
      addressId: address.id,
    });

    return {
      district,
      address,
    };
  }

  /**
   * Creates a single school with its addresses
   */
  private async createSchoolWithAddresses(
    school: CreateDistrictOnboardingInput['schools'][0],
    districtId: string
  ): Promise<CreatedSchool> {
    // Create physical address
    const physicalAddress = await this.createAddress({
      type: AddressTypeEnum.PHYSICAL,
      address: school.addresses.physical.address,
      address2: school.addresses.physical.address2,
      city: school.addresses.physical.city,
      state: school.addresses.physical.state,
      zipcode: school.addresses.physical.zipcode,
    });

    // Create postal address (reuse physical if marked as same)
    const postalAddressData = school.addresses.postal.sameAsPhysical
      ? {
          type: AddressTypeEnum.POSTAL,
          address: school.addresses.physical.address,
          address2: school.addresses.physical.address2,
          city: school.addresses.physical.city,
          state: school.addresses.physical.state,
          zipcode: school.addresses.physical.zipcode,
        }
      : {
          type: AddressTypeEnum.POSTAL,
          address: school.addresses.postal.address,
          address2: school.addresses.postal.address2,
          city: school.addresses.postal.city,
          state: school.addresses.postal.state,
          zipcode: school.addresses.postal.zipcode,
        };

    const postalAddress = await this.createAddress(postalAddressData);

    // Create school record
    const createdSchool = await this.schoolsRepo.createSchool({
      name: school.generalInfo.name,
      slug: school.generalInfo.slug,
      type: school.generalInfo.type,
      website: school.generalInfo.website,
      ncesId: school.generalInfo.ncesId,
      districtId,
      addressId: physicalAddress.id, // Use physical address as primary
    });

    return {
      ...createdSchool,
      physicalAddress,
      postalAddress,
      tempId: school.tempId,
    };
  }

  private async createSchools(
    schools: CreateDistrictOnboardingInput['schools'],
    districtId: string
  ): Promise<CreatedSchool[]> {
    const createdSchools: CreatedSchool[] = [];

    for (const school of schools) {
      const createdSchool = await this.createSchoolWithAddresses(
        school,
        districtId
      );
      createdSchools.push(createdSchool);
    }

    return createdSchools;
  }

  private async createDistrictAvailabilities(
    availabilities: CreateDistrictOnboardingInput['district']['availabilities'],
    districtId: string,
    currentUserId: string
  ): Promise<number> {
    const availabilityRecords = transformAvailabilitySchedule(
      availabilities.availabilitySchedule,
      districtId,
      availabilities.timezone,
      currentUserId
    );

    return await this.availabilitiesRepo.createAvailabilities(
      availabilityRecords
    );
  }

  private async createDistrictPreferences(
    preferences: CreateDistrictOnboardingInput['district']['preferences'],
    districtId: string,
    currentUserId: string
  ): Promise<number> {
    const preferenceRecords = transformPreferences(
      preferences,
      districtId,
      currentUserId
    );

    return await this.preferencesRepo.createPreferences(preferenceRecords);
  }

  private async getUserInfo(
    userId: string
  ): Promise<{ fullName: string; email: string }> {
    const user = await this.usersRepo.getUserInfo(userId);

    if (!user) {
      throw new DistrictOnboardingError(
        `User not found: ${userId}`,
        'USER_NOT_FOUND'
      );
    }

    return user;
  }

  private async createMemberInvitations(
    members: CreateDistrictOnboardingInput['members'],
    createdSchools: CreatedSchool[],
    districtId: string,
    districtName: string,
    currentUserId: string
  ): Promise<{
    invitationsCount: number;
    emails: Array<DistrictOnboardingEmailProps & BaseEmailProps>;
  }> {
    if (members.length === 0) {
      return { invitationsCount: 0, emails: [] };
    }

    const supabase = await getSupabaseServerClient({
      admin: true,
      script: false,
    });

    const roleMap = await this.rolesRepo.getRoleMapping();
    const userInfo = await this.getUserInfo(currentUserId);
    let invitationsCount = 0;
    const emails: Array<DistrictOnboardingEmailProps & BaseEmailProps> = [];

    // Create a mapping from client-side school identifiers to database school IDs
    const schoolIdMapping = new Map<string, string>();

    for (const school of createdSchools) {
      schoolIdMapping.set(school.tempId, school.id);
    }

    logger.info(
      {
        schoolMappingInfo: createdSchools.map((school) => ({
          tempId: school.tempId,
          slug: school.slug,
          ncesId: school.ncesId,
          dbId: school.id,
          name: school.name,
        })),
        totalMembersToInvite: members.length,
      },
      'Created school ID mapping for member invitations'
    );

    // Create users one by one using Supabase admin
    for (const member of members) {
      const roleId = roleMap.get(member.role);
      if (!roleId) {
        throw new DistrictOnboardingError(
          `Role '${member.role}' not found`,
          'ROLE_NOT_FOUND'
        );
      }

      try {
        // Map client-side school IDs to actual database school IDs
        const actualSchoolIds: string[] = [];
        const unmappedSchoolIds: string[] = [];

        for (const clientSchoolId of member.schoolIds) {
          const dbSchoolId = schoolIdMapping.get(clientSchoolId);
          if (dbSchoolId) {
            actualSchoolIds.push(dbSchoolId);
          } else {
            unmappedSchoolIds.push(clientSchoolId);
          }
        }

        // Log warning if some school IDs couldn't be mapped
        if (unmappedSchoolIds.length > 0) {
          logger.warn(
            {
              memberEmail: member.email,
              unmappedSchoolIds,
              availableIdentifiers: Array.from(schoolIdMapping.keys()),
              createdSchoolsInfo: createdSchools.map((s) => ({
                tempId: s.tempId,
                slug: s.slug,
                ncesId: s.ncesId,
                dbId: s.id,
              })),
            },
            'Some client school IDs could not be mapped to database school IDs'
          );
        }

        // Generate secure temporary password
        const temporaryPassword = generateSecureTemporaryPassword();

        // Create Supabase user with admin client
        const { data, error } = await supabase.auth.admin.createUser({
          email: member.email,
          password: temporaryPassword,
          email_confirm: true,
        });

        if (error || !data.user) {
          logger.error(
            {
              email: member.email,
              role: member.role,
              districtId,
              error: error?.message || 'Unknown error',
            },
            'Failed to create Supabase user'
          );
          throw new DistrictOnboardingError(
            `Failed to create Supabase user for ${member.email}: ${error?.message || 'Unknown error'}`,
            'SUPABASE_USER_CREATION_FAILED'
          );
        }

        const userId = data.user.id;

        logger.info(
          { userId, email: member.email },
          'Successfully created Supabase user'
        );

        // Create database user record
        await dbAdmin.insert(usersTable).values({
          id: userId,
          firstName: member.firstName,
          lastName: member.lastName,
          email: member.email,
          isOnboarded: true,
        });

        logger.info({ userId }, 'Created database user record');

        // Create user-district association
        await this.userDistrictsRepo.createAssociation(userId, districtId);

        // Create user-role association
        await this.userRolesRepo.createAssociation(userId, roleId, member.role);

        // Create user-school associations if any
        if (actualSchoolIds.length > 0) {
          const schoolAssociations = actualSchoolIds.map((schoolId) => ({
            userId,
            schoolId,
          }));
          await this.userSchoolsRepo.createAssociations(schoolAssociations);
        }

        // Get school names for the email
        const schoolNames = actualSchoolIds
          .map((id) => createdSchools.find((s) => s.id === id)?.name)
          .filter(Boolean) as string[];

        emails.push({
          recipient: member.email,
          recipientName: member.firstName,
          invitedByName: userInfo.fullName,
          invitedByEmail: userInfo.email,
          districtName,
          role: RoleEnumMap[member.role as RoleEnum],
          schoolNames,
          inviteLink: routes.app.auth.SignIn,
        });

        invitationsCount++;

        logger.info(
          {
            email: member.email,
            role: member.role,
            districtId,
            clientSchoolIds: member.schoolIds,
            actualSchoolIds,
            userId,
          },
          'User created and invitation email sent successfully'
        );
      } catch (error) {
        logger.error(
          {
            email: member.email,
            role: member.role,
            districtId,
            error: error instanceof Error ? error.message : String(error),
          },
          'Failed to create user or send email'
        );

        // Re-throw the error to stop the transaction
        throw new DistrictOnboardingError(
          `Failed to create user for ${member.email}: ${error instanceof Error ? error.message : String(error)}`,
          'USER_CREATION_FAILED'
        );
      }
    }

    return { invitationsCount, emails };
  }

  /**
   * Main execution method that orchestrates all operations
   */
  async createDistrictOnboarding(
    input: CreateDistrictOnboardingInput
  ): Promise<DistrictCreationResult> {
    try {
      // 1. Create district with address
      const { district, address } = await this.createDistrictWithAddress(
        input.district
      );

      logger.info(
        {
          districtId: district.id,
        },
        'District created successfully'
      );

      // 2. Create schools with their addresses
      const schools = await this.createSchools(input.schools, district.id);

      // 3. Create district availabilities
      const availabilitiesCount = await this.createDistrictAvailabilities(
        input.district.availabilities,
        district.id,
        input.currentUserId
      );

      // 4. Create district preferences
      const preferencesCount = await this.createDistrictPreferences(
        input.district.preferences,
        district.id,
        input.currentUserId
      );

      // 5. Create member invitations
      const { invitationsCount, emails } = await this.createMemberInvitations(
        input.members,
        schools,
        district.id,
        district.name,
        input.currentUserId
      );

      return {
        district: {
          ...district,
          address,
        },
        emails,
        schools,
        availabilities: availabilitiesCount,
        preferences: preferencesCount,
        invitations: invitationsCount,
      };
    } catch (error) {
      // Log the error for debugging
      logger.error(
        {
          error: error instanceof Error ? error.message : String(error),
          districtName: input.district.generalInfo.name,
          currentUserId: input.currentUserId,
        },
        'Error in district onboarding process'
      );

      // Re-throw the error to rollback the transaction
      throw error;
    }
  }
}
