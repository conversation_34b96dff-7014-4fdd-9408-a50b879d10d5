import type {
  AddressTypeEnum,
  DistrictTypeEnum,
  RoleEnum,
  SchoolTypeEnum,
} from '@lilypad/db/schema/enums';
import type { Address, District } from '@lilypad/db/schema/types';
import type {
  BaseEmailProps,
  DistrictOnboardingEmailProps,
} from '@lilypad/email/types';

export interface DaySchedule {
  enabled: boolean;
  slots: Array<{
    startTime: string;
    endTime: string;
  }>;
}

export interface CreateDistrictOnboardingInput {
  district: {
    generalInfo: {
      name: string;
      slug: string;
      type: DistrictTypeEnum;
      website: string;
      ncesId: string;
      stateId: string;
      county: string;
      numSchools?: number;
      numStudents?: number;
      invoiceEmail: string;
    };
    address: {
      type: AddressTypeEnum;
      address: string;
      address2?: string;
      city: string;
      state: string;
      zipcode: string;
    };
    availabilities: {
      availabilitySchedule: {
        monday: DaySchedule;
        tuesday: DaySchedule;
        wednesday: DaySchedule;
        thursday: DaySchedule;
        friday: DaySchedule;
        saturday: DaySchedule;
        sunday: DaySchedule;
      };
      timezone: string;
    };
    preferences: {
      chromebookSetup: boolean;
      quietSpaceForEvaluation: boolean;
      internetStability: boolean;
    };
  };
  schools: Array<{
    tempId: string;
    generalInfo: {
      name: string;
      slug: string;
      type: SchoolTypeEnum;
      website?: string;
      ncesId: string;
    };
    addresses: {
      physical: AddressData;
      postal: AddressData & { sameAsPhysical?: boolean };
    };
  }>;
  members: Array<{
    firstName: string;
    lastName: string;
    email: string;
    role: RoleEnum;
    schoolIds: string[];
  }>;
  currentUserId: string;
}

export interface AddressData {
  type: string;
  address: string;
  address2?: string;
  city: string;
  state: string;
  zipcode: string;
}

export interface CreatedSchool {
  id: string;
  tempId: string;
  name: string;
  slug: string;
  type: SchoolTypeEnum;
  website?: string | null;
  ncesId: string;
  districtId: string;
  addressId: string;
  physicalAddress: Address;
  postalAddress: Address;
}

export interface DistrictCreationResult {
  district: District & {
    address: Address;
  };
  emails: Array<DistrictOnboardingEmailProps & BaseEmailProps>;
  schools: CreatedSchool[];
  availabilities: number;
  preferences: number;
  invitations: number;
}

export interface PreferenceMapping {
  category: string;
  key: string;
  type: string;
}
