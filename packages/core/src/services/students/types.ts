import type {
  GenderEnum,
  ParentRelationshipEnum,
  SchoolGradeEnum,
} from '@lilypad/db/enums';
import type { DocumentCategoryEnum } from '@lilypad/db/schema/enums';

export type SerializableFile = {
  name: string;
  size: number;
  type: string;
  data: string; // Base64 encoded
};

export interface ParentInput {
  firstName: string;
  middleName?: string;
  lastName: string;
  relationshipType: ParentRelationshipEnum;
  primaryEmail?: string;
  secondaryEmail?: string;
  primaryPhone?: string;
  secondaryPhone?: string;
  isPrimaryContact: boolean;
  hasPickupAuthorization: boolean;
}

export interface DocumentInput {
  file: File | SerializableFile;
  category: DocumentCategoryEnum;
}

// Type guards for DocumentInput
export function isSerializableFile(
  file: File | SerializableFile
): file is SerializableFile {
  return (
    typeof file === 'object' && 'data' in file && typeof file.data === 'string'
  );
}

export function isFileObject(file: File | SerializableFile): file is File {
  return file instanceof File;
}

export interface StudentInput {
  id: string; // Temporary ID for tracking
  studentIdNumber: string;
  firstName: string;
  middleName?: string;
  lastName: string;
  preferredName: string;
  dateOfBirth: Date;
  dateOfConsent: Date;
  grade: SchoolGradeEnum;
  gender: GenderEnum;
  primarySchoolId: string;
  languageIds: string[];
  primaryLanguageId: string | null;
  parents: ParentInput[];
  documents: DocumentInput[];
  specialNeedsIndicator: boolean;
}

export interface BulkCreateStudentsInput {
  students: StudentInput[];
}

export interface StudentSaveResult {
  tempId: string;
  success: boolean;
  studentId?: string;
  error?: string;
}
