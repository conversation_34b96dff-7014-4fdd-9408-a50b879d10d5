import type { TransactionType } from '@lilypad/db/client';
import {
  type CreateParentData,
  DocumentsRepository,
  ParentsRepository,
  StudentEnrollmentsRepository,
  StudentLanguagesRepository,
  StudentRepository,
} from '@lilypad/db/repository/index';
import type {
  NewStudentEnrollment,
  NewStudentLanguage,
  NewStudentParent,
  Parent,
} from '@lilypad/db/schema/types';
import { serializableToFile } from '@lilypad/shared/lib/file-utils';
import type { AuthUser } from '@lilypad/supabase';
import { uploadStudentDocumentServer } from '@lilypad/supabase/storage/students/documents.server';
import {
  type BulkCreateStudentsInput,
  type DocumentInput,
  type ParentInput,
  type StudentInput,
  type StudentSaveResult,
  isSerializableFile,
} from './types';

const BATCH_SIZE = 50;

export class StudentsService {
  private tx: TransactionType;
  constructor(tx: TransactionType) {
    this.tx = tx;
  }

  // biome-ignore lint/complexity/noExcessiveCognitiveComplexity: Fix later
  async bulkCreateStudents(
    input: BulkCreateStudentsInput,
    user: AuthUser
  ): Promise<StudentSaveResult[]> {
    const results: StudentSaveResult[] = [];
    const totalStudents = input.students.length;

    // Initialize repositories
    const studentRepository = new StudentRepository(this.tx);
    const parentsRepository = new ParentsRepository(this.tx);
    const enrollmentsRepository = new StudentEnrollmentsRepository(this.tx);
    const languagesRepository = new StudentLanguagesRepository(this.tx);

    for (let i = 0; i < totalStudents; i += BATCH_SIZE) {
      const batch = input.students.slice(
        i,
        Math.min(i + BATCH_SIZE, totalStudents)
      );

      try {
        // biome-ignore lint/nursery/noAwaitInLoop: Fix later
        await this.validateBatchData(batch);

        // Prepare student data for insertion
        const studentsToInsert = batch.map((studentData) => ({
          studentIdNumber: studentData.studentIdNumber,
          firstName: studentData.firstName,
          lastName: studentData.lastName,
          preferredName: studentData.preferredName,
          dateOfBirth: studentData.dateOfBirth.toISOString().split('T')[0],
          dateOfConsent: studentData.dateOfConsent.toISOString().split('T')[0],
          gender: studentData.gender,
          grade: studentData.grade,
          primarySchoolId: studentData.primarySchoolId,
          specialNeedsIndicator: studentData.specialNeedsIndicator,
        }));

        // Create students
        const insertedStudents =
          await studentRepository.createStudents(studentsToInsert);

        // Create a map for quick lookup
        const studentIdMap = new Map(
          insertedStudents.map((student: { id: string }, index: number) => [
            batch[index].id,
            student.id,
          ])
        );

        // Prepare batch data for related tables
        const enrollments: NewStudentEnrollment[] = [];
        const languages: NewStudentLanguage[] = [];
        const parentRelationships: NewStudentParent[] = [];
        const documentsToUpload: Array<{
          studentId: string;
          tempId: string;
          doc: DocumentInput;
        }> = [];

        // Collect all parents that need to be checked/created
        const allParentsData = new Map<string, ParentInput>();

        for (const studentData of batch) {
          const studentId = studentIdMap.get(studentData.id) as string;

          // Prepare enrollment
          enrollments.push({
            studentId,
            schoolId: studentData.primarySchoolId,
          });

          // Prepare languages
          for (const languageId of studentData.languageIds) {
            languages.push({
              studentId,
              languageId,
              isPrimary: languageId === studentData.primaryLanguageId,
            });
          }

          // Collect unique parents
          for (const parent of studentData.parents) {
            const parentKey = `${parent.firstName}-${parent.lastName}-${parent.primaryEmail || parent.primaryPhone}`;
            allParentsData.set(parentKey, parent);
          }

          // Collect documents for later upload
          for (const doc of studentData.documents) {
            documentsToUpload.push({
              studentId,
              tempId: studentData.id,
              doc,
            });
          }
        }

        // Process parents
        const existingParentMap = await this.processParents(
          parentsRepository,
          allParentsData
        );

        // Create student-parent relationships
        for (const studentData of batch) {
          const studentId = studentIdMap.get(studentData.id) as string;
          if (!studentId) {
            continue;
          }

          for (const parent of studentData.parents) {
            const key = `${parent.firstName}-${parent.lastName}-${parent.primaryEmail || parent.primaryPhone}`;
            const parentId = existingParentMap.get(key);

            if (parentId) {
              parentRelationships.push({
                studentId,
                parentId,
                isPrimaryContact: parent.isPrimaryContact,
                hasPickupAuthorization: parent.hasPickupAuthorization,
              });
            }
          }
        }

        // Batch insert all relationships using repositories
        await Promise.all([
          enrollmentsRepository.createEnrollments(enrollments),
          languagesRepository.createStudentLanguages(languages),
          parentsRepository.createStudentParentRelationships(
            parentRelationships
          ),
        ]);

        // Handle document uploads asynchronously (outside transaction)
        this.handleDocumentUploads(documentsToUpload, user.id);

        // Record success for all students in batch
        for (const studentData of batch) {
          const studentId = studentIdMap.get(studentData.id) as string;
          if (studentId) {
            results.push({
              tempId: studentData.id,
              success: true,
              studentId,
            });
          }
        }
      } catch (error) {
        console.error('Batch processing error:', error);

        // Parse database errors for better user feedback
        const errorMessage = this.parseDbError(error);

        // Record failure for entire batch
        for (const studentData of batch) {
          results.push({
            tempId: studentData.id,
            success: false,
            error: errorMessage,
          });
        }
      }
    }

    return results;
  }

  private async validateBatchData(batch: StudentInput[]): Promise<void> {
    // Check for duplicate student ID numbers within the batch
    const studentIds = batch.map((s) => s.studentIdNumber);

    const duplicateIds = studentIds.filter(
      (id, index) => studentIds.indexOf(id) !== index
    );

    if (duplicateIds.length > 0) {
      throw new Error(
        `Duplicate student ID numbers in batch: ${duplicateIds.join(', ')}`
      );
    }

    // Validate required fields
    for (const student of batch) {
      if (!student.studentIdNumber?.trim()) {
        throw new Error(
          `Student ID number is required for student: ${student.firstName} ${student.lastName}`
        );
      }
      if (!student.firstName?.trim()) {
        throw new Error(
          `First name is required for student ID: ${student.studentIdNumber}`
        );
      }
      if (!student.lastName?.trim()) {
        throw new Error(
          `Last name is required for student ID: ${student.studentIdNumber}`
        );
      }
      if (!student.primarySchoolId?.trim()) {
        throw new Error(
          `School is required for student: ${student.firstName} ${student.lastName}`
        );
      }
      if (!student.languageIds || student.languageIds.length === 0) {
        throw new Error(
          `At least one language is required for student: ${student.firstName} ${student.lastName}`
        );
      }
      if (student.parents.length === 0) {
        throw new Error(
          `At least one parent/guardian is required for student: ${student.firstName} ${student.lastName}`
        );
      }
    }

    // Check for existing students in database
    await this.checkForExistingStudents(batch);
  }

  private async checkForExistingStudents(batch: StudentInput[]): Promise<void> {
    const studentRepository = new StudentRepository(this.tx);

    for (const student of batch) {
      // biome-ignore lint/nursery/noAwaitInLoop: Fix later
      const existing = await studentRepository.getStudentByStudentIdNumber(
        student.studentIdNumber
      );

      if (existing) {
        throw new Error(
          `Student with ID "${student.studentIdNumber}" already exists in the system. Please use a different Student ID.`
        );
      }
    }
  }

  private parseDbError(error: unknown): string {
    if (!error || typeof error !== 'object') {
      return 'Unknown error occurred';
    }

    // biome-ignore lint/suspicious/noExplicitAny: Supabase error
    const dbError = error as any;

    // Handle PostgreSQL constraint violations
    if (dbError.code === '23505') {
      // Unique constraint violation
      if (dbError.constraint === 'student_id_school_unique') {
        return 'A student with this Student ID is already enrolled in the selected school. Please check the Student ID and try again.';
      }
      if (dbError.constraint === 'students_student_id_number_unique') {
        return 'A student with this Student ID already exists. Please use a different Student ID.';
      }
      return 'Duplicate data detected. Please check your entries and try again.';
    }

    if (dbError.code === '23503') {
      // Foreign key constraint violation
      return 'Invalid reference data. Please ensure all schools and languages exist.';
    }

    if (dbError.code === '23502') {
      // Not null constraint violation
      return 'Required field is missing. Please check all required fields are filled.';
    }

    // Handle other database errors
    if (dbError.code?.startsWith('23')) {
      return 'Data validation error. Please check your entries and try again.';
    }

    // Handle general errors
    if (error instanceof Error) {
      return error.message;
    }

    return 'An unexpected error occurred while saving students.';
  }

  private async processParents(
    parentsRepository: ParentsRepository,
    allParentsData: Map<string, ParentInput>
  ): Promise<Map<string, string>> {
    const parentValues = Array.from(allParentsData.values());

    // Prepare search keys for existing parents
    const searchKeys = parentValues.map((parent) => ({
      firstName: parent.firstName,
      lastName: parent.lastName,
      identifier: parent.primaryEmail || parent.primaryPhone || '',
    }));

    // Find existing parents
    const existingParents =
      await parentsRepository.findExistingParents(searchKeys);

    // Create map of existing parents
    const existingParentMap = new Map(
      existingParents.map((p: Parent) => {
        const key = `${p.firstName}-${p.lastName}-${p.primaryEmail || p.primaryPhone}`;
        return [key, p.id];
      })
    );

    // Prepare new parents for batch insert
    const newParents: CreateParentData[] = parentValues
      .filter((parent) => {
        const key = `${parent.firstName}-${parent.lastName}-${parent.primaryEmail || parent.primaryPhone}`;
        return !existingParentMap.has(key);
      })
      .map((parent) => ({
        firstName: parent.firstName,
        middleName: parent.middleName || null,
        lastName: parent.lastName,
        primaryEmail: parent.primaryEmail || null,
        secondaryEmail: parent.secondaryEmail || null,
        primaryPhone: parent.primaryPhone || null,
        secondaryPhone: parent.secondaryPhone || null,
        relationshipType: parent.relationshipType,
      }));

    // Batch insert new parents
    let insertedParents: Array<{ id: string }> = [];

    if (newParents.length > 0) {
      insertedParents = await parentsRepository.createParents(newParents);
    }

    // Update parent map with newly inserted parents
    let insertIndex = 0;
    for (const parent of parentValues) {
      const key = `${parent.firstName}-${parent.lastName}-${parent.primaryEmail || parent.primaryPhone}`;
      if (!existingParentMap.has(key) && insertIndex < insertedParents.length) {
        existingParentMap.set(key, insertedParents[insertIndex].id);
        insertIndex++;
      }
    }

    return existingParentMap;
  }

  private handleDocumentUploads(
    documentsToUpload: Array<{
      studentId: string;
      tempId: string;
      doc: DocumentInput;
    }>,
    userId: string
  ): void {
    // Handle document uploads asynchronously (non-blocking)
    // Note: This will be processed outside the transaction context
    if (documentsToUpload.length === 0) {
      return;
    }

    // Process document uploads in the background
    setImmediate(async () => {
      const { createDatabaseClient } = await import('@lilypad/db/client');

      for (const { studentId, doc } of documentsToUpload) {
        try {
          // Convert serializable file back to File if needed
          const file = isSerializableFile(doc.file)
            ? serializableToFile(doc.file)
            : doc.file;

          // Upload file to storage using server-side function
          // biome-ignore lint/nursery/noAwaitInLoop: Fix later
          const uploadResult = await uploadStudentDocumentServer({
            file,
            studentId,
            userId,
          });

          // Create new database connection for this operation
          const dbAdmin = await createDatabaseClient({ admin: true });

          const documentsRepository = new DocumentsRepository(
            await dbAdmin.transaction(async (tx) => tx) // TODO: This is a hack to get the transaction to work
          );

          // Insert document record
          await documentsRepository.createDocument({
            studentId,
            name: file.name,
            url: uploadResult.url,
            category: doc.category,
            uploadedUserId: userId,
          });
        } catch (error) {
          console.error(
            `Document upload failed for student ${studentId}:`,
            error
          );
        }
      }
    });
  }
}
