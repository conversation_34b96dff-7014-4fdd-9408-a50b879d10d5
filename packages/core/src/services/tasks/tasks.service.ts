import 'server-only';
import type { DatabaseType } from '@lilypad/db';
import { TasksRepository } from '@lilypad/db/repository';
import type { NewTask } from '@lilypad/db/schema';
import { logger } from '@lilypad/shared/logger';

export class TasksService {
  private readonly db: DatabaseType;

  constructor(db: DatabaseType) {
    this.db = db;
  }

  async getTaskById(taskId: string) {
    try {
      return await this.db.transaction((tx) => {
        const repository = new TasksRepository(tx);
        return repository.getTaskById(taskId);
      });
    } catch (error) {
      logger.error({ error }, '❌ ERROR GETTING TASK');
      throw error;
    }
  }

  async create(params: NewTask, dependedOnTaskId?: string) {
    try {
      return await this.db.transaction((tx) => {
        const repository = new TasksRepository(tx);
        return repository.createTask(params, dependedOnTaskId);
      });
    } catch (error) {
      logger.error({ error }, '❌ ERROR CREATING TASK');
      throw error;
    }
  }

  async createBatch(tasks: NewTask[]) {
    try {
      return await this.db.transaction((tx) => {
        const repository = new TasksRepository(tx);
        return repository.createTasksBatch(tasks);
      });
    } catch (error) {
      logger.error(
        { error, tasksCount: tasks.length },
        '❌ ERROR CREATING TASKS BATCH'
      );
      throw error;
    }
  }

  async reassign(params: {
    taskId: string;
    reassignedTo: string;
    reassignedBy: string;
    notes?: string;
  }) {
    try {
      await this.db.transaction(async (tx) => {
        const repository = new TasksRepository(tx);
        await repository.logTaskReassigned(params);
      });
    } catch (error) {
      logger.error({ error }, '❌ ERROR REASSIGNING TASK');
      throw error;
    }
  }

  async complete(taskId: string, completedBy: string) {
    try {
      await this.db.transaction(async (tx) => {
        const repository = new TasksRepository(tx);
        await repository.logTaskCompleted(taskId, completedBy);
      });
    } catch (error) {
      logger.error({ error }, '❌ ERROR COMPLETING TASK');
      throw error;
    }
  }

  async reject(taskId: string, rejectedBy: string, rejectionReason: string) {
    try {
      await this.db.transaction(async (tx) => {
        const repository = new TasksRepository(tx);
        await repository.logTaskRejected(taskId, rejectedBy, rejectionReason);
      });
    } catch (error) {
      logger.error({ error }, '❌ ERROR REJECTING TASK');
      throw error;
    }
  }
}
