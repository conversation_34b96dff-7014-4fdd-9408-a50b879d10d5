import 'server-only';

import type { DatabaseType } from '@lilypad/db';
import { NotificationsRepository } from '@lilypad/db/repository';
import type { NewNotification } from '@lilypad/db/schema';

import { logger } from '@lilypad/shared/logger';

export class NotificationsService {
  private readonly db: DatabaseType;

  constructor(db: DatabaseType) {
    this.db = db;
  }

  async send(params: NewNotification) {
    try {
      await this.db.transaction(async (tx) => {
        const repository = new NotificationsRepository(tx);
        return await repository.createNotification(params);
      });
    } catch (error) {
      logger.error({ error }, '❌ ERROR SENDING NOTIFICATION');
      throw error;
    }
  }

  async markAsRead(userId: string, notificationId: string) {
    try {
      await this.db.transaction(async (tx) => {
        const repository = new NotificationsRepository(tx);
        return await repository.markNotificationAsRead(userId, notificationId);
      });
    } catch (error) {
      logger.error({ error }, '❌ ERROR MARKING NOTIFICATION AS READ');
      throw error;
    }
  }

  async markAllAsRead(userId: string) {
    try {
      await this.db.transaction(async (tx) => {
        const repository = new NotificationsRepository(tx);
        return await repository.markAllNotificationsAsRead(userId);
      });
    } catch (error) {
      logger.error({ error }, '❌ ERROR MARKING ALL NOTIFICATIONS AS READ');
      throw error;
    }
  }

  async archive(userId: string, notificationId: string) {
    try {
      await this.db.transaction(async (tx) => {
        const repository = new NotificationsRepository(tx);
        return await repository.archiveNotification(userId, notificationId);
      });
    } catch (error) {
      logger.error({ error }, '❌ ERROR ARCHIVING NOTIFICATION');
      throw error;
    }
  }

  async unarchive(userId: string, notificationId: string) {
    try {
      await this.db.transaction(async (tx) => {
        const repository = new NotificationsRepository(tx);
        return await repository.unarchiveNotification(userId, notificationId);
      });
    } catch (error) {
      logger.error({ error }, '❌ ERROR UNARCHIVING NOTIFICATION');
      throw error;
    }
  }

  async archiveRead(userId: string) {
    try {
      await this.db.transaction(async (tx) => {
        const repository = new NotificationsRepository(tx);
        return await repository.archiveReadNotifications(userId);
      });
    } catch (error) {
      logger.error({ error }, '❌ ERROR ARCHIVING READ NOTIFICATIONS');
      throw error;
    }
  }

  async archiveAll(userId: string) {
    try {
      await this.db.transaction(async (tx) => {
        const repository = new NotificationsRepository(tx);
        return await repository.archiveAllNotifications(userId);
      });
    } catch (error) {
      logger.error({ error }, '❌ ERROR ARCHIVING ALL NOTIFICATIONS');
      throw error;
    }
  }

  async unarchiveAll(userId: string) {
    try {
      await this.db.transaction(async (tx) => {
        const repository = new NotificationsRepository(tx);
        return await repository.unarchiveAllNotifications(userId);
      });
    } catch (error) {
      logger.error({ error }, '❌ ERROR UNARCHIVING ALL NOTIFICATIONS');
      throw error;
    }
  }

  async deleteArchived(userId: string) {
    try {
      await this.db.transaction(async (tx) => {
        const repository = new NotificationsRepository(tx);
        return await repository.deleteArchivedNotifications(userId);
      });
    } catch (error) {
      logger.error({ error }, '❌ ERROR DELETING ARCHIVED NOTIFICATIONS');
      throw error;
    }
  }
}
