import { cn } from '@lilypad/ui/lib/utils';
import { cva, type VariantProps } from 'class-variance-authority';

const districtTypeBadgeVariants = cva(
  'inline-flex w-fit shrink-0 items-center justify-center gap-1 whitespace-nowrap rounded-full border px-2 py-0.5 font-medium text-xs transition-colors [&>svg]:pointer-events-none [&>svg]:size-3',
  {
    variants: {
      districtType: {
        ELEMENTARY_DISTRICT:
          'border-emerald-500/80 bg-emerald-200/60 text-emerald-700 dark:border-border dark:bg-muted dark:text-emerald-400',
        SECONDARY_DISTRICT:
          'border-blue-500/80 bg-blue-200/60 text-blue-700 dark:border-border dark:bg-muted dark:text-blue-400',
        UNIFIED_DISTRICT:
          'border-violet-500/80 bg-violet-200/60 text-violet-700 dark:border-border dark:bg-muted dark:text-violet-400',
        SUPERVISORY_UNION_ADMIN:
          'border-amber-500/80 bg-amber-200/60 text-amber-700 dark:border-border dark:bg-muted dark:text-amber-400',
        REGIONAL_SERVICE_AGENCY:
          'border-cyan-500/80 bg-cyan-200/60 text-cyan-700 dark:border-border dark:bg-muted dark:text-cyan-400',
        STATE_OPERATED_AGENCY:
          'border-indigo-500/80 bg-indigo-200/60 text-indigo-700 dark:border-border dark:bg-muted dark:text-indigo-400',
        FEDERAL_OPERATED_AGENCY:
          'border-purple-500/80 bg-purple-200/60 text-purple-700 dark:border-border dark:bg-muted dark:text-purple-400',
        CHARTER_LEA:
          'border-teal-500/80 bg-teal-200/60 text-teal-700 dark:border-border dark:bg-muted dark:text-teal-400',
        OTHER_EDUCATION_AGENCY:
          'border-slate-500/80 bg-slate-200/60 text-slate-700 dark:border-border dark:bg-muted dark:text-slate-400',
        SPECIALIZED_PUBLIC_DISTRICT:
          'border-rose-500/80 bg-rose-200/60 text-rose-700 dark:border-border dark:bg-muted dark:text-rose-400',
      },
    },
  }
);

const districtTypeDisplayNames = {
  ELEMENTARY_DISTRICT: { full: 'Elementary District', short: 'Elementary' },
  SECONDARY_DISTRICT: { full: 'Secondary District', short: 'Secondary' },
  UNIFIED_DISTRICT: { full: 'Unified District', short: 'Unified' },
  SUPERVISORY_UNION_ADMIN: {
    full: 'Supervisory Union Admin',
    short: 'Supervisory',
  },
  REGIONAL_SERVICE_AGENCY: {
    full: 'Regional Service Agency',
    short: 'Regional',
  },
  STATE_OPERATED_AGENCY: { full: 'State Operated Agency', short: 'State Op.' },
  FEDERAL_OPERATED_AGENCY: {
    full: 'Federal Operated Agency',
    short: 'Federal Op.',
  },
  CHARTER_LEA: { full: 'Charter LEA', short: 'Charter' },
  OTHER_EDUCATION_AGENCY: {
    full: 'Other Education Agency',
    short: 'Other Ed.',
  },
  SPECIALIZED_PUBLIC_DISTRICT: {
    full: 'Specialized Public District',
    short: 'Specialized',
  },
} as const;

type DistrictTypeEnum = keyof typeof districtTypeDisplayNames;

interface DistrictTypeBadgeProps
  extends React.ComponentProps<'span'>,
    VariantProps<typeof districtTypeBadgeVariants> {
  districtType: DistrictTypeEnum;
  showFullName?: boolean;
}

export function DistrictTypeBadge({
  className,
  districtType,
  showFullName = true,
  ...props
}: DistrictTypeBadgeProps) {
  const displayName = showFullName
    ? districtTypeDisplayNames[districtType].full
    : districtTypeDisplayNames[districtType].short;

  return (
    <span
      className={cn(districtTypeBadgeVariants({ districtType }), className)}
      {...props}
    >
      {displayName}
    </span>
  );
}

export { districtTypeBadgeVariants, type DistrictTypeEnum };
