import React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>b,
  BreadcrumbEllipsis,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from './breadcrumb';
import { If } from './if';

export interface BreadcrumbItemData {
  label: string;
  href?: string;
  asChild?: boolean;
}

interface DynamicBreadcrumbProps {
  items: BreadcrumbItemData[];
  maxItems?: number;
  separator?: React.ReactNode;
  className?: string;
  linkComponent?: React.ComponentType<{
    href: string;
    children: React.ReactNode;
  }>;
}

/**
 * Dynamic Breadcrumb Component
 *
 * A flexible breadcrumb component that can handle any depth of navigation.
 * Automatically handles separators and treats the last item as the current page.
 *
 * @example
 * ```tsx
 * // Basic usage
 * <DynamicBreadcrumb
 *   items={[
 *     { label: "Home", href: "/" },
 *     { label: "Settings", href: "/settings" },
 *     { label: "Notifications" } // Current page (no href)
 *   ]}
 * />
 *
 * // With Next.js Link
 * <DynamicBreadcrumb
 *   items={[
 *     { label: "Home", href: "/", asChild: true },
 *     { label: "Students", href: "/students", asChild: true },
 *     { label: "John Doe" }
 *   ]}
 *   linkComponent={({ href, children }) => (
 *     <Link href={href}>{children}</Link>
 *   )}
 * />
 *
 * // With ellipsis for long paths
 * <DynamicBreadcrumb
 *   items={longItemsArray}
 *   maxItems={4}
 * />
 * ```
 */
export function DynamicBreadcrumb({
  items,
  maxItems = 6,
  separator,
  className,
  linkComponent: LinkComponent,
  ...props
}: DynamicBreadcrumbProps) {
  if (!items.length) {
    return null;
  }

  const shouldCollapse = items.length > maxItems;
  const displayItems = shouldCollapse
    ? [
        items[0], // Always show first item
        { label: '...', href: undefined }, // Ellipsis
        ...items.slice(-(maxItems - 2)), // Show last (maxItems - 2) items
      ]
    : items;

  const renderBreadcrumbLink = (
    item: BreadcrumbItemData,
    children: React.ReactNode
  ) => {
    if (!item.href) {
      return children;
    }

    if (LinkComponent) {
      return (
        <BreadcrumbLink asChild={item.asChild}>
          <LinkComponent href={item.href}>{children}</LinkComponent>
        </BreadcrumbLink>
      );
    }

    return (
      <BreadcrumbLink asChild={item.asChild} href={item.href}>
        {children}
      </BreadcrumbLink>
    );
  };

  return (
    <Breadcrumb className={className} {...props}>
      <BreadcrumbList>
        {displayItems.map((item, index) => {
          const isLast = index === displayItems.length - 1;
          const isEllipsis = item.label === '...';

          return (
            <React.Fragment key={`${item.label}-${index}`}>
              <BreadcrumbItem>
                <If condition={isEllipsis}>
                  <BreadcrumbEllipsis />
                </If>
                <If condition={isLast}>
                  <BreadcrumbPage>{item.label}</BreadcrumbPage>
                </If>
                <If condition={!isLast}>
                  {renderBreadcrumbLink(item, item.label)}
                </If>
              </BreadcrumbItem>

              {!isLast && (
                <BreadcrumbSeparator>{separator}</BreadcrumbSeparator>
              )}
            </React.Fragment>
          );
        })}
      </BreadcrumbList>
    </Breadcrumb>
  );
}

/**
 * Utility function to create breadcrumb items from a pathname
 * Useful for automatically generating breadcrumbs from URL segments
 *
 * @example
 * ```tsx
 * const items = createBreadcrumbsFromPath(
 *   "/settings/account/profile",
 *   {
 *     settings: "Settings",
 *     account: "Account",
 *     profile: "Profile"
 *   }
 * )
 * // Returns: [
 * //   { label: "Settings", href: "/settings" },
 * //   { label: "Account", href: "/settings/account" },
 * //   { label: "Profile" }
 * // ]
 * ```
 */
export function createBreadcrumbsFromPath(
  pathname: string,
  segmentLabels: Record<string, string> = {},
  basePath = ''
): BreadcrumbItemData[] {
  const segments = pathname.split('/').filter(Boolean);

  return segments.map((segment, index) => {
    const isLast = index === segments.length - 1;
    const href = isLast
      ? undefined
      : `${basePath}/${segments.slice(0, index + 1).join('/')}`;
    const label =
      segmentLabels[segment] ||
      segment.charAt(0).toUpperCase() + segment.slice(1);

    return {
      label,
      href,
    };
  });
}

/**
 * Hook for creating breadcrumbs with automatic current page detection
 *
 * @example
 * ```tsx
 * function MyPage() {
 *   const breadcrumbs = useBreadcrumbs([
 *     { label: "Home", href: "/" },
 *     { label: "Settings", href: "/settings" },
 *     { label: "Notifications" }
 *   ])
 *
 *   return <DynamicBreadcrumb items={breadcrumbs} />
 * }
 * ```
 */
export function useBreadcrumbs(
  items: BreadcrumbItemData[]
): BreadcrumbItemData[] {
  return React.useMemo(() => {
    // Ensure the last item doesn't have an href (current page)
    if (items.length > 0) {
      const lastIndex = items.length - 1;
      return items.map((item, index) =>
        index === lastIndex ? { ...item, href: undefined } : item
      );
    }
    return items;
  }, [items]);
}

// // Example 1: Settings Page Breadcrumbs
// export function SettingsBreadcrumb() {
//   const breadcrumbItems: BreadcrumbItemData[] = [
//     {
//       label: "Settings",
//       href: "/settings",
//       asChild: true
//     },
//     {
//       label: "Notifications"
//     }
//   ];

//   return (
//     <DynamicBreadcrumb
//       items={breadcrumbItems}
//       linkComponent={({ href, children }) => (
//         <Link href={href}>{children}</Link>
//       )}
//     />
//   );
// }

// // Example 2: Student Management Breadcrumbs
// export function StudentManagementBreadcrumb({ studentName }: { studentName: string }) {
//   const breadcrumbItems: BreadcrumbItemData[] = [
//     {
//       label: "Home",
//       href: "/",
//       asChild: true
//     },
//     {
//       label: "Students",
//       href: "/students",
//       asChild: true
//     },
//     {
//       label: studentName
//     }
//   ];

//   return (
//     <DynamicBreadcrumb
//       items={breadcrumbItems}
//       linkComponent={({ href, children }) => (
//         <Link href={href}>{children}</Link>
//       )}
//     />
//   );
// }

// // Example 3: Deep Navigation with Ellipsis
// export function DeepNavigationBreadcrumb() {
//   const breadcrumbItems: BreadcrumbItemData[] = [
//     { label: "Dashboard", href: "/dashboard", asChild: true },
//     { label: "Administration", href: "/dashboard/admin", asChild: true },
//     { label: "Districts", href: "/dashboard/admin/districts", asChild: true },
//     { label: "Bay Area Unified", href: "/dashboard/admin/districts/bay-area", asChild: true },
//     { label: "Schools", href: "/dashboard/admin/districts/bay-area/schools", asChild: true },
//     { label: "Lincoln High School", href: "/dashboard/admin/districts/bay-area/schools/lincoln", asChild: true },
//     { label: "Teachers", href: "/dashboard/admin/districts/bay-area/schools/lincoln/teachers", asChild: true },
//     { label: "Jane Doe" }
//   ];

//   return (
//     <DynamicBreadcrumb
//       items={breadcrumbItems}
//       maxItems={4} // Will show: Dashboard > ... > Teachers > Jane Doe
//       linkComponent={({ href, children }) => (
//         <Link href={href}>{children}</Link>
//       )}
//     />
//   );
// }

// // Example 4: Auto-generated from Pathname
// export function AutoBreadcrumb() {
//   const pathname = usePathname();

//   const breadcrumbs = createBreadcrumbsFromPath(
//     pathname,
//     {
//       // Custom labels for URL segments
//       settings: 'Settings',
//       profile: 'Profile',
//       security: 'Security',
//       notifications: 'Notifications',
//       district: 'District Settings',
//       schools: 'Schools',
//       members: 'Members',
//       admin: 'Administration',
//       students: 'Student Management',
//       teachers: 'Faculty',
//       dashboard: 'Dashboard',
//       reports: 'Reports'
//     }
//   );

//   return (
//     <DynamicBreadcrumb
//       items={breadcrumbs}
//       linkComponent={({ href, children }) => (
//         <Link href={href}>{children}</Link>
//       )}
//     />
//   );
// }

// // Example 5: Using the Hook for Dynamic Control
// export function DynamicControlBreadcrumb({ userRole, currentSection }: {
//   userRole: 'admin' | 'teacher' | 'student';
//   currentSection: string;
// }) {
//   const baseBreadcrumbs = React.useMemo(() => {
//     const items: BreadcrumbItemData[] = [
//       { label: "Dashboard", href: "/dashboard", asChild: true }
//     ];

//     // Add role-specific navigation
//     switch (userRole) {
//       case 'admin':
//         items.push({ label: "Administration", href: "/admin", asChild: true });
//         break;
//       case 'teacher':
//         items.push({ label: "Classroom", href: "/classroom", asChild: true });
//         break;
//       case 'student':
//         items.push({ label: "My Classes", href: "/classes", asChild: true });
//         break;
//     }

//     // Add current section
//     items.push({ label: currentSection });

//     return items;
//   }, [userRole, currentSection]);

//   const breadcrumbs = useBreadcrumbs(baseBreadcrumbs);

//   return (
//     <DynamicBreadcrumb
//       items={breadcrumbs}
//       linkComponent={({ href, children }) => (
//         <Link href={href}>{children}</Link>
//       )}
//     />
//   );
// }

// // Example 6: Custom Separator
// export function CustomSeparatorBreadcrumb() {
//   const breadcrumbItems: BreadcrumbItemData[] = [
//     { label: "Home", href: "/", asChild: true },
//     { label: "Settings", href: "/settings", asChild: true },
//     { label: "Account", href: "/settings/account", asChild: true },
//     { label: "Profile" }
//   ];

//   return (
//     <DynamicBreadcrumb
//       items={breadcrumbItems}
//       separator={<span className="text-muted-foreground">→</span>}
//       linkComponent={({ href, children }) => (
//         <Link href={href}>{children}</Link>
//       )}
//     />
//   );
// }

// // Example 7: Conditional Breadcrumbs
// export function ConditionalBreadcrumb({
//   showHome = true,
//   parentSection,
//   currentPage
// }: {
//   showHome?: boolean;
//   parentSection?: { label: string; href: string };
//   currentPage: string;
// }) {
//   const breadcrumbItems: BreadcrumbItemData[] = [];

//   if (showHome) {
//     breadcrumbItems.push({ label: "Home", href: "/", asChild: true });
//   }

//   if (parentSection) {
//     breadcrumbItems.push({
//       label: parentSection.label,
//       href: parentSection.href,
//       asChild: true
//     });
//   }

//   breadcrumbItems.push({ label: currentPage });

//   return (
//     <DynamicBreadcrumb
//       items={breadcrumbItems}
//       linkComponent={({ href, children }) => (
//         <Link href={href}>{children}</Link>
//       )}
//     />
//   );
// }

// // Example 8: Multi-tenant Breadcrumbs
// export function MultiTenantBreadcrumb({
//   organizationName,
//   departmentName,
//   currentPage
// }: {
//   organizationName: string;
//   departmentName?: string;
//   currentPage: string;
// }) {
//   const breadcrumbItems: BreadcrumbItemData[] = [
//     { label: organizationName, href: "/dashboard", asChild: true }
//   ];

//   if (departmentName) {
//     breadcrumbItems.push({
//       label: departmentName,
//       href: `/departments/${departmentName.toLowerCase()}`,
//       asChild: true
//     });
//   }

//   breadcrumbItems.push({ label: currentPage });

//   return (
//     <DynamicBreadcrumb
//       items={breadcrumbItems}
//       linkComponent={({ href, children }) => (
//         <Link href={href}>{children}</Link>
//       )}
//     />
//   );
// }
