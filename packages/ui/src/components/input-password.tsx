'use client';

import { EyeIcon, EyeOffIcon } from 'lucide-react';
import React from 'react';

import { Button } from './button';
import {
  InputWithAdornments,
  type InputWithAdornmentsElement,
  type InputWithAdornmentsProps,
} from './input-with-adornments';

export type InputPasswordElement = InputWithAdornmentsElement;
export type InputPasswordProps = Omit<InputWithAdornmentsProps, 'endAdornment'>;

const InputPassword = (props: InputPasswordProps) => {
  const [showPassword, setShowPassword] = React.useState<boolean>(false);

  const handleClickShowPassword = (): void => {
    setShowPassword((prev) => !prev);
  };

  const handleMouseDownPassword = (event: React.SyntheticEvent): void => {
    event.preventDefault();
  };

  return (
    <InputWithAdornments
      endAdornment={
        <Button
          aria-label="Toggle password visibility"
          className="-mr-2.5 size-8"
          disabled={props.disabled}
          onClick={handleClickShowPassword}
          onMouseDown={handleMouseDownPassword}
          size="icon"
          type="button"
          variant="ghost"
        >
          {showPassword ? (
            <EyeOffIcon className="size-4 shrink-0" />
          ) : (
            <EyeIcon className="size-4 shrink-0" />
          )}
        </Button>
      }
      type={showPassword ? 'text' : 'password'}
      {...props}
    />
  );
};

InputPassword.displayName = 'InputPassword';

export { InputPassword };
