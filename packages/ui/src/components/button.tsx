import { cn } from '@lilypad/ui/lib/utils';
import { cva, type VariantProps } from 'class-variance-authority';
import { Slot as SlotPrimitive } from 'radix-ui';
import type * as React from 'react';
import { Spinner } from './spinner';

const buttonVariants = cva(
  `dark:aria-invalid:ring-destructive/40'size-'])]:size-4 inline-flex shrink-0 cursor-pointer items-center justify-center gap-2 whitespace-nowrap rounded-md font-medium text-sm outline-none transition-all [&_svg:not([class*= focus-visible:border-ring focus-visible:ring-[3px] focus-visible:ring-ring/50 disabled:pointer-events-none disabled:opacity-50 aria-invalid:border-destructive aria-invalid:ring-destructive/20 [&_svg]:pointer-events-none [&_svg]:shrink-0`,
  {
    variants: {
      variant: {
        default:
          'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',
        destructive:
          'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:bg-destructive/60 dark:focus-visible:ring-destructive/40',
        outline:
          'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:border-input dark:bg-input/30 dark:hover:bg-input/50',
        secondary:
          'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',
        ghost:
          'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',
        link: 'text-primary underline-offset-4 hover:underline',
        cancel:
          'hover:bg-red-100 hover:text-red-700 dark:hover:bg-muted-foreground/10 dark:hover:text-red-300',
      },
      size: {
        default: 'h-9 px-4 py-2 has-[>svg]:px-3',
        xs: 'h-7 gap-1.5 px-3 text-xs has-[>svg]:px-2.5',
        sm: 'h-8 gap-1.5 px-3 text-xs has-[>svg]:px-2.5',
        lg: 'h-10 px-6 has-[>svg]:px-4',
        icon: 'size-9',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

export interface ButtonProps
  extends React.ComponentProps<'button'>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
  loading?: boolean;
}

function Button({
  className,
  variant,
  size,
  children,
  asChild = false,
  loading = false,
  disabled,
  ...props
}: ButtonProps) {
  const Comp = asChild ? SlotPrimitive.Slot : 'button';

  const buttonContent = loading ? (
    <>
      {/* Preserve width by keeping children but making them invisible */}
      <span className="opacity-0">{children}</span>
      <span className="-translate-x-1/2 -translate-y-1/2 absolute top-1/2 left-1/2">
        <Spinner className="size-4 text-current" />
      </span>
    </>
  ) : (
    children
  );

  return (
    <Comp
      className={cn(
        buttonVariants({ variant, size, className }),
        loading && 'relative'
      )}
      data-slot="button"
      disabled={disabled || loading}
      {...props}
    >
      {buttonContent}
    </Comp>
  );
}

export { Button, buttonVariants };
