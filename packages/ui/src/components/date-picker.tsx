import { cn } from '@lilypad/ui/lib/utils';
import { CalendarIcon } from 'lucide-react';
import React from 'react';
import { Button } from './button';
import { Calendar } from './calendar';
import { Popover, PopoverContent, PopoverTrigger } from './popover';

interface DatePickerProps {
  date: Date | undefined;
  setDate: (date: Date | undefined) => void;
  className?: string;
}

export const DatePicker = ({ date, setDate, className }: DatePickerProps) => {
  const [open, setOpen] = React.useState(false);

  const onSelect = (_date: Date | undefined) => {
    setDate(_date);
    setOpen(false);
  };

  return (
    <Popover onOpenChange={setOpen} open={open}>
      <PopoverTrigger asChild>
        <Button
          className={cn('w-48 justify-between font-normal', className)}
          id="date"
          variant="outline"
        >
          {date ? date.toLocaleDateString() : 'Select date'}
          <CalendarIcon className="size-4" />
        </Button>
      </PopoverTrigger>
      <PopoverContent align="start" className="w-auto overflow-hidden p-0">
        <Calendar
          captionLayout="dropdown"
          mode="single"
          onSelect={onSelect}
          selected={date}
        />
      </PopoverContent>
    </Popover>
  );
};
