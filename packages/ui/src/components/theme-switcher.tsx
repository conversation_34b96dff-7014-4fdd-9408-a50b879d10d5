'use client';

import { cn } from '@lilypad/ui/lib/utils';
import { cva, type VariantProps } from 'class-variance-authority';
import { Monitor, Moon, Sun } from 'lucide-react';
import { motion } from 'motion/react';
import { useTheme } from 'next-themes';
import { Slot as SlotPrimitive } from 'radix-ui';
import React, { useEffect, useState } from 'react';

const themes = [
  {
    key: 'system',
    icon: Monitor,
    label: 'System theme',
  },
  {
    key: 'light',
    icon: Sun,
    label: 'Light theme',
  },
  {
    key: 'dark',
    icon: Moon,
    label: 'Dark theme',
  },
];

const themeSwitcherVariants = cva(
  'relative isolate flex rounded-full bg-background outline-none ring-1 ring-border transition-all focus-within:ring-[3px] focus-within:ring-ring/50',
  {
    variants: {
      variant: {
        default: 'bg-background ring-border',
        secondary: 'bg-secondary ring-secondary-foreground/20',
        outline: 'bg-transparent shadow-xs ring-input',
      },
      size: {
        sm: 'h-6 gap-0.5 p-0.5',
        default: 'h-8 gap-1 p-1',
        lg: 'h-10 gap-1.5 p-1.5',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

const themeSwitcherButtonVariants = cva(
  'relative cursor-pointer rounded-full transition-all hover:bg-accent/50 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring/50 focus-visible:ring-offset-1 disabled:pointer-events-none disabled:opacity-50',
  {
    variants: {
      size: {
        sm: 'h-5 w-5',
        default: 'h-6 w-6',
        lg: 'h-7 w-7',
      },
    },
    defaultVariants: {
      size: 'default',
    },
  }
);

function ThemeSwitcher({
  className,
  variant,
  size,
  asChild = false,
  ...props
}: React.ComponentProps<'div'> &
  VariantProps<typeof themeSwitcherVariants> & {
    asChild?: boolean;
  }) {
  const [mounted, setMounted] = useState(false);
  const { setTheme, theme } = useTheme();

  const iconSize = React.useMemo(() => {
    if (size === 'sm') {
      return 'size-3';
    }
    if (size === 'lg') {
      return 'size-5';
    }
    return 'size-4';
  }, [size]);
  // Prevent hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null;
  }

  const Comp = asChild ? SlotPrimitive.Slot : 'div';

  return (
    <Comp
      className={cn(themeSwitcherVariants({ variant, size }), className)}
      data-slot="theme-switcher"
      {...props}
    >
      {themes.map(({ key, icon: Icon, label }) => {
        const isActive = theme === key;
        return (
          <button
            aria-label={label}
            aria-pressed={isActive}
            className={cn(themeSwitcherButtonVariants({ size }))}
            key={key}
            onClick={() => setTheme(key as 'light' | 'dark' | 'system')}
            type="button"
          >
            {isActive && (
              <motion.div
                className="absolute inset-0 rounded-full bg-secondary"
                layoutId="activeTheme"
                transition={{ type: 'spring', duration: 0.5 }}
              />
            )}
            <Icon
              className={cn(
                'relative z-10 m-auto',
                iconSize,
                isActive ? 'text-foreground' : 'text-muted-foreground'
              )}
            />
          </button>
        );
      })}
    </Comp>
  );
}

export { ThemeSwitcher, themeSwitcherVariants };
