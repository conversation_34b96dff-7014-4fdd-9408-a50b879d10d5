import { cn } from '@lilypad/ui/lib/utils';
import { cva, type VariantProps } from 'class-variance-authority';

const rolesBadgeVariants = cva(
  'inline-flex w-fit shrink-0 items-center justify-center gap-1 whitespace-nowrap rounded-full border px-2 py-0.5 font-medium text-xs transition-colors [&>svg]:pointer-events-none [&>svg]:size-3',
  {
    variants: {
      role: {
        SUPER_USER:
          'border-purple-500 bg-gradient-to-r from-purple-500 to-pink-500 text-white dark:from-purple-600 dark:to-pink-600',
        SPECIAL_ED_DIRECTOR:
          'border-blue-500/80 bg-blue-200/60 text-blue-600 dark:border-border dark:bg-muted dark:text-sky-600',
        CLINICAL_DIRECTOR:
          'border-teal-500/80 bg-teal-200/60 text-teal-600 dark:border-border dark:bg-muted dark:text-teal-600',
        SCHOOL_COORDINATOR:
          'border-indigo-500/80 bg-indigo-200/60 text-indigo-600 dark:border-border dark:bg-muted dark:text-indigo-600',
        SCHOOL_ADMIN:
          'border-emerald-500/80 bg-emerald-200/60 text-emerald-600 dark:border-border dark:bg-muted dark:text-emerald-600',
        CASE_MANAGER:
          'border-amber-500/80 bg-amber-200/60 text-amber-600 dark:border-border dark:bg-muted dark:text-amber-600',
        PSYCHOLOGIST:
          'border-cyan-500/80 bg-cyan-200/60 text-cyan-600 dark:border-border dark:bg-muted dark:text-cyan-600',
        PROCTOR:
          'border-orange-500/80 bg-orange-200/60 text-orange-600 dark:border-border dark:bg-muted dark:text-orange-600',
        ASSISTANT:
          'border-gray-500/80 bg-gray-200/60 text-gray-600 dark:border-border dark:bg-muted dark:text-gray-400',
      },
    },
  }
);

const roleDisplayNames = {
  SUPER_USER: { full: 'Super User', short: 'Super User' },
  SPECIAL_ED_DIRECTOR: {
    full: 'Special Education Director',
    short: 'SpEd Director',
  },
  CLINICAL_DIRECTOR: { full: 'Clinical Director', short: 'Clinical Dir.' },
  SCHOOL_COORDINATOR: { full: 'School Coordinator', short: 'Coordinator' },
  SCHOOL_ADMIN: { full: 'School Admin', short: 'Admin' },
  CASE_MANAGER: { full: 'Case Manager', short: 'Case Mgr.' },
  PSYCHOLOGIST: { full: 'Psychologist', short: 'Psychologist' },
  PROCTOR: { full: 'Proctor', short: 'Proctor' },
  ASSISTANT: { full: 'Assistant', short: 'Assistant' },
} as const;

type RoleEnum = keyof typeof roleDisplayNames;

interface RolesBadgeProps
  extends React.ComponentProps<'span'>,
    VariantProps<typeof rolesBadgeVariants> {
  role: RoleEnum;
  showFullName?: boolean;
}

export function RolesBadge({
  className,
  role,
  showFullName = true,
  ...props
}: RolesBadgeProps) {
  const displayName = showFullName
    ? roleDisplayNames[role].full
    : roleDisplayNames[role].short;

  return (
    <span className={cn(rolesBadgeVariants({ role }), className)} {...props}>
      {displayName}
    </span>
  );
}

export { rolesBadgeVariants, type RoleEnum };
