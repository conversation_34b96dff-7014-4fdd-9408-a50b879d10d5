'use client';

import { type Tab, useTabs } from '@lilypad/ui/hooks/use-tabs';
import { cn } from '@lilypad/ui/lib/utils';
import { AnimatePresence, motion } from 'motion/react';
import React from 'react';

const transition = {
  type: 'tween',
  ease: 'easeOut',
  duration: 0.15,
} as const;

const getHoverAnimationProps = (
  hoveredRect: DOMRect,
  navRect: DOMRect,
  scrollLeft = 0
) => ({
  x: hoveredRect.left - navRect.left + scrollLeft - 10,
  y: hoveredRect.top - navRect.top - 4,
  width: hoveredRect.width + 20,
  height: hoveredRect.height + 10,
});

// Enhanced Tab interface for routing support
interface RoutedTab extends Tab {
  href?: string; // Primary route for this tab
  subRoutes?: string[]; // Additional routes that should activate this tab
}

// Context for sharing tab state
interface AnimatedTabsContextValue {
  selectedTab: Tab;
  selectedTabIndex: number;
  setSelectedTab: (input: [number, number]) => void;
  tabs: Tab[];
  onValueChange?: (value: string) => void;
  // Navigation props
  enableRouting?: boolean;
  onNavigate?: (href: string, tabValue: string) => void;
}

const AnimatedTabsContext =
  React.createContext<AnimatedTabsContextValue | null>(null);

function useAnimatedTabsContext() {
  const context = React.useContext(AnimatedTabsContext);
  if (!context) {
    throw new Error('AnimatedTabs components must be used within AnimatedTabs');
  }
  return context;
}

// Hook for route-based tab detection
function useRouteBasedTab(tabs: RoutedTab[], currentPath: string) {
  return React.useMemo(() => {
    if (!currentPath) {
      return null;
    }

    // Find tab that matches current route
    const matchingTab = tabs.find((tab) => {
      // Check primary href
      if (tab.href === currentPath) {
        return true;
      }
      return false;
    });

    return matchingTab?.value || null;
  }, [tabs, currentPath]);
}

// Root component
interface AnimatedTabsProps {
  tabs: Tab[] | RoutedTab[];
  defaultValue?: string;
  value?: string;
  onValueChange?: (value: string) => void;
  children: React.ReactNode;
  className?: string;
  // Navigation props
  enableRouting?: boolean;
  currentPath?: string; // Current pathname for route-based activation
  onNavigate?: (href: string, tabValue: string) => void; // Custom navigation handler
}

function AnimatedTabs({
  tabs,
  defaultValue,
  value,
  onValueChange,
  children,
  className,
  enableRouting = false,
  currentPath,
  onNavigate,
}: AnimatedTabsProps) {
  // Determine active tab based on route if routing is enabled
  const routeBasedValue = useRouteBasedTab(
    enableRouting ? (tabs as RoutedTab[]) : [],
    currentPath || ''
  );

  const effectiveValue =
    enableRouting && routeBasedValue ? routeBasedValue : value;

  const effectiveDefaultValue =
    enableRouting && routeBasedValue ? routeBasedValue : defaultValue;

  const [hookProps] = React.useState(() => {
    const initialTabId =
      effectiveDefaultValue || effectiveValue || tabs[0]?.value || '';
    return {
      tabs: tabs.map(({ label, value: _value, subRoutes }) => ({
        label,
        value: _value,
        subRoutes,
      })),
      initialTabId,
      onChange: onValueChange,
    };
  });

  const framer = useTabs(hookProps);

  // Handle controlled value changes and route-based changes
  React.useEffect(() => {
    const targetValue =
      enableRouting && routeBasedValue
        ? routeBasedValue
        : effectiveValue || routeBasedValue;
    if (targetValue && targetValue !== framer.selectedTab.value) {
      const newIndex = tabs.findIndex((tab) => tab.value === targetValue);
      if (newIndex !== -1) {
        framer.tabProps.setSelectedTab([
          newIndex,
          newIndex > framer.tabProps.selectedTabIndex ? 1 : -1,
        ]);
      }
    }
  }, [
    effectiveValue,
    routeBasedValue,
    framer.selectedTab.value,
    framer.tabProps,
    tabs,
    enableRouting,
  ]);

  const contextValue: AnimatedTabsContextValue = {
    selectedTab: framer.selectedTab,
    selectedTabIndex: framer.tabProps.selectedTabIndex,
    setSelectedTab: framer.tabProps.setSelectedTab,
    tabs,
    onValueChange,
    enableRouting,
    onNavigate,
  };

  return (
    <AnimatedTabsContext.Provider value={contextValue}>
      <div className={cn('w-full', className)}>{children}</div>
    </AnimatedTabsContext.Provider>
  );
}

interface AnimatedTabsListProps {
  children: React.ReactNode;
  className?: string;
  enableHorizontalScroll?: boolean;
}

function AnimatedTabsList({
  children,
  className,
  enableHorizontalScroll = false,
}: AnimatedTabsListProps) {
  const {
    tabs,
    selectedTabIndex,
    setSelectedTab,
    enableRouting,
    onNavigate,
    onValueChange,
  } = useAnimatedTabsContext();
  const [buttonRefs, setButtonRefs] = React.useState<
    Array<HTMLButtonElement | null>
  >([]);

  React.useEffect(() => {
    setButtonRefs((prev) => prev.slice(0, tabs.length));
  }, [tabs.length]);

  const navRef = React.useRef<HTMLDivElement>(null);
  const navRect = navRef.current?.getBoundingClientRect();
  const selectedRect = buttonRefs[selectedTabIndex]?.getBoundingClientRect();

  const [hoveredTabIndex, setHoveredTabIndex] = React.useState<number | null>(
    null
  );
  const [scrollLeft, setScrollLeft] = React.useState(0);

  React.useEffect(() => {
    const navElement = navRef.current;
    if (!navElement) {
      return;
    }

    const handleScroll = () => {
      setScrollLeft(navElement.scrollLeft);
    };

    navElement.addEventListener('scroll', handleScroll);
    return () => navElement.removeEventListener('scroll', handleScroll);
  }, []);

  const hoveredRect =
    buttonRefs[hoveredTabIndex ?? -1]?.getBoundingClientRect();

  const handleTabClick = (index: number) => {
    const tab = tabs[index] as RoutedTab;

    // Always update the selected tab
    setSelectedTab([index, index > selectedTabIndex ? 1 : -1]);

    // Call onValueChange if provided
    if (onValueChange) {
      onValueChange(tab.value);
    }

    // Handle routing if enabled
    if (enableRouting && tab.href && onNavigate) {
      onNavigate(tab.href, tab.value);
    }
  };

  return (
    <nav
      className={cn(
        'relative flex items-center justify-start border-border border-b',
        enableHorizontalScroll ? 'overflow-x-auto' : 'overflow-x-hidden',
        'gap-4 overflow-y-hidden px-4 py-2',
        className
      )}
      onPointerLeave={() => setHoveredTabIndex(null)}
      ref={navRef}
    >
      {React.Children.map(children, (child, index) => {
        if (React.isValidElement<AnimatedTabsTriggerProps>(child)) {
          return React.cloneElement(child, {
            ...child.props,
            index,
            buttonRef: (el: HTMLButtonElement | null) => {
              buttonRefs[index] = el;
            },
            onPointerEnter: () => setHoveredTabIndex(index),
            onFocus: () => setHoveredTabIndex(index),
            onClick: () => handleTabClick(index),
            enableHorizontalScroll,
          });
        }
        return child;
      })}

      <AnimatePresence>
        {hoveredRect && navRect && (
          <motion.div
            animate={{
              ...getHoverAnimationProps(hoveredRect, navRect, scrollLeft),
              opacity: 1,
            }}
            className="absolute top-0 left-0 z-10 rounded-md bg-muted"
            exit={{
              ...getHoverAnimationProps(hoveredRect, navRect, scrollLeft),
              opacity: 0,
            }}
            initial={{
              ...getHoverAnimationProps(hoveredRect, navRect, scrollLeft),
              opacity: 0,
            }}
            key="hover"
            transition={transition}
          />
        )}
      </AnimatePresence>

      <AnimatePresence>
        {selectedRect && navRect && (
          <motion.div
            animate={{
              width: selectedRect.width + 18,
              x: `calc(${selectedRect.left - navRect.left + scrollLeft - 9}px)`,
              opacity: 1,
            }}
            className="absolute bottom-0 left-0 z-10 h-[2px] bg-primary"
            initial={false}
            transition={transition}
          />
        )}
      </AnimatePresence>
    </nav>
  );
}

// Tab trigger component
interface AnimatedTabsTriggerProps {
  value: string;
  children: React.ReactNode;
  className?: string;
  // These props are injected by AnimatedTabsList
  index?: number;
  buttonRef?: (el: HTMLButtonElement | null) => void;
  onPointerEnter?: () => void;
  onFocus?: () => void;
  onClick?: () => void;
  enableHorizontalScroll?: boolean;
}

function AnimatedTabsTrigger({
  value,
  children,
  className,
  buttonRef,
  onPointerEnter,
  onFocus,
  onClick,
  enableHorizontalScroll,
}: AnimatedTabsTriggerProps) {
  const { selectedTab } = useAnimatedTabsContext();
  const isActive = selectedTab.value === value;

  return (
    <button
      className={cn(
        'relative z-20 flex h-6 cursor-pointer select-none items-center rounded-md bg-transparent px-2 text-sm transition-colors',
        enableHorizontalScroll && 'flex-shrink-0',
        className
      )}
      onClick={onClick}
      onFocus={onFocus}
      onPointerEnter={onPointerEnter}
      ref={buttonRef}
      type="button"
    >
      <motion.span
        className={cn('block flex items-center gap-2', {
          'text-muted-foreground': !isActive,
          'font-semibold text-foreground': isActive,
        })}
      >
        {children}
      </motion.span>
    </button>
  );
}

interface AnimatedTabsContentProps {
  value: string;
  children: React.ReactNode;
  className?: string;
}

function AnimatedTabsContent({
  value,
  children,
  className,
}: AnimatedTabsContentProps) {
  const { selectedTab } = useAnimatedTabsContext();
  const isSelected = selectedTab.value === value;

  if (!isSelected) {
    return null;
  }

  return (
    <motion.div
      animate={{ opacity: 1, x: 0 }}
      className={cn('outline-none', className)}
      exit={{ opacity: 0, x: -20 }}
      initial={{ opacity: 0, x: 20 }}
      key={value}
      transition={transition}
    >
      {children}
    </motion.div>
  );
}

// Wrapper for content with AnimatePresence
interface AnimatedTabsContentWrapperProps {
  children: React.ReactNode;
  className?: string;
}

function AnimatedTabsContentWrapper({
  children,
  className,
}: AnimatedTabsContentWrapperProps) {
  const { selectedTab } = useAnimatedTabsContext();

  return (
    <div className={cn('flex-1', className)}>
      <AnimatePresence mode="wait">
        {React.Children.map(children, (child) => {
          if (
            React.isValidElement(child) &&
            (child.props as { value?: string }).value === selectedTab.value
          ) {
            return child;
          }
          return null;
        })}
      </AnimatePresence>
    </div>
  );
}

export {
  AnimatedTabs,
  AnimatedTabsList,
  AnimatedTabsTrigger,
  AnimatedTabsContent,
  AnimatedTabsContentWrapper,
  type RoutedTab,
};
