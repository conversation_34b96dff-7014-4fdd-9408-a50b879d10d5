import { cn } from '@lilypad/ui/lib/utils';
import { cva, type VariantProps } from 'class-variance-authority';

const schoolTypeBadgeVariants = cva(
  'inline-flex w-fit shrink-0 items-center justify-center gap-1 whitespace-nowrap rounded-full border px-2 py-0.5 font-medium text-xs transition-colors [&>svg]:pointer-events-none [&>svg]:size-3',
  {
    variants: {
      schoolType: {
        REGULAR_PUBLIC_PRIMARY:
          'border-blue-500/80 bg-blue-200/60 text-blue-700 dark:border-border dark:bg-muted dark:text-blue-400',
        REGULAR_PUBLIC_MIDDLE:
          'border-indigo-500/80 bg-indigo-200/60 text-indigo-700 dark:border-border dark:bg-muted dark:text-indigo-400',
        REGULAR_PUBLIC_HIGH:
          'border-purple-500/80 bg-purple-200/60 text-purple-700 dark:border-border dark:bg-muted dark:text-purple-400',
        REGULAR_PUBLIC_UNIFIED:
          'border-violet-500/80 bg-violet-200/60 text-violet-700 dark:border-border dark:bg-muted dark:text-violet-400',
        SPECIAL_ED_PUBLIC:
          'border-emerald-500/80 bg-emerald-200/60 text-emerald-700 dark:border-border dark:bg-muted dark:text-emerald-400',
        VOCATIONAL_PUBLIC:
          'border-amber-500/80 bg-amber-200/60 text-amber-700 dark:border-border dark:bg-muted dark:text-amber-400',
        ALTERNATIVE_PUBLIC:
          'border-orange-500/80 bg-orange-200/60 text-orange-700 dark:border-border dark:bg-muted dark:text-orange-400',
        REPORTABLE_PROGRAM:
          'border-slate-500/80 bg-slate-200/60 text-slate-700 dark:border-border dark:bg-muted dark:text-slate-400',
        PUBLIC_CHARTER:
          'border-teal-500/80 bg-teal-200/60 text-teal-700 dark:border-border dark:bg-muted dark:text-teal-400',
        MAGNET_PUBLIC:
          'border-cyan-500/80 bg-cyan-200/60 text-cyan-700 dark:border-border dark:bg-muted dark:text-cyan-400',
        VIRTUAL_PUBLIC:
          'border-sky-500/80 bg-sky-200/60 text-sky-700 dark:border-border dark:bg-muted dark:text-sky-400',
        DODEA_SCHOOL:
          'border-red-500/80 bg-red-200/60 text-red-700 dark:border-border dark:bg-muted dark:text-red-400',
        BIE_SCHOOL:
          'border-yellow-500/80 bg-yellow-200/60 text-yellow-700 dark:border-border dark:bg-muted dark:text-yellow-400',
        PRIVATE_CATHOLIC:
          'border-rose-500/80 bg-rose-200/60 text-rose-700 dark:border-border dark:bg-muted dark:text-rose-400',
        PRIVATE_OTHER_RELIGIOUS:
          'border-pink-500/80 bg-pink-200/60 text-pink-700 dark:border-border dark:bg-muted dark:text-pink-400',
        PRIVATE_NONSECTARIAN:
          'border-gray-500/80 bg-gray-200/60 text-gray-700 dark:border-border dark:bg-muted dark:text-gray-400',
      },
    },
  }
);

const schoolTypeDisplayNames = {
  REGULAR_PUBLIC_PRIMARY: { full: 'Regular Public Primary', short: 'Primary' },
  REGULAR_PUBLIC_MIDDLE: { full: 'Regular Public Middle', short: 'Middle' },
  REGULAR_PUBLIC_HIGH: { full: 'Regular Public High', short: 'High School' },
  REGULAR_PUBLIC_UNIFIED: { full: 'Regular Public Unified', short: 'Unified' },
  SPECIAL_ED_PUBLIC: { full: 'Special Ed Public', short: 'Special Ed' },
  VOCATIONAL_PUBLIC: { full: 'Vocational Public', short: 'Vocational' },
  ALTERNATIVE_PUBLIC: { full: 'Alternative Public', short: 'Alternative' },
  REPORTABLE_PROGRAM: { full: 'Reportable Program', short: 'Program' },
  PUBLIC_CHARTER: { full: 'Public Charter', short: 'Charter' },
  MAGNET_PUBLIC: { full: 'Magnet Public', short: 'Magnet' },
  VIRTUAL_PUBLIC: { full: 'Virtual Public', short: 'Virtual' },
  DODEA_SCHOOL: { full: 'DOD/EA School', short: 'DOD/EA' },
  BIE_SCHOOL: { full: 'BIE School', short: 'BIE' },
  PRIVATE_CATHOLIC: { full: 'Private Catholic', short: 'Catholic' },
  PRIVATE_OTHER_RELIGIOUS: {
    full: 'Private Other Religious',
    short: 'Religious',
  },
  PRIVATE_NONSECTARIAN: { full: 'Private Nonsectarian', short: 'Private' },
} as const;

type SchoolTypeEnum = keyof typeof schoolTypeDisplayNames;

interface SchoolTypeBadgeProps
  extends React.ComponentProps<'span'>,
    VariantProps<typeof schoolTypeBadgeVariants> {
  schoolType: SchoolTypeEnum;
  showFullName?: boolean;
}

export function SchoolTypeBadge({
  className,
  schoolType,
  showFullName = true,
  ...props
}: SchoolTypeBadgeProps) {
  const displayName = showFullName
    ? schoolTypeDisplayNames[schoolType].full
    : schoolTypeDisplayNames[schoolType].short;

  return (
    <span
      className={cn(schoolTypeBadgeVariants({ schoolType }), className)}
      {...props}
    >
      {displayName}
    </span>
  );
}

export { schoolTypeBadgeVariants, type SchoolTypeEnum };
