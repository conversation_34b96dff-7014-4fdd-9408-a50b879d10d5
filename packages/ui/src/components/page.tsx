'use client';

import React from 'react';

import { cn } from '../lib/utils';
import { If } from './if';
import { ScrollArea } from './scroll-area';
import { Separator } from './separator';
import { SidebarTrigger, useSidebar } from './sidebar';

export type PageElement = HTMLDivElement;

export type PageProps = React.HTMLAttributes<HTMLDivElement>;
function Page({ children, className, ...other }: PageProps) {
  return (
    <div
      className={cn('flex h-screen flex-col overflow-hidden', className)}
      {...other}
    >
      {children}
    </div>
  );
}

export type PageHeaderElement = HTMLDivElement;
export type PageHeaderProps = React.HTMLAttributes<HTMLDivElement>;

function PageHeader({ className, children, ...other }: PageHeaderProps) {
  const { isMobile, open } = useSidebar();

  // Count the number of bars (PrimaryBar, SecondaryBar, etc.)
  const childrenArray = React.Children.toArray(children);
  const barCount = childrenArray.filter(
    (child) =>
      React.isValidElement(child) &&
      (child.type === PagePrimaryBar || child.type === PageSecondaryBar)
  ).length;

  // Calculate header height based on number of bars (each bar is h-12 = 3rem)
  const headerHeight = `${barCount * 3}rem`;

  return (
    <div
      className={cn(
        'fixed top-0 z-50 w-full shrink-0 bg-background transition-[width] duration-200 ease-linear',
        !isMobile && open && 'w-[calc(100%-var(--sidebar-width))]',
        !(isMobile || open) && 'w-[calc(100%-var(--sidebar-width-icon))]',
        className
      )}
      style={
        {
          '--page-header-height': headerHeight,
        } as React.CSSProperties
      }
      {...other}
    >
      {children}
    </div>
  );
}

export type PagePrimaryBarElement = HTMLDivElement;
export type PagePrimaryBarProps = React.HTMLAttributes<HTMLDivElement> & {
  actions?: React.ReactNode;
};

function PagePrimaryBar({
  className,
  children,
  actions,
  ...other
}: PagePrimaryBarProps) {
  return (
    <div
      className={cn(
        'relative flex h-12 flex-row items-center gap-1 border-b px-4',
        className
      )}
      {...other}
    >
      <SidebarTrigger />
      <Separator className="mr-2 ml-0.5 h-4!" orientation="vertical" />
      <div className="flex w-full flex-row items-center justify-between">
        {children}
      </div>
      <If condition={actions}>
        <Separator className="mr-2 h-4!" orientation="vertical" />
        {actions}
      </If>
    </div>
  );
}

export type PagePrimaryBarActionsElement = HTMLDivElement;
export type PagePrimaryBarActionsProps = React.HTMLAttributes<HTMLDivElement>;

function PagePrimaryBarActions({
  className,
  children,
  ...other
}: PagePrimaryBarActionsProps) {
  return (
    <div className={cn('flex items-center gap-2', className)} {...other}>
      {children}
    </div>
  );
}

export type PageTitleElement = HTMLHeadingElement;
export type PageTitleProps = React.HTMLAttributes<HTMLHeadingElement>;

function PageTitle({ className, children, ...other }: PageTitleProps) {
  return (
    <h1 className={cn('font-medium text-sm', className)} {...other}>
      {children}
    </h1>
  );
}

export type PageActionsElement = HTMLDivElement;
export type PageActionsProps = React.HTMLAttributes<HTMLDivElement>;

function PageActions({ className, children, ...other }: PageActionsProps) {
  return (
    <div className={cn('flex items-center gap-2', className)} {...other}>
      {children}
    </div>
  );
}

export type PageSecondaryBarElement = HTMLDivElement;
export type PageSecondaryBarProps = React.HTMLAttributes<HTMLDivElement>;

function PageSecondaryBar({
  className,
  children,
  ...other
}: PageSecondaryBarProps) {
  return (
    <div
      className={cn(
        'relative flex h-12 items-center justify-between gap-2 border-b px-4 sm:px-6',
        className
      )}
      {...other}
    >
      {children}
    </div>
  );
}

export type PageBodyElement = HTMLDivElement;
export type PageBodyProps = React.HTMLAttributes<HTMLDivElement> & {
  disableScroll?: boolean;
};

function PageBody({
  children,
  className,
  disableScroll = false,
  ...other
}: PageBodyProps) {
  // Get the header height from the CSS variable or default to 3rem (one bar)
  const headerHeight = 'var(--page-header-height, 3rem)';

  const containerClasses = cn('flex flex-col overflow-scroll', className);

  const containerStyle: React.CSSProperties = {
    paddingTop: headerHeight,
    height: '100%',
  };

  if (disableScroll) {
    return (
      <div className={containerClasses} style={containerStyle} {...other}>
        {children}
      </div>
    );
  }

  return (
    <div
      className={cn('overflow-hidden', containerClasses)}
      style={containerStyle}
      {...other}
    >
      <ScrollArea className="h-full">{children}</ScrollArea>
    </div>
  );
}

export {
  Page,
  PageActions,
  PageBody,
  PageHeader,
  PagePrimaryBar,
  PageSecondaryBar,
  PageTitle,
  PagePrimaryBarActions,
};
