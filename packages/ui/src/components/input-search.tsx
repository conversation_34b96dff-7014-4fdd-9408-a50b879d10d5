'use client';

import { SearchIcon, XIcon } from 'lucide-react';
import React from 'react';

import { Button } from './button';
import {
  InputWithAdornments,
  type InputWithAdornmentsProps,
} from './input-with-adornments';

export type InputSearchProps = Omit<
  InputWithAdornmentsProps,
  'startAdornment' | 'endAdornment'
> & {
  debounceTime?: number;
  onClear?: () => void;
  clearButtonProps?: React.ComponentProps<typeof Button>;
  alwaysShowClearButton?: boolean;
};

export const InputSearch = ({
  onChange,
  value,
  disabled,
  debounceTime = 175,
  onClear,
  clearButtonProps,
  alwaysShowClearButton,
  ...props
}: InputSearchProps) => {
  const [innerValue, setInnerValue] = React.useState(value || '');
  const timeoutRef = React.useRef<NodeJS.Timeout | null>(null);

  React.useEffect(() => {
    setInnerValue(value || '');
  }, [value]);

  const handleChange = React.useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = event.target.value;
      setInnerValue(newValue);

      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      timeoutRef.current = setTimeout(() => {
        onChange?.(event);
      }, debounceTime);
    },
    [onChange, debounceTime]
  );

  const handleClear = React.useCallback(() => {
    setInnerValue('');
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    onChange?.({
      target: { value: '' },
    } as React.ChangeEvent<HTMLInputElement>);
    onClear?.();
  }, [onChange, onClear]);

  React.useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return (
    <InputWithAdornments
      disabled={disabled}
      endAdornment={
        alwaysShowClearButton || innerValue ? (
          <Button
            className="-mr-2.5 flex size-8"
            onClick={handleClear}
            size="icon"
            type="button"
            variant="ghost"
            {...clearButtonProps}
          >
            <XIcon className="size-4 shrink-0" />
          </Button>
        ) : undefined
      }
      onChange={handleChange}
      startAdornment={<SearchIcon className="size-4 shrink-0" />}
      value={innerValue}
      {...props}
    />
  );
};

InputSearch.displayName = 'InputSearch';
