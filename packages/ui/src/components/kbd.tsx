import { cn } from '@lilypad/ui/lib/utils';
import { cva, type VariantProps } from 'class-variance-authority';
import { Slot as SlotPrimitive } from 'radix-ui';
import type * as React from 'react';

const kbdVariants = cva(
  'inline-flex w-fit items-center gap-1 font-medium font-mono text-[10px] text-muted-foreground',
  {
    variants: {
      size: {
        default: 'h-6 rounded px-2',
        sm: 'h-5 rounded-sm px-1',
        lg: 'h-7 rounded-md px-2',
      },
      variant: {
        default: 'bg-primary/10',
        outline:
          "bg-background px-0 [&_[data-slot='kbd-key']]:min-w-[20px] [&_[data-slot='kbd-key']]:border [&_[data-slot='kbd-key']]:border-border [&_[data-slot='kbd-key']]:bg-muted/30 [&_[data-slot='kbd-key']]:px-1.5 [&_[data-slot='kbd-key']]:shadow-xs",
        ghost: 'bg-transparent shadow-none',
      },
    },
    defaultVariants: {
      size: 'default',
      variant: 'default',
    },
  }
);

interface KbdRootProps
  extends React.ComponentPropsWithoutRef<'kbd'>,
    VariantProps<typeof kbdVariants> {
  asChild?: boolean;
}

const KbdRoot = (props: KbdRootProps) => {
  const {
    variant = 'default',
    size = 'default',
    asChild,
    className,
    ...rootProps
  } = props;

  const RootPrimitive = asChild ? SlotPrimitive.Slot : 'kbd';

  return (
    <RootPrimitive
      className={cn(kbdVariants({ size, variant, className }))}
      data-slot="kbd"
      role="group"
      {...rootProps}
    />
  );
};
KbdRoot.displayName = 'KbdRoot';

const KEY_DESCRIPTIONS: Record<string, string> = {
  '⌘': 'Command',
  '⇧': 'Shift',
  '⌥': 'Option',
  '⌃': 'Control',
  Ctrl: 'Control',
  '⌫': 'Backspace',
  '⎋': 'Escape',
  '↩': 'Return',
  '⇥': 'Tab',
  '⌤': 'Enter',
  '↑': 'Arrow Up',
  '↓': 'Arrow Down',
  '←': 'Arrow Left',
  '→': 'Arrow Right',
  '⇪': 'Caps Lock',
  fn: 'Function',
  '⌦': 'Delete',
  '⇞': 'Page Up',
  '⇟': 'Page Down',
  '↖': 'Home',
  '↘': 'End',
  '↕': 'Page Up/Down',
  '↔': 'Left/Right',
} as const;

interface KbdKeyProps extends React.ComponentPropsWithoutRef<'span'> {
  asChild?: boolean;
}

const KbdKey = (props: KbdKeyProps) => {
  const { asChild, className, children, title: titleProp, ...keyProps } = props;

  const keyText = children?.toString() ?? '';
  const title = titleProp ?? KEY_DESCRIPTIONS[keyText] ?? keyText;

  const KeyPrimitive = asChild ? SlotPrimitive.Slot : 'span';

  return (
    <abbr className="no-underline" title={title}>
      <KeyPrimitive
        data-slot="kbd-key"
        {...keyProps}
        className={cn(
          'inline-flex items-center justify-center rounded',
          className
        )}
      >
        {children}
      </KeyPrimitive>
    </abbr>
  );
};
KbdKey.displayName = 'KbdKey';

interface KbdSeparatorProps extends React.ComponentPropsWithoutRef<'span'> {
  asChild?: boolean;
}

const KbdSeparator = (props: KbdSeparatorProps) => {
  const { asChild, children = '+', className, ...separatorProps } = props;

  const SeparatorPrimitive = asChild ? SlotPrimitive.Slot : 'span';

  return (
    // biome-ignore lint/a11y/useSemanticElements: Not necessary
    <SeparatorPrimitive
      aria-hidden="true"
      aria-orientation="horizontal"
      className={cn('text-foreground/70', className)}
      data-slot="kbd-separator"
      role="separator"
      {...separatorProps}
    >
      {children}
    </SeparatorPrimitive>
  );
};
KbdSeparator.displayName = 'KbdSeparator';

const Kbd = KbdRoot;
const Root = KbdRoot;
const Key = KbdKey;
const Separator = KbdSeparator;

export {
  Kbd,
  KbdKey,
  KbdSeparator,
  Key,
  //
  Root,
  Separator,
};
