/** biome-ignore-all lint/style/noNestedTernary: Fix later */
import type * as React from 'react';

import { cn } from '../lib/utils';

export type InputWithAdornmentsElement = HTMLInputElement;
export type InputWithAdornmentsProps =
  React.InputHTMLAttributes<HTMLInputElement> & {
    startAdornment?: React.JSX.Element;
    endAdornment?: React.JSX.Element;
    containerClassName?: string;
  };

const InputWithAdornments = ({
  className,
  startAdornment,
  endAdornment,
  containerClassName,
  ...props
}: InputWithAdornmentsProps) => (
  <div className={cn('relative inline-block h-9 w-full', containerClassName)}>
    {startAdornment && (
      <span className="-translate-y-1/2 absolute top-1/2 left-3 flex text-muted-foreground">
        {startAdornment}
      </span>
    )}
    <input
      className={cn(
        'flex h-9 w-full min-w-0 rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-xs outline-none transition-[color,box-shadow] selection:bg-primary selection:text-primary-foreground file:inline-flex file:h-7 file:border-0 file:bg-transparent file:font-medium file:text-foreground file:text-sm placeholder:text-muted-foreground disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm dark:bg-input/30',
        'focus-visible:border-ring focus-visible:ring-[3px] focus-visible:ring-ring/50',
        'aria-invalid:border-destructive aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40',
        startAdornment && endAdornment
          ? 'px-10'
          : startAdornment
            ? 'pr-4 pl-10'
            : endAdornment
              ? 'pr-10 pl-4'
              : '',
        className
      )}
      data-slot="input-with-adornment"
      {...props}
    />
    {endAdornment && (
      <span className="-translate-y-1/2 absolute top-1/2 right-3 left-auto flex text-muted-foreground">
        {endAdornment}
      </span>
    )}
  </div>
);

InputWithAdornments.displayName = 'InputWithAdornments';

export { InputWithAdornments };
