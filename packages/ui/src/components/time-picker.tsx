'use client';

import { But<PERSON> } from '@lilypad/ui/components/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@lilypad/ui/components/popover';
import { ScrollArea } from '@lilypad/ui/components/scroll-area';
import { cn } from '@lilypad/ui/lib/utils';
import { ClockIcon } from 'lucide-react';
import React from 'react';

interface TimePickerProps
  extends Omit<
    React.ComponentProps<typeof Button>,
    'value' | 'onChange' | 'children'
  > {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
}

// Generate time options in 15-minute increments
function generateTimeOptions() {
  const times: string[] = [];
  for (let hour = 1; hour <= 12; hour++) {
    for (let minute = 0; minute < 60; minute += 15) {
      const time = `${hour}:${minute.toString().padStart(2, '0')}`;
      times.push(time);
    }
  }
  return times;
}

// Convert 24-hour format to 12-hour format with AM/PM
function format24To12Hour(time24: string): { time: string; period: string } {
  if (!time24) {
    return { time: '', period: 'AM' };
  }

  const [hours, minutes] = time24.split(':').map(Number);
  const period = hours >= 12 ? 'PM' : 'AM';

  const hours12 = () => {
    if (hours === 0) {
      return 12;
    }
    if (hours > 12) {
      return hours - 12;
    }
    return hours;
  };

  return {
    time: `${hours12()}:${minutes.toString().padStart(2, '0')}`,
    period,
  };
}

// Convert 12-hour format with AM/PM to 24-hour format
function format12To24Hour(time: string, period: string): string {
  if (!time) {
    return '';
  }

  const [hours, minutes] = time.split(':').map(Number);
  let hour24 = hours;

  if (period === 'AM' && hours === 12) {
    hour24 = 0;
  } else if (period === 'PM' && hours !== 12) {
    hour24 = hours + 12;
  }

  return `${hour24.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
}

function TimePicker({
  value = '',
  onChange,
  className,
  placeholder = 'Select time',
  disabled,
  ...props
}: TimePickerProps) {
  const [open, setOpen] = React.useState(false);
  const [selectedTime, setSelectedTime] = React.useState('');
  const [selectedPeriod, setSelectedPeriod] = React.useState<'AM' | 'PM'>('AM');

  const timeOptions = generateTimeOptions();

  React.useEffect(() => {
    if (value) {
      const { time, period } = format24To12Hour(value);
      setSelectedTime(time);
      setSelectedPeriod(period as 'AM' | 'PM');
    }
  }, [value]);

  const handleTimeChange = (time: string) => {
    setSelectedTime(time);
    const time24 = format12To24Hour(time, selectedPeriod);
    onChange?.(time24);
  };

  const handlePeriodChange = (period: string) => {
    setSelectedPeriod(period as 'AM' | 'PM');
    if (selectedTime) {
      const time24 = format12To24Hour(selectedTime, period);
      onChange?.(time24);
    }
  };

  const displayValue = React.useMemo(() => {
    if (!selectedTime) {
      return '';
    }
    return `${selectedTime} ${selectedPeriod}`;
  }, [selectedTime, selectedPeriod]);

  return (
    <Popover modal={true} onOpenChange={setOpen} open={open}>
      <PopoverTrigger asChild>
        <Button
          aria-expanded={open}
          className={cn(
            'justify-between font-normal',
            !displayValue && 'text-muted-foreground',
            className
          )}
          disabled={disabled}
          role="combobox"
          variant="outline"
          {...props}
        >
          <div className="flex items-center gap-2">
            <ClockIcon className="size-4" />
            {displayValue || placeholder}
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent align="start" className="w-40 p-0">
        <div className="flex">
          {/* Time Selection */}
          <div className="flex-1 p-2">
            <div className="mb-2 px-2 font-medium text-muted-foreground text-xs">
              Time
            </div>
            <ScrollArea className="h-48">
              <div className="px-1">
                {timeOptions.map((time) => (
                  <button
                    className={cn(
                      'w-full rounded-sm px-2 py-1.5 text-left text-sm transition-colors hover:bg-accent hover:text-accent-foreground',
                      selectedTime === time &&
                        'bg-accent text-accent-foreground'
                    )}
                    key={time}
                    onClick={() => handleTimeChange(time)}
                    type="button"
                  >
                    {time}
                  </button>
                ))}
              </div>
            </ScrollArea>
          </div>

          {/* Period Selection */}
          <div className="w-20 border-l p-2">
            <div className="mb-2 px-2 font-medium text-muted-foreground text-xs">
              Period
            </div>
            <div className="space-y-1">
              {['AM', 'PM'].map((period) => (
                <button
                  className={cn(
                    'w-full rounded-sm px-2 py-1.5 text-left text-sm transition-colors hover:bg-accent hover:text-accent-foreground',
                    selectedPeriod === period &&
                      'bg-accent text-accent-foreground'
                  )}
                  key={period}
                  onClick={() => handlePeriodChange(period)}
                  type="button"
                >
                  {period}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Clear Button */}
        <div className="border-t p-2">
          <Button
            className="w-full"
            onClick={() => {
              setSelectedTime('');
              setSelectedPeriod('AM');
              onChange?.('');
              setOpen(false);
            }}
            size="sm"
            variant="ghost"
          >
            Clear
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  );
}

export { TimePicker };
