'use client';

import { But<PERSON> } from '@lilypad/ui/components/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@lilypad/ui/components/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@lilypad/ui/components/popover';
import { cn } from '@lilypad/ui/lib/utils';
import { Check, ChevronsUpDown } from 'lucide-react';
import React from 'react';

const STATES = [
  {
    value: 'AL',
    label: 'Alabama',
  },
  {
    value: 'AK',
    label: 'Alaska',
  },
  {
    value: 'AZ',
    label: 'Arizona',
  },
  {
    value: 'AR',
    label: 'Arkansas',
  },
  {
    value: 'CA',
    label: 'California',
  },
  {
    value: 'CO',
    label: 'Colorado',
  },
  {
    value: 'CT',
    label: 'Connecticut',
  },
  {
    value: 'DE',
    label: 'Delaware',
  },
  {
    value: 'DC',
    label: 'District of Columbia',
  },
  {
    value: 'FL',
    label: 'Florida',
  },
  {
    value: 'GA',
    label: 'Georgia',
  },
  {
    value: 'HI',
    label: 'Hawaii',
  },
  {
    value: 'ID',
    label: 'Idaho',
  },
  {
    value: 'IN',
    label: 'Indiana',
  },
  {
    value: 'IA',
    label: 'Iowa',
  },
  {
    value: 'KS',
    label: 'Kansas',
  },
  {
    value: 'KY',
    label: 'Kentucky',
  },
  {
    value: 'LA',
    label: 'Louisiana',
  },
  {
    value: 'ME',
    label: 'Maine',
  },
  {
    value: 'MD',
    label: 'Maryland',
  },
  {
    value: 'MA',
    label: 'Massachusetts',
  },
  {
    value: 'MI',
    label: 'Michigan',
  },
  {
    value: 'MN',
    label: 'Minnesota',
  },
  {
    value: 'MS',
    label: 'Mississippi',
  },
  {
    value: 'MO',
    label: 'Missouri',
  },
  {
    value: 'MT',
    label: 'Montana',
  },
  {
    value: 'NE',
    label: 'Nebraska',
  },
  {
    value: 'NV',
    label: 'Nevada',
  },
  {
    value: 'NH',
    label: 'New Hampshire',
  },
  {
    value: 'NY',
    label: 'New York',
  },
  {
    value: 'NC',
    label: 'North Carolina',
  },
  {
    value: 'ND',
    label: 'North Dakota',
  },
  {
    value: 'OH',
    label: 'Ohio',
  },
  {
    value: 'OK',
    label: 'Oklahoma',
  },
  {
    value: 'OR',
    label: 'Oregon',
  },
  {
    value: 'PA',
    label: 'Pennsylvania',
  },
  {
    value: 'RI',
    label: 'Rhode Island',
  },
  {
    value: 'SC',
    label: 'South Carolina',
  },
  {
    value: 'SD',
    label: 'South Dakota',
  },
  {
    value: 'TN',
    label: 'Tennessee',
  },
  {
    value: 'TX',
    label: 'Texas',
  },
  {
    value: 'UT',
    label: 'Utah',
  },
  {
    value: 'VT',
    label: 'Vermont',
  },
  {
    value: 'VA',
    label: 'Virginia',
  },
  {
    value: 'WA',
    label: 'Washington',
  },
  {
    value: 'WV',
    label: 'West Virginia',
  },
  {
    value: 'WI',
    label: 'Wisconsin',
  },
  {
    value: 'WY',
    label: 'Wyoming',
  },
];

interface StateSelectProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  name?: string;
  autoComplete?: string;
}

export function StateSelect({
  value: controlledValue,
  onChange,
  placeholder = 'Select state...',
  className,
  disabled = false,
  name,
  autoComplete = 'address-level1',
}: StateSelectProps = {}) {
  const [open, setOpen] = React.useState(false);
  const [internalValue, setInternalValue] = React.useState('');
  const hiddenInputRef = React.useRef<HTMLInputElement>(null);

  // Use controlled value if provided, otherwise use internal state
  const value = controlledValue !== undefined ? controlledValue : internalValue;

  const handleValueChange = (newValue: string) => {
    if (controlledValue !== undefined) {
      // Controlled mode
      onChange?.(newValue);
    } else {
      // Uncontrolled mode
      setInternalValue(newValue);
    }

    // Update hidden input for form submission
    if (hiddenInputRef.current) {
      hiddenInputRef.current.value = newValue;
    }
  };

  // Handle autofill changes from the hidden input
  const handleHiddenInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    // Find matching state by full name or abbreviation
    const matchedState = STATES.find(
      (state) =>
        state.label.toLowerCase() === newValue.toLowerCase() ||
        state.value.toLowerCase() === newValue.toLowerCase()
    );

    if (matchedState) {
      handleValueChange(matchedState.value);
    }
  };

  // Ref callback to sync initial value
  const hiddenInputRefCallback = React.useCallback(
    (node: HTMLInputElement | null) => {
      hiddenInputRef.current = node;
      if (node && value) {
        node.value = value;
      }
    },
    [value]
  );

  return (
    <div className="relative">
      {/* Hidden input for autofill compatibility */}
      <input
        aria-hidden="true"
        autoComplete={autoComplete}
        className="pointer-events-none absolute inset-0 opacity-0"
        name={name}
        onChange={handleHiddenInputChange}
        ref={hiddenInputRefCallback}
        tabIndex={-1}
        type="text"
      />

      <Popover modal={true} onOpenChange={setOpen} open={open}>
        <PopoverTrigger asChild>
          <Button
            aria-expanded={open}
            className={cn(
              `w-full justify-between font-normal ${value ? 'text-foreground' : 'text-muted-foreground'}`,
              className
            )}
            disabled={disabled}
            role="combobox"
            variant="outline"
          >
            {value
              ? STATES.find((state) => state.value === value)?.label
              : placeholder}
            <ChevronsUpDown className="opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="z-50 w-[var(--radix-popover-trigger-width)] p-0">
          <Command>
            <CommandInput className="h-9" placeholder="Search state..." />
            <CommandList>
              <CommandEmpty>No state found.</CommandEmpty>
              <CommandGroup>
                {STATES.map((state) => (
                  <CommandItem
                    key={state.value}
                    onSelect={(currentValue: string) => {
                      const newValue =
                        currentValue === value ? '' : currentValue;
                      handleValueChange(newValue);
                      setOpen(false);
                    }}
                    value={state.value}
                  >
                    {state.label}
                    <Check
                      className={cn(
                        'ml-auto',
                        value === state.value ? 'opacity-100' : 'opacity-0'
                      )}
                    />
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}
