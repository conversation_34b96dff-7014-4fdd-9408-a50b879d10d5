'use client';

import { cn } from '@lilypad/ui/lib/utils';
import type { Transition, Variants } from 'motion/react';
import { motion, useAnimation } from 'motion/react';
import type { HTMLAttributes } from 'react';
import { useCallback } from 'react';

interface AnimatedLandmarkIconProps extends HTMLAttributes<HTMLDivElement> {
  size?: number;
}

const defaultTransition: Transition = {
  duration: 0.8,
  opacity: { duration: 0.3 },
  pathLength: { duration: 0.6, delay: 0.2 },
};

const pathVariants: Variants = {
  normal: {
    pathLength: 1,
    opacity: 1,
  },
  animate: {
    opacity: [0, 1],
    pathLength: [0, 1],
  },
};

const containerVariants: Variants = {
  normal: {
    scale: 1,
  },
  animate: {
    scale: [1, 1.05, 1],
  },
};

const AnimatedLandmarkIcon = ({
  onMouseEnter,
  onMouseLeave,
  className,
  size = 16,
  ...props
}: AnimatedLandmarkIconProps) => {
  const controls = useAnimation();
  const containerControls = useAnimation();

  const handleMouseEnter = useCallback(
    (e: React.MouseEvent<HTMLDivElement>) => {
      controls.start('animate');
      containerControls.start('animate');
      onMouseEnter?.(e);
    },
    [controls, containerControls, onMouseEnter]
  );

  const handleMouseLeave = useCallback(
    (e: React.MouseEvent<HTMLDivElement>) => {
      controls.start('normal');
      containerControls.start('normal');
      onMouseLeave?.(e);
    },
    [controls, containerControls, onMouseLeave]
  );

  return (
    // biome-ignore lint/a11y/noStaticElementInteractions: Not necessary
    // biome-ignore lint/nursery/noNoninteractiveElementInteractions: Not necessary
    <div
      className={cn('group', className)}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      {...props}
    >
      <motion.svg
        animate={containerControls}
        fill="none"
        height={size}
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="2"
        transition={{ duration: 0.4 }}
        variants={containerVariants}
        viewBox="0 0 24 24"
        width={size}
        xmlns="http://www.w3.org/2000/svg"
      >
        <title className="sr-only">Animated Landmark Icon</title>
        {/* Base foundation - no animation */}
        <path d="M3 22h18" />

        {/* Top triangle roof */}
        <path d="M11.12 2.198a2 2 0 0 1 1.76.006l7.866 3.847c.476.233.31.949-.22.949H3.474c-.53 0-.695-.716-.22-.949z" />

        {/* Animated columns */}
        <motion.path
          animate={controls}
          d="M6 18v-7"
          transition={defaultTransition}
          variants={pathVariants}
        />
        <motion.path
          animate={controls}
          d="M10 18v-7"
          transition={{ ...defaultTransition, delay: 0.1 }}
          variants={pathVariants}
        />
        <motion.path
          animate={controls}
          d="M14 18v-7"
          transition={{ ...defaultTransition, delay: 0.2 }}
          variants={pathVariants}
        />
        <motion.path
          animate={controls}
          d="M18 18v-7"
          transition={{ ...defaultTransition, delay: 0.3 }}
          variants={pathVariants}
        />
      </motion.svg>
    </div>
  );
};

AnimatedLandmarkIcon.displayName = 'AnimatedLandmarkIcon';

export { AnimatedLandmarkIcon };
