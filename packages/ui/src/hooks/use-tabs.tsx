import { useState } from 'react';

export interface Tab {
  label: string;
  value: string;
  subRoutes?: string[];
}

export function useTabs({
  tabs,
  initialTabId,
  onChange,
}: {
  tabs: Tab[];
  initialTabId: string;
  onChange?: (id: string) => void;
}) {
  const [[selectedTabIndex, direction], setSelectedTab] = useState(() => {
    const indexOfInitialTab = tabs.findIndex(
      (tab) => tab.value === initialTabId
    );
    return [indexOfInitialTab === -1 ? 0 : indexOfInitialTab, 0];
  });

  return {
    tabProps: {
      tabs,
      selectedTabIndex,
      onChange,
      setSelectedTab,
    },
    selectedTab: tabs[selectedTabIndex],
    contentProps: {
      direction,
      selectedTabIndex,
    },
  };
}

// Next.js routing hook for animated tabs
export interface UseNextTabsOptions {
  basePath?: string; // Base path to prepend to tab hrefs
  shallow?: boolean; // Use shallow routing
  scroll?: boolean; // Scroll to top on navigation
}

export function useNextTabs(options: UseNextTabsOptions = {}) {
  const { basePath = '' } = options;

  // This will be implemented by the consumer using Next.js hooks
  return {
    getCurrentPath: () => {
      // This should be implemented using usePathname() from next/navigation
      if (typeof window !== 'undefined') {
        return window.location.pathname;
      }
      return '';
    },
    navigate: (href: string) => {
      // This should be implemented using useRouter() from next/navigation
      const fullPath = basePath ? `${basePath}${href}` : href;

      if (typeof window !== 'undefined') {
        // Fallback for when Next.js router is not available
        window.history.pushState({}, '', fullPath);
      }
    },
  };
}
