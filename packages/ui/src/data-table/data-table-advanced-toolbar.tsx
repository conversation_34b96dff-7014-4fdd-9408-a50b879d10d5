'use client';


import { DataTableViewOptions } from '@lilypad/ui/data-table/data-table-view-options';
import { cn } from '@lilypad/ui/lib/utils';
import type { Table } from '@tanstack/react-table';
import type * as React from 'react';

interface DataTableAdvancedToolbarProps<TData>
  extends React.ComponentProps<'div'> {
  table: Table<TData>;
  showViewOptions?: boolean;
}

export function DataTableAdvancedToolbar<TData>({
  table,
  children,
  className,
  showViewOptions = true,
  ...props
}: DataTableAdvancedToolbarProps<TData>) {
  return (
    <div
      aria-orientation="horizontal"
      className={cn('flex w-full items-start justify-between gap-2', className)}
      {...props}
      role="toolbar"
    >
      <div className="flex flex-1 flex-wrap items-center justify-between gap-2">
        {children}
      </div>
      {showViewOptions && (
        <div className="flex items-center gap-2">
          <DataTableViewOptions table={table} />
        </div>
      )}
    </div>
  );
}
