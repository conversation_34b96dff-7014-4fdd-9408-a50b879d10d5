import { type Table as TanstackTable, flexRender } from '@tanstack/react-table';
import type * as React from 'react';

import { DataTablePagination } from '@lilypad/ui/data-table/data-table-pagination';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@lilypad/ui/components/table';
import { getCommonPinningStyles } from '@lilypad/ui/data-table/lib/utils';
import { cn } from '@lilypad/ui/lib/utils';

interface CardComponentProps<TData> {
  data: TData;
  index: number;
  isSelected?: boolean;
  onSelect?: (selected: boolean) => void;
  [key: string]: unknown;
}

interface GridConfig<TData> {
  cardComponent: React.ComponentType<CardComponentProps<TData>>;
  cardProps?: (data: TData, index: number) => Record<string, unknown>;
  gridClassName?: string;
  cardClassName?: string;
  columns?: number;
}

interface DataTableProps<TData> extends React.ComponentProps<'div'> {
  table: TanstackTable<TData>;
  actionBar?: React.ReactNode;
  view?: 'table' | 'grid';
  gridConfig?: GridConfig<TData>;
  onRowClick?: (row: TData) => void;
  rowClickable?: boolean;
  paginationClassName?: string;
}

function TableView<TData>({
  table,
  onRowClick,
  rowClickable,
}: {
  table: TanstackTable<TData>;
  onRowClick?: (row: TData) => void;
  rowClickable?: boolean;
}) {
  return (
    <Table className="overflow-x-auto">
      <TableHeader className="sticky top-0 z-10 bg-background">
        {table.getHeaderGroups().map((headerGroup) => (
          <TableRow key={headerGroup.id}>
            {headerGroup.headers.map((header) => (
              <TableHead
                key={header.id}
                colSpan={header.colSpan}
                className="first:pl-4"
                style={{
                  ...getCommonPinningStyles({ column: header.column }),
                }}
              >
                {header.isPlaceholder
                  ? null
                  : flexRender(
                      header.column.columnDef.header,
                      header.getContext()
                    )}
              </TableHead>
            ))}
          </TableRow>
        ))}
      </TableHeader>
      <TableBody>
        {table.getRowModel().rows?.length ? (
          table.getRowModel().rows.map((row) => (
            <TableRow
              key={row.id}
              data-state={row.getIsSelected() && 'selected'}
              className={cn(
                rowClickable
                  ? 'cursor-pointer transition-colors hover:bg-muted/50'
                  : ''
              )}
              onClick={onRowClick ? () => onRowClick(row.original) : undefined}
            >
              {row.getVisibleCells().map((cell) => (
                <TableCell
                  key={cell.id}
                  className="first:pl-4"
                  style={{
                    ...getCommonPinningStyles({ column: cell.column }),
                  }}
                >
                  {flexRender(cell.column.columnDef.cell, cell.getContext())}
                </TableCell>
              ))}
            </TableRow>
          ))
        ) : (
          <TableRow>
            <TableCell
              colSpan={table.getAllColumns().length}
              className="h-24 text-center"
            >
              No results.
            </TableCell>
          </TableRow>
        )}
      </TableBody>
    </Table>
  );
}

function GridView<TData>({
  table,
  gridConfig,
  onRowClick,
  rowClickable,
}: {
  table: TanstackTable<TData>;
  gridConfig: GridConfig<TData>;
  onRowClick?: (row: TData) => void;
  rowClickable?: boolean;
}) {
  const {
    cardComponent: CardComponent,
    cardProps = () => ({}),
    gridClassName,
    cardClassName,
    columns = 3,
  } = gridConfig;

  const rows = table.getRowModel().rows;

  if (!rows?.length) {
    return (
      <div className="flex h-24 items-center justify-center text-center">
        No results.
      </div>
    );
  }

  const gridColsClass =
    {
      1: 'grid-cols-1',
      2: 'grid-cols-1 md:grid-cols-2',
      3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
      4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
      5: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5',
      6: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-6',
    }[Math.min(columns, 6)] || 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3';

  return (
    <div
      className={cn(
        'grid gap-4 overflow-scroll px-4 py-2',
        gridColsClass,
        gridClassName
      )}
    >
      {rows.map((row, index) => {
        const additionalProps = cardProps(row.original, index);

        return (
          // biome-ignore lint/a11y/useKeyWithClickEvents: Not necessary
          // biome-ignore lint/a11y/useSemanticElements: Not necessary
          <div
            className={cn(cardClassName, rowClickable && 'cursor-pointer')}
            key={row.id}
            onClick={onRowClick ? () => onRowClick(row.original) : undefined}
          >
            <CardComponent
              data={row.original}
              index={index}
              isSelected={row.getIsSelected()}
              onSelect={(selected) => row.toggleSelected(selected)}
              {...additionalProps}
            />
          </div>
        );
      })}
    </div>
  );
}

export function DataTable<TData>({
  table,
  actionBar,
  children,
  className,
  view = 'table',
  gridConfig,
  onRowClick,
  rowClickable = false,
  paginationClassName,
  ...props
}: DataTableProps<TData>) {
  if (view === 'grid' && !gridConfig?.cardComponent) {
    console.warn(
      "DataTable: gridConfig.cardComponent is required when view is 'grid'"
    );
    return null;
  }

  return (
    <div
      className={cn('flex w-full flex-col overflow-scroll', className)}
      {...props}
    >
      {children}

      {view === 'table' ? (
        <TableView
          onRowClick={onRowClick}
          rowClickable={rowClickable}
          table={table}
        />
      ) : (
        <GridView
          // biome-ignore lint/style/noNonNullAssertion: Fix later
          gridConfig={gridConfig!}
          onRowClick={onRowClick}
          rowClickable={rowClickable}
          table={table}
        />
      )}

      <div
        className={cn('flex flex-col gap-2.5 border-t', paginationClassName)}
      >
        <DataTablePagination table={table} />
        {actionBar &&
          table.getFilteredSelectedRowModel().rows.length > 0 &&
          actionBar}
      </div>
    </div>
  );
}
