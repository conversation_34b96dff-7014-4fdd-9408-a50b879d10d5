'use client';

import { But<PERSON> } from '@lilypad/ui/components/button';
import { Input } from '@lilypad/ui/components/input';
import { Label } from '@lilypad/ui/components/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@lilypad/ui/components/popover';
import { Separator } from '@lilypad/ui/components/separator';
import { Slider } from '@lilypad/ui/components/slider';
import { cn } from '@lilypad/ui/lib/utils';
import type { Column } from '@tanstack/react-table';
import { PlusCircle, XCircle } from 'lucide-react';
import React from 'react';

interface Range {
  min: number;
  max: number;
}

type RangeValue = [number, number];

function getIsValidRange(value: unknown): value is RangeValue {
  return (
    Array.isArray(value) &&
    value.length === 2 &&
    typeof value[0] === 'number' &&
    typeof value[1] === 'number'
  );
}

interface DataTableSliderFilterProps<TData> {
  column: Column<TData, unknown>;
  title?: string;
}

export function DataTableSliderFilter<TData>({
  column,
  title,
}: DataTableSliderFilterProps<TData>) {
  const id = React.useId();

  const columnFilterValue = getIsValidRange(column.getFilterValue())
    ? (column.getFilterValue() as RangeValue)
    : undefined;

  const defaultRange = column.columnDef.meta?.range;
  const unit = column.columnDef.meta?.unit;

  // biome-ignore lint/complexity/noExcessiveCognitiveComplexity: Fix later
  const { min, max, step } = React.useMemo<Range & { step: number }>(() => {
    let minValue = 0;
    let maxValue = 100;

    if (defaultRange && getIsValidRange(defaultRange)) {
      [minValue, maxValue] = defaultRange;
    } else {
      const values = column.getFacetedMinMaxValues();
      if (values && Array.isArray(values) && values.length === 2) {
        const [facetMinValue, facetMaxValue] = values;
        if (
          typeof facetMinValue === 'number' &&
          typeof facetMaxValue === 'number'
        ) {
          minValue = facetMinValue;
          maxValue = facetMaxValue;
        }
      }
    }

    const rangeSize = maxValue - minValue;
    const _step =
      rangeSize <= 20
        ? 1
        : // biome-ignore lint/style/noNestedTernary: Fix later
          rangeSize <= 100
          ? Math.ceil(rangeSize / 20)
          : Math.ceil(rangeSize / 50);

    return { min: minValue, max: maxValue, step: _step };
  }, [column, defaultRange]);

  const range = React.useMemo((): RangeValue => {
    return columnFilterValue ?? [min, max];
  }, [columnFilterValue, min, max]);

  const formatValue = React.useCallback((_value: number) => {
    return _value.toLocaleString(undefined, { maximumFractionDigits: 0 });
  }, []);

  const onFromInputChange = React.useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const numValue = Number(event.target.value);
      if (!Number.isNaN(numValue) && numValue >= min && numValue <= range[1]) {
        column.setFilterValue([numValue, range[1]]);
      }
    },
    [column, min, range]
  );

  const onToInputChange = React.useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const numValue = Number(event.target.value);
      if (!Number.isNaN(numValue) && numValue <= max && numValue >= range[0]) {
        column.setFilterValue([range[0], numValue]);
      }
    },
    [column, max, range]
  );

  const onSliderValueChange = React.useCallback(
    (value: RangeValue) => {
      if (Array.isArray(value) && value.length === 2) {
        column.setFilterValue(value);
      }
    },
    [column]
  );

  const onReset = React.useCallback(
    (event: React.MouseEvent) => {
      if (event.target instanceof HTMLDivElement) {
        event.stopPropagation();
      }
      column.setFilterValue(undefined);
    },
    [column]
  );

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button className="border-dashed" size="sm" variant="outline">
          {columnFilterValue ? (
            // biome-ignore lint/a11y/useKeyWithClickEvents: Not necessary
            // biome-ignore lint/a11y/useSemanticElements: Not necessary
            <div
              aria-label={`Clear ${title} filter`}
              className="rounded-sm opacity-70 transition-opacity hover:opacity-100 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring"
              onClick={onReset}
              role="button"
              tabIndex={0}
            >
              <XCircle />
            </div>
          ) : (
            <PlusCircle />
          )}
          <span>{title}</span>
          {columnFilterValue ? (
            <>
              <Separator
                className="mx-0.5 data-[orientation=vertical]:h-4"
                orientation="vertical"
              />
              {formatValue(columnFilterValue[0])} -{' '}
              {formatValue(columnFilterValue[1])}
              {unit ? ` ${unit}` : ''}
            </>
          ) : null}
        </Button>
      </PopoverTrigger>
      <PopoverContent align="start" className="flex w-auto flex-col gap-4">
        <div className="flex flex-col gap-3">
          <p className="font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
            {title}
          </p>
          <div className="flex items-center gap-4">
            <Label className="sr-only" htmlFor={`${id}-from`}>
              From
            </Label>
            <div className="relative">
              <Input
                aria-valuemax={max}
                aria-valuemin={min}
                className={cn('h-8 w-24', unit && 'pr-8')}
                id={`${id}-from`}
                inputMode="numeric"
                max={max}
                min={min}
                onChange={onFromInputChange}
                pattern="[0-9]*"
                placeholder={min.toString()}
                type="number"
                value={range[0]?.toString()}
              />
              {unit && (
                <span className="absolute top-0 right-0 bottom-0 flex items-center rounded-r-md bg-accent px-2 text-muted-foreground text-sm">
                  {unit}
                </span>
              )}
            </div>
            <Label className="sr-only" htmlFor={`${id}-to`}>
              to
            </Label>
            <div className="relative">
              <Input
                aria-valuemax={max}
                aria-valuemin={min}
                className={cn('h-8 w-24', unit && 'pr-8')}
                id={`${id}-to`}
                inputMode="numeric"
                max={max}
                min={min}
                onChange={onToInputChange}
                pattern="[0-9]*"
                placeholder={max.toString()}
                type="number"
                value={range[1]?.toString()}
              />
              {unit && (
                <span className="absolute top-0 right-0 bottom-0 flex items-center rounded-r-md bg-accent px-2 text-muted-foreground text-sm">
                  {unit}
                </span>
              )}
            </div>
          </div>
          <Label className="sr-only" htmlFor={`${id}-slider`}>
            {title} slider
          </Label>
          <Slider
            id={`${id}-slider`}
            max={max}
            min={min}
            onValueChange={onSliderValueChange}
            step={step}
            value={range}
          />
        </div>
        <Button
          aria-label={`Clear ${title} filter`}
          onClick={onReset}
          size="sm"
          variant="outline"
        >
          Clear
        </Button>
      </PopoverContent>
    </Popover>
  );
}
