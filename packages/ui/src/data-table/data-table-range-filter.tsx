'use client';

import { Input } from '@lilypad/ui/components/input';
import type { ExtendedColumnFilter } from '@lilypad/ui/data-table/lib/types';
import { cn } from '@lilypad/ui/lib/utils';
import type { Column } from '@tanstack/react-table';
import React from 'react';

interface DataTableRangeFilterProps<TData> extends React.ComponentProps<'div'> {
  filter: ExtendedColumnFilter<TData>;
  column: Column<TData>;
  inputId: string;
  onFilterUpdate: (
    filterId: string,
    updates: Partial<Omit<ExtendedColumnFilter<TData>, 'filterId'>>
  ) => void;
}

export function DataTableRangeFilter<TData>({
  filter,
  column,
  inputId,
  onFilterUpdate,
  className,
  ...props
}: DataTableRangeFilterProps<TData>) {
  const meta = column.columnDef.meta;

  const [min, max] = React.useMemo(() => {
    const range = column.columnDef.meta?.range;
    if (range) {
      return range;
    }

    const values = column.getFacetedMinMaxValues();
    if (!values) {
      return [0, 100];
    }

    return [values[0], values[1]];
  }, [column]);

  const formatValue = React.useCallback(
    (_value: string | number | undefined) => {
      if (_value === undefined || _value === '') {
        return '';
      }
      const numValue = Number(_value);
      return Number.isNaN(numValue)
        ? ''
        : numValue.toLocaleString(undefined, {
            maximumFractionDigits: 0,
          });
    },
    []
  );

  const value = React.useMemo(() => {
    if (Array.isArray(filter.value)) {
      return filter.value.map(formatValue);
    }
    return [formatValue(filter.value), ''];
  }, [filter.value, formatValue]);

  const onRangeValueChange = React.useCallback(
    // biome-ignore lint/complexity/noExcessiveCognitiveComplexity: Fix later
    (_value: string, isMin?: boolean) => {
      const numValue = Number(_value);
      const currentValues = Array.isArray(filter.value)
        ? filter.value
        : ['', ''];
      const otherValue = isMin
        ? (currentValues[1] ?? '')
        : (currentValues[0] ?? '');

      if (
        _value === '' ||
        (!Number.isNaN(numValue) &&
          (isMin
            ? numValue >= min && numValue <= (Number(otherValue) || max)
            : numValue <= max && numValue >= (Number(otherValue) || min)))
      ) {
        onFilterUpdate(filter.filterId, {
          value: isMin ? [_value, otherValue] : [otherValue, _value],
        });
      }
    },
    [filter.filterId, filter.value, min, max, onFilterUpdate]
  );

  return (
    <div
      className={cn('flex w-full items-center gap-2', className)}
      data-slot="range"
      {...props}
    >
      <Input
        aria-label={`${meta?.label} minimum value`}
        aria-valuemax={max}
        aria-valuemin={min}
        className="h-8 w-full rounded"
        data-slot="range-min"
        defaultValue={value[0]}
        id={`${inputId}-min`}
        inputMode="numeric"
        max={max}
        min={min}
        onChange={(event) => onRangeValueChange(event.target.value, true)}
        placeholder={min.toString()}
        type="number"
      />
      <span className="sr-only shrink-0 text-muted-foreground">to</span>
      <Input
        aria-label={`${meta?.label} maximum value`}
        aria-valuemax={max}
        aria-valuemin={min}
        className="h-8 w-full rounded"
        data-slot="range-max"
        defaultValue={value[1]}
        id={`${inputId}-max`}
        inputMode="numeric"
        max={max}
        min={min}
        onChange={(event) => onRangeValueChange(event.target.value)}
        placeholder={max.toString()}
        type="number"
      />
    </div>
  );
}
