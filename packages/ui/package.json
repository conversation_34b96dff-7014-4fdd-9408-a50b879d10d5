{"name": "@lilypad/ui", "version": "0.0.0", "private": true, "type": "module", "scripts": {"generate:component": "turbo gen react-component", "lint": "biome lint --write .", "format": "biome format --write .", "typecheck": "tsc --noEmit", "clean": "git clean -xdf node_modules"}, "devDependencies": {"@lilypad/shared": "workspace:*", "@lilypad/typescript": "workspace:*", "@tailwindcss/postcss": "^4.1.7", "@turbo/gen": "^2.5.0", "@types/node": "^22.15.3", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "tailwindcss": "^4.1.7", "typescript": "5.8.2"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-slot": "^1.2.3", "@stepperize/react": "^5.1.6", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "lucide-react": "^0.511.0", "motion": "^12.12.1", "next": "^15.3.3", "next-themes": "^0.4.6", "nuqs": "^2.4.3", "radix-ui": "latest", "react": "^19.1.0", "react-day-picker": "9.7.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.4", "react-resizable-panels": "^3.0.2", "recharts": "^2.15.3", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "tunnel-rat": "^0.1.2", "tw-animate-css": "^1.3.0", "vaul": "^1.1.2", "zod": "^3.25.7"}, "exports": {"./components/*": "./src/components/*.tsx", "./logos/*": "./src/logos/*.tsx", "./lib/*": "./src/lib/*.ts", "./styles/*": "./src/styles/*", "./hooks/*": "./src/hooks/*.tsx", "./icons": "./src/icons/index.ts", "./data-table/*": "./src/data-table/*.tsx", "./data-table/lib/*": "./src/data-table/lib/*.ts"}}