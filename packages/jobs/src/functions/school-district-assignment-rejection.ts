import { NotificationsService } from '@lilypad/core/services/notifications';
import { TasksService } from '@lilypad/core/services/tasks';
import { dbAdmin } from '@lilypad/db/client';
import {
  NotificationCategoryTypeEnum,
  NotificationTypeEnum,
  TaskPriorityEnum,
  TaskStatusEnum,
  TaskTypeEnum,
} from '@lilypad/db/enums';

import { calculateDueDate } from '@lilypad/shared/date';
import { inngest } from '../client';
import { Events } from '../events';

/**
 * ONBOARD-07: Psychologist: Notified of being assigned to a school district and reviews their onboarding form and school demographics, and accepts school assignment.
 * ONBOARD-08: System: All School Team receives an introduction email to the school care team introducing the psychologist with a picture and a profile.
 * PRE-01: School Coordinator: Complete referrals for students. May be single student or multiple. May upload background information or may assign task to another person.
 */

interface SchoolDistrictAssignmentRejectionInput {
  taskId: string;
}

export const schoolDistrictAssignmentRejection = inngest.createFunction(
  { id: 'school-district-assignment-rejection' },
  { event: Events.DISTRICT_ASSIGNMENT_REJECTED },
  async ({ event, step }) => {
    const tasks = new TasksService(dbAdmin);
    const notification = new NotificationsService(dbAdmin);

    const { taskId } = event.data
      .encrypted as SchoolDistrictAssignmentRejectionInput;

    const task = await step.run('get-task', async () => {
      return await tasks.getTaskById(taskId);
    });

    await step.run('send-case-manager-notification', async () => {
      await notification.send({
        userId: task.assignedBy.id,
        type: NotificationTypeEnum.TASK_REJECTED,
        content: `${task.assignedTo.fullName} has rejected their school district assignment`,
        category: NotificationCategoryTypeEnum.WORKFLOW,
      });
    });

    await step.run(
      'create-new-task-for-new-psychologist-assignment',
      async () => {
        await tasks.create({
          taskType: TaskTypeEnum.REASSIGN_PSYCHOLOGIST,
          districtId: task.district?.id,
          assignedToId: task.assignedBy.id,
          assignedById: task.assignedBy.id,
          dueDate: task?.dueDate
            ? new Date(task.dueDate)
            : calculateDueDate({
                type: 'business_days',
                amount: 2,
              }),
          priority: TaskPriorityEnum.HIGH,
          status: TaskStatusEnum.PENDING,
        });
      }
    );
  }
);
