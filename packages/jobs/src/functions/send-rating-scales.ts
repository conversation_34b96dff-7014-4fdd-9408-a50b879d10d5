import { TasksService } from '@lilypad/core/services/tasks';
import { dbAdmin } from '@lilypad/db/client';
import {
  TaskPriorityEnum,
  TaskStatusEnum,
  TaskTypeEnum,
} from '@lilypad/db/schema/enums';
import { EmailService } from '@lilypad/email/service';
import type {
  BaseEmailProps,
  RatingScalesEmailProps,
} from '@lilypad/email/types';

import { inngest } from '../client';
import { Events } from '../events';

/**
 * PRE-08: Send rating scales - Hits Send on rating scales to parents and teachers as required following the evaluation plan submission
 * PRE-09: Monitor rating scales - Responses of rating scales are tracked and reminders are automatically sent.
 */

interface SendRatingScalesInput {
  caseId: string;
  assistantId: string;
  emails: Array<RatingScalesEmailProps & BaseEmailProps>;
}

export const sendRatingScales = inngest.createFunction(
  { id: 'send-rating-scales' },
  { event: Events.EVALUATION_SEND_RATING_SCALES },
  async ({ event, step }) => {
    const { caseId, assistantId, emails } = event.data
      .encrypted as SendRatingScalesInput;

    const tasks = new TasksService(dbAdmin);

    const sendRatingScalesEmails = await step.run(
      'send-rating-scales-emails',
      async () => {
        return await Promise.all(
          emails.map((email) => EmailService.sendRatingScalesEmail(email))
        );
      }
    );

    const monitoringTask = await step.run(
      'create-monitor-rating-scales-task',
      async () => {
        return await tasks.create({
          taskType: TaskTypeEnum.MONITOR_RATING_SCALES,
          caseId,
          assignedToId: assistantId,
          assignedById: assistantId,
          dueDate: null,
          priority: TaskPriorityEnum.MEDIUM,
          status: TaskStatusEnum.PENDING,
        });
      }
    );

    await Promise.all([sendRatingScalesEmails, monitoringTask]);
  }
);
