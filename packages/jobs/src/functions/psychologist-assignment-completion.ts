import { NotificationsService } from '@lilypad/core/services/notifications';
import { TasksService } from '@lilypad/core/services/tasks';
import { dbAdmin } from '@lilypad/db/client';
import {
  NotificationCategoryTypeEnum,
  NotificationTypeEnum,
  TaskPriorityEnum,
  TaskStatusEnum,
  TaskTypeEnum,
} from '@lilypad/db/enums';
import { DistrictsRepository } from '@lilypad/db/repository';
import { calculateDueDate } from '@lilypad/shared/date';

import { inngest } from '../client';
import { Events } from '../events';

interface PsychologistAssignmentCompletionInput {
  userId: string;
  psychologistId: string;
  districtId: string;
}

export const psychologistAssignmentCompletion = inngest.createFunction(
  { id: 'psychologist-assignment-completion' },
  { event: Events.DISTRICT_PSYCHOLOGIST_ASSIGNED },
  async ({ event, step }) => {
    const { userId, psychologistId, districtId } = event.data
      .encrypted as PsychologistAssignmentCompletionInput;

    const notification = new NotificationsService(dbAdmin);
    const tasks = new TasksService(dbAdmin);

    const district = await step.run('get-district-id', async () => {
      return await dbAdmin.transaction(async (tx) => {
        const districtsRepository = new DistrictsRepository(tx);
        return await districtsRepository.getDistrictById(districtId);
      });
    });

    const sendPsychologistNotification = step.run(
      'send-psychologist-notification',
      async () => {
        await notification.send({
          userId: psychologistId,
          type: NotificationTypeEnum.TASK_ASSIGNED,
          content: `You have been assigned to ${district?.name}`,
          category: NotificationCategoryTypeEnum.WORKFLOW,
        });
      }
    );

    const createReviewDistrictAssignmentTask = step.run(
      'create-review-district-assignment-task',
      async () => {
        await tasks.create({
          taskType: TaskTypeEnum.REVIEW_DISTRICT_ASSIGNMENT,
          districtId,
          assignedToId: psychologistId,
          assignedById: userId,
          dueDate: calculateDueDate({
            type: 'business_days',
            amount: 2,
          }),
          priority: TaskPriorityEnum.HIGH,
          status: TaskStatusEnum.PENDING,
        });
      }
    );

    await Promise.all([
      sendPsychologistNotification,
      createReviewDistrictAssignmentTask,
    ]);
  }
);
