import { TasksService } from '@lilypad/core/services/tasks';
import { dbAdmin } from '@lilypad/db/client';
import {
  TaskPriorityEnum,
  TaskStatusEnum,
  TaskTypeEnum,
} from '@lilypad/db/enums';
import { calculateDueDate } from '@lilypad/shared/date';

import { inngest } from '../client';
import { Events } from '../events';

/**
 * PRE-08: Send rating scales - Hits Send on rating scales to parents and teachers as required following the evaluation plan submission
 */

interface RatingScalesPreparationCompletionInput {
  taskId: string;
  psychologistId: string;
}

export const ratingScalesPreparationCompletion = inngest.createFunction(
  { id: 'rating-scales-preparation-completion' },
  { event: Events.EVALUATION_RATING_SCALES_PREPARED },
  async ({ event, step }) => {
    const { taskId, psychologistId } = event.data
      .encrypted as RatingScalesPreparationCompletionInput;

    const tasks = new TasksService(dbAdmin);

    const task = await step.run('get-task-details', async () => {
      return await tasks.getTaskById(taskId);
    });

    await step.run('create-review-and-send-rating-scales-task', async () => {
      return await tasks.create({
        taskType: TaskTypeEnum.REVIEW_AND_SEND_RATING_SCALES,
        districtId: task.district?.id,
        assignedToId: psychologistId,
        assignedById: psychologistId,
        dueDate: calculateDueDate({
          type: 'business_days',
          amount: 2,
        }),
        priority: TaskPriorityEnum.HIGH,
        status: TaskStatusEnum.PENDING,
      });
    });
  }
);
