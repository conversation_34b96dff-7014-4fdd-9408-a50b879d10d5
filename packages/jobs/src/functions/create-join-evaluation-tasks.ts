import { TasksService } from '@lilypad/core/services/tasks';
import { dbAdmin } from '@lilypad/db/client';
import { CaseRepository } from '@lilypad/db/repository';
import type { CaseForJoinEvaluationTasks } from '@lilypad/db/repository/types/cases';
import { TaskPriorityEnum, TaskStatusEnum } from '@lilypad/db/schema/enums';

import { inngest } from '../client';
import { BATCH_CONFIG, CONCURRENCY_LIMITS, CRON_SCHEDULES } from '../constants';
import { createBatches } from '../utils';

const INTERVALS = {
  MINUTES_AHEAD: 90,
  BUFFER_MINUTES: 30,
} as const;

const getCasesNeedingJoinTasks = async () => {
  return await dbAdmin.transaction(async (tx) => {
    const repository = new CaseRepository(tx);
    return await repository.getCasesForJoinEvaluationTasks(
      INTERVALS.MINUTES_AHEAD,
      INTERVALS.BUFFER_MINUTES
    );
  });
};

const createJoinEvaluationTasksBatch = async (
  tasksService: TasksService,
  cases: CaseForJoinEvaluationTasks[]
) => {
  const tasksToCreate = cases.map((case_) => ({
    taskType: case_.taskType,
    caseId: case_.caseId,
    assignedToId: case_.userId,
    assignedById: case_.userId,
    dueDate: new Date(case_.evaluationDueDate),
    priority: TaskPriorityEnum.URGENT,
    status: TaskStatusEnum.PENDING,
    metadata: {
      evaluationDueDate: case_.evaluationDueDate,
      userRole: case_.roleName,
      createdBy: 'system',
    },
  }));

  try {
    const createdTasks = await tasksService.createBatch(tasksToCreate);
    return { success: true, tasksCreated: createdTasks.length };
  } catch (error) {
    return { success: false, tasksCreated: 0, error };
  }
};

const createTasksInBatches = async (
  tasksService: TasksService,
  cases: CaseForJoinEvaluationTasks[]
) => {
  const batches = createBatches(cases, BATCH_CONFIG.DEFAULT_BATCH_SIZE);

  await Promise.all(
    batches.map(
      async (batch) => await createJoinEvaluationTasksBatch(tasksService, batch)
    )
  );
};

export const createJoinEvaluationTasks = inngest.createFunction(
  {
    id: 'create-join-evaluation-tasks',
    name: 'Create Join Evaluation Tasks',
    concurrency: CONCURRENCY_LIMITS.SINGLE_INSTANCE,
  },
  { cron: CRON_SCHEDULES.HOURLY },
  async ({ step }) => {
    const tasksService = new TasksService(dbAdmin);

    const casesNeedingJoinTasks = await step.run(
      'get-cases-needing-join-tasks',
      async () => await getCasesNeedingJoinTasks()
    );

    if (casesNeedingJoinTasks.length === 0) {
      return;
    }

    await step.run(
      'create-join-evaluation-tasks-in-batches',
      async () =>
        await createTasksInBatches(tasksService, casesNeedingJoinTasks)
    );
  }
);
