import type { InngestFunction } from 'inngest';

// Onboarding Phase Functions

import { districtOnboardingCompletion } from './district-onboarding-completion';
import { psychologistAssignmentCompletion } from './psychologist-assignment-completion';
import { schoolDistrictAssignmentAcceptance } from './school-district-assignment-acceptance';
import { schoolDistrictAssignmentRejection } from './school-district-assignment-rejection';

// Pre-Evaluation Phase Functions

import { evaluationSchedulingCompletion } from './evaluation-scheduling-completion';
import { ratingScalesPreparationCompletion } from './rating-scales-preparation-completion';
import { reassignCompleteReferralForm } from './reassign-complete-referral-form';
import { referralFormCompletion } from './referral-form-completion';
import { sendRatingScales } from './send-rating-scales';

// Evaluation Phase Functions

import { evaluationPlanCreation } from './evaluation-plan-creation';
import { uponJoiningEvaluationMeeting } from './upon-joining-evaluation-meeting';

// Post-Evaluation Phase Functions

import { finalReportCompleted } from './final-report-completed';
import { iepMeetingScheduled } from './iep-meeting-scheduled';
import { uponEvaluationCompletion } from './upon-evaluation-completion';
import { uponNotesSubmission } from './upon-notes-submission';
import { uponReportDraftSubmission } from './upon-report-draft-submission';
import { uponReportFinalizedByClinicalDirector } from './upon-report-finalized-by-clinical-director';
import { uponReportFinalizedByPsychologist } from './upon-report-finalized-by-psychologist';

// Recurring Tasks

import { updateUserAvailabilities } from './update-user-availabilities';

// Workflow Automation Cron Jobs

import { createJoinEvaluationTasks } from './create-join-evaluation-tasks';
import { createPrepareForEvaluationTask } from './create-prepare-for-evaluation-task';
import { createPrepareForIEPMeetingTask } from './create-prepare-for-iep-meeting-task';
import { sendPeriodicReminders } from './send-periodic-reminders';
import { sendUpcomingEvaluationReminders } from './send-upcoming-evaluation-reminders';

export const functions: readonly InngestFunction.Like[] = [
  // Onboarding Phase Functions
  districtOnboardingCompletion,
  psychologistAssignmentCompletion,
  schoolDistrictAssignmentAcceptance,
  schoolDistrictAssignmentRejection,

  // Pre-Evaluation Phase Functions
  reassignCompleteReferralForm,
  referralFormCompletion,
  evaluationSchedulingCompletion,
  evaluationPlanCreation,
  ratingScalesPreparationCompletion,
  sendRatingScales,

  // Evaluation Phase Functions
  uponJoiningEvaluationMeeting,
  uponEvaluationCompletion,
  uponNotesSubmission,

  // Post-Evaluation Phase Functions
  uponReportDraftSubmission,
  uponReportFinalizedByPsychologist,
  uponReportFinalizedByClinicalDirector,
  finalReportCompleted,
  iepMeetingScheduled,

  // Recurring Tasks
  updateUserAvailabilities,

  // Workflow Automation Cron Jobs
  sendUpcomingEvaluationReminders,
  createPrepareForEvaluationTask,
  createJoinEvaluationTasks,
  createPrepareForIEPMeetingTask,
  sendPeriodicReminders,
];
