import { TasksService } from '@lilypad/core/services/tasks';
import { dbAdmin } from '@lilypad/db/client';
import {
  TaskPriorityEnum,
  TaskStatusEnum,
  TaskTypeEnum,
} from '@lilypad/db/enums';
import { calculateDueDate } from '@lilypad/shared/date';

import { inngest } from '../client';
import { Events } from '../events';

/**
 * PRE-03: Schedule student evaluation - Uses Scheduling Process to book students in to the psychologist's calendar. Enters backup students. Flagged with message if no backup students. Selects who should be kept up to date for evaluation from school team.
 * PRE-06: Create evaluation plan - Reviews list of upcoming evaluations reviews student information and submits the evaluation plan.
 */

interface ReferralFormCompletionInput {
  taskId: string;
  schoolCoordinatorId: string;
  psychologistId: string;
}

export const referralFormCompletion = inngest.createFunction(
  { id: 'referral-form-completion' },
  { event: Events.EVALUATION_REFERRAL_FORM_COMPLETED },
  async ({ event, step }) => {
    const { taskId, schoolCoordinatorId, psychologistId } = event.data
      .encrypted as ReferralFormCompletionInput;

    const tasks = new TasksService(dbAdmin);

    const task = await step.run('get-task-details', async () => {
      return await tasks.getTaskById(taskId);
    });

    const createSchedulingTask = step.run(
      'create-schedule-student-evaluations-task',
      async () => {
        return await tasks.create({
          taskType: TaskTypeEnum.SCHEDULE_STUDENT_EVALUATIONS,
          districtId: task.district?.id,
          assignedToId: schoolCoordinatorId,
          assignedById: schoolCoordinatorId,
          dueDate: calculateDueDate({
            type: 'business_days',
            amount: 2,
          }),
          priority: TaskPriorityEnum.HIGH,
          status: TaskStatusEnum.PENDING,
        });
      }
    );

    const createEvaluationPlanTask = step.run(
      'create-evaluation-plan-task',
      async () => {
        return await tasks.create({
          taskType: TaskTypeEnum.CREATE_EVALUATION_PLAN,
          districtId: task.district?.id,
          assignedToId: psychologistId,
          assignedById: psychologistId,
          dueDate: calculateDueDate({
            type: 'business_days',
            amount: 2,
          }),
          priority: TaskPriorityEnum.HIGH,
          status: TaskStatusEnum.PENDING,
        });
      }
    );

    await Promise.all([createSchedulingTask, createEvaluationPlanTask]);
  }
);
