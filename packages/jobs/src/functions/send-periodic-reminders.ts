import { NotificationsService } from '@lilypad/core/services/notifications';
import { dbAdmin } from '@lilypad/db/client';
import { TasksRepository } from '@lilypad/db/repository';
import {
  NotificationCategoryTypeEnum,
  NotificationTypeEnum,
} from '@lilypad/db/schema/enums';

import { inngest } from '../client';
import { BATCH_CONFIG, CONCURRENCY_LIMITS, CRON_SCHEDULES } from '../constants';

const INTERVALS = {
  REMINDER_INTERVAL_DAYS: 3,
  BUFFER_HOURS_FOR_REMINDERS: 2,
} as const;

export const sendPeriodicReminders = inngest.createFunction(
  {
    id: 'send-periodic-reminders',
    name: 'Send Periodic Rating Scales Reminders',
    concurrency: CONCURRENCY_LIMITS.SINGLE_INSTANCE,
  },
  { cron: CRON_SCHEDULES.DAILY },
  async ({ step }) => {
    const notifications = new NotificationsService(dbAdmin);

    const activeTasks = await step.run(
      'get-active-monitor-rating-scales-tasks',
      async () => {
        return await dbAdmin.transaction(async (tx) => {
          const repository = new TasksRepository(tx);
          return await repository.getActiveMonitorRatingScalesTasks();
        });
      }
    );

    if (activeTasks.length === 0) {
      return;
    }

    await step.run('send-rating-scales-reminders-in-batches', async () => {
      const now = new Date();

      // Group tasks by user ID and filter for tasks needing reminders
      const userTasksMap = new Map<string, typeof activeTasks>();

      for (const task of activeTasks) {
        const daysSinceCreated = Math.floor(
          (now.getTime() - new Date(task.createdAt).getTime()) /
            (1000 * 60 * 60 * 24)
        );

        const bufferTime =
          INTERVALS.BUFFER_HOURS_FOR_REMINDERS * 60 * 60 * 1000;
        const timeSinceCreated =
          now.getTime() - new Date(task.createdAt).getTime();
        const expectedReminderTime =
          INTERVALS.REMINDER_INTERVAL_DAYS * 24 * 60 * 60 * 1000;

        // Check if task needs a reminder (every 3 days with 2-hour buffer)
        if (
          timeSinceCreated >= expectedReminderTime - bufferTime &&
          daysSinceCreated % INTERVALS.REMINDER_INTERVAL_DAYS >= 0
        ) {
          const userTasks = userTasksMap.get(task.userId) || [];
          userTasks.push(task);
          userTasksMap.set(task.userId, userTasks);
        }
      }

      if (userTasksMap.size === 0) {
        return 0;
      }

      const usersNeedingReminders = Array.from(userTasksMap.entries()).map(
        ([userId, tasks]) => ({ userId, tasks })
      );

      const batches: (typeof usersNeedingReminders)[] = [];
      for (
        let i = 0;
        i < usersNeedingReminders.length;
        i += BATCH_CONFIG.DEFAULT_BATCH_SIZE
      ) {
        batches.push(
          usersNeedingReminders.slice(i, i + BATCH_CONFIG.DEFAULT_BATCH_SIZE)
        );
      }

      await Promise.all(
        batches.map(async (batch) => {
          await Promise.all(
            batch.map(async (userWithTasks) => {
              try {
                const { userId, tasks } = userWithTasks;
                const tasksCount = tasks.length;
                const caseIds = [
                  ...new Set(
                    tasks.map((task) => task.caseId).filter((id) => id)
                  ),
                ];

                // Get average days since creation for display
                const avgDaysSinceCreated = Math.floor(
                  tasks.reduce((sum, task) => {
                    return (
                      sum +
                      Math.floor(
                        (now.getTime() - new Date(task.createdAt).getTime()) /
                          (1000 * 60 * 60 * 24)
                      )
                    );
                  }, 0) / tasks.length
                );

                await notifications.send({
                  userId,
                  type: NotificationTypeEnum.RATING_SCALES_REMINDER,
                  content: `Reminder: You have ${tasksCount} active rating scales monitoring ${tasksCount === 1 ? 'task' : 'tasks'} across ${caseIds.length} ${caseIds.length === 1 ? 'case' : 'cases'} (${avgDaysSinceCreated} days average)`,
                  category: NotificationCategoryTypeEnum.WORKFLOW,
                  metadata: {
                    taskIds: tasks.map((task) => task.taskId),
                    caseIds,
                    tasksCount,
                    avgDaysSinceCreated,
                    reminderInterval: INTERVALS.REMINDER_INTERVAL_DAYS,
                    reminderType: 'periodic_consolidated',
                  },
                });

                return { success: true, userId, tasksCount };
              } catch (error) {
                return {
                  success: false,
                  userId: userWithTasks.userId,
                  error,
                };
              }
            })
          );
        })
      );
    });
  }
);
