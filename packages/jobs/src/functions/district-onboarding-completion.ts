import { TasksService } from '@lilypad/core/services/tasks';
import { dbAdmin } from '@lilypad/db/client';
import {
  TaskPriorityEnum,
  TaskStatusEnum,
  TaskTypeEnum,
} from '@lilypad/db/enums';
import { UsersRepository } from '@lilypad/db/repository/users';
import { EmailService } from '@lilypad/email/service';
import type {
  BaseEmailProps,
  DistrictOnboardingEmailProps,
} from '@lilypad/email/types';
import { calculateDueDate } from '@lilypad/shared/date';

import { inngest } from '../client';
import { Events } from '../events';

/**
 * ONBOARD-04: All School Team: Receives a welcome to the Lilypad Platform with login instructions
 * ONBOARD-06: Pairs school with a psychologist that matches the needs of the school and preferences for scheduling
 * ONBOARD-09: Case Manager: Ships package to school district with a document camera and physical materials required for the evaluations.
 */

interface SendDistrictOnboardingEmailInput {
  userId: string; // Super User / Case Manager
  districtId: string;
  emails: Array<DistrictOnboardingEmailProps & BaseEmailProps>;
}

export const districtOnboardingCompletion = inngest.createFunction(
  { id: 'district-onboarding-completion' },
  { event: Events.DISTRICT_ONBOARDING_COMPLETED },
  async ({ event, step }) => {
    const { userId, districtId, emails } = event.data
      .encrypted as SendDistrictOnboardingEmailInput;

    const tasks = new TasksService(dbAdmin);

    const sendSchoolStaffDistrictOnboardingEmails = step.run(
      'send-school-staff-district-onboarding-emails',
      async () => {
        await Promise.all(
          emails.map((email) => EmailService.sendDistrictOnboardingEmail(email))
        );
      }
    );

    const createAssignPsychologistTask = step.run(
      'create-assign-psychologist-task',
      async () => {
        await tasks.create({
          taskType: TaskTypeEnum.ASSIGN_PSYCHOLOGIST,
          districtId,
          assignedToId: userId,
          assignedById: userId,
          dueDate: calculateDueDate({
            type: 'business_days',
            amount: 2,
          }),
          priority: TaskPriorityEnum.MEDIUM,
          status: TaskStatusEnum.PENDING,
        });
      }
    );

    const createShipEvaluationMaterialsTask = step.run(
      'create-ship-evaluation-materials-task',
      async () => {
        await tasks.create({
          taskType: TaskTypeEnum.SHIP_EVALUATION_MATERIALS,
          districtId,
          assignedToId: userId,
          assignedById: userId,
          dueDate: calculateDueDate({
            type: 'business_days',
            amount: 5,
          }),
          priority: TaskPriorityEnum.MEDIUM,
          status: TaskStatusEnum.PENDING,
        });
      }
    );

    const createUpdateUserAvailabilityTask = step.run(
      'create-update-user-availability-task',
      async () => {
        const userIds = await dbAdmin.transaction(async (tx) => {
          const usersRepo = new UsersRepository(tx);
          return await Promise.all(
            emails.map(async (email) =>
              usersRepo.getUserIdByEmail(email.recipient)
            )
          );
        });

        const validUserIds = userIds.filter(Boolean);

        await Promise.all(
          validUserIds.map((validUserId) => {
            return tasks.create({
              taskType: TaskTypeEnum.UPDATE_AVAILABILITY,
              districtId,
              assignedToId: validUserId,
              assignedById: validUserId,
              dueDate: calculateDueDate({
                type: 'business_days',
                amount: 1,
              }),
              priority: TaskPriorityEnum.MEDIUM,
              status: TaskStatusEnum.PENDING,
            });
          })
        );
      }
    );

    await Promise.all([
      sendSchoolStaffDistrictOnboardingEmails,
      createAssignPsychologistTask,
      createShipEvaluationMaterialsTask,
      createUpdateUserAvailabilityTask,
    ]);
  }
);
