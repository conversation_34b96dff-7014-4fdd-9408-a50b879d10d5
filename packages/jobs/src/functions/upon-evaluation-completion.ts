import { TasksService } from '@lilypad/core/services/tasks';
import { dbAdmin } from '@lilypad/db/client';
import {
  TaskPriorityEnum,
  TaskStatusEnum,
  TaskTypeEnum,
} from '@lilypad/db/enums';
import { calculateDueDate } from '@lilypad/shared/date';

import { inngest } from '../client';
import { Events } from '../events';

/**
 * EVA-04: Complete Student Interview Form - Inputs notes directly into Student Interview Form (v1: on Google Drive)
 * EVA-06: Upload protocols used during evaluation - Uploads any paper forms that were used during the evaluation to the student folder.
 * EVA-07: Update assessment scores - Updates Q-interactive with any remaining scores so that the report is ready to be drafted
 * POST-01: Generate report draft - Once all notes are submitted generates first draft of the evaluation report, submits to psych for review.
 * POST-07: Schedule IEP meeting - Schedules IEP meeting (if not already scheduled) and uses Meeting Scheduling Form to book directly into the psychologist calendar
 */

interface UponEvaluationCompletionInput {
  taskId: string;
  psychologistId: string;
  proctorId: string;
  schoolAdminId: string;
  schoolCoordinatorId: string;
}

export const uponEvaluationCompletion = inngest.createFunction(
  { id: 'upon-evaluation-completion' },
  { event: Events.EVALUATION_SESSION_COMPLETED },
  async ({ event, step }) => {
    const {
      taskId,
      psychologistId,
      proctorId,
      schoolAdminId,
      schoolCoordinatorId,
    } = event.data.encrypted as UponEvaluationCompletionInput;

    const tasks = new TasksService(dbAdmin);

    const task = await step.run('get-task-details', async () => {
      return await tasks.getTaskById(taskId);
    });

    const createPsychologistTasks = step.run(
      'create-complete-student-interview-and-update-assessment-scores-task',
      async () => {
        const studentInterviewTaskId = await tasks.create({
          taskType: TaskTypeEnum.COMPLETE_STUDENT_INTERVIEW,
          districtId: task.district?.id,
          assignedToId: psychologistId,
          assignedById: psychologistId,
          dueDate: calculateDueDate({
            type: 'business_days',
            amount: 1,
          }),
          priority: TaskPriorityEnum.HIGH,
          status: TaskStatusEnum.PENDING,
        });
        await tasks.create(
          {
            taskType: TaskTypeEnum.UPDATE_ASSESSMENT_SCORES,
            districtId: task.district?.id,
            assignedToId: psychologistId,
            assignedById: psychologistId,
            dueDate: calculateDueDate({
              type: 'business_days',
              amount: 1,
            }),
            priority: TaskPriorityEnum.HIGH,
            status: TaskStatusEnum.PENDING,
          },
          studentInterviewTaskId
        );
      }
    );

    const createUploadProtocolsTaskForSchoolAdmin = step.run(
      'create-upload-protocols-task-school-admin',
      async () => {
        return await tasks.create({
          taskType: TaskTypeEnum.UPDLOAD_PROTOCOLS,
          districtId: task.district?.id,
          assignedToId: schoolAdminId,
          assignedById: schoolAdminId,
          dueDate: calculateDueDate({
            type: 'business_days',
            amount: 2,
          }),
          priority: TaskPriorityEnum.MEDIUM,
          status: TaskStatusEnum.PENDING,
        });
      }
    );

    const createUploadProtocolsTaskForProctor = step.run(
      'create-upload-protocols-task-proctor',
      async () => {
        return await tasks.create({
          taskType: TaskTypeEnum.UPDLOAD_PROTOCOLS,
          districtId: task.district?.id,
          assignedToId: proctorId,
          assignedById: proctorId,
          dueDate: calculateDueDate({
            type: 'business_days',
            amount: 2,
          }),
          priority: TaskPriorityEnum.MEDIUM,
          status: TaskStatusEnum.PENDING,
        });
      }
    );

    const createScheduleIepMeetingTask = step.run(
      'create-schedule-iep-meeting-task',
      async () => {
        return await tasks.create({
          taskType: TaskTypeEnum.SCHEDULE_IEP_MEETING,
          districtId: task.district?.id,
          assignedToId: schoolCoordinatorId,
          assignedById: schoolCoordinatorId,
          dueDate: null, // No due date as specified
          priority: TaskPriorityEnum.MEDIUM,
          status: TaskStatusEnum.PENDING,
        });
      }
    );

    await Promise.all([
      createPsychologistTasks,
      createUploadProtocolsTaskForSchoolAdmin,
      createUploadProtocolsTaskForProctor,
      createScheduleIepMeetingTask,
    ]);
  }
);
