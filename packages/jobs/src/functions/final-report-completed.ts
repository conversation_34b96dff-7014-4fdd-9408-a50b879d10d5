import { EmailService } from '@lilypad/email/service';
import type {
  BaseEmailProps,
  FinalReportEmailProps,
} from '@lilypad/email/types';

import { inngest } from '../client';
import { Events } from '../events';

/**
 * POST-05: Share final report with team - Send entire team (and necessary people to receive report) a link to review the report in the platform
 * POST-06: Final report completed - Hits a button that says "Received". They are free to review submitted report prior to IEP meeting and sends to parent as required.
 */

interface FinalReportCompletedInput {
  emails: Array<FinalReportEmailProps & BaseEmailProps>;
}

export const finalReportCompleted = inngest.createFunction(
  { id: 'final-report-completed' },
  { event: Events.REPORT_FINAL_REVIEWED },
  async ({ event, step }) => {
    const { emails } = event.data.encrypted as FinalReportCompletedInput;

    await step.run('send-final-report-emails', async () => {
      return await Promise.all(
        emails.map((email) => EmailService.sendFinalReportEmail(email))
      );
    });
  }
);
