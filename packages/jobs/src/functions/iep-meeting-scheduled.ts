import { TasksService } from '@lilypad/core/services/tasks';
import { dbAdmin } from '@lilypad/db/client';
import {
  TaskPriorityEnum,
  TaskStatusEnum,
  TaskTypeEnum,
} from '@lilypad/db/enums';
import { inngest } from '../client';
import { Events } from '../events';

/**
 * POST-08: Prepare for IEP meeting - Receives evaluation summary and request to review evaluation to prepare for upcoming meeting. (48 hours before meeting)
 * POST-09: Send meeting invitations - Calendar invite generated and shared with relevant people
 */

interface IepMeetingScheduledInput {
  taskId: string;
  assistantId: string;
  meetingDate: string; // ISO date string
}

export const iepMeetingScheduled = inngest.createFunction(
  { id: 'iep-meeting-scheduled' },
  { event: Events.MEETING_IEP_SCHEDULED },
  async ({ event, step }) => {
    const { taskId, assistantId, meetingDate } = event.data
      .encrypted as IepMeetingScheduledInput;

    const tasks = new TasksService(dbAdmin);
    const meetingDateTime = new Date(meetingDate);

    const task = await step.run('get-task-details', async () => {
      return await tasks.getTaskById(taskId);
    });

    await step.run('create-send-meeting-invitations-task', async () => {
      return await tasks.create({
        taskType: TaskTypeEnum.SEND_MEETING_INVITATIONS,
        districtId: task.district?.id,
        assignedToId: assistantId,
        assignedById: assistantId,
        dueDate: meetingDateTime,
        priority: TaskPriorityEnum.HIGH,
        status: TaskStatusEnum.PENDING,
      });
    });
  }
);
