import { TasksService } from '@lilypad/core/services/tasks';
import { dbAdmin } from '@lilypad/db/client';
import {
  TaskPriorityEnum,
  TaskStatusEnum,
  TaskTypeEnum,
} from '@lilypad/db/enums';
import { calculateDueDate } from '@lilypad/shared/date';

import { inngest } from '../client';
import { Events } from '../events';

/**
 * PRE-07: Prepare rating scales - Following submission of the evaluation plan, rating scales must be prepared to be sent to teachers and parents.
 * PRE-10: Prepare assessment materials - Sets up assessments for the Psychologist including setting up Pearson + other platforms and prepping rating scales. Marks student "evaluation ready".
 */

interface EvaluationPlanCreationInput {
  taskId: string;
  assistantId: string;
}

export const evaluationPlanCreation = inngest.createFunction(
  { id: 'evaluation-plan-creation' },
  { event: Events.EVALUATION_PLAN_CREATED },
  async ({ event, step }) => {
    const { taskId, assistantId } = event.data
      .encrypted as EvaluationPlanCreationInput;

    const tasks = new TasksService(dbAdmin);

    const task = await step.run('get-task-details', async () => {
      return await tasks.getTaskById(taskId);
    });

    await step.run(
      'create-prepare-rating-scales-and-assessment-materials-task',
      async () => {
        const ratingScalesTaskId = await tasks.create({
          taskType: TaskTypeEnum.PREPARE_RATING_SCALES,
          districtId: task.district?.id,
          assignedToId: assistantId,
          assignedById: assistantId,
          dueDate: calculateDueDate({
            type: 'business_days',
            amount: 1,
          }),
          priority: TaskPriorityEnum.MEDIUM,
          status: TaskStatusEnum.PENDING,
        });

        await tasks.create(
          {
            taskType: TaskTypeEnum.PREPARE_ASSESSMENT_MATERIALS,
            districtId: task.district?.id,
            assignedToId: assistantId,
            assignedById: assistantId,
            dueDate: calculateDueDate({
              type: 'business_days',
              amount: 1,
            }),
            priority: TaskPriorityEnum.MEDIUM,
            status: TaskStatusEnum.PENDING,
          },
          ratingScalesTaskId
        );
      }
    );
  }
);
