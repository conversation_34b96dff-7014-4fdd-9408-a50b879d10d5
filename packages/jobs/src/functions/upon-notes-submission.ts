import { TasksService } from '@lilypad/core/services/tasks';
import { dbAdmin } from '@lilypad/db/client';
import {
  TaskPriorityEnum,
  TaskStatusEnum,
  TaskTypeEnum,
} from '@lilypad/db/enums';
import { calculateDueDate } from '@lilypad/shared/date';

import { inngest } from '../client';
import { Events } from '../events';

/**
 * POST-01: Generate report draft - Once all notes are submitted generates first draft of the evaluation report, submits to psych for review.
 */

interface UponNotesSubmissionInput {
  taskId: string;
  assistantId: string;
}

export const uponNotesSubmission = inngest.createFunction(
  { id: 'upon-notes-submission' },
  { event: Events.EVALUATION_NOTES_SUBMITTED },
  async ({ event, step }) => {
    const { taskId, assistantId } = event.data
      .encrypted as UponNotesSubmissionInput;

    const tasks = new TasksService(dbAdmin);

    const task = await step.run('get-task-details', async () => {
      return await tasks.getTaskById(taskId);
    });

    // Create a new task for the assistant to generate report draft
    await step.run('create-generate-report-draft-task', async () => {
      return await tasks.create({
        taskType: TaskTypeEnum.GENERATE_REPORT_DRAFT,
        districtId: task.district?.id,
        assignedToId: assistantId,
        assignedById: assistantId,
        dueDate: calculateDueDate({
          type: 'business_days',
          amount: 2,
        }),
        priority: TaskPriorityEnum.HIGH,
        status: TaskStatusEnum.PENDING,
      });
    });
  }
);
