import { NotificationsService } from '@lilypad/core/services/notifications';
import { TasksService } from '@lilypad/core/services/tasks';
import { dbAdmin } from '@lilypad/db/client';
import {
  NotificationCategoryTypeEnum,
  NotificationTypeEnum,
  TaskStatusEnum,
  TaskTypeEnum,
} from '@lilypad/db/enums';
import { EmailService } from '@lilypad/email/service';
import type {
  BaseEmailProps,
  PsychologistAssignmentEmailProps,
} from '@lilypad/email/types';
import { inngest } from '../client';
import { Events } from '../events';

/**
 * ONBOARD-07: Psychologist: Notified of being assigned to a school district and reviews their onboarding form and school demographics, and accepts school assignment.
 * ONBOARD-08: System: All School Team receives an introduction email to the school care team introducing the psychologist with a picture and a profile.
 * PRE-01: School Coordinator: Complete referrals for students. May be single student or multiple. May upload background information or may assign task to another person.
 */

interface SchoolDistrictAssignmentAcceptanceInput {
  taskId: string;
  districtId: string;
  schoolCoordinatorId: string;
  emails: Array<PsychologistAssignmentEmailProps & BaseEmailProps>;
}

export const schoolDistrictAssignmentAcceptance = inngest.createFunction(
  { id: 'school-district-assignment-acceptance' },
  { event: Events.DISTRICT_ASSIGNMENT_ACCEPTED },
  async ({ event, step }) => {
    const tasks = new TasksService(dbAdmin);
    const notification = new NotificationsService(dbAdmin);

    const { taskId, districtId, schoolCoordinatorId, emails } = event.data
      .encrypted as SchoolDistrictAssignmentAcceptanceInput;

    const task = await step.run('get-task', async () => {
      return await tasks.getTaskById(taskId);
    });

    const sendCaseManagerNotification = step.run(
      'send-case-manager-notification',
      async () => {
        await notification.send({
          userId: task.assignedBy.id,
          type: NotificationTypeEnum.TASK_ACCEPTED,
          content: `${task.assignedTo.fullName} has accepted their school district assignment`,
          category: NotificationCategoryTypeEnum.WORKFLOW,
        });
      }
    );

    const sendSchoolTeamPsychologistAssignmentEmail = step.run(
      'send-school-team-psychologist-assignment-email',
      async () => {
        await Promise.all(
          emails.map((email) =>
            EmailService.sendPsychologistAssignmentEmail(email)
          )
        );
      }
    );

    const createReferralFormTask = step.run(
      'create-referral-form-task',
      async () => {
        await tasks.create({
          taskType: TaskTypeEnum.COMPLETE_REFERRAL_FORM,
          districtId,
          status: TaskStatusEnum.PENDING,
          assignedToId: schoolCoordinatorId,
          assignedById: schoolCoordinatorId,
          dueDate: null,
        });
      }
    );

    await Promise.all([
      sendCaseManagerNotification,
      sendSchoolTeamPsychologistAssignmentEmail,
    ]);

    await createReferralFormTask;
  }
);
