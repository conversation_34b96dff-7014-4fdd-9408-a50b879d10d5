import { NotificationsService } from '@lilypad/core/services/notifications';
import { dbAdmin } from '@lilypad/db/client';
import {
  NotificationCategoryTypeEnum,
  NotificationTypeEnum,
} from '@lilypad/db/enums';

import { inngest } from '../client';
import { Events } from '../events';

/**
 * POST-05: Share final report with team - Send entire team (and necessary people to receive report) a link to review the report in the platform
 */

interface UponReportFinalizedByClinicalDirectorInput {
  teamMemberIds: string[];
  reportUrl: string;
}

export const uponReportFinalizedByClinicalDirector = inngest.createFunction(
  { id: 'upon-report-finalized-by-clinical-director' },
  { event: Events.REPORT_FINALIZED_BY_CLINICAL_DIRECTOR },
  async ({ event, step }) => {
    const { teamMemberIds, reportUrl } = event.data
      .encrypted as UponReportFinalizedByClinicalDirectorInput;

    const notifications = new NotificationsService(dbAdmin);

    await step.run('send-team-notifications', async () => {
      return await Promise.all(
        teamMemberIds.map((memberId) =>
          notifications.send({
            userId: memberId,
            type: NotificationTypeEnum.REPORT_READY,
            content: 'Final evaluation report is ready for review.',
            category: NotificationCategoryTypeEnum.WORKFLOW,
            metadata: { reportUrl },
          })
        )
      );
    });
  }
);
