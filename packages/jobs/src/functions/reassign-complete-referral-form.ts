import { TasksService } from '@lilypad/core/services/tasks';
import { dbAdmin } from '@lilypad/db/client';

import { inngest } from '../client';
import { Events } from '../events';

/**
 * PRE-01: Complete referrals for students. May be single student or multiple. May upload background information or may assign task to another person. During this workflow the user enters who should be kept up to date on the evaluation progress and which people should receive the completed report (aka Assigns a 'team' to the 'case').
 * PRE-02: Uploads all required referral/background information. (V2: If any data is missing, the person must explain why and confirm no further data will be provided.) (For V1: Note: Please upload all referral information available. This may include xxxxxxxx.)
 */

interface ReassignCompleteReferralFormInput {
  taskId: string;
  userId: string; // School Coordinator
  assignedToId: string; // School Staff
  notes?: string;
}

export const reassignCompleteReferralForm = inngest.createFunction(
  { id: 'reassign-complete-referral-form' },
  { event: Events.REASSIGN_COMPLETE_REFERRAL_FORM },
  async ({ event, step }) => {
    const { taskId, userId, assignedToId, notes } = event.data
      .encrypted as ReassignCompleteReferralFormInput;

    const tasks = new TasksService(dbAdmin);

    await step.run('reassign-task', async () => {
      return await tasks.reassign({
        taskId,
        reassignedTo: assignedToId,
        reassignedBy: userId,
        notes,
      });
    });
  }
);
