import { NotificationsService } from '@lilypad/core/services/notifications';
import { TasksService } from '@lilypad/core/services/tasks';
import { dbAdmin } from '@lilypad/db/client';
import {
  NotificationCategoryTypeEnum,
  NotificationTypeEnum,
  TaskPriorityEnum,
  TaskStatusEnum,
  TaskTypeEnum,
} from '@lilypad/db/enums';
import { calculateDueDate, formatDate } from '@lilypad/shared/date';

import { inngest } from '../client';
import { Events } from '../events';

/**
 * PRE-04: Generate calendar invites - Calendar invite generated for evaluation.
 * PRE-05: Send notification of upcoming evaluations - Notifies entire eval team of upcoming scheduled evaluation summaries.
 */

interface EvaluationSchedulingCompletionInput {
  taskId: string;
  assistantId: string;
  teamMemberIds: string[];
  evaluationDate: string; // ISO date string
}

export const evaluationSchedulingCompletion = inngest.createFunction(
  { id: 'evaluation-scheduling-completion' },
  { event: Events.EVALUATION_SCHEDULING_COMPLETED },
  async ({ event, step }) => {
    const { taskId, assistantId, teamMemberIds, evaluationDate } = event.data
      .encrypted as EvaluationSchedulingCompletionInput;

    const tasks = new TasksService(dbAdmin);
    const notifications = new NotificationsService(dbAdmin);

    const formattedEvaluationDate = formatDate(evaluationDate, {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });

    const task = await step.run('get-task-details', async () => {
      return await tasks.getTaskById(taskId);
    });

    const createCalendarInvitesTask = step.run(
      'create-calendar-invites-task',
      async () => {
        return await tasks.create({
          taskType: TaskTypeEnum.GENERATE_CALENDAR_INVITES,
          districtId: task.district?.id,
          assignedToId: assistantId,
          assignedById: assistantId,
          dueDate: calculateDueDate({
            type: 'business_days',
            amount: 1,
          }),
          priority: TaskPriorityEnum.HIGH,
          status: TaskStatusEnum.PENDING,
        });
      }
    );

    const sendEvaluationScheduledNotifications = step.run(
      'send-evaluation-scheduled-notifications',
      async () => {
        return await Promise.all(
          teamMemberIds.map((memberId) =>
            notifications.send({
              userId: memberId,
              type: NotificationTypeEnum.EVALUATION_SCHEDULED,
              content: `Evaluation has been scheduled for ${formattedEvaluationDate}`,
              category: NotificationCategoryTypeEnum.WORKFLOW,
            })
          )
        );
      }
    );

    await Promise.all([
      createCalendarInvitesTask,
      sendEvaluationScheduledNotifications,
    ]);
  }
);
