import { TasksService } from '@lilypad/core/services/tasks';
import { dbAdmin } from '@lilypad/db/client';
import {
  TaskPriorityEnum,
  TaskStatusEnum,
  TaskTypeEnum,
} from '@lilypad/db/enums';
import { calculateDueDate } from '@lilypad/shared/date';

import { inngest } from '../client';
import { Events } from '../events';

/**
 * POST-03: Score report quality - Reviews completed evaluation report and (v2: scores based on quality of report)
 * POST-04: Review evaluation final report - Reviews completed evaluation report and provides edits as necessary
 */

interface UponReportFinalizedByPsychologistInput {
  taskId: string;
  assistantId: string;
  clinicalDirectorId: string;
}

export const uponReportFinalizedByPsychologist = inngest.createFunction(
  { id: 'upon-report-finalized-by-psychologist' },
  { event: Events.REPORT_FINALIZED_BY_PSYCHOLOGIST },
  async ({ event, step }) => {
    const { taskId, assistantId, clinicalDirectorId } = event.data
      .encrypted as UponReportFinalizedByPsychologistInput;

    const tasks = new TasksService(dbAdmin);

    const task = await step.run('get-task-details', async () => {
      return await tasks.getTaskById(taskId);
    });

    const createScoreQualityTask = step.run(
      'create-score-report-quality-task',
      async () => {
        return await tasks.create({
          taskType: TaskTypeEnum.SCORE_REPORT_QUALITY,
          districtId: task.district?.id,
          assignedToId: assistantId,
          assignedById: assistantId,
          dueDate: calculateDueDate({
            type: 'hours',
            amount: 1,
          }),
          priority: TaskPriorityEnum.HIGH,
          status: TaskStatusEnum.PENDING,
        });
      }
    );

    const createReviewFinalReportTask = step.run(
      'create-review-final-report-task',
      async () => {
        return await tasks.create({
          taskType: TaskTypeEnum.REVIEW_FINAL_REPORT,
          districtId: task.district?.id,
          assignedToId: clinicalDirectorId,
          assignedById: clinicalDirectorId,
          dueDate: calculateDueDate({
            type: 'business_days',
            amount: 2,
          }),
          priority: TaskPriorityEnum.HIGH,
          status: TaskStatusEnum.PENDING,
        });
      }
    );

    await Promise.all([createScoreQualityTask, createReviewFinalReportTask]);
  }
);
