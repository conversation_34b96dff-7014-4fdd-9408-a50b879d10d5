import { TasksService } from '@lilypad/core/services/tasks';
import { dbAdmin } from '@lilypad/db/client';
import {
  TaskPriorityEnum,
  TaskStatusEnum,
  TaskTypeEnum,
} from '@lilypad/db/enums';
import { UserAvailabilitiesRepository } from '@lilypad/db/repository';
import type { StaleAvailabilityUser } from '@lilypad/db/repository/types/user-availabilities';
import { calculateDueDate } from '@lilypad/shared/date';

import { inngest } from '../client';
import { BATCH_CONFIG, CONCURRENCY_LIMITS, CRON_SCHEDULES } from '../constants';
import { createBatches } from '../utils';

const INTERVALS = {
  STALE_DAYS: 10,
  TASK_DUE_DAYS: 5,
} as const;

const getStaleAvailabilitiesCount = async () => {
  return await dbAdmin.transaction(async (tx) => {
    const repository = new UserAvailabilitiesRepository(tx);
    return await repository.getStaleAvailabilitiesCount(INTERVALS.STALE_DAYS);
  });
};

const getStaleUsers = async () => {
  return await dbAdmin.transaction(async (tx) => {
    const repository = new UserAvailabilitiesRepository(tx);
    return await repository.findUsersWithStaleAvailabilitiesWithoutExistingTasks(
      INTERVALS.STALE_DAYS
    );
  });
};

const createAvailabilityTasksBatch = async (
  tasksService: TasksService,
  users: StaleAvailabilityUser[]
) => {
  try {
    const batchTasks = users.map((user) => ({
      taskType: TaskTypeEnum.UPDATE_AVAILABILITY,
      assignedToId: user.userId,
      assignedById: user.userId,
      dueDate: calculateDueDate({
        type: 'calendar_days',
        amount: INTERVALS.TASK_DUE_DAYS,
      }),
      priority: TaskPriorityEnum.MEDIUM,
      status: TaskStatusEnum.PENDING,
      metadata: { daysSinceUpdated: user.daysSinceUpdate },
    }));

    const taskIds = await tasksService.createBatch(batchTasks);

    return users.map((user, index) => ({
      success: true,
      userId: user.userId,
      taskId: taskIds[index],
    }));
  } catch (error) {
    return users.map((user) => ({
      success: false,
      userId: user.userId,
      error,
    }));
  }
};

const createTasksInBatches = async (
  tasksService: TasksService,
  users: StaleAvailabilityUser[]
) => {
  const batches = createBatches(users, BATCH_CONFIG.DEFAULT_BATCH_SIZE);

  await Promise.all(
    batches.map(
      async (batch) => await createAvailabilityTasksBatch(tasksService, batch)
    )
  );
};

export const updateUserAvailabilities = inngest.createFunction(
  {
    id: 'update-user-availability-cron',
    name: 'Update User Availability Reminder',
    concurrency: CONCURRENCY_LIMITS.SINGLE_INSTANCE,
  },
  { cron: CRON_SCHEDULES.BIWEEKLY },
  async ({ step }) => {
    const tasksService = new TasksService(dbAdmin);

    const totalStaleUsers = await step.run(
      'get-stale-availabilities-count',
      async () => await getStaleAvailabilitiesCount()
    );

    if (totalStaleUsers === 0) {
      return;
    }

    await step.run('process-stale-availabilities-in-batches', async () => {
      const staleUsers = await getStaleUsers();

      if (staleUsers.length === 0) {
        return 0;
      }

      await createTasksInBatches(tasksService, staleUsers);
    });
  }
);
