import { NotificationsService } from '@lilypad/core/services/notifications';
import { dbAdmin } from '@lilypad/db/client';
import { CaseRepository } from '@lilypad/db/repository';
import type { CaseForUpcomingEvaluations } from '@lilypad/db/repository/types/cases';
import {
  NotificationCategoryTypeEnum,
  NotificationTypeEnum,
} from '@lilypad/db/schema/enums';

import { inngest } from '../client';
import { BATCH_CONFIG, CONCURRENCY_LIMITS, CRON_SCHEDULES } from '../constants';
import { createBatches, formatEvaluationDate } from '../utils';

const INTERVALS = {
  DAYS_AHEAD: 7,
} as const;

const getUpcomingEvaluations = async () => {
  return await dbAdmin.transaction(async (tx) => {
    const repository = new CaseRepository(tx);
    return await repository.getCasesWithUpcomingEvaluations(
      INTERVALS.DAYS_AHEAD
    );
  });
};

const sendEvaluationReminder = async (
  notifications: NotificationsService,
  evaluation: CaseForUpcomingEvaluations
) => {
  try {
    const formattedDate = formatEvaluationDate(
      new Date(evaluation.evaluationDueDate)
    );

    await notifications.send({
      userId: evaluation.userId,
      type: NotificationTypeEnum.UPCOMING_EVALUATION_REMINDER,
      content: `Upcoming evaluation reminder - evaluation scheduled for ${formattedDate}`,
      category: NotificationCategoryTypeEnum.WORKFLOW,
      metadata: {
        caseId: evaluation.caseId,
        evaluationDueDate: evaluation.evaluationDueDate,
        userRole: evaluation.roleName,
      },
    });

    return { success: true, userId: evaluation.userId };
  } catch (error) {
    return { success: false, userId: evaluation.userId, error };
  }
};

const sendRemindersInBatches = async (
  notifications: NotificationsService,
  evaluations: CaseForUpcomingEvaluations[]
) => {
  const batches = createBatches(evaluations, BATCH_CONFIG.DEFAULT_BATCH_SIZE);

  await Promise.all(
    batches.map(async (batch) => {
      await Promise.all(
        batch.map(
          async (evaluation) =>
            await sendEvaluationReminder(notifications, evaluation)
        )
      );
    })
  );
};

export const sendUpcomingEvaluationReminders = inngest.createFunction(
  {
    id: 'send-upcoming-evaluation-reminders',
    name: 'Send Upcoming Evaluation Reminders',
    concurrency: CONCURRENCY_LIMITS.SINGLE_INSTANCE,
  },
  { cron: CRON_SCHEDULES.WEEKLY },
  async ({ step }) => {
    const notifications = new NotificationsService(dbAdmin);

    const upcomingEvaluations = await step.run(
      'get-upcoming-evaluations',
      async () => await getUpcomingEvaluations()
    );

    if (upcomingEvaluations.length === 0) {
      return;
    }

    await step.run(
      'send-evaluation-reminders-in-batches',
      async () =>
        await sendRemindersInBatches(notifications, upcomingEvaluations)
    );
  }
);
