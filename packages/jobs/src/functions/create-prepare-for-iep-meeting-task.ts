import { TasksService } from '@lilypad/core/services/tasks';
import { dbAdmin } from '@lilypad/db/client';
import { CaseRepository } from '@lilypad/db/repository';
import type { CaseForIEPMeetingTasks } from '@lilypad/db/repository/types/cases';
import {
  TaskPriorityEnum,
  TaskStatusEnum,
  TaskTypeEnum,
} from '@lilypad/db/schema/enums';

import { inngest } from '../client';
import { BATCH_CONFIG, CRON_SCHEDULES } from '../constants';
import { createBatches } from '../utils';

const INTERVALS = {
  HOURS_AHEAD: 72,
  BUFFER_HOURS: 24,
} as const;

const getCasesNeedingIEPPreparation = async () => {
  return await dbAdmin.transaction(async (tx) => {
    const repository = new CaseRepository(tx);
    return await repository.getCasesForIEPMeetingTasks(
      INTERVALS.HOURS_AHEAD,
      INTERVALS.BUFFER_HOURS
    );
  });
};

const createIEPMeetingTaskPair = async (
  tasksService: TasksService,
  caseItem: CaseForIEPMeetingTasks
) => {
  try {
    const meetingDate = new Date(caseItem.meetingDate);

    const prepareTaskId = await tasksService.create({
      taskType: TaskTypeEnum.PREPARE_FOR_IEP_MEETING,
      caseId: caseItem.caseId,
      assignedToId: caseItem.userId,
      assignedById: caseItem.userId,
      dueDate: meetingDate,
      priority: TaskPriorityEnum.HIGH,
      status: TaskStatusEnum.PENDING,
      metadata: {
        meetingDate,
        createdBy: 'system',
      },
    });

    const completeTaskId = await tasksService.create(
      {
        taskType: TaskTypeEnum.COMPLETE_IEP_MEETING,
        caseId: caseItem.caseId,
        assignedToId: caseItem.userId,
        assignedById: caseItem.userId,
        dueDate: meetingDate,
        priority: TaskPriorityEnum.HIGH,
        status: TaskStatusEnum.PENDING,
        metadata: {
          meetingDate,
          createdBy: 'system',
          dependentOnTask: prepareTaskId,
        },
      },
      prepareTaskId
    );

    return {
      success: true,
      caseId: caseItem.caseId,
      prepareTaskId,
      completeTaskId,
    };
  } catch (error) {
    return { success: false, caseId: caseItem.caseId, error };
  }
};

const createTasksInBatches = async (
  tasksService: TasksService,
  cases: CaseForIEPMeetingTasks[]
) => {
  const batches = createBatches(cases, BATCH_CONFIG.DEFAULT_BATCH_SIZE);

  await Promise.all(
    batches.map(async (batch) => {
      await Promise.all(
        batch.map(
          async (caseItem) =>
            await createIEPMeetingTaskPair(tasksService, caseItem)
        )
      );
    })
  );
};

export const createPrepareForIEPMeetingTask = inngest.createFunction(
  {
    id: 'create-prepare-for-iep-meeting-task',
    name: 'Create Prepare for IEP Meeting Tasks',
    concurrency: {
      limit: 1,
    },
  },
  { cron: CRON_SCHEDULES.DAILY },
  async ({ step }) => {
    const tasksService = new TasksService(dbAdmin);

    const casesNeedingIEPPreparation = await step.run(
      'get-cases-needing-iep-preparation',
      async () => await getCasesNeedingIEPPreparation()
    );

    if (casesNeedingIEPPreparation.length === 0) {
      return;
    }

    await step.run(
      'create-iep-meeting-tasks-in-batches',
      async () =>
        await createTasksInBatches(tasksService, casesNeedingIEPPreparation)
    );
  }
);
