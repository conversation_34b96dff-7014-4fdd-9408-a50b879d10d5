import { TasksService } from '@lilypad/core/services/tasks';
import { dbAdmin } from '@lilypad/db/client';
import {
  TaskPriorityEnum,
  TaskStatusEnum,
  TaskTypeEnum,
} from '@lilypad/db/enums';

import { inngest } from '../client';
import { Events } from '../events';

/**
 * EVA-03: Mark evaluation complete - Completes evaluation and marks "Mark Done" in task list. The amount of time the evaluation will take depends on the evaluation plan. Other option: "Request More Time", opens "Incomplete Evaluation Form", which triggers other tasks.
 */

interface UponJoiningEvaluationMeetingInput {
  taskId: string;
  psychologistId: string;
}

export const uponJoiningEvaluationMeeting = inngest.createFunction(
  { id: 'upon-joining-evaluation-meeting' },
  { event: Events.EVALUATION_SESSION_START },
  async ({ event, step }) => {
    const { taskId, psychologistId } = event.data
      .encrypted as UponJoiningEvaluationMeetingInput;

    const tasks = new TasksService(dbAdmin);

    const task = await step.run('get-task-details', async () => {
      return await tasks.getTaskById(taskId);
    });

    await step.run('create-mark-evaluation-complete-task', async () => {
      return await tasks.create({
        taskType: TaskTypeEnum.MARK_EVALUATION_COMPLETE,
        districtId: task.district?.id,
        assignedToId: psychologistId,
        assignedById: psychologistId,
        dueDate: null,
        priority: TaskPriorityEnum.HIGH,
        status: TaskStatusEnum.PENDING,
      });
    });
  }
);
