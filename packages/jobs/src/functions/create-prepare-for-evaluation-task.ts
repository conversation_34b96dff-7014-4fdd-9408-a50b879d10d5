import { TasksService } from '@lilypad/core/services/tasks';
import { dbAdmin } from '@lilypad/db/client';
import { CaseRepository } from '@lilypad/db/repository';
import type { CaseForPreparationTasks } from '@lilypad/db/repository/types/cases';
import {
  TaskPriorityEnum,
  TaskStatusEnum,
  TaskTypeEnum,
} from '@lilypad/db/schema/enums';

import { inngest } from '../client';
import { BATCH_CONFIG, CONCURRENCY_LIMITS, CRON_SCHEDULES } from '../constants';
import { createBatches } from '../utils';

const INTERVALS = {
  HOURS_AHEAD: 72,
  BUFFER_HOURS: 24,
} as const;

const getCasesNeedingPreparation = async () => {
  return await dbAdmin.transaction(async (tx) => {
    const repository = new CaseRepository(tx);
    return await repository.getCasesForPreparationTasks(
      INTERVALS.HOURS_AHEAD,
      INTERVALS.BUFFER_HOURS
    );
  });
};

const createPreparationTasksBatch = async (
  tasksService: TasksService,
  cases: CaseForPreparationTasks[]
) => {
  const tasksToCreate = cases.map((case_) => ({
    taskType: TaskTypeEnum.PREPARE_FOR_EVALUATION,
    caseId: case_.caseId,
    assignedToId: case_.userId,
    assignedById: case_.userId,
    dueDate: new Date(case_.evaluationDueDate),
    priority: TaskPriorityEnum.HIGH,
    status: TaskStatusEnum.PENDING,
    metadata: {
      evaluationDueDate: case_.evaluationDueDate,
      createdBy: 'system',
    },
  }));

  try {
    const createdTasks = await tasksService.createBatch(tasksToCreate);
    return { success: true, tasksCreated: createdTasks.length };
  } catch (error) {
    return { success: false, tasksCreated: 0, error };
  }
};

const createTasksInBatches = async (
  tasksService: TasksService,
  cases: CaseForPreparationTasks[]
) => {
  const batches = createBatches(cases, BATCH_CONFIG.DEFAULT_BATCH_SIZE);

  await Promise.all(
    batches.map(
      async (batch) => await createPreparationTasksBatch(tasksService, batch)
    )
  );
};

export const createPrepareForEvaluationTask = inngest.createFunction(
  {
    id: 'create-prepare-for-evaluation-task',
    name: 'Create Prepare for Evaluation Tasks',
    concurrency: CONCURRENCY_LIMITS.SINGLE_INSTANCE,
  },
  { cron: CRON_SCHEDULES.DAILY },
  async ({ step }) => {
    const tasksService = new TasksService(dbAdmin);

    const casesNeedingPreparation = await step.run(
      'get-cases-needing-preparation',
      async () => await getCasesNeedingPreparation()
    );

    if (casesNeedingPreparation.length === 0) {
      return;
    }

    await step.run(
      'create-preparation-tasks-in-batches',
      async () =>
        await createTasksInBatches(tasksService, casesNeedingPreparation)
    );
  }
);
