import { TasksService } from '@lilypad/core/services/tasks';
import { dbAdmin } from '@lilypad/db/client';
import {
  TaskPriorityEnum,
  TaskStatusEnum,
  TaskTypeEnum,
} from '@lilypad/db/enums';
import { calculateDueDate } from '@lilypad/shared/date';

import { inngest } from '../client';
import { Events } from '../events';

/**
 * POST-01: Generate report draft - Once all notes are submitted generates first draft of the evaluation report, submits to psych for review.
 */

interface UponReportDraftSubmissionInput {
  taskId: string;
  psychologistId: string;
}

export const uponReportDraftSubmission = inngest.createFunction(
  { id: 'upon-report-draft-submission' },
  { event: Events.REPORT_DRAFT_SUBMITTED },
  async ({ event, step }) => {
    const { taskId, psychologistId } = event.data
      .encrypted as UponReportDraftSubmissionInput;

    const tasks = new TasksService(dbAdmin);

    const task = await step.run('get-task-details', async () => {
      return await tasks.getTaskById(taskId);
    });

    // within 5 calendar days or 5 calendar days before IEP meeting (whichever sooner) - Porblem: We don't have IEP meeting date set up at this point.
    const fiveCalendarDaysFromNow = calculateDueDate({
      type: 'calendar_days',
      amount: 5,
    });

    await step.run('create-finalize-evaluation-report-task', async () => {
      return await tasks.create({
        taskType: TaskTypeEnum.FINALIZE_EVALUATION_REPORT,
        districtId: task.district?.id,
        assignedToId: psychologistId,
        assignedById: psychologistId,
        dueDate: fiveCalendarDaysFromNow,
        priority: TaskPriorityEnum.HIGH,
        status: TaskStatusEnum.PENDING,
      });
    });
  }
);
