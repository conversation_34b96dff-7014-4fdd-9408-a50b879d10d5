export enum Events {
  // District/School Events
  DISTRICT_ONBOARDING_COMPLETED = 'district/onboarding.completed',
  DISTRICT_PSYCHOLOGIST_ASSIGNED = 'district/psychologist.assigned',
  DISTRICT_ASSIGNMENT_ACCEPTED = 'district/assignment.accepted',
  DISTRICT_ASSIGNMENT_REJECTED = 'district/assignment.rejected',

  // Evaluation Events
  REASSIGN_COMPLETE_REFERRAL_FORM = 'evaluation/complete.referral.form.reassigned',
  EVALUATION_REFERRAL_FORM_COMPLETED = 'evaluation/referral.form.completed',
  EVALUATION_SCHEDULING_COMPLETED = 'evaluation/scheduling.completed',
  EVALUATION_PLAN_CREATED = 'evaluation/plan.created',
  EVALUATION_RATING_SCALES_PREPARED = 'evaluation/rating.scales.prepared',
  EVALUATION_SEND_RATING_SCALES = 'evaluation/send.rating.scales',
  EVALUATION_SESSION_START = 'evaluation/session.start',
  EVALUATION_SESSION_COMPLETED = 'evaluation/session.completed',
  EVALUATION_NOTES_SUBMITTED = 'evaluation/notes.submitted',

  // Report Events
  REPORT_DRAFT_SUBMITTED = 'report/draft.submitted',
  REPORT_FINALIZED_BY_PSYCHOLOGIST = 'report/finalized.by.psychologist',
  REPORT_FINALIZED_BY_CLINICAL_DIRECTOR = 'report/finalized.by.clinical.director',
  REPORT_FINAL_REVIEWED = 'report/final.reviewed',

  // Meeting Events
  MEETING_IEP_SCHEDULED = 'meeting/iep.scheduled',
  MEETING_IEP_JOINED = 'meeting/iep.joined',

  // Psychologist Events
  PSYCHOLOGIST_AVAILABILITY_UPDATE_REMINDER = 'psychologist/availability.update-reminder',
}
