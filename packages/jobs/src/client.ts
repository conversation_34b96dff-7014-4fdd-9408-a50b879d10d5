import { Inngest } from 'inngest';
import { keys } from '../keys';

import { manualEncryptionMiddleware } from '@inngest/middleware-encryption/manual';

const env = keys();

export const { decryptionMiddleware, encryptionMiddleware } =
  manualEncryptionMiddleware({
    key: env.INNGEST_ENCRYPTION_KEY,
    fallbackDecryptionKeys: [env.FALLBACK_INNGEST_ENCRYPTION_KEY],
    eventEncryptionField: 'encrypted',
  });

export const inngest = new Inngest({
  id: env.INNGEST_APP_ID,
  middleware: [
    decryptionMiddleware,
    /* OTHER MIDDLEWARES */
    encryptionMiddleware,
  ],
});
