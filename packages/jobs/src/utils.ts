export interface BaseBatchResult {
  [key: string]: number;
}

export const createBatches = <T>(items: T[], batchSize: number): T[][] => {
  const batches: T[][] = [];
  for (let i = 0; i < items.length; i += batchSize) {
    batches.push(items.slice(i, i + batchSize));
  }
  return batches;
};

export const calculateDaysFromNow = (date: Date): number => {
  const now = new Date();
  return Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
};

export const calculateTimeDifferenceInMs = (
  startDate: Date,
  endDate: Date
): number => {
  return endDate.getTime() - startDate.getTime();
};

export const formatEvaluationDate = (date: Date): string => {
  return date.toLocaleDateString('en-US', {
    weekday: 'short',
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};
