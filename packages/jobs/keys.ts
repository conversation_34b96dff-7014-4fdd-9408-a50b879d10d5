import { createEnv } from '@t3-oss/env-nextjs';
import { z } from 'zod';

export const keys = () =>
  createEnv({
    server: {
      INNGEST_APP_ID: z.string().min(1),
      INNGEST_ENCRYPTION_KEY: z.string().min(1),
      FALLBACK_INNGEST_ENCRYPTION_KEY: z.string().min(1),
    },
    runtimeEnv: {
      INNGEST_APP_ID: process.env.INNGEST_APP_ID,
      INNGEST_ENCRYPTION_KEY: process.env.INNGEST_ENCRYPTION_KEY,
      FALLBACK_INNGEST_ENCRYPTION_KEY:
        process.env.FALLBACK_INNGEST_ENCRYPTION_KEY,
    },
  });
