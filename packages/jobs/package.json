{"name": "@lilypad/jobs", "private": true, "version": "0.0.0", "type": "module", "scripts": {"clean": "git clean -xdf node_modules", "format": "biome format --write .", "lint": "biome lint --write .", "typecheck": "tsc --noEmit", "dev": "inngest-cli dev"}, "dependencies": {"@inngest/middleware-encryption": "^1.0.1", "@lilypad/db": "workspace:*", "@lilypad/core": "workspace:*", "@lilypad/email": "workspace:*", "@lilypad/shared": "workspace:*", "@lilypad/supabase": "workspace:*", "@t3-oss/env-nextjs": "^0.13.4", "inngest": "^3.40.0", "zod": "^3.25.7"}, "devDependencies": {"@lilypad/typescript": "workspace:*", "@types/node": "^22.15.3", "inngest-cli": "^1.8.2"}, "exports": {"./client": "./src/client.ts", "./api": "./src/api.ts", "./keys": "./keys.ts", "./events": "./src/events.ts"}}