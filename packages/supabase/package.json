{"name": "@lilypad/supabase", "private": true, "version": "0.0.0", "type": "module", "scripts": {"clean": "git clean -xdf node_modules", "format": "biome format --write .", "lint": "biome lint --write .", "typecheck": "tsc --noEmit"}, "dependencies": {"@lilypad/shared": "workspace:*", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.7", "@t3-oss/env-nextjs": "^0.13.4", "bcryptjs": "^3.0.2", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "nanoid": "^5.1.5", "next": "^15.3.2", "react": "^19.1.0", "uuid": "^11.1.0", "zod": "^3.25.7"}, "devDependencies": {"@lilypad/typescript": "workspace:*", "@types/crypto-js": "^4.2.2", "@types/node": "^22.15.3", "@types/react": "19.1.0"}, "exports": {".": "./src/index.ts", "./keys": "./keys.ts", "./server": "./src/config/server.ts", "./client": "./src/config/client.ts", "./middleware": "./src/config/middleware.ts", "./hooks": "./src/hooks/index.ts", "./storage": "./src/storage/index.ts", "./types": "./src/types/index.ts", "./auth": "./src/auth/index.ts", "./auth/*": "./src/auth/*.ts", "./storage/students/documents.server": "./src/storage/students/documents.server.ts", "./storage/students/documents.client": "./src/storage/students/documents.client.ts"}}