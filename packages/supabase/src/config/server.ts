import type { CookieMethodsServer } from '@supabase/ssr';
import { createServerClient } from '@supabase/ssr';
import type { ReadonlyRequestCookies } from 'next/dist/server/web/spec-extension/adapters/request-cookies';
import { cookies, headers } from 'next/headers';
import { keys } from '../../keys';
import type { Database } from '../types/db';

export const getSupabaseServerClient = async (
  params = { admin: false, script: false }
) => {
  const url = keys().NEXT_PUBLIC_SUPABASE_URL;
  const key = params.admin
    ? keys().SUPABASE_SERVICE_ROLE_KEY
    : keys().NEXT_PUBLIC_SUPABASE_ANON_KEY;

  // Script context - use minimal setup without cookies/headers
  if (params.script) {
    return createSupabaseScriptClient(url, key);
  }

  // Next.js server component context - use full setup with cookies/headers
  const cookieStore = await cookies();
  const headersList = await headers();

  return createServerClient<Database>(url, key, {
    auth: {
      persistSession: false,
      autoRefreshToken: false,
      detectSessionInUrl: false,
    },
    cookies: getCookieStrategy(cookieStore, params.admin),
    global: {
      headers: {
        // Pass user agent from browser
        'user-agent': headersList.get('user-agent') as string,
      },
    },
  });
};

function getCookieStrategy(
  cookieStore: ReadonlyRequestCookies,
  admin: boolean
): CookieMethodsServer {
  if (admin) {
    return {
      getAll: () => [],
    } as CookieMethodsServer;
  }

  return {
    getAll: () => {
      return cookieStore.getAll();
    },
    setAll: (cookiesToSet) => {
      try {
        for (const { name, value, options } of cookiesToSet) {
          cookieStore.set(name, value, options);
        }
      } catch (error) {
        console.error('Error setting cookies', error);
      }
    },
  } as CookieMethodsServer;
}

function createSupabaseScriptClient(url: string, key: string) {
  return createServerClient<Database>(url, key, {
    auth: {
      persistSession: false,
      autoRefreshToken: false,
      detectSessionInUrl: false,
    },
    cookies: {
      getAll: () => [],
    } as CookieMethodsServer,
  });
}
