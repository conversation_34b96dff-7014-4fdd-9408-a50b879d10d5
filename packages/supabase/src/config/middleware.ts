import { createServerClient } from '@supabase/ssr';
import type { NextRequest, NextResponse } from 'next/server';
import { keys } from '../../keys';
import type { Database } from '../types/db';

export function getSupabaseMiddlewareClient(
  request: NextRequest,
  response: NextResponse
) {
  const url = keys().NEXT_PUBLIC_SUPABASE_URL;
  const key = keys().NEXT_PUBLIC_SUPABASE_ANON_KEY;

  return createServerClient<Database>(url, key, {
    cookies: {
      getAll() {
        return request.cookies.getAll();
      },
      setAll(cookiesToSet) {
        for (const { name, value } of cookiesToSet) {
          request.cookies.set(name, value);
        }
        for (const { name, value, options } of cookiesToSet) {
          response.cookies.set(name, value, options);
        }
      },
    },
  });
}
