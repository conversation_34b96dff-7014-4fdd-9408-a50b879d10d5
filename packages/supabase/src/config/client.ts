import { createBrowserClient } from '@supabase/ssr';
import { keys } from '../../keys';
import type { Database } from '../types/db';

let client: ReturnType<typeof createBrowserClient<Database>>;

export function getSupabaseBrowserClient() {
  if (!client) {
    const url = keys().NEXT_PUBLIC_SUPABASE_URL;
    const key = keys().NEXT_PUBLIC_SUPABASE_ANON_KEY;

    client = createBrowserClient<Database>(url, key, {
      auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: true,
      },
    });
  }
  return client;
}
