export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  graphql_public: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      graphql: {
        Args: {
          operationName?: string
          query?: string
          variables?: Json
          extensions?: Json
        }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  public: {
    Tables: {
      addresses: {
        Row: {
          address: string
          address2: string | null
          city: string
          created_at: string
          district_id: string | null
          id: string
          is_primary: boolean
          parent_id: string | null
          school_id: string | null
          state: string
          student_id: string | null
          type: Database["public"]["Enums"]["address_type"]
          updated_at: string
          zipcode: string
        }
        Insert: {
          address: string
          address2?: string | null
          city: string
          created_at?: string
          district_id?: string | null
          id?: string
          is_primary?: boolean
          parent_id?: string | null
          school_id?: string | null
          state: string
          student_id?: string | null
          type?: Database["public"]["Enums"]["address_type"]
          updated_at?: string
          zipcode: string
        }
        Update: {
          address?: string
          address2?: string | null
          city?: string
          created_at?: string
          district_id?: string | null
          id?: string
          is_primary?: boolean
          parent_id?: string | null
          school_id?: string | null
          state?: string
          student_id?: string | null
          type?: Database["public"]["Enums"]["address_type"]
          updated_at?: string
          zipcode?: string
        }
        Relationships: []
      }
      assessment_sessions: {
        Row: {
          accommodations_provided: Json | null
          background_information: string | null
          behavioral_observations: string | null
          case_id: string | null
          created_at: string
          environmental_factors: string | null
          id: string
          location: string | null
          psychologist_id: string
          referral_reason: string | null
          session_date: string
          session_duration: number | null
          session_status: Database["public"]["Enums"]["session_status"] | null
          session_type: Database["public"]["Enums"]["session_type"]
          student_id: string
          testing_conditions: string | null
          updated_at: string
          validity_concerns: string | null
        }
        Insert: {
          accommodations_provided?: Json | null
          background_information?: string | null
          behavioral_observations?: string | null
          case_id?: string | null
          created_at?: string
          environmental_factors?: string | null
          id?: string
          location?: string | null
          psychologist_id: string
          referral_reason?: string | null
          session_date: string
          session_duration?: number | null
          session_status?: Database["public"]["Enums"]["session_status"] | null
          session_type: Database["public"]["Enums"]["session_type"]
          student_id: string
          testing_conditions?: string | null
          updated_at?: string
          validity_concerns?: string | null
        }
        Update: {
          accommodations_provided?: Json | null
          background_information?: string | null
          behavioral_observations?: string | null
          case_id?: string | null
          created_at?: string
          environmental_factors?: string | null
          id?: string
          location?: string | null
          psychologist_id?: string
          referral_reason?: string | null
          session_date?: string
          session_duration?: number | null
          session_status?: Database["public"]["Enums"]["session_status"] | null
          session_type?: Database["public"]["Enums"]["session_type"]
          student_id?: string
          testing_conditions?: string | null
          updated_at?: string
          validity_concerns?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "assessment_sessions_case_id_cases_id_fk"
            columns: ["case_id"]
            isOneToOne: false
            referencedRelation: "cases"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "assessment_sessions_psychologist_id_users_id_fk"
            columns: ["psychologist_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "assessment_sessions_student_id_students_id_fk"
            columns: ["student_id"]
            isOneToOne: false
            referencedRelation: "students"
            referencedColumns: ["id"]
          },
        ]
      }
      availabilities: {
        Row: {
          created_at: string
          day: Database["public"]["Enums"]["day_of_week"]
          end_time: string
          id: string
          start_time: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          day: Database["public"]["Enums"]["day_of_week"]
          end_time: string
          id?: string
          start_time: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          day?: Database["public"]["Enums"]["day_of_week"]
          end_time?: string
          id?: string
          start_time?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "availabilities_user_id_users_id_fk"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      case_assignments: {
        Row: {
          case_id: string
          created_at: string
          deleted_at: string | null
          deleted_by: string | null
          id: string
          is_deleted: boolean
          updated_at: string
          user_id: string
        }
        Insert: {
          case_id: string
          created_at?: string
          deleted_at?: string | null
          deleted_by?: string | null
          id?: string
          is_deleted?: boolean
          updated_at?: string
          user_id: string
        }
        Update: {
          case_id?: string
          created_at?: string
          deleted_at?: string | null
          deleted_by?: string | null
          id?: string
          is_deleted?: boolean
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "case_assignments_case_id_cases_id_fk"
            columns: ["case_id"]
            isOneToOne: false
            referencedRelation: "cases"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "case_assignments_deleted_by_users_id_fk"
            columns: ["deleted_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "case_assignments_user_id_users_id_fk"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      case_details: {
        Row: {
          case_id: string
          created_at: string
          deleted_at: string | null
          deleted_by: string | null
          id: string
          is_deleted: boolean
          key: string
          updated_at: string
          value: string
        }
        Insert: {
          case_id: string
          created_at?: string
          deleted_at?: string | null
          deleted_by?: string | null
          id?: string
          is_deleted?: boolean
          key: string
          updated_at?: string
          value: string
        }
        Update: {
          case_id?: string
          created_at?: string
          deleted_at?: string | null
          deleted_by?: string | null
          id?: string
          is_deleted?: boolean
          key?: string
          updated_at?: string
          value?: string
        }
        Relationships: [
          {
            foreignKeyName: "case_details_case_id_cases_id_fk"
            columns: ["case_id"]
            isOneToOne: false
            referencedRelation: "cases"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "case_details_deleted_by_users_id_fk"
            columns: ["deleted_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      cases: {
        Row: {
          case_type: Database["public"]["Enums"]["case_type"]
          created_at: string
          deleted_at: string | null
          deleted_by: string | null
          evaluation_due_date: string | null
          id: string
          iep_end_date: string
          iep_start_date: string
          iep_status: Database["public"]["Enums"]["iep_status"]
          is_active: boolean
          is_deleted: boolean
          meeting_date: string | null
          priority: Database["public"]["Enums"]["case_priority"]
          referral_date: string | null
          status: Database["public"]["Enums"]["case_status"]
          student_id: string
          updated_at: string
        }
        Insert: {
          case_type: Database["public"]["Enums"]["case_type"]
          created_at?: string
          deleted_at?: string | null
          deleted_by?: string | null
          evaluation_due_date?: string | null
          id?: string
          iep_end_date: string
          iep_start_date: string
          iep_status: Database["public"]["Enums"]["iep_status"]
          is_active: boolean
          is_deleted?: boolean
          meeting_date?: string | null
          priority?: Database["public"]["Enums"]["case_priority"]
          referral_date?: string | null
          status: Database["public"]["Enums"]["case_status"]
          student_id: string
          updated_at?: string
        }
        Update: {
          case_type?: Database["public"]["Enums"]["case_type"]
          created_at?: string
          deleted_at?: string | null
          deleted_by?: string | null
          evaluation_due_date?: string | null
          id?: string
          iep_end_date?: string
          iep_start_date?: string
          iep_status?: Database["public"]["Enums"]["iep_status"]
          is_active?: boolean
          is_deleted?: boolean
          meeting_date?: string | null
          priority?: Database["public"]["Enums"]["case_priority"]
          referral_date?: string | null
          status?: Database["public"]["Enums"]["case_status"]
          student_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "cases_deleted_by_users_id_fk"
            columns: ["deleted_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "cases_student_id_students_id_fk"
            columns: ["student_id"]
            isOneToOne: false
            referencedRelation: "students"
            referencedColumns: ["id"]
          },
        ]
      }
      district_availabilities: {
        Row: {
          created_at: string
          created_by: string
          day: Database["public"]["Enums"]["day_of_week"]
          district_id: string
          end_time: string
          id: string
          is_active: boolean
          notes: string | null
          start_time: string
          time_zone: string
          type: Database["public"]["Enums"]["availability_type"]
          updated_at: string
        }
        Insert: {
          created_at?: string
          created_by: string
          day: Database["public"]["Enums"]["day_of_week"]
          district_id: string
          end_time: string
          id?: string
          is_active?: boolean
          notes?: string | null
          start_time: string
          time_zone: string
          type?: Database["public"]["Enums"]["availability_type"]
          updated_at?: string
        }
        Update: {
          created_at?: string
          created_by?: string
          day?: Database["public"]["Enums"]["day_of_week"]
          district_id?: string
          end_time?: string
          id?: string
          is_active?: boolean
          notes?: string | null
          start_time?: string
          time_zone?: string
          type?: Database["public"]["Enums"]["availability_type"]
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "district_availabilities_created_by_users_id_fk"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "district_availabilities_district_id_districts_id_fk"
            columns: ["district_id"]
            isOneToOne: false
            referencedRelation: "districts"
            referencedColumns: ["id"]
          },
        ]
      }
      district_blocked_dates: {
        Row: {
          affects_types: string[] | null
          block_type: Database["public"]["Enums"]["blocked_date_type"]
          created_at: string
          created_by: string
          description: string | null
          district_id: string
          end_date: string
          id: string
          is_active: boolean
          is_recurring: boolean
          recurrence_pattern: string | null
          start_date: string
          title: string
          updated_at: string
        }
        Insert: {
          affects_types?: string[] | null
          block_type?: Database["public"]["Enums"]["blocked_date_type"]
          created_at?: string
          created_by: string
          description?: string | null
          district_id: string
          end_date: string
          id?: string
          is_active?: boolean
          is_recurring?: boolean
          recurrence_pattern?: string | null
          start_date: string
          title: string
          updated_at?: string
        }
        Update: {
          affects_types?: string[] | null
          block_type?: Database["public"]["Enums"]["blocked_date_type"]
          created_at?: string
          created_by?: string
          description?: string | null
          district_id?: string
          end_date?: string
          id?: string
          is_active?: boolean
          is_recurring?: boolean
          recurrence_pattern?: string | null
          start_date?: string
          title?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "district_blocked_dates_created_by_users_id_fk"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "district_blocked_dates_district_id_districts_id_fk"
            columns: ["district_id"]
            isOneToOne: false
            referencedRelation: "districts"
            referencedColumns: ["id"]
          },
        ]
      }
      district_preferences: {
        Row: {
          category: Database["public"]["Enums"]["preference_category"]
          created_at: string
          district_id: string
          id: string
          key: string
          last_modified_by: string | null
          type: Database["public"]["Enums"]["preference_type"]
          updated_at: string
          value: string
        }
        Insert: {
          category: Database["public"]["Enums"]["preference_category"]
          created_at?: string
          district_id: string
          id?: string
          key: string
          last_modified_by?: string | null
          type: Database["public"]["Enums"]["preference_type"]
          updated_at?: string
          value: string
        }
        Update: {
          category?: Database["public"]["Enums"]["preference_category"]
          created_at?: string
          district_id?: string
          id?: string
          key?: string
          last_modified_by?: string | null
          type?: Database["public"]["Enums"]["preference_type"]
          updated_at?: string
          value?: string
        }
        Relationships: [
          {
            foreignKeyName: "district_preferences_district_id_districts_id_fk"
            columns: ["district_id"]
            isOneToOne: false
            referencedRelation: "districts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "district_preferences_last_modified_by_users_id_fk"
            columns: ["last_modified_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      districts: {
        Row: {
          address_id: string
          county: string
          id: string
          invoice_email: string
          name: string
          nces_id: string
          num_schools: number | null
          num_students: number | null
          slug: string
          state_id: string
          type: Database["public"]["Enums"]["district_type"]
          website: string
        }
        Insert: {
          address_id: string
          county: string
          id?: string
          invoice_email: string
          name: string
          nces_id: string
          num_schools?: number | null
          num_students?: number | null
          slug: string
          state_id: string
          type?: Database["public"]["Enums"]["district_type"]
          website: string
        }
        Update: {
          address_id?: string
          county?: string
          id?: string
          invoice_email?: string
          name?: string
          nces_id?: string
          num_schools?: number | null
          num_students?: number | null
          slug?: string
          state_id?: string
          type?: Database["public"]["Enums"]["district_type"]
          website?: string
        }
        Relationships: [
          {
            foreignKeyName: "districts_address_id_addresses_id_fk"
            columns: ["address_id"]
            isOneToOne: false
            referencedRelation: "addresses"
            referencedColumns: ["id"]
          },
        ]
      }
      documents: {
        Row: {
          assessment_session_id: string | null
          category: Database["public"]["Enums"]["document_category"]
          created_at: string
          id: string
          name: string
          student_id: string
          test_administration_id: string | null
          updated_at: string
          uploaded_user_id: string
          url: string
        }
        Insert: {
          assessment_session_id?: string | null
          category: Database["public"]["Enums"]["document_category"]
          created_at?: string
          id?: string
          name: string
          student_id: string
          test_administration_id?: string | null
          updated_at?: string
          uploaded_user_id: string
          url: string
        }
        Update: {
          assessment_session_id?: string | null
          category?: Database["public"]["Enums"]["document_category"]
          created_at?: string
          id?: string
          name?: string
          student_id?: string
          test_administration_id?: string | null
          updated_at?: string
          uploaded_user_id?: string
          url?: string
        }
        Relationships: [
          {
            foreignKeyName: "documents_student_id_students_id_fk"
            columns: ["student_id"]
            isOneToOne: false
            referencedRelation: "students"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "documents_uploaded_user_id_users_id_fk"
            columns: ["uploaded_user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      feedback: {
        Row: {
          created_at: string
          description: string | null
          id: string
          issue_type: Database["public"]["Enums"]["issue_type"]
          rating: number | null
          status: Database["public"]["Enums"]["status"]
          title: string | null
          type: Database["public"]["Enums"]["feedback_type"]
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          id?: string
          issue_type?: Database["public"]["Enums"]["issue_type"]
          rating?: number | null
          status?: Database["public"]["Enums"]["status"]
          title?: string | null
          type: Database["public"]["Enums"]["feedback_type"]
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string
          description?: string | null
          id?: string
          issue_type?: Database["public"]["Enums"]["issue_type"]
          rating?: number | null
          status?: Database["public"]["Enums"]["status"]
          title?: string | null
          type?: Database["public"]["Enums"]["feedback_type"]
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "feedback_user_id_users_id_fk"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      feedback_files: {
        Row: {
          created_at: string
          feedback_id: string
          file_url: string
          id: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string
          feedback_id: string
          file_url: string
          id?: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string
          feedback_id?: string
          file_url?: string
          id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "feedback_files_feedback_id_feedback_id_fk"
            columns: ["feedback_id"]
            isOneToOne: false
            referencedRelation: "feedback"
            referencedColumns: ["id"]
          },
        ]
      }
      index_scores: {
        Row: {
          administration_id: string
          calculation_method: string | null
          component_subtests: Json | null
          composite_score: number | null
          confidence_interval_lower: number | null
          confidence_interval_upper: number | null
          confidence_level: number | null
          created_at: string
          descriptive_category: string | null
          id: string
          index_id: string
          interpretation_notes: string | null
          is_valid: boolean | null
          percentile_rank: number | null
          qualitative_descriptor: string | null
          strengths_identified: string | null
          updated_at: string
          validity_notes: string | null
          weaknesses_identified: string | null
        }
        Insert: {
          administration_id: string
          calculation_method?: string | null
          component_subtests?: Json | null
          composite_score?: number | null
          confidence_interval_lower?: number | null
          confidence_interval_upper?: number | null
          confidence_level?: number | null
          created_at?: string
          descriptive_category?: string | null
          id?: string
          index_id: string
          interpretation_notes?: string | null
          is_valid?: boolean | null
          percentile_rank?: number | null
          qualitative_descriptor?: string | null
          strengths_identified?: string | null
          updated_at?: string
          validity_notes?: string | null
          weaknesses_identified?: string | null
        }
        Update: {
          administration_id?: string
          calculation_method?: string | null
          component_subtests?: Json | null
          composite_score?: number | null
          confidence_interval_lower?: number | null
          confidence_interval_upper?: number | null
          confidence_level?: number | null
          created_at?: string
          descriptive_category?: string | null
          id?: string
          index_id?: string
          interpretation_notes?: string | null
          is_valid?: boolean | null
          percentile_rank?: number | null
          qualitative_descriptor?: string | null
          strengths_identified?: string | null
          updated_at?: string
          validity_notes?: string | null
          weaknesses_identified?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "index_scores_administration_id_test_administrations_id_fk"
            columns: ["administration_id"]
            isOneToOne: false
            referencedRelation: "test_administrations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "index_scores_index_id_test_indices_id_fk"
            columns: ["index_id"]
            isOneToOne: false
            referencedRelation: "test_indices"
            referencedColumns: ["id"]
          },
        ]
      }
      index_subtest_mappings: {
        Row: {
          created_at: string
          id: string
          index_id: string
          is_required: boolean | null
          subtest_id: string
          weight: number | null
        }
        Insert: {
          created_at?: string
          id?: string
          index_id: string
          is_required?: boolean | null
          subtest_id: string
          weight?: number | null
        }
        Update: {
          created_at?: string
          id?: string
          index_id?: string
          is_required?: boolean | null
          subtest_id?: string
          weight?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "index_subtest_mappings_index_id_test_indices_id_fk"
            columns: ["index_id"]
            isOneToOne: false
            referencedRelation: "test_indices"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "index_subtest_mappings_subtest_id_subtests_id_fk"
            columns: ["subtest_id"]
            isOneToOne: false
            referencedRelation: "subtests"
            referencedColumns: ["id"]
          },
        ]
      }
      invitation_schools: {
        Row: {
          created_at: string
          id: string
          invitation_id: string
          school_id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          invitation_id: string
          school_id: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          invitation_id?: string
          school_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "invitation_schools_invitation_id_invitations_id_fk"
            columns: ["invitation_id"]
            isOneToOne: false
            referencedRelation: "invitations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invitation_schools_school_id_schools_id_fk"
            columns: ["school_id"]
            isOneToOne: false
            referencedRelation: "schools"
            referencedColumns: ["id"]
          },
        ]
      }
      invitations: {
        Row: {
          accepted_at: string | null
          created_at: string
          district_id: string
          email: string
          expires_at: string
          first_name: string
          id: string
          invited_by_id: string | null
          inviter_id: string
          last_name: string
          metadata: string | null
          rejected_at: string | null
          role_id: string
          status: Database["public"]["Enums"]["invitation_status"]
          token: string
          updated_at: string
        }
        Insert: {
          accepted_at?: string | null
          created_at?: string
          district_id: string
          email: string
          expires_at?: string
          first_name: string
          id?: string
          invited_by_id?: string | null
          inviter_id: string
          last_name: string
          metadata?: string | null
          rejected_at?: string | null
          role_id: string
          status?: Database["public"]["Enums"]["invitation_status"]
          token: string
          updated_at?: string
        }
        Update: {
          accepted_at?: string | null
          created_at?: string
          district_id?: string
          email?: string
          expires_at?: string
          first_name?: string
          id?: string
          invited_by_id?: string | null
          inviter_id?: string
          last_name?: string
          metadata?: string | null
          rejected_at?: string | null
          role_id?: string
          status?: Database["public"]["Enums"]["invitation_status"]
          token?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "invitations_district_id_districts_id_fk"
            columns: ["district_id"]
            isOneToOne: false
            referencedRelation: "districts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invitations_invited_by_id_users_id_fk"
            columns: ["invited_by_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invitations_inviter_id_users_id_fk"
            columns: ["inviter_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invitations_role_id_roles_id_fk"
            columns: ["role_id"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["id"]
          },
        ]
      }
      join_requests: {
        Row: {
          created_at: string
          district_name: string
          email: string
          first_name: string
          id: string
          last_name: string
          message: string | null
          phone: string
        }
        Insert: {
          created_at?: string
          district_name: string
          email: string
          first_name: string
          id?: string
          last_name: string
          message?: string | null
          phone: string
        }
        Update: {
          created_at?: string
          district_name?: string
          email?: string
          first_name?: string
          id?: string
          last_name?: string
          message?: string | null
          phone?: string
        }
        Relationships: []
      }
      languages: {
        Row: {
          code: string
          emoji: string
          id: string
          name: string
        }
        Insert: {
          code: string
          emoji: string
          id?: string
          name: string
        }
        Update: {
          code?: string
          emoji?: string
          id?: string
          name?: string
        }
        Relationships: []
      }
      notifications: {
        Row: {
          archived_at: string | null
          category: Database["public"]["Enums"]["notification_category_type"]
          content: string
          created_at: string
          expires_at: string | null
          id: string
          is_archived: boolean
          is_read: boolean
          metadata: Json | null
          read_at: string | null
          type: string
          user_id: string
        }
        Insert: {
          archived_at?: string | null
          category?: Database["public"]["Enums"]["notification_category_type"]
          content: string
          created_at?: string
          expires_at?: string | null
          id?: string
          is_archived?: boolean
          is_read?: boolean
          metadata?: Json | null
          read_at?: string | null
          type: string
          user_id: string
        }
        Update: {
          archived_at?: string | null
          category?: Database["public"]["Enums"]["notification_category_type"]
          content?: string
          created_at?: string
          expires_at?: string | null
          id?: string
          is_archived?: boolean
          is_read?: boolean
          metadata?: Json | null
          read_at?: string | null
          type?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "notifications_user_id_users_id_fk"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      parents: {
        Row: {
          created_at: string
          deleted_at: string | null
          deleted_by: string | null
          first_name: string
          full_name: string
          id: string
          is_deleted: boolean
          last_name: string
          middle_name: string | null
          primary_email: string | null
          primary_phone: string | null
          relationship_type: Database["public"]["Enums"]["parent_relationship"]
          secondary_email: string | null
          secondary_phone: string | null
          updated_at: string
        }
        Insert: {
          created_at?: string
          deleted_at?: string | null
          deleted_by?: string | null
          first_name: string
          full_name?: string
          id?: string
          is_deleted?: boolean
          last_name: string
          middle_name?: string | null
          primary_email?: string | null
          primary_phone?: string | null
          relationship_type?: Database["public"]["Enums"]["parent_relationship"]
          secondary_email?: string | null
          secondary_phone?: string | null
          updated_at?: string
        }
        Update: {
          created_at?: string
          deleted_at?: string | null
          deleted_by?: string | null
          first_name?: string
          full_name?: string
          id?: string
          is_deleted?: boolean
          last_name?: string
          middle_name?: string | null
          primary_email?: string | null
          primary_phone?: string | null
          relationship_type?: Database["public"]["Enums"]["parent_relationship"]
          secondary_email?: string | null
          secondary_phone?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "parents_deleted_by_users_id_fk"
            columns: ["deleted_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      permissions: {
        Row: {
          created_at: string
          id: string
          name: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          name: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          name?: string
          updated_at?: string
        }
        Relationships: []
      }
      plans: {
        Row: {
          case_id: string
          created_at: string
          deleted_at: string | null
          deleted_by: string | null
          expiration_date: string
          id: string
          is_deleted: boolean
          status: Database["public"]["Enums"]["plan_status"]
          student_id: string
          type: Database["public"]["Enums"]["plan_type"]
          updated_at: string
        }
        Insert: {
          case_id: string
          created_at?: string
          deleted_at?: string | null
          deleted_by?: string | null
          expiration_date: string
          id?: string
          is_deleted?: boolean
          status: Database["public"]["Enums"]["plan_status"]
          student_id: string
          type: Database["public"]["Enums"]["plan_type"]
          updated_at?: string
        }
        Update: {
          case_id?: string
          created_at?: string
          deleted_at?: string | null
          deleted_by?: string | null
          expiration_date?: string
          id?: string
          is_deleted?: boolean
          status?: Database["public"]["Enums"]["plan_status"]
          student_id?: string
          type?: Database["public"]["Enums"]["plan_type"]
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "plans_case_id_cases_id_fk"
            columns: ["case_id"]
            isOneToOne: false
            referencedRelation: "cases"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "plans_deleted_by_users_id_fk"
            columns: ["deleted_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "plans_student_id_students_id_fk"
            columns: ["student_id"]
            isOneToOne: false
            referencedRelation: "students"
            referencedColumns: ["id"]
          },
        ]
      }
      role_permissions: {
        Row: {
          created_at: string
          id: string
          permission_id: string
          role_id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          permission_id: string
          role_id: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          permission_id?: string
          role_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "role_permissions_permission_id_permissions_id_fk"
            columns: ["permission_id"]
            isOneToOne: false
            referencedRelation: "permissions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "role_permissions_role_id_roles_id_fk"
            columns: ["role_id"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["id"]
          },
        ]
      }
      roles: {
        Row: {
          created_at: string
          id: string
          name: Database["public"]["Enums"]["role"]
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          name: Database["public"]["Enums"]["role"]
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          name?: Database["public"]["Enums"]["role"]
          updated_at?: string
        }
        Relationships: []
      }
      schools: {
        Row: {
          address_id: string
          district_id: string
          id: string
          name: string
          nces_id: string
          slug: string
          type: Database["public"]["Enums"]["school_type"]
          website: string | null
        }
        Insert: {
          address_id: string
          district_id: string
          id?: string
          name: string
          nces_id: string
          slug: string
          type: Database["public"]["Enums"]["school_type"]
          website?: string | null
        }
        Update: {
          address_id?: string
          district_id?: string
          id?: string
          name?: string
          nces_id?: string
          slug?: string
          type?: Database["public"]["Enums"]["school_type"]
          website?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "schools_address_id_addresses_id_fk"
            columns: ["address_id"]
            isOneToOne: false
            referencedRelation: "addresses"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "schools_district_id_districts_id_fk"
            columns: ["district_id"]
            isOneToOne: false
            referencedRelation: "districts"
            referencedColumns: ["id"]
          },
        ]
      }
      student_enrollments: {
        Row: {
          created_at: string
          district_id: string
          end_date: string | null
          id: string
          school_id: string
          start_date: string | null
          student_id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          district_id: string
          end_date?: string | null
          id?: string
          school_id: string
          start_date?: string | null
          student_id: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          district_id?: string
          end_date?: string | null
          id?: string
          school_id?: string
          start_date?: string | null
          student_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "student_enrollments_district_id_districts_id_fk"
            columns: ["district_id"]
            isOneToOne: false
            referencedRelation: "districts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "student_enrollments_school_id_schools_id_fk"
            columns: ["school_id"]
            isOneToOne: false
            referencedRelation: "schools"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "student_enrollments_student_id_students_id_fk"
            columns: ["student_id"]
            isOneToOne: false
            referencedRelation: "students"
            referencedColumns: ["id"]
          },
        ]
      }
      student_languages: {
        Row: {
          created_at: string
          id: string
          is_primary: boolean
          language_id: string
          student_id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          is_primary?: boolean
          language_id: string
          student_id: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          is_primary?: boolean
          language_id?: string
          student_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "student_languages_language_id_languages_id_fk"
            columns: ["language_id"]
            isOneToOne: false
            referencedRelation: "languages"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "student_languages_student_id_students_id_fk"
            columns: ["student_id"]
            isOneToOne: false
            referencedRelation: "students"
            referencedColumns: ["id"]
          },
        ]
      }
      student_parents: {
        Row: {
          created_at: string
          has_pickup_authorization: boolean
          id: string
          is_primary_contact: boolean
          parent_id: string
          student_id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          has_pickup_authorization?: boolean
          id?: string
          is_primary_contact?: boolean
          parent_id: string
          student_id: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          has_pickup_authorization?: boolean
          id?: string
          is_primary_contact?: boolean
          parent_id?: string
          student_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "student_parents_parent_id_parents_id_fk"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "parents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "student_parents_student_id_students_id_fk"
            columns: ["student_id"]
            isOneToOne: false
            referencedRelation: "students"
            referencedColumns: ["id"]
          },
        ]
      }
      students: {
        Row: {
          created_at: string
          date_of_birth: string
          date_of_consent: string
          deleted_at: string | null
          deleted_by: string | null
          emergency_contact_name: string | null
          emergency_contact_phone: string | null
          enrollment_status: Database["public"]["Enums"]["enrollment_status"]
          first_name: string
          full_name: string | null
          gender: Database["public"]["Enums"]["gender"]
          grade: string
          id: string
          is_deleted: boolean
          last_name: string
          middle_name: string | null
          preferred_name: string
          primary_school_id: string | null
          special_needs_indicator: boolean
          student_id_number: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          date_of_birth: string
          date_of_consent: string
          deleted_at?: string | null
          deleted_by?: string | null
          emergency_contact_name?: string | null
          emergency_contact_phone?: string | null
          enrollment_status?: Database["public"]["Enums"]["enrollment_status"]
          first_name: string
          full_name?: string | null
          gender: Database["public"]["Enums"]["gender"]
          grade: string
          id?: string
          is_deleted?: boolean
          last_name: string
          middle_name?: string | null
          preferred_name: string
          primary_school_id?: string | null
          special_needs_indicator?: boolean
          student_id_number: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          date_of_birth?: string
          date_of_consent?: string
          deleted_at?: string | null
          deleted_by?: string | null
          emergency_contact_name?: string | null
          emergency_contact_phone?: string | null
          enrollment_status?: Database["public"]["Enums"]["enrollment_status"]
          first_name?: string
          full_name?: string | null
          gender?: Database["public"]["Enums"]["gender"]
          grade?: string
          id?: string
          is_deleted?: boolean
          last_name?: string
          middle_name?: string | null
          preferred_name?: string
          primary_school_id?: string | null
          special_needs_indicator?: boolean
          student_id_number?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "students_deleted_by_users_id_fk"
            columns: ["deleted_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "students_primary_school_id_schools_id_fk"
            columns: ["primary_school_id"]
            isOneToOne: false
            referencedRelation: "schools"
            referencedColumns: ["id"]
          },
        ]
      }
      subtest_scores: {
        Row: {
          administration_id: string
          age_equivalent: string | null
          completion_time_minutes: number | null
          confidence_interval_lower: number | null
          confidence_interval_upper: number | null
          confidence_level: number | null
          created_at: string
          descriptive_category: string | null
          discontinued_item: number | null
          grade_equivalent: string | null
          id: string
          is_valid: boolean | null
          percentile_rank: number | null
          qualitative_descriptor: string | null
          raw_score: number | null
          scaled_score: number | null
          scoring_notes: string | null
          strengths_identified: string | null
          subtest_id: string
          updated_at: string
          validity_notes: string | null
          weaknesses_identified: string | null
        }
        Insert: {
          administration_id: string
          age_equivalent?: string | null
          completion_time_minutes?: number | null
          confidence_interval_lower?: number | null
          confidence_interval_upper?: number | null
          confidence_level?: number | null
          created_at?: string
          descriptive_category?: string | null
          discontinued_item?: number | null
          grade_equivalent?: string | null
          id?: string
          is_valid?: boolean | null
          percentile_rank?: number | null
          qualitative_descriptor?: string | null
          raw_score?: number | null
          scaled_score?: number | null
          scoring_notes?: string | null
          strengths_identified?: string | null
          subtest_id: string
          updated_at?: string
          validity_notes?: string | null
          weaknesses_identified?: string | null
        }
        Update: {
          administration_id?: string
          age_equivalent?: string | null
          completion_time_minutes?: number | null
          confidence_interval_lower?: number | null
          confidence_interval_upper?: number | null
          confidence_level?: number | null
          created_at?: string
          descriptive_category?: string | null
          discontinued_item?: number | null
          grade_equivalent?: string | null
          id?: string
          is_valid?: boolean | null
          percentile_rank?: number | null
          qualitative_descriptor?: string | null
          raw_score?: number | null
          scaled_score?: number | null
          scoring_notes?: string | null
          strengths_identified?: string | null
          subtest_id?: string
          updated_at?: string
          validity_notes?: string | null
          weaknesses_identified?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "subtest_scores_administration_id_test_administrations_id_fk"
            columns: ["administration_id"]
            isOneToOne: false
            referencedRelation: "test_administrations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subtest_scores_subtest_id_subtests_id_fk"
            columns: ["subtest_id"]
            isOneToOne: false
            referencedRelation: "subtests"
            referencedColumns: ["id"]
          },
        ]
      }
      subtests: {
        Row: {
          administration_instructions: string | null
          battery_id: string
          code: string
          created_at: string
          description: string | null
          id: string
          is_active: boolean
          mean_score: number | null
          measured_abilities: Json | null
          name: string
          score_range_max: number | null
          score_range_min: number | null
          sort_order: number | null
          standard_deviation: number | null
          subtest_type: Database["public"]["Enums"]["subtest_type"]
          time_limit_minutes: number | null
          updated_at: string
        }
        Insert: {
          administration_instructions?: string | null
          battery_id: string
          code: string
          created_at?: string
          description?: string | null
          id?: string
          is_active?: boolean
          mean_score?: number | null
          measured_abilities?: Json | null
          name: string
          score_range_max?: number | null
          score_range_min?: number | null
          sort_order?: number | null
          standard_deviation?: number | null
          subtest_type: Database["public"]["Enums"]["subtest_type"]
          time_limit_minutes?: number | null
          updated_at?: string
        }
        Update: {
          administration_instructions?: string | null
          battery_id?: string
          code?: string
          created_at?: string
          description?: string | null
          id?: string
          is_active?: boolean
          mean_score?: number | null
          measured_abilities?: Json | null
          name?: string
          score_range_max?: number | null
          score_range_min?: number | null
          sort_order?: number | null
          standard_deviation?: number | null
          subtest_type?: Database["public"]["Enums"]["subtest_type"]
          time_limit_minutes?: number | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "subtests_battery_id_test_batteries_id_fk"
            columns: ["battery_id"]
            isOneToOne: false
            referencedRelation: "test_batteries"
            referencedColumns: ["id"]
          },
        ]
      }
      task_dependencies: {
        Row: {
          created_at: string
          id: string
          predecessor_task_id: string
          successor_task_id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          predecessor_task_id: string
          successor_task_id: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          predecessor_task_id?: string
          successor_task_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "task_dependencies_predecessor_task_id_tasks_id_fk"
            columns: ["predecessor_task_id"]
            isOneToOne: false
            referencedRelation: "tasks"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "task_dependencies_successor_task_id_tasks_id_fk"
            columns: ["successor_task_id"]
            isOneToOne: false
            referencedRelation: "tasks"
            referencedColumns: ["id"]
          },
        ]
      }
      task_history: {
        Row: {
          action: Database["public"]["Enums"]["task_history_action"]
          created_at: string
          id: string
          new_status: Database["public"]["Enums"]["task_status"] | null
          previous_status: Database["public"]["Enums"]["task_status"] | null
          task_id: string
          user_id: string
        }
        Insert: {
          action: Database["public"]["Enums"]["task_history_action"]
          created_at?: string
          id?: string
          new_status?: Database["public"]["Enums"]["task_status"] | null
          previous_status?: Database["public"]["Enums"]["task_status"] | null
          task_id: string
          user_id: string
        }
        Update: {
          action?: Database["public"]["Enums"]["task_history_action"]
          created_at?: string
          id?: string
          new_status?: Database["public"]["Enums"]["task_status"] | null
          previous_status?: Database["public"]["Enums"]["task_status"] | null
          task_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "task_history_task_id_tasks_id_fk"
            columns: ["task_id"]
            isOneToOne: false
            referencedRelation: "tasks"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "task_history_user_id_users_id_fk"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      tasks: {
        Row: {
          assigned_by_id: string
          assigned_to_id: string
          cancelled_at: string | null
          case_id: string | null
          completed_at: string | null
          created_at: string
          district_id: string | null
          due_date: string | null
          id: string
          metadata: Json | null
          notes: string
          priority: Database["public"]["Enums"]["task_priority"]
          reason: string | null
          rejected_at: string | null
          school_id: string | null
          status: Database["public"]["Enums"]["task_status"]
          student_id: string | null
          task_type: Database["public"]["Enums"]["task_type"]
          updated_at: string
        }
        Insert: {
          assigned_by_id: string
          assigned_to_id: string
          cancelled_at?: string | null
          case_id?: string | null
          completed_at?: string | null
          created_at?: string
          district_id?: string | null
          due_date?: string | null
          id?: string
          metadata?: Json | null
          notes?: string
          priority?: Database["public"]["Enums"]["task_priority"]
          reason?: string | null
          rejected_at?: string | null
          school_id?: string | null
          status?: Database["public"]["Enums"]["task_status"]
          student_id?: string | null
          task_type: Database["public"]["Enums"]["task_type"]
          updated_at?: string
        }
        Update: {
          assigned_by_id?: string
          assigned_to_id?: string
          cancelled_at?: string | null
          case_id?: string | null
          completed_at?: string | null
          created_at?: string
          district_id?: string | null
          due_date?: string | null
          id?: string
          metadata?: Json | null
          notes?: string
          priority?: Database["public"]["Enums"]["task_priority"]
          reason?: string | null
          rejected_at?: string | null
          school_id?: string | null
          status?: Database["public"]["Enums"]["task_status"]
          student_id?: string | null
          task_type?: Database["public"]["Enums"]["task_type"]
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "tasks_assigned_by_id_users_id_fk"
            columns: ["assigned_by_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tasks_assigned_to_id_users_id_fk"
            columns: ["assigned_to_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tasks_case_id_cases_id_fk"
            columns: ["case_id"]
            isOneToOne: false
            referencedRelation: "cases"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tasks_district_id_districts_id_fk"
            columns: ["district_id"]
            isOneToOne: false
            referencedRelation: "districts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tasks_school_id_schools_id_fk"
            columns: ["school_id"]
            isOneToOne: false
            referencedRelation: "schools"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tasks_student_id_students_id_fk"
            columns: ["student_id"]
            isOneToOne: false
            referencedRelation: "students"
            referencedColumns: ["id"]
          },
        ]
      }
      test_administrations: {
        Row: {
          accommodations_used: Json | null
          admin_status: Database["public"]["Enums"]["admin_status"] | null
          administration_notes: string | null
          administration_order: number
          battery_id: string
          created_at: string
          discontinuation_notes: string | null
          end_time: string | null
          id: string
          session_id: string
          start_time: string | null
          updated_at: string
          validity_indicators: Json | null
        }
        Insert: {
          accommodations_used?: Json | null
          admin_status?: Database["public"]["Enums"]["admin_status"] | null
          administration_notes?: string | null
          administration_order: number
          battery_id: string
          created_at?: string
          discontinuation_notes?: string | null
          end_time?: string | null
          id?: string
          session_id: string
          start_time?: string | null
          updated_at?: string
          validity_indicators?: Json | null
        }
        Update: {
          accommodations_used?: Json | null
          admin_status?: Database["public"]["Enums"]["admin_status"] | null
          administration_notes?: string | null
          administration_order?: number
          battery_id?: string
          created_at?: string
          discontinuation_notes?: string | null
          end_time?: string | null
          id?: string
          session_id?: string
          start_time?: string | null
          updated_at?: string
          validity_indicators?: Json | null
        }
        Relationships: [
          {
            foreignKeyName: "test_administrations_battery_id_test_batteries_id_fk"
            columns: ["battery_id"]
            isOneToOne: false
            referencedRelation: "test_batteries"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "test_administrations_session_id_assessment_sessions_id_fk"
            columns: ["session_id"]
            isOneToOne: false
            referencedRelation: "assessment_sessions"
            referencedColumns: ["id"]
          },
        ]
      }
      test_batteries: {
        Row: {
          administration_time: number | null
          age_range_max: number | null
          age_range_min: number | null
          category: Database["public"]["Enums"]["test_category"]
          code: string
          created_at: string
          description: string | null
          id: string
          is_active: boolean
          name: string
          norming_information: Json | null
          publisher: string | null
          updated_at: string
          version: string | null
        }
        Insert: {
          administration_time?: number | null
          age_range_max?: number | null
          age_range_min?: number | null
          category: Database["public"]["Enums"]["test_category"]
          code: string
          created_at?: string
          description?: string | null
          id?: string
          is_active?: boolean
          name: string
          norming_information?: Json | null
          publisher?: string | null
          updated_at?: string
          version?: string | null
        }
        Update: {
          administration_time?: number | null
          age_range_max?: number | null
          age_range_min?: number | null
          category?: Database["public"]["Enums"]["test_category"]
          code?: string
          created_at?: string
          description?: string | null
          id?: string
          is_active?: boolean
          name?: string
          norming_information?: Json | null
          publisher?: string | null
          updated_at?: string
          version?: string | null
        }
        Relationships: []
      }
      test_indices: {
        Row: {
          battery_id: string
          code: string
          created_at: string
          description: string | null
          id: string
          index_type: Database["public"]["Enums"]["index_type"]
          interpretive_guidelines: string | null
          is_active: boolean
          mean_score: number | null
          name: string
          score_range_max: number | null
          score_range_min: number | null
          sort_order: number | null
          standard_deviation: number | null
          updated_at: string
        }
        Insert: {
          battery_id: string
          code: string
          created_at?: string
          description?: string | null
          id?: string
          index_type: Database["public"]["Enums"]["index_type"]
          interpretive_guidelines?: string | null
          is_active?: boolean
          mean_score?: number | null
          name: string
          score_range_max?: number | null
          score_range_min?: number | null
          sort_order?: number | null
          standard_deviation?: number | null
          updated_at?: string
        }
        Update: {
          battery_id?: string
          code?: string
          created_at?: string
          description?: string | null
          id?: string
          index_type?: Database["public"]["Enums"]["index_type"]
          interpretive_guidelines?: string | null
          is_active?: boolean
          mean_score?: number | null
          name?: string
          score_range_max?: number | null
          score_range_min?: number | null
          sort_order?: number | null
          standard_deviation?: number | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "test_indices_battery_id_test_batteries_id_fk"
            columns: ["battery_id"]
            isOneToOne: false
            referencedRelation: "test_batteries"
            referencedColumns: ["id"]
          },
        ]
      }
      user_districts: {
        Row: {
          created_at: string
          district_id: string
          id: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          district_id: string
          id?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          district_id?: string
          id?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_districts_district_id_districts_id_fk"
            columns: ["district_id"]
            isOneToOne: false
            referencedRelation: "districts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_districts_user_id_users_id_fk"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      user_notification_preferences: {
        Row: {
          created_at: string
          id: string
          is_email_enabled: boolean
          is_in_app_notifications_enabled: boolean
          is_push_notifications_enabled: boolean
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          is_email_enabled?: boolean
          is_in_app_notifications_enabled?: boolean
          is_push_notifications_enabled?: boolean
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          is_email_enabled?: boolean
          is_in_app_notifications_enabled?: boolean
          is_push_notifications_enabled?: boolean
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_notification_preferences_user_id_users_id_fk"
            columns: ["user_id"]
            isOneToOne: true
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      user_roles: {
        Row: {
          created_at: string
          id: string
          role_id: string
          role_name: Database["public"]["Enums"]["role"]
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          role_id: string
          role_name: Database["public"]["Enums"]["role"]
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          role_id?: string
          role_name?: Database["public"]["Enums"]["role"]
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_roles_role_id_roles_id_fk"
            columns: ["role_id"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_roles_user_id_users_id_fk"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      user_schools: {
        Row: {
          created_at: string
          id: string
          school_id: string | null
          updated_at: string
          user_id: string | null
        }
        Insert: {
          created_at?: string
          id?: string
          school_id?: string | null
          updated_at?: string
          user_id?: string | null
        }
        Update: {
          created_at?: string
          id?: string
          school_id?: string | null
          updated_at?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_schools_school_id_schools_id_fk"
            columns: ["school_id"]
            isOneToOne: false
            referencedRelation: "schools"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_schools_user_id_users_id_fk"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      users: {
        Row: {
          avatar: string | null
          created_at: string
          email: string
          first_name: string
          full_name: string
          id: string
          is_onboarded: boolean
          last_name: string
          middle_name: string | null
          updated_at: string
        }
        Insert: {
          avatar?: string | null
          created_at?: string
          email: string
          first_name: string
          full_name?: string
          id: string
          is_onboarded?: boolean
          last_name: string
          middle_name?: string | null
          updated_at?: string
        }
        Update: {
          avatar?: string | null
          created_at?: string
          email?: string
          first_name?: string
          full_name?: string
          id?: string
          is_onboarded?: boolean
          last_name?: string
          middle_name?: string | null
          updated_at?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      binary_quantize: {
        Args: { "": string } | { "": unknown }
        Returns: unknown
      }
      concat_names: {
        Args: { first_name: string; middle_name: string; last_name: string }
        Returns: string
      }
      halfvec_avg: {
        Args: { "": number[] }
        Returns: unknown
      }
      halfvec_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      halfvec_send: {
        Args: { "": unknown }
        Returns: string
      }
      halfvec_typmod_in: {
        Args: { "": unknown[] }
        Returns: number
      }
      hnsw_bit_support: {
        Args: { "": unknown }
        Returns: unknown
      }
      hnsw_halfvec_support: {
        Args: { "": unknown }
        Returns: unknown
      }
      hnsw_sparsevec_support: {
        Args: { "": unknown }
        Returns: unknown
      }
      hnswhandler: {
        Args: { "": unknown }
        Returns: unknown
      }
      ivfflat_bit_support: {
        Args: { "": unknown }
        Returns: unknown
      }
      ivfflat_halfvec_support: {
        Args: { "": unknown }
        Returns: unknown
      }
      ivfflathandler: {
        Args: { "": unknown }
        Returns: unknown
      }
      l2_norm: {
        Args: { "": unknown } | { "": unknown }
        Returns: number
      }
      l2_normalize: {
        Args: { "": string } | { "": unknown } | { "": unknown }
        Returns: string
      }
      sparsevec_out: {
        Args: { "": unknown }
        Returns: unknown
      }
      sparsevec_send: {
        Args: { "": unknown }
        Returns: string
      }
      sparsevec_typmod_in: {
        Args: { "": unknown[] }
        Returns: number
      }
      uuid_generate_v7: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      vector_avg: {
        Args: { "": number[] }
        Returns: string
      }
      vector_dims: {
        Args: { "": string } | { "": unknown }
        Returns: number
      }
      vector_norm: {
        Args: { "": string }
        Returns: number
      }
      vector_out: {
        Args: { "": string }
        Returns: unknown
      }
      vector_send: {
        Args: { "": string }
        Returns: string
      }
      vector_typmod_in: {
        Args: { "": unknown[] }
        Returns: number
      }
    }
    Enums: {
      address_type: "PHYSICAL" | "POSTAL" | "BILLING" | "OTHER"
      admin_status:
        | "PLANNED"
        | "IN_PROGRESS"
        | "COMPLETED"
        | "DISCONTINUED"
        | "INVALID"
        | "PARTIAL"
      attendee_status: "PENDING" | "ACCEPTED" | "DECLINED" | "TENTATIVE"
      availability_type: "EVALUATION" | "MEETING" | "CONSULTATION" | "ALL"
      blocked_date_type:
        | "HOLIDAY"
        | "STAFF_TRAINING"
        | "DISTRICT_CLOSURE"
        | "MAINTENANCE"
        | "SPECIAL_EVENT"
        | "VACATION"
        | "WEATHER"
        | "EMERGENCY"
        | "OTHER"
      case_priority: "LOW" | "MEDIUM" | "HIGH" | "URGENT"
      case_status:
        | "READY_FOR_EVALUATION"
        | "EVALUATION_IN_PROGRESS"
        | "REPORT_IN_PROGRESS"
        | "AWAITING_MEETING"
        | "MEETING_COMPLETE"
      case_type:
        | "INITIAL_EVALUATION"
        | "TRIENNIAL_EVALUATION"
        | "REEVALUATION"
        | "INDEPENDENT_EVALUATION"
        | "CHANGE_OF_PLACEMENT"
        | "DISCIPLINE_EVALUATION"
        | "TRANSITION_EVALUATION"
      day_of_week:
        | "SUNDAY"
        | "MONDAY"
        | "TUESDAY"
        | "WEDNESDAY"
        | "THURSDAY"
        | "FRIDAY"
        | "SATURDAY"
      district_type:
        | "ELEMENTARY_DISTRICT"
        | "SECONDARY_DISTRICT"
        | "UNIFIED_DISTRICT"
        | "SUPERVISORY_UNION_ADMIN"
        | "REGIONAL_SERVICE_AGENCY"
        | "STATE_OPERATED_AGENCY"
        | "FEDERAL_OPERATED_AGENCY"
        | "CHARTER_LEA"
        | "OTHER_EDUCATION_AGENCY"
        | "SPECIALIZED_PUBLIC_DISTRICT"
      document_category: "BACKGROUND" | "ASSESSMENT"
      enrollment_status:
        | "ENROLLED"
        | "WITHDRAWN"
        | "TRANSFERRED"
        | "GRADUATED"
        | "SUSPENDED"
        | "EXPELLED"
        | "INACTIVE"
      event_status:
        | "SCHEDULED"
        | "CANCELLED"
        | "COMPLETED"
        | "RESCHEDULED"
        | "NO_SHOW"
      event_type:
        | "EVALUATION"
        | "IEP_MEETING"
        | "OBSERVATION_MEETING"
        | "GENERAL_MEETING"
      feedback_type: "BUG" | "FEATURE_REQUEST" | "GENERAL"
      gender: "MALE" | "FEMALE" | "NON_BINARY" | "PREFER_NOT_TO_SAY" | "OTHER"
      iep_status: "ACTIVE" | "INACTIVE"
      index_type:
        | "PRIMARY"
        | "COMPOSITE"
        | "SUPPLEMENTAL"
        | "PROCESS_SPECIFIC"
        | "ANCILLARY"
      invitation_status: "PENDING" | "ACCEPTED" | "REJECTED" | "EXPIRED"
      issue_type:
        | "VISUAL"
        | "FUNCTIONALITY"
        | "PERFORMANCE"
        | "SECURITY"
        | "DATA"
        | "USABILITY"
        | "ACCESSIBILITY"
        | "OTHER"
      notification_category_type: "GENERAL" | "WORKFLOW" | "ADMINISTRATIVE"
      parent_relationship:
        | "MOTHER"
        | "FATHER"
        | "STEP_MOTHER"
        | "STEP_FATHER"
        | "GUARDIAN"
        | "ADOPTIVE_MOTHER"
        | "ADOPTIVE_FATHER"
        | "GRANDMOTHER"
        | "GRANDFATHER"
        | "AUNT"
        | "UNCLE"
        | "FOSTER_MOTHER"
        | "FOSTER_FATHER"
        | "OTHER_RELATIVE"
        | "NON_RELATIVE"
        | "UNKNOWN"
      plan_status: "PENDING" | "ACTIVE" | "CANCELLED"
      plan_type: "IEP" | "504" | "BIP" | "SST"
      preference_category:
        | "NOTIFICATIONS"
        | "USER_MANAGEMENT"
        | "WORKFLOW"
        | "REPORTING"
        | "INTEGRATIONS"
        | "EVALUATION"
        | "SECURITY"
      preference_type: "BOOLEAN" | "STRING" | "NUMBER" | "JSON"
      role:
        | "SUPER_USER"
        | "SPECIAL_ED_DIRECTOR"
        | "SCHOOL_COORDINATOR"
        | "SCHOOL_ADMIN"
        | "PROCTOR"
        | "CASE_MANAGER"
        | "CLINICAL_DIRECTOR"
        | "PSYCHOLOGIST"
        | "ASSISTANT"
      school_type:
        | "REGULAR_PUBLIC_PRIMARY"
        | "REGULAR_PUBLIC_MIDDLE"
        | "REGULAR_PUBLIC_HIGH"
        | "REGULAR_PUBLIC_UNIFIED"
        | "SPECIAL_ED_PUBLIC"
        | "VOCATIONAL_PUBLIC"
        | "ALTERNATIVE_PUBLIC"
        | "REPORTABLE_PROGRAM"
        | "PUBLIC_CHARTER"
        | "MAGNET_PUBLIC"
        | "VIRTUAL_PUBLIC"
        | "DODEA_SCHOOL"
        | "BIE_SCHOOL"
        | "PRIVATE_CATHOLIC"
        | "PRIVATE_OTHER_RELIGIOUS"
        | "PRIVATE_NONSECTARIAN"
      session_status:
        | "SCHEDULED"
        | "IN_PROGRESS"
        | "COMPLETED"
        | "CANCELLED"
        | "RESCHEDULED"
        | "INCOMPLETE"
      session_type:
        | "INITIAL_EVALUATION"
        | "TRIENNIAL_EVALUATION"
        | "REEVALUATION"
        | "INDEPENDENT_EVALUATION"
        | "PROGRESS_MONITORING"
        | "DIAGNOSTIC_CLARIFICATION"
        | "COMPREHENSIVE_EVALUATION"
      status: "OPEN" | "IN_PROGRESS" | "RESOLVED" | "RELEASED" | "REJECTED"
      subtest_type:
        | "CORE"
        | "SUPPLEMENTAL"
        | "PROCESS_SPECIFIC"
        | "OPTIONAL"
        | "ANCILLARY"
        | "RATING_SCALE"
      task_history_action:
        | "CREATED"
        | "ASSIGNED"
        | "REASSIGNED"
        | "STATUS_CHANGED"
        | "PRIORITY_CHANGED"
        | "DUE_DATE_CHANGED"
        | "COMPLETED"
        | "CANCELLED"
        | "NOTES_ADDED"
        | "REOPENED"
        | "REJECTED"
      task_priority: "LOW" | "MEDIUM" | "HIGH" | "URGENT"
      task_status:
        | "PENDING"
        | "IN_PROGRESS"
        | "COMPLETED"
        | "CANCELLED"
        | "BLOCKED"
        | "REJECTED"
      task_type:
        | "ASSIGN_PSYCHOLOGIST"
        | "REASSIGN_PSYCHOLOGIST"
        | "REVIEW_DISTRICT_ASSIGNMENT"
        | "SHIP_EVALUATION_MATERIALS"
        | "UPDATE_AVAILABILITY"
        | "COMPLETE_REFERRAL_FORM"
        | "SCHEDULE_STUDENT_EVALUATIONS"
        | "GENERATE_CALENDAR_INVITES"
        | "CREATE_EVALUATION_PLAN"
        | "PREPARE_RATING_SCALES"
        | "REVIEW_AND_SEND_RATING_SCALES"
        | "MONITOR_RATING_SCALES"
        | "PREPARE_ASSESSMENT_MATERIALS"
        | "PREPARE_FOR_EVALUATION"
        | "JOIN_EVALUATION_AS_PROCTOR"
        | "JOIN_EVALUATION_AS_PSYCHOLOGIST"
        | "MARK_EVALUATION_COMPLETE"
        | "COMPLETE_STUDENT_INTERVIEW"
        | "UPLOAD_PROTOCOLS"
        | "UPDATE_ASSESSMENT_SCORES"
        | "GENERATE_REPORT_DRAFT"
        | "FINALIZE_EVALUATION_REPORT"
        | "SCORE_REPORT_QUALITY"
        | "REVIEW_FINAL_REPORT"
        | "MARK_REPORT_RECEIVED"
        | "SCHEDULE_IEP_MEETING"
        | "PREPARE_FOR_IEP_MEETING"
        | "SEND_MEETING_INVITATIONS"
        | "COMPLETE_IEP_MEETING"
      test_category:
        | "COGNITIVE_ASSESSMENT"
        | "ACADEMIC_ACHIEVEMENT"
        | "SOCIAL_EMOTIONAL_ASSESSMENT"
        | "NEUROPSYCHOLOGICAL_ASSESSMENT"
        | "ADAPTIVE_BEHAVIOR"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  graphql_public: {
    Enums: {},
  },
  public: {
    Enums: {
      address_type: ["PHYSICAL", "POSTAL", "BILLING", "OTHER"],
      admin_status: [
        "PLANNED",
        "IN_PROGRESS",
        "COMPLETED",
        "DISCONTINUED",
        "INVALID",
        "PARTIAL",
      ],
      attendee_status: ["PENDING", "ACCEPTED", "DECLINED", "TENTATIVE"],
      availability_type: ["EVALUATION", "MEETING", "CONSULTATION", "ALL"],
      blocked_date_type: [
        "HOLIDAY",
        "STAFF_TRAINING",
        "DISTRICT_CLOSURE",
        "MAINTENANCE",
        "SPECIAL_EVENT",
        "VACATION",
        "WEATHER",
        "EMERGENCY",
        "OTHER",
      ],
      case_priority: ["LOW", "MEDIUM", "HIGH", "URGENT"],
      case_status: [
        "READY_FOR_EVALUATION",
        "EVALUATION_IN_PROGRESS",
        "REPORT_IN_PROGRESS",
        "AWAITING_MEETING",
        "MEETING_COMPLETE",
      ],
      case_type: [
        "INITIAL_EVALUATION",
        "TRIENNIAL_EVALUATION",
        "REEVALUATION",
        "INDEPENDENT_EVALUATION",
        "CHANGE_OF_PLACEMENT",
        "DISCIPLINE_EVALUATION",
        "TRANSITION_EVALUATION",
      ],
      day_of_week: [
        "SUNDAY",
        "MONDAY",
        "TUESDAY",
        "WEDNESDAY",
        "THURSDAY",
        "FRIDAY",
        "SATURDAY",
      ],
      district_type: [
        "ELEMENTARY_DISTRICT",
        "SECONDARY_DISTRICT",
        "UNIFIED_DISTRICT",
        "SUPERVISORY_UNION_ADMIN",
        "REGIONAL_SERVICE_AGENCY",
        "STATE_OPERATED_AGENCY",
        "FEDERAL_OPERATED_AGENCY",
        "CHARTER_LEA",
        "OTHER_EDUCATION_AGENCY",
        "SPECIALIZED_PUBLIC_DISTRICT",
      ],
      document_category: ["BACKGROUND", "ASSESSMENT"],
      enrollment_status: [
        "ENROLLED",
        "WITHDRAWN",
        "TRANSFERRED",
        "GRADUATED",
        "SUSPENDED",
        "EXPELLED",
        "INACTIVE",
      ],
      event_status: [
        "SCHEDULED",
        "CANCELLED",
        "COMPLETED",
        "RESCHEDULED",
        "NO_SHOW",
      ],
      event_type: [
        "EVALUATION",
        "IEP_MEETING",
        "OBSERVATION_MEETING",
        "GENERAL_MEETING",
      ],
      feedback_type: ["BUG", "FEATURE_REQUEST", "GENERAL"],
      gender: ["MALE", "FEMALE", "NON_BINARY", "PREFER_NOT_TO_SAY", "OTHER"],
      iep_status: ["ACTIVE", "INACTIVE"],
      index_type: [
        "PRIMARY",
        "COMPOSITE",
        "SUPPLEMENTAL",
        "PROCESS_SPECIFIC",
        "ANCILLARY",
      ],
      invitation_status: ["PENDING", "ACCEPTED", "REJECTED", "EXPIRED"],
      issue_type: [
        "VISUAL",
        "FUNCTIONALITY",
        "PERFORMANCE",
        "SECURITY",
        "DATA",
        "USABILITY",
        "ACCESSIBILITY",
        "OTHER",
      ],
      notification_category_type: ["GENERAL", "WORKFLOW", "ADMINISTRATIVE"],
      parent_relationship: [
        "MOTHER",
        "FATHER",
        "STEP_MOTHER",
        "STEP_FATHER",
        "GUARDIAN",
        "ADOPTIVE_MOTHER",
        "ADOPTIVE_FATHER",
        "GRANDMOTHER",
        "GRANDFATHER",
        "AUNT",
        "UNCLE",
        "FOSTER_MOTHER",
        "FOSTER_FATHER",
        "OTHER_RELATIVE",
        "NON_RELATIVE",
        "UNKNOWN",
      ],
      plan_status: ["PENDING", "ACTIVE", "CANCELLED"],
      plan_type: ["IEP", "504", "BIP", "SST"],
      preference_category: [
        "NOTIFICATIONS",
        "USER_MANAGEMENT",
        "WORKFLOW",
        "REPORTING",
        "INTEGRATIONS",
        "EVALUATION",
        "SECURITY",
      ],
      preference_type: ["BOOLEAN", "STRING", "NUMBER", "JSON"],
      role: [
        "SUPER_USER",
        "SPECIAL_ED_DIRECTOR",
        "SCHOOL_COORDINATOR",
        "SCHOOL_ADMIN",
        "PROCTOR",
        "CASE_MANAGER",
        "CLINICAL_DIRECTOR",
        "PSYCHOLOGIST",
        "ASSISTANT",
      ],
      school_type: [
        "REGULAR_PUBLIC_PRIMARY",
        "REGULAR_PUBLIC_MIDDLE",
        "REGULAR_PUBLIC_HIGH",
        "REGULAR_PUBLIC_UNIFIED",
        "SPECIAL_ED_PUBLIC",
        "VOCATIONAL_PUBLIC",
        "ALTERNATIVE_PUBLIC",
        "REPORTABLE_PROGRAM",
        "PUBLIC_CHARTER",
        "MAGNET_PUBLIC",
        "VIRTUAL_PUBLIC",
        "DODEA_SCHOOL",
        "BIE_SCHOOL",
        "PRIVATE_CATHOLIC",
        "PRIVATE_OTHER_RELIGIOUS",
        "PRIVATE_NONSECTARIAN",
      ],
      session_status: [
        "SCHEDULED",
        "IN_PROGRESS",
        "COMPLETED",
        "CANCELLED",
        "RESCHEDULED",
        "INCOMPLETE",
      ],
      session_type: [
        "INITIAL_EVALUATION",
        "TRIENNIAL_EVALUATION",
        "REEVALUATION",
        "INDEPENDENT_EVALUATION",
        "PROGRESS_MONITORING",
        "DIAGNOSTIC_CLARIFICATION",
        "COMPREHENSIVE_EVALUATION",
      ],
      status: ["OPEN", "IN_PROGRESS", "RESOLVED", "RELEASED", "REJECTED"],
      subtest_type: [
        "CORE",
        "SUPPLEMENTAL",
        "PROCESS_SPECIFIC",
        "OPTIONAL",
        "ANCILLARY",
        "RATING_SCALE",
      ],
      task_history_action: [
        "CREATED",
        "ASSIGNED",
        "REASSIGNED",
        "STATUS_CHANGED",
        "PRIORITY_CHANGED",
        "DUE_DATE_CHANGED",
        "COMPLETED",
        "CANCELLED",
        "NOTES_ADDED",
        "REOPENED",
        "REJECTED",
      ],
      task_priority: ["LOW", "MEDIUM", "HIGH", "URGENT"],
      task_status: [
        "PENDING",
        "IN_PROGRESS",
        "COMPLETED",
        "CANCELLED",
        "BLOCKED",
        "REJECTED",
      ],
      task_type: [
        "ASSIGN_PSYCHOLOGIST",
        "REASSIGN_PSYCHOLOGIST",
        "REVIEW_DISTRICT_ASSIGNMENT",
        "SHIP_EVALUATION_MATERIALS",
        "UPDATE_AVAILABILITY",
        "COMPLETE_REFERRAL_FORM",
        "SCHEDULE_STUDENT_EVALUATIONS",
        "GENERATE_CALENDAR_INVITES",
        "CREATE_EVALUATION_PLAN",
        "PREPARE_RATING_SCALES",
        "REVIEW_AND_SEND_RATING_SCALES",
        "MONITOR_RATING_SCALES",
        "PREPARE_ASSESSMENT_MATERIALS",
        "PREPARE_FOR_EVALUATION",
        "JOIN_EVALUATION_AS_PROCTOR",
        "JOIN_EVALUATION_AS_PSYCHOLOGIST",
        "MARK_EVALUATION_COMPLETE",
        "COMPLETE_STUDENT_INTERVIEW",
        "UPLOAD_PROTOCOLS",
        "UPDATE_ASSESSMENT_SCORES",
        "GENERATE_REPORT_DRAFT",
        "FINALIZE_EVALUATION_REPORT",
        "SCORE_REPORT_QUALITY",
        "REVIEW_FINAL_REPORT",
        "MARK_REPORT_RECEIVED",
        "SCHEDULE_IEP_MEETING",
        "PREPARE_FOR_IEP_MEETING",
        "SEND_MEETING_INVITATIONS",
        "COMPLETE_IEP_MEETING",
      ],
      test_category: [
        "COGNITIVE_ASSESSMENT",
        "ACADEMIC_ACHIEVEMENT",
        "SOCIAL_EMOTIONAL_ASSESSMENT",
        "NEUROPSYCHOLOGICAL_ASSESSMENT",
        "ADAPTIVE_BEHAVIOR",
      ],
    },
  },
} as const

