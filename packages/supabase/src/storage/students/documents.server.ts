import { nanoid } from 'nanoid';
import { getSupabaseServerClient } from '../../config/server';

interface UploadStudentDocumentParams {
  file: File;
  studentId: string;
  userId: string;
}

interface UploadResult {
  url: string;
  path: string;
}

export async function uploadStudentDocumentServer({
  file,
  studentId,
}: UploadStudentDocumentParams): Promise<UploadResult> {
  const supabase = await getSupabaseServerClient();

  const fileExt = file.name.split('.').pop();
  const fileName = `${nanoid()}.${fileExt}`;
  const filePath = `students/${studentId}/documents/${fileName}`;

  const { error } = await supabase.storage
    .from('documents')
    .upload(filePath, file, {
      cacheControl: '3600',
      upsert: false,
    });

  if (error) {
    throw new Error(`Failed to upload document: ${error.message}`);
  }

  const {
    data: { publicUrl },
  } = supabase.storage.from('documents').getPublicUrl(filePath);

  return {
    url: publicUrl,
    path: filePath,
  };
}
