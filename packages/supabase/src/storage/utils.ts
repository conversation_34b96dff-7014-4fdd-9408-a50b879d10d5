import { nanoid } from 'nanoid';

export function extractFilenameFromUrl(url: string) {
  const parsedUrl = new URL(url);
  const pathname = parsedUrl.pathname;
  const fileName = pathname.split('/').pop();
  return String(fileName);
}

export function generateBucketFilename(fileName: string, id: string) {
  const randomString = nanoid(16);
  const extension = fileName.split('.').pop();
  return `${id}__${randomString}.${extension}`;
}
