import type { Supabase } from '../types';

import { extractFilenameFromUrl, generateBucketFilename } from './utils';

interface UploadLogoParams {
  supabase: Supabase;
  organizationId: string;
  logoFile: File;
}

export async function uploadLogo({
  supabase,
  organizationId,
  logoFile,
}: UploadLogoParams) {
  const bytes = await logoFile.arrayBuffer();
  const bucket = supabase.storage.from('logos');
  const fileName = generateBucketFilename(logoFile.name, organizationId);

  const result = await bucket.upload(fileName, bytes, {
    contentType: logoFile.type,
  });

  if (result.error) {
    throw result.error;
  }

  return bucket.getPublicUrl(fileName).data.publicUrl;
}

export async function deleteLogo({
  supabase,
  publicUrl,
}: {
  supabase: Supabase;
  publicUrl: string;
}) {
  const bucket = supabase.storage.from('logos');
  const fileName = extractFilenameFromUrl(publicUrl);
  const result = await bucket.remove([fileName]);
  return result;
}
