import type { Supabase } from '../types';

import { extractFilenameFromUrl, generateBucketFilename } from './utils';

interface UploadContactImageParams {
  supabase: Supabase;
  contactId: string;
  image: File;
}

export async function uploadContactImage({
  supabase,
  contactId,
  image,
}: UploadContactImageParams) {
  const bytes = await image.arrayBuffer();
  const bucket = supabase.storage.from('contact_images');
  const fileName = generateBucketFilename(image.name, contactId);
  const result = await bucket.upload(fileName, bytes, {
    contentType: image.type,
  });
  if (result.error) {
    throw result.error;
  }
  return bucket.getPublicUrl(fileName).data.publicUrl;
}

export async function deleteContactImage({
  supabase,
  publicUrl,
}: {
  supabase: Supabase;
  publicUrl: string;
}) {
  const bucket = supabase.storage.from('contact_images');
  const fileName = extractFilenameFromUrl(publicUrl);
  const result = await bucket.remove([fileName]);
  return result;
}
