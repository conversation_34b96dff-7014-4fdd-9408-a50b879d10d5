import type { Supabase } from '../types/index';

import { extractFilenameFromUrl, generateBucketFilename } from './utils';

interface UploadAvatarParams {
  supabase: Supabase;
  userId: string;
  avatarFile: File;
}

export async function uploadAvatar({
  supabase,
  userId,
  avatarFile,
}: UploadAvatarParams) {
  const bytes = await avatarFile.arrayBuffer();
  const bucket = supabase.storage.from('avatars');
  const fileName = generateBucketFilename(avatarFile.name, userId);

  const result = await bucket.upload(fileName, bytes, {
    contentType: avatarFile.type,
  });

  if (result.error) {
    throw result.error;
  }

  return bucket.getPublicUrl(fileName).data.publicUrl;
}

export async function deleteAvatar({
  supabase,
  publicUrl,
}: {
  supabase: Supabase;
  publicUrl: string;
}) {
  const bucket = supabase.storage.from('avatars');
  const fileName = extractFilenameFromUrl(publicUrl);
  const result = await bucket.remove([fileName]);
  return result;
}
