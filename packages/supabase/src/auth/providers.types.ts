// biome-ignore lint/style/noEnum: Can be used as a type
export enum Provider {
  Email = 'email',
  // TotpCode = 'totp-code'
  Google = 'google',
  Github = 'github',
  Apple = 'apple',
  Azure = 'azure',
  Bitbucket = 'bitbucket',
  Discord = 'discord',
  Facebook = 'facebook',
  Figma = 'figma',
  Gitlab = 'gitlab',
  Kakao = 'kakao',
  Keycloak = 'keycloak',
  Linkedin = 'linkedin',
  LinkedinOidc = 'linkedin_oidc',
  Notion = 'notion',
  Slack = 'slack',
  SlackOidc = 'slack_oidc',
  Spotify = 'spotify',
  Twitch = 'twitch',
  Twitter = 'twitter',
  Workos = 'workos',
  Zoom = 'zoom',
  Fly = 'fly',
}

// biome-ignore lint/style/noEnum: Can be used as a type
export enum OAuthProvider {
  Google = 'google',
  Github = 'github',
  Apple = 'apple',
  Azure = 'azure',
  Bitbucket = 'bitbucket',
  Discord = 'discord',
  Facebook = 'facebook',
  Figma = 'figma',
  Gitlab = 'gitlab',
  Kakao = 'kakao',
  Keycloak = 'keycloak',
  Linkedin = 'linkedin',
  LinkedinOidc = 'linkedin_oidc',
  Notion = 'notion',
  Slack = 'slack',
  SlackOidc = 'slack_oidc',
  Spotify = 'spotify',
  Twitch = 'twitch',
  Twitter = 'twitter',
  Workos = 'workos',
  Zoom = 'zoom',
  Fly = 'fly',
}
