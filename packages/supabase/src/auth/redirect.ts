import { addMinutes } from 'date-fns';
import { hasBasePath } from 'next/dist/client/has-base-path';
import { removeBasePath } from 'next/dist/client/remove-base-path';
import { workUnitAsyncStorage } from 'next/dist/server/app-render/work-unit-async-storage.external';

import { routes } from '@lilypad/shared/routes';

import { keys } from '../../keys';
import { TOTP_AND_RECOVERY_CODES_EXPIRY_MINUTES } from './constants';
import { symmetricEncrypt } from './encryption';

export function getRequestStoragePathname(): string | null {
  const store = workUnitAsyncStorage.getStore();
  if (!store || store.type !== 'request') {
    return null;
  }

  const url = new URL(store.url.pathname + store.url.search, 'http://n');
  if (hasBasePath(url.pathname)) {
    return removeBasePath(url.pathname) + url.search;
  }

  return url.pathname + url.search;
}

export function getRedirectToSignIn(): string {
  const callbackUrl = getRequestStoragePathname();

  return callbackUrl
    ? `${routes.app.Api}/auth/sign-in?${new URLSearchParams({ callbackUrl })}`
    : `${routes.app.Api}/auth/sign-in`;
}

export function getRedirectToTotp(userId: string): string {
  const key = keys().SUPABASE_RECOVERY_CODE_SECRET;
  const token = symmetricEncrypt(userId, key);
  const expiry = symmetricEncrypt(
    addMinutes(
      new Date(),
      TOTP_AND_RECOVERY_CODES_EXPIRY_MINUTES
    ).toISOString(),
    key
  );

  return `${routes.app.auth.totp.Verify}?token=${encodeURIComponent(token)}&expiry=${encodeURIComponent(expiry)}`;
}
