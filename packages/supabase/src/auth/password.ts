import { compare } from 'bcryptjs';

import { MINIMUM_PASSWORD_LENGTH } from './constants';

export function verifyPassword(
  password: string,
  hashedPassword: string
): Promise<boolean> {
  return compare(password, hashedPassword);
}

const NUMBER_REGEX = /\d/;

class PasswordValidator {
  containsLowerAndUpperCase(str?: string | null): boolean {
    return this.isNotNullOrEmpty(str) && str !== str?.toLowerCase();
  }
  hasMinimumLength(str?: string | null): boolean {
    return (
      this.isNotNullOrEmpty(str) &&
      (str as string)?.length >= MINIMUM_PASSWORD_LENGTH
    );
  }

  containsNumber(str?: string | null): boolean {
    return this.isNotNullOrEmpty(str) && NUMBER_REGEX.test(str as string);
  }

  validate(str?: string | null): { success: boolean; errors: string[] } {
    let success = true;
    const errors: string[] = [];

    if (!this.containsLowerAndUpperCase(str)) {
      success = false;
      errors.push(
        'The password should contain lower and upper case characters.'
      );
    }

    if (!this.hasMinimumLength(str)) {
      success = false;
      errors.push(
        `The password should be at least ${MINIMUM_PASSWORD_LENGTH} characters long.`
      );
    }

    if (!this.containsNumber(str)) {
      success = false;
      errors.push('The password should contain at least one number.');
    }

    return { success, errors };
  }

  private isNotNullOrEmpty(str?: string | null): boolean {
    return !!str;
  }
}

export const passwordValidator = new PasswordValidator();
