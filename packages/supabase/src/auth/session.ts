import { type IsDefinedGuard, isDefined, isString } from '@lilypad/shared';
import type { User as AuthUser } from '@supabase/supabase-js';
import { validate as uuidValidate } from 'uuid';

export function checkSession(session: {
  user: AuthUser | null;
}): session is IsDefinedGuard<{
  user: IsDefinedGuard<AuthUser> & {
    id: string;
    email: string;
  };
}> {
  // session
  if (!session) {
    // Normal behavior, no need to log a warning
    return false;
  }

  // session.user
  if (!session.user) {
    console.warn('No user found in the session. Unable to validate session.');
    return false;
  }

  // session.user.id
  if (!isDefined(session.user.id)) {
    console.warn('User ID is undefined. Validation failed.');
    return false;
  }
  if (!uuidValidate(session.user.id)) {
    console.warn('Invalid user ID format. Expected a UUID.');
    return false;
  }

  // session.user.email
  if (!isDefined(session.user.email)) {
    console.warn(`User ${session.user.id} has an undefined email.`);
    return false;
  }
  if (!isString(session.user.email)) {
    console.warn(
      `Invalid email type for user ${session.user.id}. Expected a string.`
    );
    return false;
  }

  return true;
}
