{"name": "@lilypad/kv", "private": true, "version": "0.0.0", "type": "module", "scripts": {"clean": "git clean -xdf node_modules", "format": "biome format --write .", "lint": "biome lint --write .", "typecheck": "tsc --noEmit"}, "devDependencies": {"@lilypad/typescript": "workspace:*", "@types/node": "^22.15.3"}, "dependencies": {"@t3-oss/env-nextjs": "^0.13.4", "@upstash/ratelimit": "^2.0.5", "@upstash/redis": "^1.34.9", "server-only": "^0.0.1", "zod": "^3.25.7"}, "exports": {"./redis": "./src/index.ts", "./ratelimit": "./src/ratelimit.ts", "./keys": "./keys.ts"}}