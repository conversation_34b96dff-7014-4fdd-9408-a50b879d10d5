# Psychological Tests Data Seeding

This script populates the psychological testing tables with comprehensive data for common assessment batteries used in educational and clinical settings.

## Overview

The script seeds the following tables:
- `test_batteries` - Specific test batteries (e.g., WISC-V, WIAT-III)
- `test_indices` - Composite scores and indices within batteries
- `subtests` - Individual subtests within batteries  
- `index_subtest_mappings` - Relationships between indices and their component subtests

## Included Test Batteries

### Cognitive Assessment
- **WISC-V (Wechsler Intelligence Scale for Children, Fifth Edition)**
  - Primary Indices: VCI, VSI, FRI, WMI, PSI, FSIQ
  - Ancillary Indices: QRI, AWMI, NVI, GAI, CPI
  - 16 subtests (10 primary, 6 supplemental)

- **WAIS-IV (Wechsler Adult Intelligence Scale, Fourth Edition)**
  - For ages 16-90
  - Comprehensive adult cognitive assessment

- **WPPSI-IV (Wechsler Preschool and Primary Scale of Intelligence, Fourth Edition)**
  - For ages 2:6-7:7
  - Early childhood cognitive assessment

### Academic Achievement
- **WIAT-III (Wechsler Individual Achievement Test, Third Edition)**
  - Composites: Total Achievement, Reading, Mathematics, Written Expression, Oral Language
  - 9 core subtests measuring academic skills

- **WJ-IV ACH (<PERSON><PERSON>-Johnson IV Tests of Achievement)**
  - Comprehensive achievement battery
  - Ages 2-90+

### Social-Emotional
- **BASC-3 (Behavior Assessment System for Children, Third Edition)**
  - Comprehensive behavioral and emotional assessment
  - Ages 2-21

## Test Categories

1. **Cognitive Assessment** - Intellectual ability and cognitive processes
2. **Academic Achievement** - Reading, writing, mathematics skills
3. **Social-Emotional Assessment** - Emotional and behavioral functioning
4. **Neuropsychological Assessment** - Specific cognitive functions
5. **Adaptive Behavior** - Everyday life skills and functioning

## WISC-V Details

### Primary Indices
- **VCI (Verbal Comprehension Index)**: Similarities, Vocabulary
- **VSI (Visual Spatial Index)**: Block Design, Visual Puzzles  
- **FRI (Fluid Reasoning Index)**: Matrix Reasoning, Figure Weights
- **WMI (Working Memory Index)**: Digit Span, Picture Span
- **PSI (Processing Speed Index)**: Coding, Symbol Search
- **FSIQ (Full Scale IQ)**: Composite of primary subtests

### Ancillary Indices
- **QRI (Quantitative Reasoning Index)**: Mathematical thinking
- **AWMI (Auditory Working Memory Index)**: Auditory processing
- **NVI (Nonverbal Index)**: For language/hearing difficulties
- **GAI (General Ability Index)**: Excludes WM and PS
- **CPI (Cognitive Proficiency Index)**: WM and PS combined

### Subtests
**Core Subtests (Primary)**:
- Similarities (SI) - Verbal reasoning
- Vocabulary (VC) - Word knowledge
- Block Design (BD) - Visual-spatial construction
- Visual Puzzles (VP) - Visual-spatial analysis
- Matrix Reasoning (MR) - Fluid reasoning
- Figure Weights (FW) - Quantitative reasoning
- Digit Span (DS) - Working memory
- Picture Span (PS) - Visual working memory
- Coding (CD) - Processing speed
- Symbol Search (SS) - Processing speed

**Supplemental Subtests**:
- Information (IN) - General knowledge
- Comprehension (CO) - Practical reasoning
- Picture Concepts (PCn) - Categorical reasoning
- Arithmetic (AR) - Mathematical reasoning
- Letter-Number Sequencing (LN) - Working memory
- Cancellation (CA) - Visual attention

## WIAT-III Details

### Composites
- **Total Achievement**: Overall academic performance
- **Reading**: Word Reading, Reading Comprehension, Pseudoword Decoding
- **Mathematics**: Numerical Operations, Math Problem Solving
- **Written Expression**: Spelling, Essay Composition
- **Oral Language**: Listening Comprehension, Oral Expression

### Subtests
- **Word Reading (WR)**: Single word reading accuracy
- **Reading Comprehension (RC)**: Reading comprehension skills
- **Pseudoword Decoding (PD)**: Phonetic decoding abilities
- **Numerical Operations (NO)**: Mathematical computation
- **Math Problem Solving (MPS)**: Mathematical reasoning
- **Spelling (SP)**: Written spelling skills
- **Essay Composition (EC)**: Written expression
- **Listening Comprehension (LC)**: Auditory comprehension
- **Oral Expression (OE)**: Oral language skills

## Running the Script

### Individual Script
```bash
pnpm seed:tests
```

### As Part of Full Seeding
```bash
pnpm seed
```

The script will:
1. Check for existing data to avoid duplicates
2. Seed in dependency order (categories → batteries → indices → subtests → mappings)
3. Log progress and success/failure for each step
4. Provide comprehensive error handling

## Data Structure

### Test Categories
- Organized by assessment domain
- Includes description and sort order
- All marked as active by default

### Test Batteries
- Linked to appropriate category
- Includes publisher, age ranges, administration time
- Norming information stored as JSONB
- Unique codes for easy reference

### Test Indices
- Linked to parent battery
- Categorized by type (PRIMARY, COMPOSITE, ANCILLARY, etc.)
- Standard score ranges (typically 40-160, mean=100, SD=15)
- Interpretive guidelines included

### Subtests
- Linked to parent battery
- Categorized by type (CORE, SUPPLEMENTAL, etc.)
- Scaled score ranges (typically 1-19, mean=10, SD=3)
- Measured abilities stored as array
- Administration instructions included

### Index-Subtest Mappings
- Many-to-many relationships between indices and subtests
- Weighting factors (typically 1.0)
- Required vs. optional subtest designations
- Ensures indices and subtests belong to same battery

## Error Handling

The script includes comprehensive error handling:
- Validates foreign key relationships
- Checks for existing records before insertion
- Logs detailed error information
- Continues processing on individual failures
- Exits with appropriate codes

## Notes

- All age ranges are stored in months for consistency
- Time limits stored in minutes (null for untimed)
- JSONB fields used for complex data (norming info, abilities)
- Standard psychological test score ranges implemented
- Follows established psychological testing conventions

This seed data provides a comprehensive foundation for psychological testing functionality in the application, supporting common assessment workflows used in educational and clinical settings. 