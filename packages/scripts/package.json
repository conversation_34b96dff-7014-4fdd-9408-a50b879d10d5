{"name": "@lilypad/scripts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"clean": "git clean -xdf node_modules", "format": "biome format --write .", "lint": "biome lint --write .", "typecheck": "tsc --noEmit", "seed:users": "dotenv -e ./.env -- tsx src/seed/users.ts | pino-pretty", "seed:districts": "dotenv -e ./.env -- tsx src/seed/districts.ts | pino-pretty", "seed:students": "dotenv -e ./.env -- tsx src/seed/students.ts | pino-pretty", "seed:tests": "dotenv -e ./.env -- tsx src/seed/tests.ts | pino-pretty", "seed": "pnpm seed:districts && pnpm seed:users && pnpm seed:tests && pnpm seed:students"}, "dependencies": {"@lilypad/db": "workspace:*", "@lilypad/shared": "workspace:*", "@lilypad/supabase": "workspace:*", "@ngneat/falso": "^7.3.0", "@supabase/ssr": "^0.6.1"}, "devDependencies": {"@lilypad/typescript": "workspace:*", "@types/node": "^22.15.3", "dotenv": "^16.5.0", "dotenv-cli": "^8.0.0", "pino-pretty": "^13.0.0", "tsx": "^4.19.4", "typescript": "5.8.2"}}