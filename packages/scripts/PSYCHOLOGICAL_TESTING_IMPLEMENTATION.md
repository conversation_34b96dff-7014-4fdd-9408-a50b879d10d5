# Psychological Testing Seeding Implementation

## Overview

This document outlines the implementation of psychological testing dummy data generation in the seeding scripts. The implementation adds comprehensive support for generating realistic assessment sessions, test administrations, subtest scores, index scores, documents, and case details for students.

## Key Features Added

### 1. Assessment Sessions Generation
- **Coverage**: 70% of students receive assessment sessions (configurable)
- **Frequency**: 1-2 assessment sessions per eligible student
- **Data Points**:
  - Session date (within last 2 years)
  - Session duration (90-180 minutes)
  - Session notes with behavioral observations
  - Completion status tracking

### 2. Test Administrations
- **Battery Selection**: 1-2 test batteries per session from available options
- **Realistic Scoring**: Age-appropriate standard scores with proper distributions
- **Administration Details**:
  - Administration date matching session timeline
  - Examiner notes with behavioral observations
  - Completion status and validity indicators

### 3. Subtest Scores Generation
- **Score Distribution**: Realistic bell curve distribution (mean: 10, std dev: 3)
- **Score Ranges**: 1-19 with proper statistical distribution
- **Subtest Coverage**: All subtests for selected test batteries
- **Data Integrity**: Scores align with psychological testing standards

### 4. Index Scores Generation
- **Composite Scores**: Standard scores (mean: 100, std dev: 15)
- **Score Ranges**: 40-160 with realistic distribution
- **Index Types**: All available indices for administered test batteries
- **Percentile Rankings**: Automatically calculated percentile equivalents

### 5. Assessment Documents
- **Document Types**: Assessment reports, score summaries, behavioral observations
- **File References**: Links to sample PDF reports in `/public` folder
- **Metadata**: Proper file size, type, and upload tracking
- **Security**: Secure file naming and access control

### 6. Case Details Generation
- **Detail Types**: 8 different categories of case information
  - Referral source and reason
  - Previous evaluations history
  - Current medications
  - Behavioral concerns
  - Academic concerns
  - Family history
  - Interpreter needs
  - Testing accommodations
- **Realistic Content**: Contextually appropriate information using psychological terminology
- **Probability-Based**: Different generation probabilities for realistic data distribution

## Technical Implementation

### Configuration

```typescript
const PSYCH_TESTING_CONFIG = {
  percentageWithAssessments: 0.7,      // 70% of students get assessments
  maxSessionsPerStudent: 2,            // 1-2 sessions per student
  maxBatteriesPerSession: 2,           // 1-2 test batteries per session
  assessmentWindowMonths: 24,          // Assessments within last 24 months
  sessionDurationRange: [90, 180],     // Session duration in minutes
  caseDetailsRange: [3, 5],            // 3-5 case details per case
};
```

### Enhanced Data Types

#### StudentBatch Type Extensions
```typescript
interface StudentBatch {
  // ... existing properties
  
  // Psychological testing arrays
  assessmentSessions: NewAssessmentSession[];
  testAdministrations: NewTestAdministration[];
  subtestScores: NewSubtestScore[];
  indexScores: NewIndexScore[];
  documents: NewDocument[];
  caseDetails: NewCaseDetail[];
  
  // Relationship mapping arrays
  sessionRelations: { tempId: string; studentIndex: number; caseIndex: number }[];
  administrationRelations: { tempId: string; sessionIndex: number; batteryId: string }[];
  subtestRelations: { tempId: string; administrationIndex: number; subtestId: string }[];
  indexRelations: { tempId: string; administrationIndex: number; indexId: string }[];
  documentRelations: { tempId: string; studentIndex: number; sessionIndex: number }[];
  caseDetailRelations: { tempId: string; caseIndex: number }[];
}
```

### Database Integration

#### Enhanced Data Fetching
```typescript
async function getExistingData() {
  // ... existing queries
  
  // Psychological testing data
  const testBatteries = await db.select({
    id: testBatteriesTable.id,
    name: testBatteriesTable.name,
  }).from(testBatteriesTable);
  
  const testSubtests = await db.select({
    id: testSubtestsTable.id,
    batteryId: testSubtestsTable.batteryId,
    name: testSubtestsTable.name,
  }).from(testSubtestsTable);
  
  const testIndices = await db.select({
    id: testIndicesTable.id,
    batteryId: testIndicesTable.batteryId,
    name: testIndicesTable.name,
  }).from(testIndicesTable);
  
  return { /* ... */ testBatteries, testSubtests, testIndices };
}
```

#### Batch Insertion Process

The seeding process follows a 14-step insertion pipeline:

1. **Districts** - Educational districts
2. **Schools** - Schools within districts
3. **Cases** - Student cases
4. **Students** - Student records
5. **Enrollments** - School enrollments
6. **User Districts** - User-district associations
7. **User Schools** - User-school associations
8. **Case Assignments** - Case-user assignments
9. **Assessment Sessions** - Psychological assessment sessions
10. **Test Administrations** - Test battery administrations
11. **Subtest Scores** - Individual subtest results
12. **Index Scores** - Composite index scores
13. **Documents** - Assessment reports and documents
14. **Case Details** - Additional case information

### Score Generation Logic

#### Subtest Scores
```typescript
// Realistic subtest score distribution (mean: 10, std dev: 3)
const subtestScore = Math.max(1, Math.min(19, 
  Math.round(randFloat({ min: 4, max: 16 }))
));
```

#### Index Scores
```typescript
// Standard score distribution (mean: 100, std dev: 15)
const indexScore = Math.max(40, Math.min(160, 
  Math.round(randFloat({ min: 70, max: 130 }))
));

// Calculate percentile rank
const percentileRank = calculatePercentileFromStandardScore(indexScore);
```

#### Behavioral Observations
```typescript
const behavioralObservations = [
  "Student was cooperative and engaged throughout the session",
  "Demonstrated good attention and focus during testing",
  "Required minimal prompting to complete tasks",
  // ... more realistic observations
];
```

### Case Details Generation

#### Detail Categories
1. **Referral Information** (100% probability)
2. **Previous Evaluations** (30% probability)
3. **Current Medications** (25% probability)
4. **Behavioral Concerns** (40% probability)
5. **Academic Concerns** (45% probability)
6. **Family History** (20% probability)
7. **Interpreter Needs** (15% probability)
8. **Testing Accommodations** (35% probability)

#### Content Generation
```typescript
const caseDetailGenerators = {
  referralSource: () => ({
    type: CaseDetailTypeEnum.REFERRAL_SOURCE,
    content: `Referred by ${randElement(referralSources)} for ${randElement(referralReasons)}`
  }),
  
  previousEvaluations: () => ({
    type: CaseDetailTypeEnum.PREVIOUS_EVALUATIONS,
    content: `Previous evaluation: ${randElement(previousEvalTypes)} conducted ${randInt({ min: 1, max: 5 })} years ago`
  }),
  
  // ... other generators
};
```

## File Structure Changes

### New Dependencies Added
```json
{
  "@ngneat/falso": "^7.2.0" // For realistic dummy data generation
}
```

### Modified Files
- `packages/scripts/src/seed/students.ts` - Main implementation
- `packages/scripts/package.json` - Added falso dependency
- `packages/scripts/src/seed/utils.ts` - Utility functions (created)

### Import Structure
```typescript
// Database imports
import {
  assessmentSessionsTable,
  testAdministrationsTable,
  subtestScoresTable,
  indexScoresTable,
  documentsTable,
  caseDetailsTable,
  // ... other tables
} from "@lilypad/db/schema/tables";

// Type imports
import type {
  NewAssessmentSession,
  NewTestAdministration,
  NewSubtestScore,
  NewIndexScore,
  NewDocument,
  NewCaseDetail,
  // ... other types
} from "@lilypad/db/schema/types";

// Falso imports for data generation
import {
  randSentence,
  randFloat,
  randWord,
  randElement,
  randInt,
  randPastDate,
  randBoolean,
} from "@ngneat/falso";
```

## Usage Instructions

### Running the Seed Script
```bash
# From project root
pnpm --filter=@lilypad/scripts run seed:students

# With specific batch size
BATCH_SIZE=50 pnpm --filter=@lilypad/scripts run seed:students
```

### Configuration Options
Modify `PSYCH_TESTING_CONFIG` in `students.ts` to adjust:
- Percentage of students receiving assessments
- Number of sessions per student
- Assessment time window
- Session duration ranges
- Case details quantity

### Sample Data Generated

For a typical seeding run with 100 students:
- **Students with assessments**: ~70 students
- **Assessment sessions**: ~100 sessions
- **Test administrations**: ~150 administrations
- **Subtest scores**: ~1,500 scores
- **Index scores**: ~300 scores
- **Documents**: ~100 assessment reports
- **Case details**: ~1,000 case detail records

## Data Quality and Realism

### Psychological Accuracy
- Score distributions match real-world psychological testing norms
- Test batteries and subtests reflect actual assessment instruments
- Behavioral observations use appropriate professional terminology
- Case details include realistic referral and background information

### Statistical Validity
- Normal distribution for cognitive scores
- Appropriate correlation between subtests and composite scores
- Realistic assessment timelines and frequencies
- Proper age-based scoring considerations

### Data Relationships
- Maintains referential integrity across all psychological testing tables
- Proper foreign key relationships between sessions, administrations, and scores
- Consistent timestamp ordering (session → administration → scoring)
- Logical document associations with assessment sessions

## Error Handling and Validation

### Type Safety
- Full TypeScript integration with database schema
- Compile-time validation of data structures
- Proper type inference for complex relationships

### Database Constraints
- Respects all database foreign key constraints
- Handles unique constraints appropriately
- Validates enum values before insertion
- Proper null handling for optional fields

### Error Recovery
- Transaction-based insertion prevents partial data corruption
- Detailed error logging for debugging
- Graceful handling of missing reference data
- Rollback capabilities for failed batch operations

## Performance Considerations

### Batch Processing
- Inserts data in optimized batches to reduce database load
- Uses prepared statements where appropriate
- Minimizes database round trips through bulk operations

### Memory Management
- Processes students in configurable batch sizes
- Clears temporary arrays between batches
- Efficient data structure usage for large datasets

### Query Optimization
- Selective fetching of reference data
- Indexed lookups for foreign key relationships
- Minimal data transfer for large operations

## Future Enhancements

### Planned Features
1. **Additional Assessment Types**: IEP assessments, functional behavioral assessments
2. **Advanced Scoring**: Age equivalents, grade equivalents, growth scale values
3. **Report Generation**: Automated assessment report creation
4. **Data Export**: CSV/Excel export functionality for generated data
5. **Template System**: Configurable templates for different assessment types

### Configuration Improvements
1. **JSON Configuration**: External configuration file support
2. **Environment Variables**: Runtime configuration through env vars
3. **Validation Rules**: Custom validation rule definitions
4. **Distribution Controls**: Fine-tuned score distribution controls

## Troubleshooting

### Common Issues
1. **Missing Reference Data**: Ensure test batteries and subtests are properly seeded
2. **Type Errors**: Verify schema imports match current database structure
3. **Performance Issues**: Reduce batch size if memory constraints occur
4. **Score Validation**: Check that generated scores fall within valid ranges

### Debug Mode
Enable detailed logging by setting `NODE_ENV=development` to see:
- Batch processing progress
- Generated data samples
- Database insertion timing
- Error stack traces

This implementation provides a comprehensive foundation for psychological testing data generation while maintaining data quality, performance, and extensibility. 