# Scripts Package

This package contains database seeding scripts for the Lilypad application.

## Overview

The seeding scripts generate realistic test data for development and testing environments using the [@ngneat/falso](https://ngneat.github.io/falso/) library for fake data generation.

## Prerequisites

Before running the seeding scripts, ensure you have:

1. A running Supabase instance (local or remote)
2. Environment variables properly configured in `.env` file
3. Database migrations applied
4. RLS policies and functions properly set up

## Available Scripts

### 1. Districts and Schools (`seed:districts`)

Seeds the database with sample school districts and their associated schools.

```bash
pnpm seed:districts
```

**What it creates:**
- Districts with addresses
- Schools within each district
- Geographic distribution across Massachusetts

### 2. Users (`seed:users`)

Seeds the database with users across different roles for testing authentication and authorization.

```bash
pnpm seed:users
```

**What it creates:**
- Users with different roles (Super User, Psychologist, Case Manager, etc.)
- User-district associations
- User-role assignments
- Authentication records in Supabase Auth

### 3. Students, Parents, and Cases (`seed:students`)

Seeds the database with student data and all related entities.

#### Standard Version (for < 100 students)
```bash
pnpm seed:students
```

#### Optimized Version (for 100+ students)
```bash
pnpm seed:students:optimized
```

**Performance Comparison:**
- **Standard**: ~12 database calls per student, sequential processing
- **Optimized**: Bulk inserts with batching, 5-8x faster for large datasets

**What it creates:**
- Students with realistic demographic data
- Parent records and parent-student relationships
- Student addresses
- School enrollments
- Multi-language associations (2-4 languages per student)
- Cases with IEP information
- Case assignments to staff members (Psychologist, Case Manager, Assistant, Clinical Director)

### 4. Complete Seeding

#### Standard Seeding (small datasets)
```bash
pnpm seed
```

#### Optimized Seeding (large datasets)
```bash
pnpm seed:optimized
```

## Performance Guidance

### When to Use Standard Scripts
- **Development/Testing**: Quick setup with < 100 students
- **Feature Testing**: When you need a small, manageable dataset
- **Debugging**: When you want detailed logs for each operation

### When to Use Optimized Scripts
- **Load Testing**: Testing with realistic data volumes (500-2000+ students)
- **Performance Testing**: Measuring application performance under load
- **Demo Environments**: Creating impressive datasets for demonstrations
- **Batch Processing**: When seeding speed is critical

### Performance Characteristics

| Dataset Size | Standard Script | Optimized Script | Recommendation |
|--------------|----------------|------------------|----------------|
| 1-50 students | ~2-5 minutes | ~30-60 seconds | Either |
| 50-100 students | ~5-10 minutes | ~1-2 minutes | Either |
| 100-500 students | ~10-30 minutes | ~2-5 minutes | **Use Optimized** |
| 500-1000 students | ~30-60 minutes | ~3-8 minutes | **Use Optimized** |
| 1000+ students | Not recommended | ~5-15 minutes | **Use Optimized** |

## Configuration

### Standard Configuration
Located in `src/data/students.ts`:

```typescript
export const STUDENT_CONFIG = {
  STUDENTS_COUNT: 50,
  MIN_PARENTS_PER_STUDENT: 2,
  MAX_PARENTS_PER_STUDENT: 2,
  MIN_LANGUAGES_PER_STUDENT: 2,
  MAX_LANGUAGES_PER_STUDENT: 4,
} as const;
```

### Optimized Configuration
For large datasets, modify the constants in `students-optimized.ts`:

```typescript
// For 1000+ students
const BATCH_SIZE = 100;  // Students per batch
const CHUNK_SIZE = 50;   // Records per database insert
```

## Data Relationships

The seeding scripts create a realistic data hierarchy:

```
District
├── Schools (multiple)
│   └── Student Enrollments
└── Users (staff members)
    └── Case Assignments

Student
├── Address (1)
├── Parents (2+)
├── Languages (2-4)
├── Case (1)
│   └── Case Assignments (4 staff members)
└── School Enrollment (1)
```

## Monitoring and Logging

All scripts provide detailed logging:

```bash
# View logs in real-time with pretty formatting
pnpm seed:students | pino-pretty

# Standard output shows progress
[INFO] Processing student 45/50
[INFO] Student created: John Doe (Grade 8)
[INFO] Parents created: 2, Languages: 3, Case assigned

# Optimized output shows batch progress
[INFO] Processing batch: 5/10, progress: 400/1000
[INFO] Batch completed: 100 students created
```

## Troubleshooting

### Common Issues

1. **"No schools found"** - Run `pnpm seed:districts` first
2. **"No languages found"** - Ensure Supabase seed.sql has been applied
3. **Authentication errors** - Check Supabase credentials in `.env`
4. **Memory issues with large datasets** - Reduce `BATCH_SIZE` and `CHUNK_SIZE`

### Database Connection Issues

```bash
# Test database connection
pnpm typecheck

# Check environment variables
echo $DATABASE_URL
echo $SUPABASE_URL
```

### Performance Issues

If seeding is slow:

1. **Check database resources** - Ensure adequate CPU/memory
2. **Use optimized scripts** - Switch to `seed:students:optimized`
3. **Adjust batch sizes** - Reduce if running out of memory
4. **Monitor connection limits** - Check database connection pool

## Development

### Adding New Seeding Scripts

1. Create script in `src/seed/`
2. Add to `package.json` scripts
3. Follow existing patterns for logging and error handling
4. Document in this README

### Testing Scripts

```bash
# Type check all scripts
pnpm typecheck

# Test with small dataset first
STUDENT_COUNT=5 pnpm seed:students

# Clean database between runs
# (Manual process - truncate tables or reset database)
```

## Architecture

For detailed information about the performance optimizations, see [PERFORMANCE_OPTIMIZATION.md](./PERFORMANCE_OPTIMIZATION.md).

## Data Quality

The seeded data includes:

- **Realistic names** - Using Falso's demographic-aware name generation
- **Valid addresses** - Massachusetts-based addresses with real zip codes
- **Age-appropriate grades** - Students assigned grades based on calculated age
- **Diverse demographics** - Gender, language, and geographic diversity
- **Proper relationships** - Referential integrity maintained across all entities
- **IEP compliance** - Realistic IEP dates and status combinations 