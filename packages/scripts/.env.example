
ENVIRONMENT=local

# -------------------------- DATABASE --------------------------

DATABASE_URL=postgresql://protected:postgres@12*******:54322/postgres
ADMIN_DATABASE_URL=postgresql://postgres:postgres@12*******:54322/postgres

# -------------------------- SUPABASE --------------------------

NEXT_PUBLIC_SUPABASE_URL=http://12*******:54321
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU
SUPABASE_RECOVERY_CODE_SECRET='N4zI1suUdpvZSrMnp7bjvLB4Gip63MhmJrwXBperTTQ=' # run 'openssl rand -base64 32' to generate a new secret