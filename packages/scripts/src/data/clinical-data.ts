/**
 * Clinical Data Configuration
 *
 * This file contains all medical and clinical data used for generating
 * realistic case details in psychological testing scenarios.
 *
 * Note: All medication names, dosages, and clinical terms are for
 * educational/testing purposes only and should be reviewed by
 * qualified medical professionals for accuracy.
 */

// Referral Sources
export const REFERRAL_SOURCES = [
  'Teacher',
  'Parent',
  'Pediatrician',
  'School Counselor',
  'Previous Psychologist',
  'Therapist',
  'Psychiatrist',
] as const;

// Previous Evaluations with realistic assessment tools and years
export const PREVIOUS_EVALUATIONS = [
  'Yes - WISC-V (2021)',
  'Yes - WIAT-III (2020)',
  'Yes - ADHD screening (2022)',
  'Yes - Speech evaluation (2021)',
  'Yes - Autism screening (2020)',
  'Yes - Cognitive assessment (2022)',
  'Yes - Behavioral evaluation (2021)',
  'Yes - Learning disability assessment (2020)',
] as const;

// Medication Information
export interface Medication {
  name: string;
  dosage: string;
  category: 'adhd' | 'anxiety' | 'depression' | 'other';
}

export const MEDICATIONS: Medication[] = [
  // ADHD Medications
  { name: '<PERSON><PERSON><PERSON>', dosage: '10mg', category: 'adhd' },
  { name: '<PERSON><PERSON>', dosage: '5mg', category: 'adhd' },
  { name: 'Concerta', dosage: '18mg', category: 'adhd' },
  { name: 'Strattera', dosage: '25mg', category: 'adhd' },
  { name: 'Vyvanse', dosage: '20mg', category: 'adhd' },
  { name: 'Focalin', dosage: '5mg', category: 'adhd' },
  { name: 'Daytrana', dosage: '15mg patch', category: 'adhd' },

  // Anxiety/Depression Medications (less common in school settings)
  { name: 'Zoloft', dosage: '25mg', category: 'anxiety' },
  { name: 'Prozac', dosage: '10mg', category: 'depression' },
  { name: 'Lexapro', dosage: '5mg', category: 'anxiety' },

  // Other medications
  { name: 'Melatonin', dosage: '3mg', category: 'other' },
  { name: 'Clonidine', dosage: '0.1mg', category: 'other' },
] as const;


export const BEHAVIORAL_CONCERNS = [
  'Attention difficulties',
  'Hyperactivity',
  'Impulsivity',
  'Social interaction challenges',
  'Emotional regulation',
  'Defiant behavior',
  'Anxiety in academic settings',
  'Difficulty following directions',
  'Aggressive behavior',
  'Withdrawal from peers',
  'Mood swings',
  'Difficulty with transitions',
  'Sensory processing issues',
  'Self-stimulating behaviors',
] as const;


export const ACADEMIC_CONCERNS = [
  'Reading comprehension',
  'Math calculation',
  'Written expression',
  'Processing speed',
  'Working memory',
  'Executive functioning',
  'Phonological awareness',
  'Reading fluency',
  'Math reasoning',
  'Spelling difficulties',
  'Handwriting challenges',
  'Organization skills',
  'Time management',
  'Following multi-step directions',
  'Auditory processing',
  'Visual processing',
] as const;


export const FAMILY_HISTORY_CONDITIONS = [
  'ADHD - father',
  'Learning disabilities - sibling',
  'Anxiety - mother',
  'Depression - family history',
  'Autism - cousin',
  'Dyslexia - parent',
  'Speech delay - sibling',
  'Developmental delays - family history',
  'Mental health concerns - extended family',
  'Educational challenges - parent',
] as const;


export const INTERPRETER_LANGUAGES = [
  'Spanish',
  'Mandarin',
  'Vietnamese',
  'Portuguese',
  'Arabic',
  'Somali',
  'Haitian Creole',
  'Russian',
  'Korean',
  'French',
] as const;


export const SPECIAL_ACCOMMODATIONS = [
  'Extended time',
  'Quiet environment',
  'Frequent breaks',
  'Small group setting',
  'Preferential seating',
  'Visual supports',
  'Reduce distractions',
  'Alternative response format',
  'Oral administration',
  'Large print materials',
  'Assistive technology',
  'Movement breaks',
  'Simplified instructions',
  'Additional processing time',
  'Behavioral support plan',
] as const;


export const CASE_DETAIL_PROBABILITIES = {
  PREVIOUS_EVALUATIONS: 0.3, // 30% chance
  CURRENT_MEDICATIONS: 0.25, // 25% chance
  FAMILY_HISTORY: 0.4, // 40% chance
  INTERPRETER_NEEDED: 0.15, // 15% chance
  SPECIAL_ACCOMMODATIONS: 0.6, // 60% chance
} as const;


export function formatMedication(medication: Medication): string {
  return `${medication.name} ${medication.dosage}`;
}


export function formatInterpreterNeed(language: string): string {
  return `Yes - ${language}`;
}


export function getMedicationsByCategory(
  category: Medication['category']
): Medication[] {
  return MEDICATIONS.filter((med) => med.category === category);
}
