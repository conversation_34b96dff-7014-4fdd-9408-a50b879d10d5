import type { NewAvailability } from '@lilypad/db/types';

import { DayOfWeekEnum } from '@lilypad/db/enums';
import { RoleEnum } from '@lilypad/db/enums';
import { randElement } from '../seed/utils';

export const USERS = [
  {
    email: '<EMAIL>',
    password: 'lilypad123',
    role: RoleEnum.SUPER_USER,
    firstName: 'Super',
    lastName: 'User',
  },
  {
    email: '<EMAIL>',
    password: 'lilypad123',
    role: RoleEnum.SPECIAL_ED_DIRECTOR,
    firstName: 'Special',
    lastName: 'Ed',
  },
  {
    email: '<EMAIL>',
    password: 'lilypad123',
    role: RoleEnum.SCHOOL_COORDINATOR,
    firstName: 'School',
    lastName: 'Coordinator',
  },
  {
    email: '<EMAIL>',
    password: 'lilypad123',
    role: RoleEnum.SCHOOL_ADMIN,
    firstName: 'School',
    lastName: 'Admin',
  },
  {
    email: '<EMAIL>',
    password: 'lilypad123',
    role: RoleEnum.PROCTOR,
    firstName: 'Proctor',
    lastName: 'User',
  },
  {
    email: '<EMAIL>',
    password: 'lilypad123',
    role: RoleEnum.CASE_MANAGER,
    firstName: 'Case',
    lastName: 'Manager',
  },
  {
    email: '<EMAIL>',
    password: 'lilypad123',
    role: RoleEnum.CLINICAL_DIRECTOR,
    firstName: 'Clinical',
    lastName: 'Director',
  },
  {
    email: '<EMAIL>',
    password: 'lilypad123',
    role: RoleEnum.PSYCHOLOGIST,
    firstName: 'Psychologist',
    lastName: 'User',
  },
  {
    email: '<EMAIL>',
    password: 'lilypad123',
    role: RoleEnum.ASSISTANT,
    firstName: 'Assistant',
    lastName: 'User',
  },
];

type BaseAvailability = Omit<NewAvailability, 'userId'>;

const AVAILABILITIES: BaseAvailability[] = [
  {
    day: DayOfWeekEnum.MONDAY,
    startTime: new Date('2025-01-01T08:00:00Z'),
    endTime: new Date('2025-01-01T12:00:00Z'),
  },
  {
    day: DayOfWeekEnum.TUESDAY,
    startTime: new Date('2025-01-01T13:00:00Z'),
    endTime: new Date('2025-01-01T17:00:00Z'),
  },
  {
    day: DayOfWeekEnum.WEDNESDAY,
    startTime: new Date('2025-01-01T08:00:00Z'),
    endTime: new Date('2025-01-01T17:00:00Z'),
  },
  {
    day: DayOfWeekEnum.THURSDAY,
    startTime: new Date('2025-01-01T10:00:00Z'),
    endTime: new Date('2025-01-01T15:00:00Z'),
  },
  {
    day: DayOfWeekEnum.SATURDAY,
    startTime: new Date('2025-01-01T09:00:00Z'),
    endTime: new Date('2025-01-01T15:00:00Z'),
  },
] as const;

export function generateAvailabilityPattern(): BaseAvailability {
  const basePattern = randElement(AVAILABILITIES);
  const daysOfWeek = Object.values(DayOfWeekEnum);
  return {
    ...basePattern,
    day: randElement(daysOfWeek),
  };
}
