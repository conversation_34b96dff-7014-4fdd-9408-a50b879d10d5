import {
  IndexTypeEnum,
  SubtestTypeEnum,
  TestCategoryEnum,
} from '@lilypad/db/enums';

export interface TestCategory {
  name: string;
  categoryType: TestCategoryEnum;
  description: string;
  sortOrder: number;
  isActive: boolean;
}

export const TEST_CATEGORIES: TestCategory[] = [
  {
    name: 'Cognitive Assessment',
    categoryType: TestCategoryEnum.COGNITIVE_ASSESSMENT,
    description:
      'Tests measuring intellectual ability, cognitive skills, and related cognitive processes',
    sortOrder: 1,
    isActive: true,
  },
  {
    name: 'Academic Achievement',
    categoryType: TestCategoryEnum.ACADEMIC_ACHIEVEMENT,
    description:
      'Tests measuring academic skills in reading, writing, mathematics, and related areas',
    sortOrder: 2,
    isActive: true,
  },
  {
    name: 'Social-Emotional Assessment',
    categoryType: TestCategoryEnum.SOCIAL_EMOTIONAL_ASSESSMENT,
    description:
      'Tests measuring emotional functioning, social skills, and behavioral patterns',
    sortOrder: 3,
    isActive: true,
  },
  {
    name: 'Neuropsychological Assessment',
    categoryType: TestCategoryEnum.NEUROPSYCHOLOGICAL_ASSESSMENT,
    description:
      'Tests measuring specific cognitive functions and neuropsychological processes',
    sortOrder: 4,
    isActive: true,
  },
  {
    name: 'Adaptive Behavior',
    categoryType: TestCategoryEnum.ADAPTIVE_BEHAVIOR,
    description:
      'Tests measuring everyday adaptive functioning and life skills',
    sortOrder: 5,
    isActive: true,
  },
];

export const TEST_BATTERIES = [
  // Cognitive Assessment
  {
    categoryName: 'Cognitive Assessment',
    name: 'Wechsler Intelligence Scale for Children, Fifth Edition',
    code: 'WISC-V',
    version: '5.0',
    publisher: 'Pearson',
    ageRangeMin: 72, // 6:0
    ageRangeMax: 192, // 16:0
    administrationTime: 65,
    description:
      'Comprehensive cognitive assessment measuring intellectual ability and cognitive domains',
    normingInformation: {
      normingSample: 2200,
      ageRange: '6:0-16:11',
      publicationYear: 2014,
      demographicVariables: [
        'age',
        'sex',
        'race/ethnicity',
        'parent_education',
        'geographic_region',
      ],
    },
    isActive: true,
  },
  {
    categoryName: 'Cognitive Assessment',
    name: 'Wechsler Adult Intelligence Scale, Fourth Edition',
    code: 'WAIS-IV',
    version: '4.0',
    publisher: 'Pearson',
    ageRangeMin: 192, // 16:0
    ageRangeMax: 1080, // 90:0
    administrationTime: 90,
    description:
      'Comprehensive cognitive assessment for adults measuring intellectual ability',
    normingInformation: {
      normingSample: 2200,
      ageRange: '16:0-90:11',
      publicationYear: 2008,
      demographicVariables: [
        'age',
        'sex',
        'race/ethnicity',
        'education',
        'geographic_region',
      ],
    },
    isActive: true,
  },
  {
    categoryName: 'Cognitive Assessment',
    name: 'Wechsler Preschool and Primary Scale of Intelligence, Fourth Edition',
    code: 'WPPSI-IV',
    version: '4.0',
    publisher: 'Pearson',
    ageRangeMin: 30, // 2:6
    ageRangeMax: 91, // 7:7
    administrationTime: 60,
    description:
      'Cognitive assessment for young children measuring intellectual ability',
    normingInformation: {
      normingSample: 1700,
      ageRange: '2:6-7:7',
      publicationYear: 2012,
      demographicVariables: [
        'age',
        'sex',
        'race/ethnicity',
        'parent_education',
        'geographic_region',
      ],
    },
    isActive: true,
  },
  {
    categoryName: 'Cognitive Assessment',
    name: 'Wechsler Adult Intelligence Scale, Fifth Edition',
    code: 'WAIS-V',
    version: '5.0',
    publisher: 'Pearson',
    ageRangeMin: 192,
    ageRangeMax: 1080,
    administrationTime: 75,
    description:
      'Updated adult cognitive assessment measuring intellectual ability and expanded indices',
    normingInformation: {
      normingSample: 2500,
      ageRange: '16:0-90:11',
      publicationYear: 2022,
      demographicVariables: [
        'age',
        'sex',
        'race/ethnicity',
        'education',
        'geographic_region',
      ],
    },
    isActive: true,
  },
  {
    categoryName: 'Cognitive Assessment',
    name: 'Reynolds Intellectual Assessment Scales, Second Edition',
    code: 'RIAS-2',
    version: '2.0',
    publisher: 'Pearson',
    ageRangeMin: 36, // 3:0
    ageRangeMax: 1128, // 94:0
    administrationTime: 25,
    description: 'Brief assessment measuring intelligence and memory indices',
    normingInformation: {
      normingSample: 1700,
      ageRange: '3:0-94:0',
      publicationYear: 2015,
      demographicVariables: [
        'age',
        'sex',
        'race/ethnicity',
        'education',
        'geographic_region',
      ],
    },
    isActive: true,
  },
  {
    categoryName: 'Cognitive Assessment',
    name: 'Ortiz Vocabulary Acquisition Test',
    code: 'PVAT',
    version: '1.0',
    publisher: 'Riverside Insights',
    ageRangeMin: 36,
    ageRangeMax: 1200, // 100:0
    administrationTime: 15,
    description:
      'Vocabulary acquisition assessment measuring word learning and vocabulary knowledge',
    normingInformation: {
      normingSample: 1500,
      ageRange: '3:0-100:0',
      publicationYear: 2001,
      demographicVariables: [
        'age',
        'sex',
        'race/ethnicity',
        'education',
        'geographic_region',
      ],
    },
    isActive: true,
  },
  {
    categoryName: 'Cognitive Assessment',
    name: 'Batería-IV (Cognitivas)',
    code: 'BATERIA-IV-COG',
    version: '1.0',
    publisher: 'Riverside Insights',
    ageRangeMin: 72,
    ageRangeMax: 192,
    administrationTime: 70,
    description: 'Spanish cognitive battery measuring broad abilities',
    normingInformation: {
      normingSample: 2000,
      ageRange: '6:0-16:11',
      publicationYear: 2020,
      demographicVariables: [
        'age',
        'sex',
        'race/ethnicity',
        'parent_education',
        'geographic_region',
      ],
    },
    isActive: true,
  },

  // Academic Achievement
  {
    categoryName: 'Academic Achievement',
    name: 'Wechsler Individual Achievement Test, Third Edition',
    code: 'WIAT-III',
    version: '3.0',
    publisher: 'Pearson',
    ageRangeMin: 48,
    ageRangeMax: 611,
    administrationTime: 90,
    description:
      'Comprehensive achievement test measuring academic skills in reading, writing, and mathematics',
    normingInformation: {
      normingSample: 2775,
      ageRange: '4:0-50:11',
      publicationYear: 2009,
      demographicVariables: [
        'age',
        'sex',
        'race/ethnicity',
        'parent_education',
        'geographic_region',
      ],
    },
    isActive: true,
  },
  {
    categoryName: 'Academic Achievement',
    name: 'Wechsler Individual Achievement Test, Fourth Edition',
    code: 'WIAT-IV',
    version: '4.0',
    publisher: 'Pearson',
    ageRangeMin: 48,
    ageRangeMax: 611,
    administrationTime: 120,
    description:
      'Updated achievement test measuring academic skills in reading, writing, and mathematics',
    normingInformation: {
      normingSample: 3700,
      ageRange: '4:0-50:11',
      publicationYear: 2020,
      demographicVariables: [
        'age',
        'sex',
        'race/ethnicity',
        'parent_education',
        'geographic_region',
      ],
    },
    isActive: true,
  },
  {
    categoryName: 'Academic Achievement',
    name: 'Woodcock-Johnson IV Tests of Achievement',
    code: 'WJ-IV ACH',
    version: '4.0',
    publisher: 'Riverside Insights',
    ageRangeMin: 24,
    ageRangeMax: 1080,
    administrationTime: 60,
    description:
      'Comprehensive achievement battery measuring academic skills and knowledge',
    normingInformation: {
      normingSample: 7416,
      ageRange: '2:0-90+',
      publicationYear: 2014,
      demographicVariables: [
        'age',
        'sex',
        'race/ethnicity',
        'education',
        'geographic_region',
      ],
    },
    isActive: true,
  },
  {
    categoryName: 'Academic Achievement',
    name: 'Kaufman Test of Educational Achievement, Third Edition',
    code: 'KTEA-3',
    version: '3.0',
    publisher: 'Pearson',
    ageRangeMin: 48,
    ageRangeMax: 216,
    administrationTime: 60,
    description:
      'Assessment of academic skills in reading, mathematics, written language, and oral language',
    normingInformation: {
      normingSample: 3500,
      ageRange: '4:0-18:11',
      publicationYear: 2023,
      demographicVariables: [
        'age',
        'sex',
        'race/ethnicity',
        'education',
        'geographic_region',
      ],
    },
    isActive: true,
  },
  {
    categoryName: 'Academic Achievement',
    name: 'Batería-IV (Aprovechamiento)',
    code: 'BATERIA-IV-AP',
    version: '1.0',
    publisher: 'Riverside Insights',
    ageRangeMin: 48,
    ageRangeMax: 611,
    administrationTime: 100,
    description: 'Spanish academic battery measuring specific skill indices',
    normingInformation: {
      normingSample: 2300,
      ageRange: '4:0-50:11',
      publicationYear: 2020,
      demographicVariables: [
        'age',
        'sex',
        'race/ethnicity',
        'parent_education',
        'geographic_region',
      ],
    },
    isActive: true,
  },

  // Social-Emotional Assessment
  {
    categoryName: 'Social-Emotional Assessment',
    name: 'Behavior Assessment System for Children, Third Edition',
    code: 'BASC-3',
    version: '3.0',
    publisher: 'Pearson',
    ageRangeMin: 24,
    ageRangeMax: 252,
    administrationTime: 30,
    description:
      'Comprehensive assessment of emotional and behavioral functioning',
    normingInformation: {
      normingSample: 3400,
      ageRange: '2:0-21:11',
      publicationYear: 2015,
      demographicVariables: [
        'age',
        'sex',
        'race/ethnicity',
        'parent_education',
        'geographic_region',
      ],
    },
    isActive: true,
  },
  {
    categoryName: 'Social-Emotional Assessment',
    name: 'Behavior Rating Inventory of Executive Function, Second Edition',
    code: 'BRIEF-2',
    version: '2.0',
    publisher: 'PAR',
    ageRangeMin: 60,
    ageRangeMax: 264,
    administrationTime: 20,
    description:
      'Assessment of executive function behaviors in home and school environments',
    normingInformation: {
      normingSample: 3000,
      ageRange: '5:0-22:11',
      publicationYear: 2015,
      demographicVariables: [
        'age',
        'sex',
        'race/ethnicity',
        'education',
        'geographic_region',
      ],
    },
    isActive: true,
  },
  {
    categoryName: 'Social-Emotional Assessment',
    name: 'Conners Fourth Edition Short Form',
    code: 'CON4-S',
    version: '4.0',
    publisher: 'MHS',
    ageRangeMin: 72,
    ageRangeMax: 228,
    administrationTime: 15,
    description:
      'Brief screening tool for ADHD and behavioral issues in children and adolescents',
    normingInformation: {
      normingSample: 2000,
      ageRange: '6:0-19:0',
      publicationYear: 2015,
      demographicVariables: ['age', 'sex', 'race/ethnicity'],
    },
    isActive: true,
  },
  {
    categoryName: 'Social-Emotional Assessment',
    name: 'Conners Fourth Edition Long Form',
    code: 'CON4-L',
    version: '4.0',
    publisher: 'MHS',
    ageRangeMin: 72,
    ageRangeMax: 216,
    administrationTime: 30,
    description:
      'Comprehensive assessment for ADHD and behavioral issues in children and adolescents',
    normingInformation: {
      normingSample: 3000,
      ageRange: '6:0-18:11',
      publicationYear: 2015,
      demographicVariables: ['age', 'sex', 'race/ethnicity'],
    },
    isActive: true,
  },
  {
    categoryName: 'Social-Emotional Assessment',
    name: 'Adult ADHD Self-Report Scale',
    code: 'ASRS',
    version: '1.1',
    publisher: 'WHO',
    ageRangeMin: 216,
    ageRangeMax: 1080,
    administrationTime: 5,
    description: 'Screening tool for ADHD symptoms in adults',
    normingInformation: {
      normingSample: 1000,
      ageRange: '18:0-90:11',
      publicationYear: 2005,
      demographicVariables: ['age', 'sex'],
    },
    isActive: true,
  },
  {
    categoryName: 'Social-Emotional Assessment',
    name: 'Beck Depression Inventory, Second Edition',
    code: 'BDI-II',
    version: '2.0',
    publisher: 'Pearson',
    ageRangeMin: 216,
    ageRangeMax: 1080,
    administrationTime: 10,
    description:
      'Self-report inventory measuring characteristic attitudes and symptoms of depression',
    normingInformation: {
      normingSample: 3000,
      ageRange: '17:0-90:11',
      publicationYear: 1996,
      demographicVariables: ['age', 'sex'],
    },
    isActive: true,
  },

  // Adaptive Behavior
  {
    categoryName: 'Adaptive Behavior',
    name: 'Adaptive Behavior Assessment System, Third Edition',
    code: 'ABAS-3',
    version: '3.0',
    publisher: 'Pearson',
    ageRangeMin: 6, // 0:6
    ageRangeMax: 266, // 22:2
    administrationTime: 30,
    description:
      'Assessment of adaptive behaviors across communication, social, and practical domains',
    normingInformation: {
      normingSample: 5000,
      ageRange: '0:6-21:11',
      publicationYear: 2016,
      demographicVariables: ['age', 'sex', 'race/ethnicity'],
    },
    isActive: true,
  },
];

export const TEST_INDICES = [
  // WISC-V Primary Indices
  {
    batteryCode: 'WISC-V',
    name: 'Verbal Comprehension Index',
    code: 'VCI',
    indexType: IndexTypeEnum.PRIMARY,
    description: 'Measures verbal concept formation and verbal reasoning',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines:
      'Measures crystallized intelligence, verbal concept formation, and verbal reasoning abilities',
    sortOrder: 1,
    isActive: true,
  },
  {
    batteryCode: 'WISC-V',
    name: 'Visual Spatial Index',
    code: 'VSI',
    indexType: IndexTypeEnum.PRIMARY,
    description:
      'Measures visual spatial processing and visual-motor integration',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines:
      'Measures visual spatial processing, visual perception, and visual-motor integration',
    sortOrder: 2,
    isActive: true,
  },
  {
    batteryCode: 'WISC-V',
    name: 'Fluid Reasoning Index',
    code: 'FRI',
    indexType: IndexTypeEnum.PRIMARY,
    description: 'Measures logical reasoning and novel problem-solving',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines:
      'Measures fluid intelligence, logical reasoning, and novel problem-solving abilities',
    sortOrder: 3,
    isActive: true,
  },
  {
    batteryCode: 'WISC-V',
    name: 'Working Memory Index',
    code: 'WMI',
    indexType: IndexTypeEnum.PRIMARY,
    description: 'Measures working memory capacity and mental manipulation',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines:
      'Measures working memory, attention, and mental manipulation abilities',
    sortOrder: 4,
    isActive: true,
  },
  {
    batteryCode: 'WISC-V',
    name: 'Processing Speed Index',
    code: 'PSI',
    indexType: IndexTypeEnum.PRIMARY,
    description: 'Measures processing speed and visual-motor coordination',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines:
      'Measures processing speed, visual scanning, and visual-motor coordination',
    sortOrder: 5,
    isActive: true,
  },
  {
    batteryCode: 'WISC-V',
    name: 'Full Scale IQ',
    code: 'FSIQ',
    indexType: IndexTypeEnum.COMPOSITE,
    description: 'Overall measure of general intellectual ability',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines:
      'Represents general intellectual ability based on performance across cognitive domains',
    sortOrder: 0,
    isActive: true,
  },

  // WISC-V Ancillary Indices
  {
    batteryCode: 'WISC-V',
    name: 'Quantitative Reasoning Index',
    code: 'QRI',
    indexType: IndexTypeEnum.ANCILLARY,
    description: 'Measures quantitative reasoning and mathematical thinking',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines:
      'Measures quantitative reasoning, mathematical thinking, and numerical problem-solving',
    sortOrder: 6,
    isActive: true,
  },
  {
    batteryCode: 'WISC-V',
    name: 'Auditory Working Memory Index',
    code: 'AWMI',
    indexType: IndexTypeEnum.ANCILLARY,
    description: 'Measures auditory working memory capacity',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines:
      'Measures auditory working memory and sequential processing abilities',
    sortOrder: 7,
    isActive: true,
  },
  {
    batteryCode: 'WISC-V',
    name: 'Nonverbal Index',
    code: 'NVI',
    indexType: IndexTypeEnum.ANCILLARY,
    description: 'Measures nonverbal intellectual ability',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines:
      'Measures nonverbal intellectual ability for children with language or hearing difficulties',
    sortOrder: 8,
    isActive: true,
  },
  {
    batteryCode: 'WISC-V',
    name: 'General Ability Index',
    code: 'GAI',
    indexType: IndexTypeEnum.ANCILLARY,
    description:
      'Measures general intellectual ability excluding working memory and processing speed',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines:
      'Measures general ability while minimizing impact of working memory and processing speed',
    sortOrder: 9,
    isActive: true,
  },
  {
    batteryCode: 'WISC-V',
    name: 'Cognitive Proficiency Index',
    code: 'CPI',
    indexType: IndexTypeEnum.ANCILLARY,
    description: 'Measures cognitive efficiency and proficiency',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines:
      'Measures cognitive efficiency combining working memory and processing speed',
    sortOrder: 10,
    isActive: true,
  },

  // WAIS-V (same indices as WISC-V)
  {
    batteryCode: 'WAIS-V',
    name: 'Verbal Comprehension Index',
    code: 'VCI',
    indexType: IndexTypeEnum.PRIMARY,
    description: 'Adult measure of verbal concept formation and reasoning',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines:
      'Measures adult crystallized intelligence, concept formation, and verbal reasoning',
    sortOrder: 1,
    isActive: true,
  },
  {
    batteryCode: 'WAIS-V',
    name: 'Visual Spatial Index',
    code: 'VSI',
    indexType: IndexTypeEnum.PRIMARY,
    description: 'Adult measure of visual spatial processing and integration',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines:
      'Measures adult visual spatial processing and visual-motor integration',
    sortOrder: 2,
    isActive: true,
  },
  {
    batteryCode: 'WAIS-V',
    name: 'Fluid Reasoning Index',
    code: 'FRI',
    indexType: IndexTypeEnum.PRIMARY,
    description: 'Adult measure of logical reasoning and novel problem-solving',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines:
      'Measures adult fluid intelligence, logical reasoning, and novel problem-solving',
    sortOrder: 3,
    isActive: true,
  },
  {
    batteryCode: 'WAIS-V',
    name: 'Working Memory Index',
    code: 'WMI',
    indexType: IndexTypeEnum.PRIMARY,
    description: 'Adult measure of working memory capacity',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines:
      'Measures adult working memory and attention abilities',
    sortOrder: 4,
    isActive: true,
  },
  {
    batteryCode: 'WAIS-V',
    name: 'Processing Speed Index',
    code: 'PSI',
    indexType: IndexTypeEnum.PRIMARY,
    description: 'Adult measure of processing speed and coordination',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines:
      'Measures adult processing speed and visual-motor coordination',
    sortOrder: 5,
    isActive: true,
  },
  {
    batteryCode: 'WAIS-V',
    name: 'Full Scale IQ',
    code: 'FSIQ',
    indexType: IndexTypeEnum.COMPOSITE,
    description: 'Overall adult intellectual ability',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines:
      'Represents adult general intellectual ability across domains',
    sortOrder: 0,
    isActive: true,
  },

  // RIAS-2 Indices
  {
    batteryCode: 'RIAS-2',
    name: 'Verbal Intelligence Index',
    code: 'VIX',
    indexType: IndexTypeEnum.PRIMARY,
    description: 'Measures verbal reasoning and vocabulary',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines:
      'Measures verbal reasoning, vocabulary, and comprehension',
    sortOrder: 1,
    isActive: true,
  },
  {
    batteryCode: 'RIAS-2',
    name: 'Nonverbal Intelligence Index',
    code: 'NIX',
    indexType: IndexTypeEnum.PRIMARY,
    description: 'Measures nonverbal reasoning and pattern recognition',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines:
      'Measures nonverbal problem-solving and pattern recognition',
    sortOrder: 2,
    isActive: true,
  },
  {
    batteryCode: 'RIAS-2',
    name: 'Memory Index',
    code: 'MI',
    indexType: IndexTypeEnum.PRIMARY,
    description: 'Measures immediate and delayed memory',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines:
      'Measures short-term, long-term, and sequential memory',
    sortOrder: 3,
    isActive: true,
  },
  {
    batteryCode: 'RIAS-2',
    name: 'Composite Intelligence Index',
    code: 'CIX',
    indexType: IndexTypeEnum.COMPOSITE,
    description: 'Overall intellectual ability based on RIAS-2',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines:
      'Represents general intellectual ability from RIAS-2 subtests',
    sortOrder: 0,
    isActive: true,
  },

  // PVAT Index
  {
    batteryCode: 'PVAT',
    name: 'Vocabulary Acquisition Index',
    code: 'VAI',
    indexType: IndexTypeEnum.COMPOSITE,
    description: 'Measures vocabulary acquisition and word learning',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines:
      'Represents vocabulary knowledge and acquisition ability',
    sortOrder: 0,
    isActive: true,
  },

  // WIAT-III Composites and Primaries
  {
    batteryCode: 'WIAT-III',
    name: 'Total Achievement',
    code: 'TOTAL',
    indexType: IndexTypeEnum.COMPOSITE,
    description: 'Overall measure of academic achievement',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines:
      'Represents overall academic achievement across reading, writing, and mathematics',
    sortOrder: 0,
    isActive: true,
  },
  {
    batteryCode: 'WIAT-III',
    name: 'Reading',
    code: 'READ',
    indexType: IndexTypeEnum.PRIMARY,
    description: 'Measures reading decoding and comprehension',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines:
      'Measures reading decoding, fluency, and comprehension skills',
    sortOrder: 1,
    isActive: true,
  },
  {
    batteryCode: 'WIAT-III',
    name: 'Mathematics',
    code: 'MATH',
    indexType: IndexTypeEnum.PRIMARY,
    description: 'Measures mathematical reasoning and computation',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines:
      'Measures mathematical problem-solving and computational skills',
    sortOrder: 2,
    isActive: true,
  },
  {
    batteryCode: 'WIAT-III',
    name: 'Written Expression',
    code: 'WRIT',
    indexType: IndexTypeEnum.PRIMARY,
    description: 'Measures writing mechanics and composition',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines:
      'Measures written expression, spelling, and writing mechanics',
    sortOrder: 3,
    isActive: true,
  },
  {
    batteryCode: 'WIAT-III',
    name: 'Oral Language',
    code: 'ORAL',
    indexType: IndexTypeEnum.PRIMARY,
    description: 'Measures listening comprehension and oral expression',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines:
      'Measures listening comprehension and oral expression abilities',
    sortOrder: 4,
    isActive: true,
  },

  // WIAT-IV (same as WIAT-III)
  {
    batteryCode: 'WIAT-IV',
    name: 'Total Achievement',
    code: 'TOTAL',
    indexType: IndexTypeEnum.COMPOSITE,
    description: 'Overall measure of academic achievement (WIAT-IV)',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines:
      'Represents overall academic achievement across all WIAT-IV subtests',
    sortOrder: 0,
    isActive: true,
  },
  {
    batteryCode: 'WIAT-IV',
    name: 'Reading',
    code: 'READ',
    indexType: IndexTypeEnum.PRIMARY,
    description: 'Measures reading decoding and comprehension (WIAT-IV)',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines:
      'Measures reading decoding, fluency, and comprehension skills',
    sortOrder: 1,
    isActive: true,
  },
  {
    batteryCode: 'WIAT-IV',
    name: 'Mathematics',
    code: 'MATH',
    indexType: IndexTypeEnum.PRIMARY,
    description: 'Measures mathematical reasoning and computation (WIAT-IV)',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines:
      'Measures mathematical problem-solving and computational skills',
    sortOrder: 2,
    isActive: true,
  },
  {
    batteryCode: 'WIAT-IV',
    name: 'Written Expression',
    code: 'WRIT',
    indexType: IndexTypeEnum.PRIMARY,
    description: 'Measures writing mechanics and composition (WIAT-IV)',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines:
      'Measures written expression, spelling, and writing mechanics',
    sortOrder: 3,
    isActive: true,
  },
  {
    batteryCode: 'WIAT-IV',
    name: 'Oral Language',
    code: 'ORAL',
    indexType: IndexTypeEnum.PRIMARY,
    description:
      'Measures listening comprehension and oral expression (WIAT-IV)',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines:
      'Measures listening comprehension and oral expression abilities',
    sortOrder: 4,
    isActive: true,
  },

  // KTEA-3 Indices
  {
    batteryCode: 'KTEA-3',
    name: 'Total Achievement',
    code: 'TOTAL',
    indexType: IndexTypeEnum.COMPOSITE,
    description: 'Overall academic achievement (KTEA-3)',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines: 'Represents overall academic achievement',
    sortOrder: 0,
    isActive: true,
  },
  {
    batteryCode: 'KTEA-3',
    name: 'Reading Composite',
    code: 'READ',
    indexType: IndexTypeEnum.PRIMARY,
    description: 'Measures basic reading skills (KTEA-3)',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines: 'Measures reading decoding and fluency',
    sortOrder: 1,
    isActive: true,
  },
  {
    batteryCode: 'KTEA-3',
    name: 'Mathematics Composite',
    code: 'MATH',
    indexType: IndexTypeEnum.PRIMARY,
    description: 'Measures mathematical reasoning and computation (KTEA-3)',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines: 'Measures math computation and problem solving',
    sortOrder: 2,
    isActive: true,
  },
  {
    batteryCode: 'KTEA-3',
    name: 'Written Language Composite',
    code: 'WRIT',
    indexType: IndexTypeEnum.PRIMARY,
    description: 'Measures writing skills (KTEA-3)',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines: 'Measures written expression and mechanics',
    sortOrder: 3,
    isActive: true,
  },
  {
    batteryCode: 'KTEA-3',
    name: 'Oral Language Composite',
    code: 'ORAL',
    indexType: IndexTypeEnum.PRIMARY,
    description: 'Measures oral language skills (KTEA-3)',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines: 'Measures listening and expressive language',
    sortOrder: 4,
    isActive: true,
  },

  // Spanish Cognitive Battery Indices
  {
    batteryCode: 'BATERIA-IV-COG',
    name: 'Full Scale Intellectual Ability',
    code: 'GIA',
    indexType: IndexTypeEnum.COMPOSITE,
    description: 'Overall measure of general intellectual ability',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines: 'Composite of all cognitive broad abilities',
    sortOrder: 0,
    isActive: true,
  },
  {
    batteryCode: 'BATERIA-IV-COG',
    name: 'Comprehension-Knowledge (Gc)',
    code: 'Gc',
    indexType: IndexTypeEnum.PRIMARY,
    description: 'Measures acquired knowledge and verbal comprehension',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines: 'Assesses verbal knowledge and concept formation',
    sortOrder: 1,
    isActive: true,
  },
  {
    batteryCode: 'BATERIA-IV-COG',
    name: 'Fluid Reasoning (Gf)',
    code: 'Gf',
    indexType: IndexTypeEnum.PRIMARY,
    description: 'Measures novel problem-solving and reasoning',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines:
      'Assesses abstract reasoning without prior knowledge',
    sortOrder: 2,
    isActive: true,
  },
  {
    batteryCode: 'BATERIA-IV-COG',
    name: 'Working Memory (Gwm)',
    code: 'Gwm',
    indexType: IndexTypeEnum.PRIMARY,
    description: 'Measures short-term memory and mental manipulation',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines: 'Assesses sequential processing and attention',
    sortOrder: 3,
    isActive: true,
  },
  {
    batteryCode: 'BATERIA-IV-COG',
    name: 'Processing Speed (Gs)',
    code: 'Gs',
    indexType: IndexTypeEnum.PRIMARY,
    description: 'Measures speed of visual scanning and decision making',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines: 'Assesses quick visual-motor responses',
    sortOrder: 4,
    isActive: true,
  },
  {
    batteryCode: 'BATERIA-IV-COG',
    name: 'Auditory Processing (Ga)',
    code: 'Ga',
    indexType: IndexTypeEnum.PRIMARY,
    description: 'Measures processing of auditory information',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines: 'Assesses sound-symbol recognition and memory',
    sortOrder: 5,
    isActive: true,
  },
  {
    batteryCode: 'BATERIA-IV-COG',
    name: 'Visual Processing (Gv)',
    code: 'Gv',
    indexType: IndexTypeEnum.PRIMARY,
    description: 'Measures visual perception and spatial reasoning',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines:
      'Assesses visual discrimination and spatial reasoning',
    sortOrder: 6,
    isActive: true,
  },
  {
    batteryCode: 'BATERIA-IV-COG',
    name: 'Brief Intellectual Ability',
    code: 'BIA',
    indexType: IndexTypeEnum.ANCILLARY,
    description: 'Abbreviated measure of general cognitive ability',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines: 'Composite of core subtests for brief assessment',
    sortOrder: 7,
    isActive: true,
  },

  // Spanish Academic Battery Indices
  {
    batteryCode: 'BATERIA-IV-AP',
    name: 'Broad Achievement',
    code: 'BA',
    indexType: IndexTypeEnum.COMPOSITE,
    description: 'Overall measure of academic skill',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines: 'Composite across all academic subtests',
    sortOrder: 0,
    isActive: true,
  },
  {
    batteryCode: 'BATERIA-IV-AP',
    name: 'Basic Reading Skills',
    code: 'BRS',
    indexType: IndexTypeEnum.COMPOSITE,
    description: 'Measures decoding and word recognition',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines: 'Composite of IP and AP subtests',
    sortOrder: 1,
    isActive: true,
  },
  {
    batteryCode: 'BATERIA-IV-AP',
    name: 'Reading Comprehension',
    code: 'RC',
    indexType: IndexTypeEnum.COMPOSITE,
    description: 'Measures understanding of written text',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines: 'Composite of CT and RL subtests',
    sortOrder: 2,
    isActive: true,
  },
  {
    batteryCode: 'BATERIA-IV-AP',
    name: 'Reading Fluency',
    code: 'RF',
    indexType: IndexTypeEnum.COMPOSITE,
    description: 'Measures speed and accuracy of reading',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines: 'Composite of LO and FLF subtests',
    sortOrder: 3,
    isActive: true,
  },
  {
    batteryCode: 'BATERIA-IV-AP',
    name: 'Math Calculation Skills',
    code: 'MCS',
    indexType: IndexTypeEnum.COMPOSITE,
    description: 'Measures basic calculation ability',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines: 'Composite of CAL and FDM subtests',
    sortOrder: 4,
    isActive: true,
  },
  {
    batteryCode: 'BATERIA-IV-AP',
    name: 'Math Problem Solving',
    code: 'MPS',
    indexType: IndexTypeEnum.COMPOSITE,
    description: 'Measures applied math reasoning',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines: 'Composite of PA and NM subtests',
    sortOrder: 5,
    isActive: true,
  },
  {
    batteryCode: 'BATERIA-IV-AP',
    name: 'Academic Fluency',
    code: 'AF',
    indexType: IndexTypeEnum.COMPOSITE,
    description: 'Measures fluency across domains',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines: 'Composite of RF, MCS, and FEF subtests',
    sortOrder: 6,
    isActive: true,
  },
  {
    batteryCode: 'BATERIA-IV-AP',
    name: 'Academic Applications',
    code: 'AA',
    indexType: IndexTypeEnum.COMPOSITE,
    description: 'Measures application of academic skills',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines: 'Composite of RC, MPS, and ELE subtests',
    sortOrder: 7,
    isActive: true,
  },
  {
    batteryCode: 'BATERIA-IV-AP',
    name: 'Brief Achievement',
    code: 'BAJ',
    indexType: IndexTypeEnum.COMPOSITE,
    description: 'Abbreviated measure of academic achievement',
    scoreRangeMin: 40,
    scoreRangeMax: 160,
    meanScore: 100,
    standardDeviation: 15,
    interpretiveGuidelines: 'Composite of IP, PA, and ELE subtests',
    sortOrder: 8,
    isActive: true,
  },
];

export const SUBTESTS = [
  // WISC-V Core
  {
    batteryCode: 'WISC-V',
    name: 'Similarities',
    code: 'SI',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Verbal concept formation and abstract reasoning',
    measuredAbilities: [
      'verbal_reasoning',
      'concept_formation',
      'abstract_thinking',
    ],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'How are these two things alike?',
    sortOrder: 1,
    isActive: true,
  },
  {
    batteryCode: 'WISC-V',
    name: 'Vocabulary',
    code: 'VC',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Word knowledge and verbal concept formation',
    measuredAbilities: [
      'vocabulary',
      'verbal_knowledge',
      'crystallized_intelligence',
    ],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Define words presented orally and visually',
    sortOrder: 2,
    isActive: true,
  },
  {
    batteryCode: 'WISC-V',
    name: 'Block Design',
    code: 'BD',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Visual-spatial processing and visual-motor coordination',
    measuredAbilities: ['visual_spatial', 'visual_motor', 'construction'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Construct designs with colored blocks',
    sortOrder: 3,
    isActive: true,
  },
  {
    batteryCode: 'WISC-V',
    name: 'Visual Puzzles',
    code: 'VP',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Visual-spatial processing and mental rotation',
    measuredAbilities: ['visual_spatial', 'mental_rotation', 'visual_analysis'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Select pieces to complete a puzzle',
    sortOrder: 4,
    isActive: true,
  },
  {
    batteryCode: 'WISC-V',
    name: 'Matrix Reasoning',
    code: 'MR',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Fluid reasoning and pattern recognition',
    measuredAbilities: [
      'fluid_reasoning',
      'pattern_recognition',
      'visual_processing',
    ],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Choose the missing piece in a matrix',
    sortOrder: 5,
    isActive: true,
  },
  {
    batteryCode: 'WISC-V',
    name: 'Figure Weights',
    code: 'FW',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Quantitative reasoning and logical thinking',
    measuredAbilities: [
      'quantitative_reasoning',
      'logical_thinking',
      'fluid_reasoning',
    ],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Select the picture that balances the scale',
    sortOrder: 6,
    isActive: true,
  },
  {
    batteryCode: 'WISC-V',
    name: 'Digit Span',
    code: 'DS',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Working memory and auditory processing',
    measuredAbilities: ['working_memory', 'auditory_processing', 'attention'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions:
      'Repeat number sequences forward, backward, and in order',
    sortOrder: 7,
    isActive: true,
  },
  {
    batteryCode: 'WISC-V',
    name: 'Picture Span',
    code: 'PS',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Visual working memory',
    measuredAbilities: [
      'visual_working_memory',
      'visual_attention',
      'sequential_processing',
    ],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Recall sequence of pictures in order',
    sortOrder: 8,
    isActive: true,
  },
  {
    batteryCode: 'WISC-V',
    name: 'Coding',
    code: 'CD',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Processing speed and visual-motor coordination',
    measuredAbilities: ['processing_speed', 'visual_motor', 'attention'],
    timeLimitMinutes: 2,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Copy symbols per key under time limit',
    sortOrder: 9,
    isActive: true,
  },
  {
    batteryCode: 'WISC-V',
    name: 'Symbol Search',
    code: 'SS',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Processing speed and visual perception',
    measuredAbilities: ['processing_speed', 'visual_perception', 'attention'],
    timeLimitMinutes: 2,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Search for target symbols in groups',
    sortOrder: 10,
    isActive: true,
  },

  // WISC-V Supplemental
  {
    batteryCode: 'WISC-V',
    name: 'Information',
    code: 'IN',
    subtestType: SubtestTypeEnum.SUPPLEMENTAL,
    description: 'General knowledge and long-term memory',
    measuredAbilities: [
      'general_knowledge',
      'long_term_memory',
      'crystallized_intelligence',
    ],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Ask questions about world knowledge',
    sortOrder: 11,
    isActive: true,
  },
  {
    batteryCode: 'WISC-V',
    name: 'Comprehension',
    code: 'CO',
    subtestType: SubtestTypeEnum.SUPPLEMENTAL,
    description: 'Practical reasoning and social judgment',
    measuredAbilities: [
      'practical_reasoning',
      'social_judgment',
      'common_sense',
    ],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Discuss social scenarios and reasoning',
    sortOrder: 12,
    isActive: true,
  },
  {
    batteryCode: 'WISC-V',
    name: 'Picture Concepts',
    code: 'PCn',
    subtestType: SubtestTypeEnum.SUPPLEMENTAL,
    description: 'Fluid reasoning and categorical thinking',
    measuredAbilities: [
      'fluid_reasoning',
      'categorical_thinking',
      'concept_formation',
    ],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions:
      'Select pictures that conceptually belong together',
    sortOrder: 13,
    isActive: true,
  },
  {
    batteryCode: 'WISC-V',
    name: 'Arithmetic',
    code: 'AR',
    subtestType: SubtestTypeEnum.SUPPLEMENTAL,
    description: 'Mathematical reasoning and working memory',
    measuredAbilities: [
      'mathematical_reasoning',
      'working_memory',
      'mental_computation',
    ],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Solve mental arithmetic problems orally',
    sortOrder: 14,
    isActive: true,
  },
  {
    batteryCode: 'WISC-V',
    name: 'Letter-Number Sequencing',
    code: 'LN',
    subtestType: SubtestTypeEnum.SUPPLEMENTAL,
    description: 'Working memory and mental flexibility',
    measuredAbilities: ['working_memory', 'mental_flexibility', 'sequencing'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Reorder mixed letters and numbers',
    sortOrder: 15,
    isActive: true,
  },
  {
    batteryCode: 'WISC-V',
    name: 'Cancellation',
    code: 'CA',
    subtestType: SubtestTypeEnum.SUPPLEMENTAL,
    description: 'Processing speed and selective attention',
    measuredAbilities: [
      'processing_speed',
      'visual_attention',
      'selective_attention',
    ],
    timeLimitMinutes: 2,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Mark target images under time limit',
    sortOrder: 16,
    isActive: true,
  },

  // WAIS-IV Primary :contentReference[oaicite:0]{index=0}
  {
    batteryCode: 'WAIS-IV',
    name: 'Block Design',
    code: 'BD',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Spatial perception and visual-motor integration',
    measuredAbilities: ['visual_spatial', 'visual_motor', 'construction'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Reproduce designs with blocks',
    sortOrder: 1,
    isActive: true,
  },
  {
    batteryCode: 'WAIS-IV',
    name: 'Similarities',
    code: 'SI',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Verbal concept formation',
    measuredAbilities: ['verbal_reasoning', 'abstract_thinking'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'How are these alike?',
    sortOrder: 2,
    isActive: true,
  },
  {
    batteryCode: 'WAIS-IV',
    name: 'Digit Span',
    code: 'DS',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Working memory and attention',
    measuredAbilities: ['working_memory', 'attention'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Repeat digits forward and backward',
    sortOrder: 3,
    isActive: true,
  },
  {
    batteryCode: 'WAIS-IV',
    name: 'Matrix Reasoning',
    code: 'MR',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Nonverbal abstract problem solving',
    measuredAbilities: ['fluid_reasoning', 'pattern_recognition'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Select missing element in matrices',
    sortOrder: 4,
    isActive: true,
  },
  {
    batteryCode: 'WAIS-IV',
    name: 'Vocabulary',
    code: 'VC',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Word knowledge',
    measuredAbilities: ['vocabulary', 'verbal_knowledge'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Define words orally and visually',
    sortOrder: 5,
    isActive: true,
  },
  {
    batteryCode: 'WAIS-IV',
    name: 'Arithmetic',
    code: 'AR',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Mental arithmetic and working memory',
    measuredAbilities: ['mathematical_reasoning', 'working_memory'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Solve oral arithmetic problems',
    sortOrder: 6,
    isActive: true,
  },
  {
    batteryCode: 'WAIS-IV',
    name: 'Symbol Search',
    code: 'SS',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Processing speed and visual discrimination',
    measuredAbilities: ['processing_speed', 'visual_perception'],
    timeLimitMinutes: 2,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Scan symbols for matches',
    sortOrder: 7,
    isActive: true,
  },
  {
    batteryCode: 'WAIS-IV',
    name: 'Visual Puzzles',
    code: 'VP',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Spatial reasoning',
    measuredAbilities: ['visual_spatial', 'analysis'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Select puzzle pieces to match',
    sortOrder: 8,
    isActive: true,
  },
  {
    batteryCode: 'WAIS-IV',
    name: 'Information',
    code: 'IN',
    subtestType: SubtestTypeEnum.CORE,
    description: 'General knowledge',
    measuredAbilities: ['general_knowledge', 'long_term_memory'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Answer factual questions',
    sortOrder: 9,
    isActive: true,
  },
  {
    batteryCode: 'WAIS-IV',
    name: 'Coding',
    code: 'CD',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Processing speed',
    measuredAbilities: ['processing_speed', 'visual_motor'],
    timeLimitMinutes: 2,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Copy symbols per code key',
    sortOrder: 10,
    isActive: true,
  },

  // WAIS-IV Supplemental
  {
    batteryCode: 'WAIS-IV',
    name: 'Letter-Number Sequencing',
    code: 'LN',
    subtestType: SubtestTypeEnum.SUPPLEMENTAL,
    description: 'Working memory sequencing',
    measuredAbilities: ['working_memory', 'sequencing'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Reorder letters and numbers',
    sortOrder: 11,
    isActive: true,
  },
  {
    batteryCode: 'WAIS-IV',
    name: 'Figure Weights',
    code: 'FW',
    subtestType: SubtestTypeEnum.SUPPLEMENTAL,
    description: 'Quantitative reasoning',
    measuredAbilities: ['quantitative_reasoning', 'logical_thinking'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Balance scales with figures',
    sortOrder: 12,
    isActive: true,
  },
  {
    batteryCode: 'WAIS-IV',
    name: 'Comprehension',
    code: 'CM',
    subtestType: SubtestTypeEnum.SUPPLEMENTAL,
    description: 'Social judgment and common sense',
    measuredAbilities: ['social_judgment', 'common_sense'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Explain rules and social situations',
    sortOrder: 13,
    isActive: true,
  },
  {
    batteryCode: 'WAIS-IV',
    name: 'Cancellation',
    code: 'CA',
    subtestType: SubtestTypeEnum.SUPPLEMENTAL,
    description: 'Attention and processing speed',
    measuredAbilities: ['selective_attention', 'processing_speed'],
    timeLimitMinutes: 2,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Mark target shapes quickly',
    sortOrder: 14,
    isActive: true,
  },
  {
    batteryCode: 'WAIS-IV',
    name: 'Picture Completion',
    code: 'PC',
    subtestType: SubtestTypeEnum.SUPPLEMENTAL,
    description: 'Perceptual completion and detail recognition',
    measuredAbilities: ['visual_perception', 'detail_recognition'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Identify missing detail in pictures',
    sortOrder: 15,
    isActive: true,
  },

  // WAIS-V Primary :contentReference[oaicite:1]{index=1}
  {
    batteryCode: 'WAIS-V',
    name: 'Similarities',
    code: 'SI',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Verbal concept formation',
    measuredAbilities: ['verbal_reasoning', 'abstract_thinking'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'How are these alike?',
    sortOrder: 1,
    isActive: true,
  },
  {
    batteryCode: 'WAIS-V',
    name: 'Block Design',
    code: 'BD',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Visual-spatial processing',
    measuredAbilities: ['visual_spatial', 'visual_motor'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Reproduce patterns with blocks',
    sortOrder: 2,
    isActive: true,
  },
  {
    batteryCode: 'WAIS-V',
    name: 'Matrix Reasoning',
    code: 'MR',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Pattern completion and reasoning',
    measuredAbilities: ['fluid_reasoning', 'pattern_recognition'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Choose the missing element',
    sortOrder: 3,
    isActive: true,
  },
  {
    batteryCode: 'WAIS-V',
    name: 'Digit Sequencing',
    code: 'DSQ',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Working memory sequencing',
    measuredAbilities: ['working_memory', 'sequencing'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Repeat digits in sequence',
    sortOrder: 4,
    isActive: true,
  },
  {
    batteryCode: 'WAIS-V',
    name: 'Coding',
    code: 'CD',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Processing speed',
    measuredAbilities: ['processing_speed', 'visual_motor'],
    timeLimitMinutes: 2,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Copy symbols under time pressure',
    sortOrder: 5,
    isActive: true,
  },
  {
    batteryCode: 'WAIS-V',
    name: 'Vocabulary',
    code: 'VC',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Word knowledge',
    measuredAbilities: ['vocabulary', 'verbal_knowledge'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Define presented words',
    sortOrder: 6,
    isActive: true,
  },
  {
    batteryCode: 'WAIS-V',
    name: 'Figure Weights',
    code: 'FW',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Quantitative reasoning',
    measuredAbilities: ['quantitative_reasoning', 'logical_thinking'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Determine scale balance',
    sortOrder: 7,
    isActive: true,
  },
  {
    batteryCode: 'WAIS-V',
    name: 'Visual Puzzles',
    code: 'VP',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Mental rotation',
    measuredAbilities: ['visual_spatial', 'mental_rotation'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Select pieces to complete puzzle',
    sortOrder: 8,
    isActive: true,
  },
  {
    batteryCode: 'WAIS-V',
    name: 'Running Digits',
    code: 'RD',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Working memory and processing speed',
    measuredAbilities: ['working_memory', 'processing_speed'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Continue sequences aloud',
    sortOrder: 9,
    isActive: true,
  },
  {
    batteryCode: 'WAIS-V',
    name: 'Symbol Search',
    code: 'SS',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Visual scanning and speed',
    measuredAbilities: ['processing_speed', 'visual_perception'],
    timeLimitMinutes: 2,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Find matching symbols',
    sortOrder: 10,
    isActive: true,
  },

  // WAIS-V Supplemental :contentReference[oaicite:2]{index=2}
  {
    batteryCode: 'WAIS-V',
    name: 'Digits Forward',
    code: 'DF',
    subtestType: SubtestTypeEnum.SUPPLEMENTAL,
    description: 'Immediate recall of digits in order',
    measuredAbilities: ['working_memory', 'attention'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Repeat digits in the same order',
    sortOrder: 11,
    isActive: true,
  },
  {
    batteryCode: 'WAIS-V',
    name: 'Information',
    code: 'IN',
    subtestType: SubtestTypeEnum.SUPPLEMENTAL,
    description: 'General factual knowledge',
    measuredAbilities: ['general_knowledge', 'long_term_memory'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Answer general knowledge questions',
    sortOrder: 12,
    isActive: true,
  },
  {
    batteryCode: 'WAIS-V',
    name: 'Arithmetic',
    code: 'AR',
    subtestType: SubtestTypeEnum.SUPPLEMENTAL,
    description: 'Mental arithmetic problems',
    measuredAbilities: ['mathematical_reasoning', 'working_memory'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Solve oral math questions',
    sortOrder: 13,
    isActive: true,
  },
  {
    batteryCode: 'WAIS-V',
    name: 'Digits Backward',
    code: 'DB',
    subtestType: SubtestTypeEnum.SUPPLEMENTAL,
    description: 'Reverse recall of digit sequences',
    measuredAbilities: ['working_memory', 'mental_flexibility'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Repeat digits backward',
    sortOrder: 14,
    isActive: true,
  },
  {
    batteryCode: 'WAIS-V',
    name: 'Symbol Span',
    code: 'SSP',
    subtestType: SubtestTypeEnum.SUPPLEMENTAL,
    description: 'Visual working memory',
    measuredAbilities: ['visual_working_memory', 'sequential_processing'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Reproduce symbol sequences',
    sortOrder: 15,
    isActive: true,
  },
  {
    batteryCode: 'WAIS-V',
    name: 'Naming Speed Quantity',
    code: 'NSQ',
    subtestType: SubtestTypeEnum.SUPPLEMENTAL,
    description: 'Speeded naming of quantities',
    measuredAbilities: ['processing_speed', 'verbal_fluency'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Name quantities quickly',
    sortOrder: 16,
    isActive: true,
  },
  {
    batteryCode: 'WAIS-V',
    name: 'Comprehension',
    code: 'CO',
    subtestType: SubtestTypeEnum.SUPPLEMENTAL,
    description: 'Social reasoning and judgment',
    measuredAbilities: ['social_judgment', 'practical_reasoning'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Explain social scenarios',
    sortOrder: 17,
    isActive: true,
  },
  {
    batteryCode: 'WAIS-V',
    name: 'Set Relations',
    code: 'SR',
    subtestType: SubtestTypeEnum.SUPPLEMENTAL,
    description: 'Complex reasoning with sets',
    measuredAbilities: ['abstract_reasoning', 'pattern_recognition'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Identify relationships among items',
    sortOrder: 18,
    isActive: true,
  },
  {
    batteryCode: 'WAIS-V',
    name: 'Spatial Addition',
    code: 'SA',
    subtestType: SubtestTypeEnum.SUPPLEMENTAL,
    description: 'Visuo-spatial working memory',
    measuredAbilities: ['visual_spatial', 'working_memory'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Combine patterns mentally',
    sortOrder: 19,
    isActive: true,
  },
  {
    batteryCode: 'WAIS-V',
    name: 'Letter-Number Sequencing',
    code: 'LN',
    subtestType: SubtestTypeEnum.SUPPLEMENTAL,
    description: 'Order manipulation of letters and numbers',
    measuredAbilities: ['working_memory', 'sequencing'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Reorder mixed letters and numbers',
    sortOrder: 20,
    isActive: true,
  },

  // PVAT (Ortiz Vocabulary Acquisition Test) :contentReference[oaicite:3]{index=3}
  {
    batteryCode: 'PVAT',
    name: 'Vocabulary Acquisition',
    code: 'VAT',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Measure of word learning and retention',
    measuredAbilities: ['vocabulary_acquisition', 'verbal_memory'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Learn and recall novel words',
    sortOrder: 1,
    isActive: true,
  },

  // RIAS-2 :contentReference[oaicite:4]{index=4}
  {
    batteryCode: 'RIAS-2',
    name: 'Guess What',
    code: 'GWH',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Picture-based verbal reasoning',
    measuredAbilities: ['verbal_reasoning', 'abstract_thinking'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Describe pictured scenes',
    sortOrder: 1,
    isActive: true,
  },
  {
    batteryCode: 'RIAS-2',
    name: 'Verbal Reasoning',
    code: 'VRZ',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Logical reasoning with words',
    measuredAbilities: ['verbal_reasoning', 'logic'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Answer verbal logic questions',
    sortOrder: 2,
    isActive: true,
  },
  {
    batteryCode: 'RIAS-2',
    name: 'Odd-Item Out',
    code: 'OIO',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Nonverbal pattern recognition',
    measuredAbilities: ['pattern_recognition', 'fluid_reasoning'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Identify the item that does not belong',
    sortOrder: 3,
    isActive: true,
  },
  {
    batteryCode: 'RIAS-2',
    name: "What's Missing",
    code: 'WHM',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Visual completion ability',
    measuredAbilities: ['visual_processing', 'perceptual_reasoning'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Point out missing parts in pictures',
    sortOrder: 4,
    isActive: true,
  },
  {
    batteryCode: 'RIAS-2',
    name: 'Verbal Memory',
    code: 'VRM',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Immediate and delayed verbal recall',
    measuredAbilities: ['verbal_memory', 'sequential_memory'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Recall word lists',
    sortOrder: 5,
    isActive: true,
  },
  {
    batteryCode: 'RIAS-2',
    name: 'Nonverbal Memory',
    code: 'NVM',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Visual memory and reproduction',
    measuredAbilities: ['visual_memory', 'sequential_memory'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Reproduce sequences of designs',
    sortOrder: 6,
    isActive: true,
  },
  {
    batteryCode: 'RIAS-2',
    name: 'Speeded Naming Task',
    code: 'SNT',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Rapid naming of objects',
    measuredAbilities: ['processing_speed', 'verbal_fluency'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Name pictured objects as quickly as possible',
    sortOrder: 7,
    isActive: true,
  },
  {
    batteryCode: 'RIAS-2',
    name: 'Speeded Picture Search',
    code: 'SPS',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Visual search and processing speed',
    measuredAbilities: ['processing_speed', 'visual_scanning'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Find target pictures quickly',
    sortOrder: 8,
    isActive: true,
  },

  // Spanish Batería-IV (Cognitivas)
  ...[
    'VC',
    'IG',
    'AK',
    'CF',
    'AS',
    'NS',
    'NR',
    'MW',
    'LPM',
    'PC',
    'PP',
    'NWR',
    'VZ',
    'PR',
  ].map((code, i) => ({
    batteryCode: 'BATERIA-IV-COG',
    name: code /* full names in Spanish omitted here */,
    code,
    subtestType: SubtestTypeEnum.CORE,
    description: `Spanish cognitive subtest ${code}`,
    measuredAbilities: [],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: '',
    sortOrder: i + 1,
    isActive: true,
  })),

  // WIAT-III Core
  {
    batteryCode: 'WIAT-III',
    name: 'Word Reading',
    code: 'WR',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Single word reading accuracy and fluency',
    measuredAbilities: ['word_reading', 'decoding', 'sight_vocabulary'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Read single words aloud',
    sortOrder: 1,
    isActive: true,
  },

  // WIAT-IV Core
  {
    batteryCode: 'WIAT-IV',
    name: 'Word Reading',
    code: 'WR',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Word reading accuracy',
    measuredAbilities: ['word_reading', 'decoding'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Read words aloud',
    sortOrder: 1,
    isActive: true,
  },
  {
    batteryCode: 'WIAT-IV',
    name: 'Reading Comprehension',
    code: 'RC',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Reading comprehension',
    measuredAbilities: ['reading_comprehension'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Read passages and answer questions',
    sortOrder: 2,
    isActive: true,
  },
  {
    batteryCode: 'WIAT-IV',
    name: 'Oral Reading Fluency',
    code: 'ORF',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Reading fluency',
    measuredAbilities: ['oral_reading_fluency'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Read sentences aloud quickly',
    sortOrder: 3,
    isActive: true,
  },
  {
    batteryCode: 'WIAT-IV',
    name: 'Pseudoword Decoding',
    code: 'PD',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Phonetic decoding',
    measuredAbilities: ['phonetic_decoding'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Read nonsense words aloud',
    sortOrder: 4,
    isActive: true,
  },
  {
    batteryCode: 'WIAT-IV',
    name: 'Decoding Fluency',
    code: 'DF',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Decoding fluency',
    measuredAbilities: ['decoding_fluency'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Decode words quickly',
    sortOrder: 5,
    isActive: true,
  },
  {
    batteryCode: 'WIAT-IV',
    name: 'Math Problem Solving',
    code: 'MPS',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Applied math reasoning',
    measuredAbilities: ['mathematical_reasoning'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Solve word problems',
    sortOrder: 6,
    isActive: true,
  },
  {
    batteryCode: 'WIAT-IV',
    name: 'Numerical Operations',
    code: 'NO',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Written math computation',
    measuredAbilities: ['mathematical_computation'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Solve written math problems',
    sortOrder: 7,
    isActive: true,
  },
  {
    batteryCode: 'WIAT-IV',
    name: 'Alphabet Writing Fluency',
    code: 'AWF',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Writing fluency',
    measuredAbilities: ['writing_fluency'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Write letters in sequence quickly',
    sortOrder: 8,
    isActive: true,
  },
  {
    batteryCode: 'WIAT-IV',
    name: 'Orthographic Fluency',
    code: 'OF',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Spelling fluency',
    measuredAbilities: ['orthographic_processing'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Write as many words as possible',
    sortOrder: 9,
    isActive: true,
  },
  {
    batteryCode: 'WIAT-IV',
    name: 'Orthographic Choice',
    code: 'OC',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Spelling accuracy',
    measuredAbilities: ['orthographic_processing'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Choose correct spellings',
    sortOrder: 10,
    isActive: true,
  },
  {
    batteryCode: 'WIAT-IV',
    name: 'Listening Comprehension',
    code: 'LC',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Auditory comprehension',
    measuredAbilities: ['listening_comprehension'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Listen and answer questions',
    sortOrder: 11,
    isActive: true,
  },
  {
    batteryCode: 'WIAT-IV',
    name: 'Oral Expression',
    code: 'OE',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Verbal expression',
    measuredAbilities: ['oral_expression'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Answer open-ended questions',
    sortOrder: 12,
    isActive: true,
  },

  // KTEA-3
  {
    batteryCode: 'KTEA-3',
    name: 'Letter & Word Recognition',
    code: 'LWR',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Identify letters/words',
    measuredAbilities: ['reading_accuracy'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Read letters and words',
    sortOrder: 1,
    isActive: true,
  },
  {
    batteryCode: 'KTEA-3',
    name: 'Nonsense Word Decoding',
    code: 'NWD',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Decode pseudowords',
    measuredAbilities: ['phonetic_decoding'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Read nonsense words',
    sortOrder: 2,
    isActive: true,
  },
  {
    batteryCode: 'KTEA-3',
    name: 'Silent Reading Fluency',
    code: 'SRF',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Reading speed',
    measuredAbilities: ['silent_reading_fluency'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Identify words quickly in text',
    sortOrder: 3,
    isActive: true,
  },
  {
    batteryCode: 'KTEA-3',
    name: 'Reading Comprehension',
    code: 'RC',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Reading comprehension',
    measuredAbilities: ['reading_comprehension'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Read passages and answer questions',
    sortOrder: 4,
    isActive: true,
  },
  {
    batteryCode: 'KTEA-3',
    name: 'Reading Vocabulary',
    code: 'RV',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Word meaning',
    measuredAbilities: ['vocabulary'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Choose word meanings',
    sortOrder: 5,
    isActive: true,
  },
  {
    batteryCode: 'KTEA-3',
    name: 'Word Recognition Fluency',
    code: 'WRF',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Oral reading fluency',
    measuredAbilities: ['oral_reading_fluency'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Read as many words as possible',
    sortOrder: 6,
    isActive: true,
  },
  {
    batteryCode: 'KTEA-3',
    name: 'Decoding Fluency',
    code: 'DF',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Decoding speed',
    measuredAbilities: ['decoding_fluency'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Decode words quickly',
    sortOrder: 7,
    isActive: true,
  },
  {
    batteryCode: 'KTEA-3',
    name: 'Math Concepts & Applications',
    code: 'MCA',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Applied math reasoning',
    measuredAbilities: ['math_reasoning'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Solve applied math problems',
    sortOrder: 8,
    isActive: true,
  },
  {
    batteryCode: 'KTEA-3',
    name: 'Math Computation',
    code: 'MC',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Written math computation',
    measuredAbilities: ['math_computation'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Complete written computation tasks',
    sortOrder: 9,
    isActive: true,
  },
  {
    batteryCode: 'KTEA-3',
    name: 'Writing Fluency',
    code: 'WF',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Written fluency',
    measuredAbilities: ['writing_fluency'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Write as many letters/words as possible',
    sortOrder: 10,
    isActive: true,
  },
  {
    batteryCode: 'KTEA-3',
    name: 'Written Expression',
    code: 'WE',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Composition skills',
    measuredAbilities: ['written_expression'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Compose written responses',
    sortOrder: 11,
    isActive: true,
  },
  {
    batteryCode: 'KTEA-3',
    name: 'Spelling',
    code: 'SP',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Spelling accuracy',
    measuredAbilities: ['spelling'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Spell dictated words',
    sortOrder: 12,
    isActive: true,
  },
  {
    batteryCode: 'KTEA-3',
    name: 'Phonological Processing',
    code: 'PP',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Phonological awareness',
    measuredAbilities: ['phonological_processing'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Manipulate phonemes in words',
    sortOrder: 13,
    isActive: true,
  },
  {
    batteryCode: 'KTEA-3',
    name: 'Associational Fluency',
    code: 'AF',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Verbal fluency',
    measuredAbilities: ['verbal_fluency'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Generate words in categories',
    sortOrder: 14,
    isActive: true,
  },
  {
    batteryCode: 'KTEA-3',
    name: 'Object Naming Facility',
    code: 'ONF',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Speeded naming',
    measuredAbilities: ['processing_speed'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Name pictured objects quickly',
    sortOrder: 15,
    isActive: true,
  },
  {
    batteryCode: 'KTEA-3',
    name: 'Letter Naming Facility',
    code: 'LNF',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Speeded letter naming',
    measuredAbilities: ['processing_speed'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Name letters quickly',
    sortOrder: 16,
    isActive: true,
  },
  {
    batteryCode: 'KTEA-3',
    name: 'Listening Comprehension',
    code: 'LC',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Auditory comprehension',
    measuredAbilities: ['listening_comprehension'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Listen and answer questions',
    sortOrder: 17,
    isActive: true,
  },
  {
    batteryCode: 'KTEA-3',
    name: 'Oral Expression',
    code: 'OE',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Verbal expression',
    measuredAbilities: ['oral_expression'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Express ideas verbally',
    sortOrder: 18,
    isActive: true,
  },

  // Spanish Batería-IV (Aprovechamiento)
  ...[
    'IP',
    'AP',
    'CT',
    'LO',
    'FLF',
    'RL',
    'PA',
    'CAL',
    'FDM',
    'NM',
    'ORT',
    'ELE',
    'FEF',
  ].map((code, i) => ({
    batteryCode: 'BATERIA-IV-AP',
    name: code,
    code,
    subtestType: SubtestTypeEnum.CORE,
    description: `Spanish academic subtest ${code}`,
    measuredAbilities: [],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: '',
    sortOrder: i + 1,
    isActive: true,
  })),
  // BASC-3 Rating Scales
  {
    batteryCode: 'BASC-3',
    name: 'Teacher Rating Scale',
    code: 'TRS',
    subtestType: SubtestTypeEnum.RATING_SCALE,
    description:
      'Teacher Rating Scale form for BASC-3 assessing emotional and behavioral functioning',
    measuredAbilities: [],
    timeLimitMinutes: null,
    scoreRangeMin: 0,
    scoreRangeMax: 100,
    meanScore: 50,
    standardDeviation: 10,
    administrationInstructions: 'Teacher completes the BASC-3 rating scale',
    sortOrder: 1,
    isActive: true,
  },
  {
    batteryCode: 'BASC-3',
    name: 'Parent Rating Scale',
    code: 'PRS',
    subtestType: SubtestTypeEnum.RATING_SCALE,
    description:
      'Parent Rating Scale form for BASC-3 assessing emotional and behavioral functioning',
    measuredAbilities: [],
    timeLimitMinutes: null,
    scoreRangeMin: 0,
    scoreRangeMax: 100,
    meanScore: 50,
    standardDeviation: 10,
    administrationInstructions: 'Parent completes the BASC-3 rating scale',
    sortOrder: 2,
    isActive: true,
  },
  {
    batteryCode: 'BASC-3',
    name: 'Self-Report',
    code: 'SRP',
    subtestType: SubtestTypeEnum.RATING_SCALE,
    description:
      'Self-Report form for BASC-3 assessing emotional and behavioral functioning',
    measuredAbilities: [],
    timeLimitMinutes: null,
    scoreRangeMin: 0,
    scoreRangeMax: 100,
    meanScore: 50,
    standardDeviation: 10,
    administrationInstructions: 'Individual completes the BASC-3 self-report',
    sortOrder: 3,
    isActive: true,
  },

  // BRIEF-2 Rating Scales
  {
    batteryCode: 'BRIEF-2',
    name: 'Teacher Rating Scale',
    code: 'TRS',
    subtestType: SubtestTypeEnum.RATING_SCALE,
    description:
      'Teacher Rating Scale form for BRIEF-2 assessing executive function behaviors',
    measuredAbilities: [],
    timeLimitMinutes: null,
    scoreRangeMin: 0,
    scoreRangeMax: 100,
    meanScore: 50,
    standardDeviation: 10,
    administrationInstructions: 'Teacher completes the BRIEF-2 rating form',
    sortOrder: 1,
    isActive: true,
  },
  {
    batteryCode: 'BRIEF-2',
    name: 'Parent Rating Scale',
    code: 'PRS',
    subtestType: SubtestTypeEnum.RATING_SCALE,
    description:
      'Parent Rating Scale form for BRIEF-2 assessing executive function behaviors',
    measuredAbilities: [],
    timeLimitMinutes: null,
    scoreRangeMin: 0,
    scoreRangeMax: 100,
    meanScore: 50,
    standardDeviation: 10,
    administrationInstructions: 'Parent completes the BRIEF-2 rating form',
    sortOrder: 2,
    isActive: true,
  },
  {
    batteryCode: 'BRIEF-2',
    name: 'Self-Report',
    code: 'SRP',
    subtestType: SubtestTypeEnum.RATING_SCALE,
    description:
      'Self-Report form for BRIEF-2 assessing executive function behaviors',
    measuredAbilities: [],
    timeLimitMinutes: null,
    scoreRangeMin: 0,
    scoreRangeMax: 100,
    meanScore: 50,
    standardDeviation: 10,
    administrationInstructions: 'Individual completes the BRIEF-2 self-report',
    sortOrder: 3,
    isActive: true,
  },

  // Conners 4 Short Form Rating Scales
  {
    batteryCode: 'CON4-S',
    name: 'Teacher Rating Scale',
    code: 'TRS',
    subtestType: SubtestTypeEnum.RATING_SCALE,
    description:
      'Teacher Rating Scale form for Conners 4 Short Form assessing ADHD and behavioral issues',
    measuredAbilities: [],
    timeLimitMinutes: null,
    scoreRangeMin: 0,
    scoreRangeMax: 100,
    meanScore: 50,
    standardDeviation: 10,
    administrationInstructions:
      'Teacher completes the Conners 4 Short Form rating scale',
    sortOrder: 1,
    isActive: true,
  },
  {
    batteryCode: 'CON4-S',
    name: 'Parent Rating Scale',
    code: 'PRS',
    subtestType: SubtestTypeEnum.RATING_SCALE,
    description:
      'Parent Rating Scale form for Conners 4 Short Form assessing ADHD and behavioral issues',
    measuredAbilities: [],
    timeLimitMinutes: null,
    scoreRangeMin: 0,
    scoreRangeMax: 100,
    meanScore: 50,
    standardDeviation: 10,
    administrationInstructions:
      'Parent completes the Conners 4 Short Form rating scale',
    sortOrder: 2,
    isActive: true,
  },
  {
    batteryCode: 'CON4-S',
    name: 'Self-Report',
    code: 'SRP',
    subtestType: SubtestTypeEnum.RATING_SCALE,
    description:
      'Self-Report form for Conners 4 Short Form assessing ADHD and behavioral issues',
    measuredAbilities: [],
    timeLimitMinutes: null,
    scoreRangeMin: 0,
    scoreRangeMax: 100,
    meanScore: 50,
    standardDeviation: 10,
    administrationInstructions:
      'Individual completes the Conners 4 Short Form self-report',
    sortOrder: 3,
    isActive: true,
  },

  // Conners 4 Long Form Rating Scales
  {
    batteryCode: 'CON4-L',
    name: 'Teacher Rating Scale',
    code: 'TRS',
    subtestType: SubtestTypeEnum.RATING_SCALE,
    description:
      'Teacher Rating Scale form for Conners 4 Long Form assessing ADHD and behavioral issues',
    measuredAbilities: [],
    timeLimitMinutes: null,
    scoreRangeMin: 0,
    scoreRangeMax: 100,
    meanScore: 50,
    standardDeviation: 10,
    administrationInstructions:
      'Teacher completes the Conners 4 Long Form rating scale',
    sortOrder: 1,
    isActive: true,
  },
  {
    batteryCode: 'CON4-L',
    name: 'Parent Rating Scale',
    code: 'PRS',
    subtestType: SubtestTypeEnum.RATING_SCALE,
    description:
      'Parent Rating Scale form for Conners 4 Long Form assessing ADHD and behavioral issues',
    measuredAbilities: [],
    timeLimitMinutes: null,
    scoreRangeMin: 0,
    scoreRangeMax: 100,
    meanScore: 50,
    standardDeviation: 10,
    administrationInstructions:
      'Parent completes the Conners 4 Long Form rating scale',
    sortOrder: 2,
    isActive: true,
  },
  {
    batteryCode: 'CON4-L',
    name: 'Self-Report',
    code: 'SRP',
    subtestType: SubtestTypeEnum.RATING_SCALE,
    description:
      'Self-Report form for Conners 4 Long Form assessing ADHD and behavioral issues',
    measuredAbilities: [],
    timeLimitMinutes: null,
    scoreRangeMin: 0,
    scoreRangeMax: 100,
    meanScore: 50,
    standardDeviation: 10,
    administrationInstructions:
      'Individual completes the Conners 4 Long Form self-report',
    sortOrder: 3,
    isActive: true,
  },

  // ASRS (Adult ADHD Self-Report Scale) Rating Scales
  {
    batteryCode: 'ASRS',
    name: 'Teacher Rating Scale',
    code: 'TRS',
    subtestType: SubtestTypeEnum.RATING_SCALE,
    description:
      'Teacher Rating Scale form for ASRS assessing adult ADHD symptoms',
    measuredAbilities: [],
    timeLimitMinutes: null,
    scoreRangeMin: 0,
    scoreRangeMax: 100,
    meanScore: 50,
    standardDeviation: 10,
    administrationInstructions: 'Teacher completes the ASRS rating scale',
    sortOrder: 1,
    isActive: true,
  },
  {
    batteryCode: 'ASRS',
    name: 'Parent Rating Scale',
    code: 'PRS',
    subtestType: SubtestTypeEnum.RATING_SCALE,
    description:
      'Parent Rating Scale form for ASRS assessing adult ADHD symptoms',
    measuredAbilities: [],
    timeLimitMinutes: null,
    scoreRangeMin: 0,
    scoreRangeMax: 100,
    meanScore: 50,
    standardDeviation: 10,
    administrationInstructions: 'Parent completes the ASRS rating scale',
    sortOrder: 2,
    isActive: true,
  },
  {
    batteryCode: 'ASRS',
    name: 'Self-Report',
    code: 'SRP',
    subtestType: SubtestTypeEnum.RATING_SCALE,
    description: 'Self-Report form for ASRS assessing adult ADHD symptoms',
    measuredAbilities: [],
    timeLimitMinutes: null,
    scoreRangeMin: 0,
    scoreRangeMax: 100,
    meanScore: 50,
    standardDeviation: 10,
    administrationInstructions: 'Individual completes the ASRS self-report',
    sortOrder: 3,
    isActive: true,
  },

  // BDI-II Rating Scales
  {
    batteryCode: 'BDI-II',
    name: 'Teacher Rating Scale',
    code: 'TRS',
    subtestType: SubtestTypeEnum.RATING_SCALE,
    description:
      'Teacher Rating Scale form for BDI-II assessing depressive symptoms',
    measuredAbilities: [],
    timeLimitMinutes: null,
    scoreRangeMin: 0,
    scoreRangeMax: 100,
    meanScore: 50,
    standardDeviation: 10,
    administrationInstructions: 'Teacher completes the BDI-II rating scale',
    sortOrder: 1,
    isActive: true,
  },
  {
    batteryCode: 'BDI-II',
    name: 'Parent Rating Scale',
    code: 'PRS',
    subtestType: SubtestTypeEnum.RATING_SCALE,
    description:
      'Parent Rating Scale form for BDI-II assessing depressive symptoms',
    measuredAbilities: [],
    timeLimitMinutes: null,
    scoreRangeMin: 0,
    scoreRangeMax: 100,
    meanScore: 50,
    standardDeviation: 10,
    administrationInstructions: 'Parent completes the BDI-II rating scale',
    sortOrder: 2,
    isActive: true,
  },
  {
    batteryCode: 'BDI-II',
    name: 'Self-Report',
    code: 'SRP',
    subtestType: SubtestTypeEnum.RATING_SCALE,
    description: 'Self-Report form for BDI-II assessing depressive symptoms',
    measuredAbilities: [],
    timeLimitMinutes: null,
    scoreRangeMin: 0,
    scoreRangeMax: 100,
    meanScore: 50,
    standardDeviation: 10,
    administrationInstructions: 'Individual completes the BDI-II self-report',
    sortOrder: 3,
    isActive: true,
  },
  {
    batteryCode: 'WIAT-III',
    name: 'Reading Comprehension',
    code: 'RC',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Reading comprehension ability',
    measuredAbilities: ['reading_comprehension'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Read passages and answer questions',
    sortOrder: 2,
    isActive: true,
  },
  {
    batteryCode: 'WIAT-III',
    name: 'Pseudoword Decoding',
    code: 'PD',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Phonetic decoding of nonsense words',
    measuredAbilities: ['phonetic_decoding'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Read nonsense words aloud',
    sortOrder: 3,
    isActive: true,
  },
  {
    batteryCode: 'WIAT-III',
    name: 'Numerical Operations',
    code: 'NO',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Written math computation',
    measuredAbilities: ['mathematical_computation'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Solve written math problems',
    sortOrder: 4,
    isActive: true,
  },
  {
    batteryCode: 'WIAT-III',
    name: 'Math Problem Solving',
    code: 'MPS',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Applied mathematical reasoning',
    measuredAbilities: ['mathematical_reasoning'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Solve word problems',
    sortOrder: 5,
    isActive: true,
  },
  {
    batteryCode: 'WIAT-III',
    name: 'Spelling',
    code: 'SP',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Spelling accuracy',
    measuredAbilities: ['spelling'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Spell dictated words',
    sortOrder: 6,
    isActive: true,
  },
  {
    batteryCode: 'WIAT-III',
    name: 'Essay Composition',
    code: 'EC',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Written expression and composition',
    measuredAbilities: ['written_expression'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Write essays on given topics',
    sortOrder: 7,
    isActive: true,
  },
  {
    batteryCode: 'WIAT-III',
    name: 'Listening Comprehension',
    code: 'LC',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Auditory comprehension',
    measuredAbilities: ['listening_comprehension'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Listen and answer questions',
    sortOrder: 8,
    isActive: true,
  },
  {
    batteryCode: 'WIAT-III',
    name: 'Oral Expression',
    code: 'OE',
    subtestType: SubtestTypeEnum.CORE,
    description: 'Verbal expression',
    measuredAbilities: ['oral_expression'],
    timeLimitMinutes: null,
    scoreRangeMin: 1,
    scoreRangeMax: 19,
    meanScore: 10,
    standardDeviation: 3,
    administrationInstructions: 'Answer open-ended questions',
    sortOrder: 9,
    isActive: true,
  },
];

export const INDEX_SUBTEST_MAPPINGS = [
  // WISC-V Primary
  {
    indexCode: 'VCI',
    subtestCode: 'SI',
    batteryCode: 'WISC-V',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'VCI',
    subtestCode: 'VC',
    batteryCode: 'WISC-V',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'VSI',
    subtestCode: 'BD',
    batteryCode: 'WISC-V',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'VSI',
    subtestCode: 'VP',
    batteryCode: 'WISC-V',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'FRI',
    subtestCode: 'MR',
    batteryCode: 'WISC-V',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'FRI',
    subtestCode: 'FW',
    batteryCode: 'WISC-V',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'WMI',
    subtestCode: 'DS',
    batteryCode: 'WISC-V',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'WMI',
    subtestCode: 'PS',
    batteryCode: 'WISC-V',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'PSI',
    subtestCode: 'CD',
    batteryCode: 'WISC-V',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'PSI',
    subtestCode: 'SS',
    batteryCode: 'WISC-V',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'FSIQ',
    subtestCode: 'SI',
    batteryCode: 'WISC-V',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'FSIQ',
    subtestCode: 'VC',
    batteryCode: 'WISC-V',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'FSIQ',
    subtestCode: 'BD',
    batteryCode: 'WISC-V',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'FSIQ',
    subtestCode: 'MR',
    batteryCode: 'WISC-V',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'FSIQ',
    subtestCode: 'FW',
    batteryCode: 'WISC-V',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'FSIQ',
    subtestCode: 'DS',
    batteryCode: 'WISC-V',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'FSIQ',
    subtestCode: 'CD',
    batteryCode: 'WISC-V',
    weight: 1.0,
    isRequired: true,
  },

  // WISC-V Ancillary
  {
    indexCode: 'QRI',
    subtestCode: 'MR',
    batteryCode: 'WISC-V',
    weight: 1.0,
    isRequired: false,
  },
  {
    indexCode: 'QRI',
    subtestCode: 'FW',
    batteryCode: 'WISC-V',
    weight: 1.0,
    isRequired: false,
  },
  {
    indexCode: 'AWMI',
    subtestCode: 'DS',
    batteryCode: 'WISC-V',
    weight: 1.0,
    isRequired: false,
  },
  {
    indexCode: 'NVI',
    subtestCode: 'BD',
    batteryCode: 'WISC-V',
    weight: 1.0,
    isRequired: false,
  },
  {
    indexCode: 'GAI',
    subtestCode: 'SI',
    batteryCode: 'WISC-V',
    weight: 1.0,
    isRequired: false,
  },
  {
    indexCode: 'CPI',
    subtestCode: 'DS',
    batteryCode: 'WISC-V',
    weight: 1.0,
    isRequired: false,
  },

  // WISC-V FRI optional
  {
    indexCode: 'FRI',
    subtestCode: 'PCn',
    batteryCode: 'WISC-V',
    weight: 1.0,
    isRequired: false,
  },
  {
    indexCode: 'FRI',
    subtestCode: 'AR',
    batteryCode: 'WISC-V',
    weight: 1.0,
    isRequired: false,
  },
  // WISC-V WMI optional
  {
    indexCode: 'WMI',
    subtestCode: 'LN',
    batteryCode: 'WISC-V',
    weight: 1.0,
    isRequired: false,
  },
  // WISC-V PSI optional
  {
    indexCode: 'PSI',
    subtestCode: 'CA',
    batteryCode: 'WISC-V',
    weight: 1.0,
    isRequired: false,
  },
  // WISC-V VCI supplemental
  {
    indexCode: 'VCI',
    subtestCode: 'IN',
    batteryCode: 'WISC-V',
    weight: 1.0,
    isRequired: false,
  },
  {
    indexCode: 'VCI',
    subtestCode: 'CO',
    batteryCode: 'WISC-V',
    weight: 1.0,
    isRequired: false,
  },

  // WAIS-V (same structure as WISC-V)
  {
    indexCode: 'VCI',
    subtestCode: 'SI',
    batteryCode: 'WAIS-V',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'VCI',
    subtestCode: 'VC',
    batteryCode: 'WAIS-V',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'VSI',
    subtestCode: 'BD',
    batteryCode: 'WAIS-V',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'VSI',
    subtestCode: 'VP',
    batteryCode: 'WAIS-V',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'FRI',
    subtestCode: 'MR',
    batteryCode: 'WAIS-V',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'FRI',
    subtestCode: 'FW',
    batteryCode: 'WAIS-V',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'WMI',
    subtestCode: 'DSQ',
    batteryCode: 'WAIS-V',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'PSI',
    subtestCode: 'CD',
    batteryCode: 'WAIS-V',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'PSI',
    subtestCode: 'SS',
    batteryCode: 'WAIS-V',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'FSIQ',
    subtestCode: 'SI',
    batteryCode: 'WAIS-V',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'FSIQ',
    subtestCode: 'VC',
    batteryCode: 'WAIS-V',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'FSIQ',
    subtestCode: 'BD',
    batteryCode: 'WAIS-V',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'FSIQ',
    subtestCode: 'MR',
    batteryCode: 'WAIS-V',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'FSIQ',
    subtestCode: 'FW',
    batteryCode: 'WAIS-V',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'FSIQ',
    subtestCode: 'DSQ',
    batteryCode: 'WAIS-V',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'FSIQ',
    subtestCode: 'CD',
    batteryCode: 'WAIS-V',
    weight: 1.0,
    isRequired: true,
  },

  // RIAS-2
  {
    indexCode: 'VIX',
    subtestCode: 'GWH',
    batteryCode: 'RIAS-2',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'VIX',
    subtestCode: 'VRZ',
    batteryCode: 'RIAS-2',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'NIX',
    subtestCode: 'OIO',
    batteryCode: 'RIAS-2',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'NIX',
    subtestCode: 'WHM',
    batteryCode: 'RIAS-2',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'MI',
    subtestCode: 'VRM',
    batteryCode: 'RIAS-2',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'MI',
    subtestCode: 'NVM',
    batteryCode: 'RIAS-2',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'CIX',
    subtestCode: 'GWH',
    batteryCode: 'RIAS-2',
    weight: 1.0,
    isRequired: false,
  },
  {
    indexCode: 'CIX',
    subtestCode: 'VRZ',
    batteryCode: 'RIAS-2',
    weight: 1.0,
    isRequired: false,
  },
  {
    indexCode: 'CIX',
    subtestCode: 'OIO',
    batteryCode: 'RIAS-2',
    weight: 1.0,
    isRequired: false,
  },
  {
    indexCode: 'CIX',
    subtestCode: 'WHM',
    batteryCode: 'RIAS-2',
    weight: 1.0,
    isRequired: false,
  },

  // PVAT
  {
    indexCode: 'VAI',
    subtestCode: 'VAT',
    batteryCode: 'PVAT',
    weight: 1.0,
    isRequired: true,
  },

  // WIAT-III
  {
    indexCode: 'READ',
    subtestCode: 'WR',
    batteryCode: 'WIAT-III',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'READ',
    subtestCode: 'RC',
    batteryCode: 'WIAT-III',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'READ',
    subtestCode: 'PD',
    batteryCode: 'WIAT-III',
    weight: 1.0,
    isRequired: false,
  },
  {
    indexCode: 'MATH',
    subtestCode: 'NO',
    batteryCode: 'WIAT-III',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'MATH',
    subtestCode: 'MPS',
    batteryCode: 'WIAT-III',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'WRIT',
    subtestCode: 'SP',
    batteryCode: 'WIAT-III',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'WRIT',
    subtestCode: 'EC',
    batteryCode: 'WIAT-III',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'ORAL',
    subtestCode: 'LC',
    batteryCode: 'WIAT-III',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'ORAL',
    subtestCode: 'OE',
    batteryCode: 'WIAT-III',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'TOTAL',
    subtestCode: 'WR',
    batteryCode: 'WIAT-III',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'TOTAL',
    subtestCode: 'RC',
    batteryCode: 'WIAT-III',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'TOTAL',
    subtestCode: 'NO',
    batteryCode: 'WIAT-III',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'TOTAL',
    subtestCode: 'MPS',
    batteryCode: 'WIAT-III',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'TOTAL',
    subtestCode: 'SP',
    batteryCode: 'WIAT-III',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'TOTAL',
    subtestCode: 'EC',
    batteryCode: 'WIAT-III',
    weight: 1.0,
    isRequired: true,
  },

  // WIAT-IV (same as WIAT-III)
  {
    indexCode: 'READ',
    subtestCode: 'WR',
    batteryCode: 'WIAT-IV',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'READ',
    subtestCode: 'RC',
    batteryCode: 'WIAT-IV',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'READ',
    subtestCode: 'ORF',
    batteryCode: 'WIAT-IV',
    weight: 1.0,
    isRequired: false,
  },
  {
    indexCode: 'MATH',
    subtestCode: 'NO',
    batteryCode: 'WIAT-IV',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'MATH',
    subtestCode: 'MPS',
    batteryCode: 'WIAT-IV',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'WRIT',
    subtestCode: 'AWF',
    batteryCode: 'WIAT-IV',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'WRIT',
    subtestCode: 'OF',
    batteryCode: 'WIAT-IV',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'ORAL',
    subtestCode: 'LC',
    batteryCode: 'WIAT-IV',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'ORAL',
    subtestCode: 'OE',
    batteryCode: 'WIAT-IV',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'TOTAL',
    subtestCode: 'WR',
    batteryCode: 'WIAT-IV',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'TOTAL',
    subtestCode: 'RC',
    batteryCode: 'WIAT-IV',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'TOTAL',
    subtestCode: 'NO',
    batteryCode: 'WIAT-IV',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'TOTAL',
    subtestCode: 'MPS',
    batteryCode: 'WIAT-IV',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'TOTAL',
    subtestCode: 'AWF',
    batteryCode: 'WIAT-IV',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'TOTAL',
    subtestCode: 'OF',
    batteryCode: 'WIAT-IV',
    weight: 1.0,
    isRequired: true,
  },

  // KTEA-3
  {
    indexCode: 'READ',
    subtestCode: 'LWR',
    batteryCode: 'KTEA-3',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'READ',
    subtestCode: 'NWD',
    batteryCode: 'KTEA-3',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'READ',
    subtestCode: 'SRF',
    batteryCode: 'KTEA-3',
    weight: 1.0,
    isRequired: false,
  },
  {
    indexCode: 'MATH',
    subtestCode: 'MCA',
    batteryCode: 'KTEA-3',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'MATH',
    subtestCode: 'MC',
    batteryCode: 'KTEA-3',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'WRIT',
    subtestCode: 'WF',
    batteryCode: 'KTEA-3',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'WRIT',
    subtestCode: 'WE',
    batteryCode: 'KTEA-3',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'ORAL',
    subtestCode: 'LC',
    batteryCode: 'KTEA-3',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'ORAL',
    subtestCode: 'OE',
    batteryCode: 'KTEA-3',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'TOTAL',
    subtestCode: 'LWR',
    batteryCode: 'KTEA-3',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'TOTAL',
    subtestCode: 'NWD',
    batteryCode: 'KTEA-3',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'TOTAL',
    subtestCode: 'MCA',
    batteryCode: 'KTEA-3',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'TOTAL',
    subtestCode: 'MC',
    batteryCode: 'KTEA-3',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'TOTAL',
    subtestCode: 'WF',
    batteryCode: 'KTEA-3',
    weight: 1.0,
    isRequired: true,
  },

  // Spanish Batería-IV (Cognitivas)
  {
    indexCode: 'GIA',
    subtestCode: 'VC',
    batteryCode: 'BATERIA-IV-COG',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'GIA',
    subtestCode: 'IG',
    batteryCode: 'BATERIA-IV-COG',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'GIA',
    subtestCode: 'AK',
    batteryCode: 'BATERIA-IV-COG',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'GIA',
    subtestCode: 'CF',
    batteryCode: 'BATERIA-IV-COG',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'GIA',
    subtestCode: 'AS',
    batteryCode: 'BATERIA-IV-COG',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'GIA',
    subtestCode: 'NS',
    batteryCode: 'BATERIA-IV-COG',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'GIA',
    subtestCode: 'NR',
    batteryCode: 'BATERIA-IV-COG',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'GIA',
    subtestCode: 'MW',
    batteryCode: 'BATERIA-IV-COG',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'GIA',
    subtestCode: 'LPM',
    batteryCode: 'BATERIA-IV-COG',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'GIA',
    subtestCode: 'PC',
    batteryCode: 'BATERIA-IV-COG',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'GIA',
    subtestCode: 'PP',
    batteryCode: 'BATERIA-IV-COG',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'GIA',
    subtestCode: 'NWR',
    batteryCode: 'BATERIA-IV-COG',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'GIA',
    subtestCode: 'VZ',
    batteryCode: 'BATERIA-IV-COG',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'GIA',
    subtestCode: 'PR',
    batteryCode: 'BATERIA-IV-COG',
    weight: 1.0,
    isRequired: true,
  },

  {
    indexCode: 'Gc',
    subtestCode: 'VC',
    batteryCode: 'BATERIA-IV-COG',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'Gc',
    subtestCode: 'IG',
    batteryCode: 'BATERIA-IV-COG',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'Gc',
    subtestCode: 'AK',
    batteryCode: 'BATERIA-IV-COG',
    weight: 1.0,
    isRequired: true,
  },

  {
    indexCode: 'Gf',
    subtestCode: 'CF',
    batteryCode: 'BATERIA-IV-COG',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'Gf',
    subtestCode: 'AS',
    batteryCode: 'BATERIA-IV-COG',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'Gf',
    subtestCode: 'NS',
    batteryCode: 'BATERIA-IV-COG',
    weight: 1.0,
    isRequired: true,
  },

  {
    indexCode: 'Gwm',
    subtestCode: 'NR',
    batteryCode: 'BATERIA-IV-COG',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'Gwm',
    subtestCode: 'MW',
    batteryCode: 'BATERIA-IV-COG',
    weight: 1.0,
    isRequired: true,
  },

  {
    indexCode: 'Gs',
    subtestCode: 'LPM',
    batteryCode: 'BATERIA-IV-COG',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'Gs',
    subtestCode: 'PC',
    batteryCode: 'BATERIA-IV-COG',
    weight: 1.0,
    isRequired: true,
  },

  {
    indexCode: 'Ga',
    subtestCode: 'PP',
    batteryCode: 'BATERIA-IV-COG',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'Ga',
    subtestCode: 'NWR',
    batteryCode: 'BATERIA-IV-COG',
    weight: 1.0,
    isRequired: true,
  },

  {
    indexCode: 'Gv',
    subtestCode: 'VZ',
    batteryCode: 'BATERIA-IV-COG',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'Gv',
    subtestCode: 'PR',
    batteryCode: 'BATERIA-IV-COG',
    weight: 1.0,
    isRequired: true,
  },

  // Spanish Batería-IV (Aprovechamiento)
  {
    indexCode: 'BA',
    subtestCode: 'IP',
    batteryCode: 'BATERIA-IV-AP',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'BA',
    subtestCode: 'CT',
    batteryCode: 'BATERIA-IV-AP',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'BA',
    subtestCode: 'PA',
    batteryCode: 'BATERIA-IV-AP',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'BA',
    subtestCode: 'CAL',
    batteryCode: 'BATERIA-IV-AP',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'BA',
    subtestCode: 'ORT',
    batteryCode: 'BATERIA-IV-AP',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'BA',
    subtestCode: 'ELE',
    batteryCode: 'BATERIA-IV-AP',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'BRS',
    subtestCode: 'IP',
    batteryCode: 'BATERIA-IV-AP',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'BRS',
    subtestCode: 'AP',
    batteryCode: 'BATERIA-IV-AP',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'RC',
    subtestCode: 'CT',
    batteryCode: 'BATERIA-IV-AP',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'RC',
    subtestCode: 'RL',
    batteryCode: 'BATERIA-IV-AP',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'RF',
    subtestCode: 'LO',
    batteryCode: 'BATERIA-IV-AP',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'RF',
    subtestCode: 'FLF',
    batteryCode: 'BATERIA-IV-AP',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'MCS',
    subtestCode: 'CAL',
    batteryCode: 'BATERIA-IV-AP',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'MCS',
    subtestCode: 'FDM',
    batteryCode: 'BATERIA-IV-AP',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'MPS',
    subtestCode: 'PA',
    batteryCode: 'BATERIA-IV-AP',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'MPS',
    subtestCode: 'NM',
    batteryCode: 'BATERIA-IV-AP',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'AF',
    subtestCode: 'FLF',
    batteryCode: 'BATERIA-IV-AP',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'AF',
    subtestCode: 'FDM',
    batteryCode: 'BATERIA-IV-AP',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'AF',
    subtestCode: 'FEF',
    batteryCode: 'BATERIA-IV-AP',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'AA',
    subtestCode: 'CT',
    batteryCode: 'BATERIA-IV-AP',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'AA',
    subtestCode: 'PA',
    batteryCode: 'BATERIA-IV-AP',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'AA',
    subtestCode: 'ELE',
    batteryCode: 'BATERIA-IV-AP',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'BAJ',
    subtestCode: 'IP',
    batteryCode: 'BATERIA-IV-AP',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'BAJ',
    subtestCode: 'PA',
    batteryCode: 'BATERIA-IV-AP',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'BAJ',
    subtestCode: 'ELE',
    batteryCode: 'BATERIA-IV-AP',
    weight: 1.0,
    isRequired: true,
  },
  {
    indexCode: 'BIA',
    subtestCode: 'VC',
    batteryCode: 'BATERIA-IV-COG',
    weight: 1.0,
    isRequired: false,
  },
  {
    indexCode: 'BIA',
    subtestCode: 'CF',
    batteryCode: 'BATERIA-IV-COG',
    weight: 1.0,
    isRequired: false,
  },
  {
    indexCode: 'BIA',
    subtestCode: 'NR',
    batteryCode: 'BATERIA-IV-COG',
    weight: 1.0,
    isRequired: false,
  },
];
