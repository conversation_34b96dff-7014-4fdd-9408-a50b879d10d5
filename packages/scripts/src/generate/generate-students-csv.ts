#!/usr/bin/env node

import { SchoolGradeEnum } from '@lilypad/db/enums';
import {
  randBetweenDate,
  randBoolean,
  randEmail,
  randFirstName,
  randLastName,
  randPhoneNumber,
} from '@ngneat/falso';
import { writeFileSync } from 'node:fs';
import { dirname, join } from 'node:path';
import { fileURLToPath } from 'node:url';
import { DISTRICTS } from '../data/districts';
import { randElement } from '../seed/utils';

const STUDENT_COUNT = 500;

const COMMON_LANGUAGES = [
  'English',
  'Spanish',
  'French',
  'Portuguese',
  'Mandarin',
  'Vietnamese',
  'Arabic',
  'Somali',
  'Haitian Creole',
  'Russian',
  'Korean',
  'Italian',
  'German',
  'Japanese',
  'Polish',
  'Hindi',
  'Tagalog',
];

const GRADE_BY_AGE: Record<number, string> = {
  5: SchoolGradeEnum.KINDERGARTEN,
  6: SchoolGradeEnum.FIRST_GRADE,
  7: SchoolGradeEnum.SECOND_GRADE,
  8: SchoolGradeEnum.THIRD_GRADE,
  9: SchoolGradeEnum.FOURTH_GRADE,
  10: SchoolGradeEnum.FIFTH_GRADE,
  11: SchoolGradeEnum.SIXTH_GRADE,
  12: SchoolGradeEnum.SEVENTH_GRADE,
  13: SchoolGradeEnum.EIGHTH_GRADE,
  14: SchoolGradeEnum.NINTH_GRADE,
  15: SchoolGradeEnum.TENTH_GRADE,
  16: SchoolGradeEnum.ELEVENTH_GRADE,
  17: SchoolGradeEnum.TWELFTH_GRADE,
  18: SchoolGradeEnum.TWELFTH_GRADE,
};

const ALL_SCHOOLS = DISTRICTS.flatMap((district) =>
  district.schools.map((school) => ({
    ...school,
    districtName: district.name,
  }))
);

interface StudentRecord {
  studentId: string;
  firstName: string;
  middleName: string;
  lastName: string;
  preferredName: string;
  dateOfBirth: string;
  grade: string;
  school: string;
  languages: string;
  guardianName: string;
  guardianEmail: string;
  guardianPhone: string;
}

function generateStudentId(index: number, schoolSlug: string): string {
  const currentYear = new Date().getFullYear();
  const yearSuffix = currentYear.toString().slice(-2);
  const schoolPrefix = schoolSlug.slice(0, 3).toUpperCase();
  const studentNumber = String(index + 1).padStart(5, '0');
  return `${schoolPrefix}${yearSuffix}${studentNumber}`;
}

function generateRandomAge(): number {
  const weights = [
    0.05, 0.15, 0.15, 0.15, 0.15, 0.1, 0.1, 0.05, 0.05, 0.03, 0.01, 0.01, 0.005,
    0.005,
  ];
  const ages = [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18];

  const random = Math.random();
  let cumulative = 0;

  for (let i = 0; i < weights.length; i++) {
    cumulative += weights[i];
    if (random <= cumulative) {
      return ages[i];
    }
  }

  return 10;
}

function generateDateOfBirth(age: number): string {
  const today = new Date();
  const birthYear = today.getFullYear() - age;

  const startDate = new Date(birthYear, 0, 1);
  const endDate = new Date(birthYear, 11, 31);

  const birthDate = randBetweenDate({ from: startDate, to: endDate });

  return `${birthDate.getMonth() + 1}/${birthDate.getDate()}/${birthDate.getFullYear()}`;
}

function generateLanguages(): string {
  const numLanguages = randElement([1, 2, 2, 2, 3]);
  const selectedLanguages = new Set<string>();

  if (Math.random() < 0.95) {
    selectedLanguages.add('English');
  }

  while (selectedLanguages.size < numLanguages && selectedLanguages.size < 3) {
    const language = randElement(COMMON_LANGUAGES);
    selectedLanguages.add(language);
  }

  return Array.from(selectedLanguages).join(', ');
}

function generateGradeDisplay(gradeEnum: string): string {
  const gradeMap: Record<string, string> = {
    [SchoolGradeEnum.PRESCHOOL]: 'PK',
    [SchoolGradeEnum.KINDERGARTEN]: 'K',
    [SchoolGradeEnum.FIRST_GRADE]: '1st',
    [SchoolGradeEnum.SECOND_GRADE]: '2nd',
    [SchoolGradeEnum.THIRD_GRADE]: '3rd',
    [SchoolGradeEnum.FOURTH_GRADE]: '4th',
    [SchoolGradeEnum.FIFTH_GRADE]: '5th',
    [SchoolGradeEnum.SIXTH_GRADE]: '6th',
    [SchoolGradeEnum.SEVENTH_GRADE]: '7th',
    [SchoolGradeEnum.EIGHTH_GRADE]: '8th',
    [SchoolGradeEnum.NINTH_GRADE]: '9th',
    [SchoolGradeEnum.TENTH_GRADE]: '10th',
    [SchoolGradeEnum.ELEVENTH_GRADE]: '11th',
    [SchoolGradeEnum.TWELFTH_GRADE]: '12th',
    [SchoolGradeEnum.UNGRADED]: 'U',
    [SchoolGradeEnum.POST_GRADUATE]: 'PG',
  };

  return gradeMap[gradeEnum] || gradeEnum;
}

function generateStudent(index: number): StudentRecord {
  const firstName = randFirstName();
  const lastName = randLastName();
  const middleName = randBoolean() ? randFirstName() : '';
  const preferredName = randBoolean() ? randFirstName() : firstName;

  const age = generateRandomAge();
  const dateOfBirth = generateDateOfBirth(age);
  const gradeEnum = GRADE_BY_AGE[age] || SchoolGradeEnum.KINDERGARTEN;
  const grade = generateGradeDisplay(gradeEnum);

  const school = randElement(ALL_SCHOOLS);
  const studentId = generateStudentId(index, school.slug);

  const languages = generateLanguages();

  const guardianFirstName = randFirstName();
  const guardianLastName = randBoolean() ? lastName : randLastName();
  const guardianName = `${guardianFirstName} ${guardianLastName}`;
  const guardianEmail = randEmail();
  const guardianPhone = randPhoneNumber({ countryCode: 'US' });

  return {
    studentId,
    firstName,
    middleName,
    lastName,
    preferredName,
    dateOfBirth,
    grade,
    school: school.name,
    languages,
    guardianName,
    guardianEmail,
    guardianPhone,
  };
}

function generateCSV(): string {
  const header =
    'Student ID,First Name,Middle Name,Last Name,Preferred Name,Date of Birth,Grade,School,Languages,Guardian Name,Guardian Email,Guardian Phone';

  const rows = [header];

  for (let i = 0; i < STUDENT_COUNT; i++) {
    const student = generateStudent(i);

    const row = [
      student.studentId,
      student.firstName,
      student.middleName,
      student.lastName,
      student.preferredName,
      student.dateOfBirth,
      student.grade,
      `"${student.school}"`, // Quote school names as they may contain commas
      `"${student.languages}"`, // Quote languages as they contain commas
      `"${student.guardianName}"`,
      student.guardianEmail,
      student.guardianPhone,
    ].join(',');

    rows.push(row);
  }

  return rows.join('\n');
}

function main() {
  console.log(`Generating CSV with ${STUDENT_COUNT} students...`);

  const csvContent = generateCSV();

  const __filename = fileURLToPath(import.meta.url);
  const __dirname = dirname(__filename);
  const publicPath = join(__dirname, '../../../../apps/web/public');
  const outputPath = join(
    publicPath,
    `generated_students_${STUDENT_COUNT}.csv`
  );

  try {
    writeFileSync(outputPath, csvContent, 'utf8');
  } catch (error) {
    console.error('❌ Error writing CSV file:', error);
    process.exit(1);
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { generateCSV, generateStudent, STUDENT_COUNT };
