import { and, dbAdmin, eq } from '@lilypad/db/client';
import { RoleEnum } from '@lilypad/db/enums';
import {
  availabilitiesTable,
  districtsTable,
  rolesTable,
  schoolsTable,
  userDistrictsTable,
  userRolesTable,
  userSchoolsTable,
  usersTable,
  type NewAvailability,
} from '@lilypad/db/schema';
import { logger } from '@lilypad/shared/logger';
import { getSupabaseServerClient } from '@lilypad/supabase/server';
import { USERS, generateAvailabilityPattern } from '../data/users';

async function signUpWithEmailPassword(email: string, password: string) {
  const supabase = await getSupabaseServerClient({ admin: true, script: true });
  const {
    data: { user },
    error,
  } = await supabase.auth.signUp({
    email,
    password,
  });

  if (error) {
    logger.error({ error, email }, 'Error signing up user');
    throw error;
  }

  const identities = user?.identities ?? [];

  if (identities.length === 0) {
    const message = 'User already registered';
    logger.error({ email }, message);
    throw new Error(message);
  }

  return String(user?.id);
}

async function verifyUser(email: string, password: string) {
  const supabase = await getSupabaseServerClient({ admin: true, script: true });
  const { data, error: linkError } = await supabase.auth.admin.generateLink({
    email,
    password,
    type: 'signup',
  });

  if (linkError) {
    logger.error({ linkError, email }, 'Error generating signup link');
    throw linkError;
  }

  const { error } = await supabase.auth.verifyOtp({
    type: 'signup',
    token_hash: data.properties.hashed_token,
  });

  if (error) {
    logger.error({ error, email }, 'Error verifying signup link');
    throw error;
  }
}

async function createUserRecord(userId: string, userData: (typeof USERS)[0]) {
  return await dbAdmin.transaction(async (tx) => {
    const [user] = await tx
      .insert(usersTable)
      .values({
        id: userId,
        firstName: userData.firstName,
        lastName: userData.lastName,
        email: userData.email,
      })
      .returning();
    return user;
  });
}

async function createUserAvailability(userId: string) {
  const numAvailabilities = Math.random() < 0.7 ? 1 : 3;
  const availabilities: NewAvailability[] = [];

  for (let i = 0; i < numAvailabilities; i++) {
    const availability = generateAvailabilityPattern();
    availabilities.push({
      userId,
      day: availability.day,
      startTime: availability.startTime,
      endTime: availability.endTime,
    });
  }

  if (availabilities.length > 0) {
    await dbAdmin.insert(availabilitiesTable).values(availabilities);
  }
}

async function associateUserWithDistrictsAndRoles(
  userId: string,
  userRole: RoleEnum
) {
  // Get all districts and roles
  const [districts, roles] = await dbAdmin.transaction(async (tx) => {
    const [districtsData, rolesData] = await Promise.all([
      tx.select().from(districtsTable).orderBy(districtsTable.name),
      tx.select().from(rolesTable),
    ]);
    return [districtsData, rolesData];
  });

  // Find the user's role
  const role = roles.find((r) => r.name === userRole);

  if (!role) {
    throw new Error(`Role ${userRole} not found`);
  }

  if (userRole === RoleEnum.SUPER_USER) {
    // SUPER_USER gets associated with ALL districts
    await dbAdmin.transaction(async (tx) => {
      // Add role association once (not per district)
      const existingUserRole = await tx
        .select()
        .from(userRolesTable)
        .where(
          and(
            eq(userRolesTable.userId, userId),
            eq(userRolesTable.roleId, role.id)
          )
        )
        .limit(1);

      if (existingUserRole.length === 0) {
        await tx.insert(userRolesTable).values({
          userId,
          roleId: role.id,
          roleName: role.name,
        });
      }

      // Add associations to all districts
      for (const district of districts) {
        // biome-ignore lint/nursery/noAwaitInLoop: Not a big deal
        const existingUserDistrict = await tx
          .select()
          .from(userDistrictsTable)
          .where(
            and(
              eq(userDistrictsTable.userId, userId),
              eq(userDistrictsTable.districtId, district.id)
            )
          )
          .limit(1);

        if (existingUserDistrict.length === 0) {
          await tx.insert(userDistrictsTable).values({
            userId,
            districtId: district.id,
          });
        }
      }
    });
  } else {
    // All other users get associated with only the FIRST district
    const firstDistrict = districts[0];
    if (!firstDistrict) {
      throw new Error('No districts found');
    }

    await dbAdmin.transaction(async (tx) => {
      // Add to user_districts
      const existingUserDistrict = await tx
        .select()
        .from(userDistrictsTable)
        .where(
          and(
            eq(userDistrictsTable.userId, userId),
            eq(userDistrictsTable.districtId, firstDistrict.id)
          )
        )
        .limit(1);

      if (existingUserDistrict.length === 0) {
        await tx.insert(userDistrictsTable).values({
          userId,
          districtId: firstDistrict.id,
        });
      }

      // Add to user_roles (no districtId field in this table)
      const existingUserRole = await tx
        .select()
        .from(userRolesTable)
        .where(
          and(
            eq(userRolesTable.userId, userId),
            eq(userRolesTable.roleId, role.id)
          )
        )
        .limit(1);

      if (existingUserRole.length === 0) {
        await tx.insert(userRolesTable).values({
          userId,
          roleId: role.id,
          roleName: role.name,
        });
      }
    });

    // Special handling for PROCTOR - associate with subset of schools
    if (userRole === RoleEnum.PROCTOR) {
      await associateProctorWithSchools(userId, firstDistrict.id);
    }
  }
}

async function associateProctorWithSchools(userId: string, districtId: string) {
  const schools = await dbAdmin.transaction(async (tx) => {
    return await tx
      .select()
      .from(schoolsTable)
      .where(eq(schoolsTable.districtId, districtId))
      .orderBy(schoolsTable.name);
  });

  // For PROCTOR, associate with first 5 schools (subset)
  const proctorSchools = schools.slice(0, 5);

  // Now actually create the user-school associations
  for (const school of proctorSchools) {
    // biome-ignore lint/nursery/noAwaitInLoop: Not a big deal
    await dbAdmin.transaction(async (tx) => {
      const existingUserSchool = await tx
        .select()
        .from(userSchoolsTable)
        .where(
          and(
            eq(userSchoolsTable.userId, userId),
            eq(userSchoolsTable.schoolId, school.id)
          )
        )
        .limit(1);

      if (existingUserSchool.length === 0) {
        await tx.insert(userSchoolsTable).values({
          userId,
          schoolId: school.id,
        });
      }
    });
  }
}

async function main() {
  logger.info({ totalUsers: USERS.length }, 'Starting user seeding process');

  for (const userData of USERS) {
    try {
      // Step 1: Create auth user
      // biome-ignore lint/nursery/noAwaitInLoop: Not a big deal
      const userId = await signUpWithEmailPassword(
        userData.email,
        userData.password
      );

      // Step 2: Verify user
      await verifyUser(userData.email, userData.password);

      // Step 3: Create user record in database
      await createUserRecord(userId, userData);

      await createUserAvailability(userId);

      // Step 4: Associate with districts and roles based on user role
      await associateUserWithDistrictsAndRoles(userId, userData.role);
    } catch (error) {
      logger.error(
        {
          error,
          email: userData.email,
          role: userData.role,
        },
        'Error processing user'
      );

      // Continue with next user instead of failing entire process
    }
  }

  logger.info('User seeding process completed');
}

main().catch((error) => {
  logger.error({ error }, 'Fatal error in user seeding');
  process.exit(1);
});
