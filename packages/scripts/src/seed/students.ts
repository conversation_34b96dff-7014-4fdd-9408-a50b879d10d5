import { createDatabaseClient, eq } from '@lilypad/db/client';
import {
  AddressTypeEnum,
  AdminStatusEnum,
  CasePriorityEnum,
  CaseStatusEnum,
  CaseTypeEnum,
  DocumentCategoryEnum,
  EnrollmentStatusEnum,
  GenderEnum,
  IepStatusEnum,
  ParentRelationshipEnum,
  RoleEnum,
  SessionStatusEnum,
  SessionTypeEnum,
} from '@lilypad/db/enums';
import {
  type Language,
  type NewAddress,
  type NewAssessmentSession,
  type NewCase,
  type NewCaseDetail,
  type NewDocument,
  type NewIndexScore,
  type NewParent,
  type NewStudent,
  type NewStudentEnrollment,
  type NewSubtestScore,
  type NewTestAdministration,
  type School,
  addressesTable,
  assessmentSessionsTable,
  caseAssignmentsTable,
  caseDetailsTable,
  casesTable,
  districtsTable,
  documentsTable,
  indexScoresTable,
  languagesTable,
  parentsTable,
  rolesTable,
  schoolsTable,
  studentEnrollmentsTable,
  studentLanguagesTable,
  studentParentsTable,
  studentsTable,
  subtestScoresTable,
  subtestsTable,
  testAdministrationsTable,
  testBatteriesTable,
  testIndicesTable,
  userRolesTable,
  usersTable,
} from '@lilypad/db/schema';
import { logger } from '@lilypad/shared/logger';
import {
  randBetweenDate,
  randBoolean,
  randEmail,
  randFirstName,
  randFloat,
  randLastName,
  randNumber,
  randPhoneNumber,
  randSentence,
  randStreetAddress,
} from '@ngneat/falso';
import {
  ACADEMIC_CONCERNS,
  BEHAVIORAL_CONCERNS,
  CASE_DETAIL_PROBABILITIES,
  FAMILY_HISTORY_CONDITIONS,
  INTERPRETER_LANGUAGES,
  MEDICATIONS,
  PREVIOUS_EVALUATIONS,
  REFERRAL_SOURCES,
  SPECIAL_ACCOMMODATIONS,
  formatInterpreterNeed,
  formatMedication,
} from '../data/clinical-data';
import {
  CITIES,
  GRADE_OPTIONS,
  STUDENT_CONFIG,
  ZIP_CODES,
} from '../data/students';
import { randElement } from './utils';

/*
 * -----------------------------------------------------------------------------
 * SECTION: Configuration & Types
 * -----------------------------------------------------------------------------
 */

// Increase batch size for better performance
const BATCH_SIZE = 100; // Process students in batches of 100
const CHUNK_SIZE = 50; // Insert records in chunks of 50

// Psychological testing configuration
const PSYCH_TESTING_CONFIG = {
  SESSIONS_PER_STUDENT: { min: 1, max: 3 }, // Most students have 1-3 assessment sessions
  BATTERIES_PER_SESSION: { min: 1, max: 2 }, // 1-2 test batteries per session
  DOCUMENT_URL: '/docs/sample-report.pdf', // Generic PDF from public folder
  SESSION_PROBABILITY: 0.7, // 70% of students have assessment sessions
} as const;

type UserType = {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: string;
};

// Partial types for the data we actually select from the database
type TestBatterySelection = {
  id: string;
  name: string;
  code: string;
};

type SubtestSelection = {
  id: string;
  batteryId: string;
  name: string;
  code: string;
};

type TestIndexSelection = {
  id: string;
  batteryId: string;
  name: string;
  code: string;
};

type StudentBatch = {
  students: Omit<NewStudent, 'id'>[];
  addresses: Omit<NewAddress, 'id' | 'studentId'>[];
  enrollments: Omit<NewStudentEnrollment, 'id' | 'studentId'>[];
  parents: Omit<NewParent, 'id'>[];
  parentRelations: {
    parentIndex: number;
    studentIndex: number;
    relationshipType: ParentRelationshipEnum;
  }[];
  languageRelations: {
    languageId: string;
    studentIndex: number;
    isPrimary: boolean;
  }[];
  cases: Omit<NewCase, 'id' | 'studentId'>[];
  caseAssignments: { userIds: string[]; studentIndex: number }[];
  // Psychological testing data
  assessmentSessions: Omit<
    NewAssessmentSession,
    'id' | 'studentId' | 'caseId' | 'psychologistId'
  >[];
  sessionRelations: {
    sessionIndex: number;
    studentIndex: number;
    caseIndex: number;
    psychologistId: string;
  }[];
  testAdministrations: Omit<
    NewTestAdministration,
    'id' | 'sessionId' | 'batteryId'
  >[];
  administrationRelations: {
    administrationIndex: number;
    sessionIndex: number;
    batteryId: string;
  }[];
  subtestScores: Omit<
    NewSubtestScore,
    'id' | 'administrationId' | 'subtestId'
  >[];
  subtestScoreRelations: {
    scoreIndex: number;
    administrationIndex: number;
    subtestId: string;
  }[];
  indexScores: Omit<NewIndexScore, 'id' | 'administrationId' | 'indexId'>[];
  indexScoreRelations: {
    scoreIndex: number;
    administrationIndex: number;
    indexId: string;
  }[];
  documents: Omit<
    NewDocument,
    'id' | 'studentId' | 'uploadedUserId' | 'assessmentSessionId'
  >[];
  documentRelations: {
    documentIndex: number;
    studentIndex: number;
    sessionIndex: number;
    uploadedUserId: string;
  }[];
  caseDetails: Omit<NewCaseDetail, 'id' | 'caseId'>[];
  caseDetailRelations: {
    detailIndex: number;
    caseIndex: number;
  }[];
};

/*
 * -----------------------------------------------------------------------------
 * SECTION: Helper Functions
 * -----------------------------------------------------------------------------
 */

// Helper function to chunk arrays for batch inserts
function chunk<T>(array: T[], size: number): T[][] {
  const chunks: T[][] = [];
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size));
  }
  return chunks;
}

// Pre-generate random selections to avoid repeated calls
function createRandomSelectors(totalStudents: number) {
  const schools: number[] = [];
  const cities: number[] = [];
  const zipCodes: number[] = [];
  const genders: GenderEnum[] = [];
  const caseStatuses: CaseStatusEnum[] = [];
  const casePriorities: CasePriorityEnum[] = [];
  const caseTypes: CaseTypeEnum[] = [];
  const enrollmentStatuses: EnrollmentStatusEnum[] = [];
  const iepStatuses: IepStatusEnum[] = [];

  for (let i = 0; i < totalStudents; i++) {
    schools.push(Math.floor(Math.random() * 100)); // Will be modded later with actual school count
    cities.push(Math.floor(Math.random() * CITIES.length));
    zipCodes.push(Math.floor(Math.random() * ZIP_CODES.length));
    genders.push(randElement(Object.values(GenderEnum)));
    caseStatuses.push(randElement(Object.values(CaseStatusEnum)));
    casePriorities.push(randElement(Object.values(CasePriorityEnum)));
    caseTypes.push(randElement(Object.values(CaseTypeEnum)));
    enrollmentStatuses.push(randElement(Object.values(EnrollmentStatusEnum)));
    iepStatuses.push(randElement(Object.values(IepStatusEnum)));
  }

  return {
    schools,
    cities,
    zipCodes,
    genders,
    caseStatuses,
    casePriorities,
    caseTypes,
    enrollmentStatuses,
    iepStatuses,
  };
}

async function getExistingData() {
  const db = await createDatabaseClient({ admin: true });

  logger.info('Fetching existing data from database...');

  const [
    users,
    districts,
    schools,
    languages,
    testBatteries,
    testSubtests,
    testIndices,
  ] = await db.transaction(async (tx) => {
    const usersWithRoles = await tx
      .select({
        id: usersTable.id,
        firstName: usersTable.firstName,
        lastName: usersTable.lastName,
        email: usersTable.email,
        role: rolesTable.name,
      })
      .from(usersTable)
      .innerJoin(userRolesTable, eq(usersTable.id, userRolesTable.userId))
      .innerJoin(rolesTable, eq(userRolesTable.roleId, rolesTable.id));

    const [
      districtsData,
      schoolsData,
      languagesData,
      testBatteriesData,
      testSubtestsData,
      testIndicesData,
    ] = await Promise.all([
      tx.select().from(districtsTable),
      tx.select().from(schoolsTable),
      tx.select().from(languagesTable),
      tx
        .select({
          id: testBatteriesTable.id,
          name: testBatteriesTable.name,
          code: testBatteriesTable.code,
        })
        .from(testBatteriesTable)
        .where(eq(testBatteriesTable.isActive, true)),
      tx
        .select({
          id: subtestsTable.id,
          batteryId: subtestsTable.batteryId,
          name: subtestsTable.name,
          code: subtestsTable.code,
        })
        .from(subtestsTable)
        .where(eq(subtestsTable.isActive, true)),
      tx
        .select({
          id: testIndicesTable.id,
          batteryId: testIndicesTable.batteryId,
          name: testIndicesTable.name,
          code: testIndicesTable.code,
        })
        .from(testIndicesTable)
        .where(eq(testIndicesTable.isActive, true)),
    ]);

    return [
      usersWithRoles,
      districtsData,
      schoolsData,
      languagesData,
      testBatteriesData,
      testSubtestsData,
      testIndicesData,
    ];
  });

  logger.info(
    {
      usersCount: users.length,
      districtsCount: districts.length,
      schoolsCount: schools.length,
      languagesCount: languages.length,
      testBatteriesCount: testBatteries.length,
      testSubtestsCount: testSubtests.length,
      testIndicesCount: testIndices.length,
    },
    'Existing data fetched'
  );

  return {
    users,
    districts,
    schools,
    languages,
    testBatteries,
    testSubtests,
    testIndices,
  };
}

// biome-ignore lint/complexity/noExcessiveCognitiveComplexity: Not a big deal
function generateStudentBatch(
  batchSize: number,
  schools: School[],
  languages: Language[],
  usersByRole: Record<string, UserType[]>,
  selectors: ReturnType<typeof createRandomSelectors>,
  batchStartIndex: number,
  testBatteries: TestBatterySelection[],
  testSubtests: SubtestSelection[],
  testIndices: TestIndexSelection[]
): StudentBatch {
  const students: Omit<NewStudent, 'id'>[] = [];
  const addresses: Omit<NewAddress, 'id' | 'studentId'>[] = [];
  const enrollments: Omit<NewStudentEnrollment, 'id' | 'studentId'>[] = [];
  const parents: Omit<NewParent, 'id'>[] = [];
  const parentRelations: {
    parentIndex: number;
    studentIndex: number;
    relationshipType: ParentRelationshipEnum;
  }[] = [];
  const languageRelations: {
    languageId: string;
    studentIndex: number;
    isPrimary: boolean;
  }[] = [];
  const cases: Omit<NewCase, 'id' | 'studentId'>[] = [];
  const caseAssignments: { userIds: string[]; studentIndex: number }[] = [];

  // Psychological testing arrays
  const assessmentSessions: Omit<
    NewAssessmentSession,
    'id' | 'studentId' | 'caseId' | 'psychologistId'
  >[] = [];
  const sessionRelations: {
    sessionIndex: number;
    studentIndex: number;
    caseIndex: number;
    psychologistId: string;
  }[] = [];
  const testAdministrations: Omit<
    NewTestAdministration,
    'id' | 'sessionId' | 'batteryId'
  >[] = [];
  const administrationRelations: {
    administrationIndex: number;
    sessionIndex: number;
    batteryId: string;
  }[] = [];
  const subtestScores: Omit<
    NewSubtestScore,
    'id' | 'administrationId' | 'subtestId'
  >[] = [];
  const subtestScoreRelations: {
    scoreIndex: number;
    administrationIndex: number;
    subtestId: string;
  }[] = [];
  const indexScores: Omit<
    NewIndexScore,
    'id' | 'administrationId' | 'indexId'
  >[] = [];
  const indexScoreRelations: {
    scoreIndex: number;
    administrationIndex: number;
    indexId: string;
  }[] = [];
  const documents: Omit<
    NewDocument,
    'id' | 'studentId' | 'uploadedUserId' | 'assessmentSessionId'
  >[] = [];
  const documentRelations: {
    documentIndex: number;
    studentIndex: number;
    sessionIndex: number;
    uploadedUserId: string;
  }[] = [];
  const caseDetails: Omit<NewCaseDetail, 'id' | 'caseId'>[] = [];
  const caseDetailRelations: {
    detailIndex: number;
    caseIndex: number;
  }[] = [];

  let parentIndex = 0;

  for (let i = 0; i < batchSize; i++) {
    const globalIndex = batchStartIndex + i;

    // Generate student data
    const firstName = randFirstName();
    const lastName = randLastName();
    const middleName = randBoolean() ? randFirstName() : null;

    const minDate = new Date();
    minDate.setFullYear(minDate.getFullYear() - 18);
    const maxDate = new Date();
    maxDate.setFullYear(maxDate.getFullYear() - 5);
    const dateOfBirth = randBetweenDate({ from: minDate, to: maxDate });

    const age = new Date().getFullYear() - dateOfBirth.getFullYear();
    const grade =
      GRADE_OPTIONS[Math.min(Math.max(age - 5, 0), GRADE_OPTIONS.length - 1)];

    const dateOfConsent = randBetweenDate({
      from: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000),
      to: new Date(),
    });

    // Generate enrollment data first to get school info for student
    const schoolIndex = selectors.schools[globalIndex] % schools.length;
    const selectedSchool = schools[schoolIndex];

    // Generate unique student ID number: {school_code}{year}{sequential_number}
    const currentYear = new Date().getFullYear();
    const currentYearLastTwoDigits = currentYear.toString().slice(-2);
    const studentIdNumber = `${selectedSchool.slug.slice(0, 3).toUpperCase()}${currentYearLastTwoDigits}${String(globalIndex + 1).padStart(4, '0')}`;

    // Generate emergency contact info (70% of students have this)
    const hasEmergencyContact = Math.random() < 0.7;

    students.push({
      firstName,
      middleName,
      lastName,
      preferredName: randBoolean() ? randFirstName() : firstName,
      studentIdNumber,
      dateOfBirth: dateOfBirth.toISOString().split('T')[0],
      dateOfConsent: dateOfConsent.toISOString().split('T')[0],
      grade,
      gender: selectors.genders[globalIndex],
      primarySchoolId: selectedSchool.id,
      enrollmentStatus: selectors.enrollmentStatuses[globalIndex],
      specialNeedsIndicator: randBoolean(), // ~50% have special needs indicator
      emergencyContactName: hasEmergencyContact
        ? `${randFirstName()} ${randLastName()}`
        : null,
      emergencyContactPhone: hasEmergencyContact
        ? randPhoneNumber({ countryCode: 'US' })
        : null,
      isDeleted: false,
      deletedAt: null,
      deletedBy: null,
    });

    // Generate address data
    addresses.push({
      type: AddressTypeEnum.PHYSICAL,
      address: randStreetAddress(),
      address2: randBoolean() ? `Apt ${randNumber({ min: 1, max: 999 })}` : '',
      city: CITIES[selectors.cities[globalIndex]],
      state: 'MA',
      zipcode: ZIP_CODES[selectors.zipCodes[globalIndex]],
    });

    // Generate enrollment data
    enrollments.push({
      schoolId: selectedSchool.id,
      districtId: selectedSchool.districtId,
      startDate: new Date().toISOString().split('T')[0],
      endDate: null,
    });

    // Generate parent data
    const parentCount = randNumber({
      min: STUDENT_CONFIG.MIN_PARENTS_PER_STUDENT,
      max: STUDENT_CONFIG.MAX_PARENTS_PER_STUDENT,
    });

    // Realistic parent relationship distribution
    const parentRelationships = [
      ParentRelationshipEnum.MOTHER,
      ParentRelationshipEnum.FATHER,
      ParentRelationshipEnum.GUARDIAN,
      ParentRelationshipEnum.STEP_MOTHER,
      ParentRelationshipEnum.STEP_FATHER,
    ];

    for (let j = 0; j < parentCount; j++) {
      const parentFirstName = randFirstName();
      const parentLastName = randLastName();
      const parentMiddleName = randBoolean() ? randFirstName() : null;

      // 80% of parents have contact info
      const hasContactInfo = Math.random() < 0.8;

      // Select appropriate relationship type for this parent
      let relationshipType: ParentRelationshipEnum;
      if (j === 0) {
        relationshipType =
          Math.random() < 0.6
            ? ParentRelationshipEnum.MOTHER
            : ParentRelationshipEnum.FATHER;
      } else if (j === 1) {
        relationshipType =
          parentRelations.at(-1)?.relationshipType ===
          ParentRelationshipEnum.MOTHER
            ? ParentRelationshipEnum.FATHER
            : ParentRelationshipEnum.MOTHER;
      } else {
        relationshipType = randElement(parentRelationships);
      }

      parents.push({
        firstName: parentFirstName,
        middleName: parentMiddleName,
        lastName: parentLastName,
        primaryEmail: hasContactInfo
          ? randEmail({ firstName: parentFirstName, lastName: parentLastName })
          : null,
        secondaryEmail:
          hasContactInfo && randBoolean()
            ? randEmail({
                firstName: parentFirstName,
                lastName: parentLastName,
              })
            : null,
        primaryPhone: hasContactInfo
          ? randPhoneNumber({ countryCode: 'US' })
          : null,
        secondaryPhone:
          hasContactInfo && randBoolean()
            ? randPhoneNumber({ countryCode: 'US' })
            : null,
        relationshipType,
        isDeleted: false,
        deletedAt: null,
        deletedBy: null,
      });

      parentRelations.push({
        parentIndex: parentIndex++,
        studentIndex: i,
        relationshipType,
      });
    }

    // Generate language relations
    const languageCount = randNumber({
      min: STUDENT_CONFIG.MIN_LANGUAGES_PER_STUDENT,
      max: STUDENT_CONFIG.MAX_LANGUAGES_PER_STUDENT,
    });

    const selectedLanguageIds = new Set<string>();
    for (let j = 0; j < languageCount; j++) {
      let randomLanguage: Language;
      do {
        randomLanguage = randElement(languages);
      } while (selectedLanguageIds.has(randomLanguage.id));

      selectedLanguageIds.add(randomLanguage.id);
      languageRelations.push({
        languageId: randomLanguage.id,
        studentIndex: i,
        isPrimary: j === 0,
      });
    }

    // Generate case data
    const iepStartDate = new Date(
      currentYear,
      randNumber({ min: 0, max: 8 }),
      randNumber({ min: 1, max: 28 })
    );
    const iepEndDate = new Date(iepStartDate);
    iepEndDate.setFullYear(iepEndDate.getFullYear() + 1);

    // Generate realistic timeline dates based on case type
    const caseType = selectors.caseTypes[globalIndex];
    let referralDate: Date | null = null;
    let evaluationDueDate: Date | null = null;
    let meetingDate: Date | null = null;

    // 70% of cases have referral dates
    if (Math.random() < 0.7) {
      referralDate = randBetweenDate({
        from: new Date(Date.now() - 180 * 24 * 60 * 60 * 1000), // 6 months ago
        to: new Date(),
      });
    }

    // 60% of cases have evaluation due dates
    if (Math.random() < 0.6) {
      evaluationDueDate = randBetweenDate({
        from: new Date(),
        to: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000), // 60 days from now
      });
    }

    // 40% of cases have meeting dates scheduled
    if (Math.random() < 0.4) {
      meetingDate = randBetweenDate({
        from: new Date(),
        to: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      });
    }

    cases.push({
      status: selectors.caseStatuses[globalIndex],
      priority: selectors.casePriorities[globalIndex],
      caseType,
      isActive: true,
      iepStatus: selectors.iepStatuses[globalIndex],
      iepStartDate,
      iepEndDate,
      referralDate,
      evaluationDueDate,
      meetingDate,
      isDeleted: false,
      deletedAt: null,
      deletedBy: null,
    });

    // Generate case assignments
    const requiredRoles = [
      RoleEnum.PSYCHOLOGIST,
      RoleEnum.CASE_MANAGER,
      RoleEnum.ASSISTANT,
      RoleEnum.CLINICAL_DIRECTOR,
    ];

    const assignedUserIds: string[] = [];
    for (const role of requiredRoles) {
      const usersForRole = usersByRole[role];
      if (usersForRole && usersForRole.length > 0) {
        const randomUser = randElement(usersForRole);
        assignedUserIds.push(randomUser.id);
      }
    }

    caseAssignments.push({ userIds: assignedUserIds, studentIndex: i });

    // Generate case details (custom key-value pairs for additional case information)
    const caseDetailKeys = [
      {
        key: 'referral_source',
        getValue: () => randElement(REFERRAL_SOURCES),
      },
      {
        key: 'previous_evaluations',
        getValue: () =>
          Math.random() < CASE_DETAIL_PROBABILITIES.PREVIOUS_EVALUATIONS
            ? randElement(PREVIOUS_EVALUATIONS)
            : 'None',
      },
      {
        key: 'current_medications',
        getValue: () =>
          Math.random() < CASE_DETAIL_PROBABILITIES.CURRENT_MEDICATIONS
            ? formatMedication(randElement(MEDICATIONS))
            : 'None',
      },
      {
        key: 'behavioral_concerns',
        getValue: () => randElement(BEHAVIORAL_CONCERNS),
      },
      {
        key: 'academic_concerns',
        getValue: () => randElement(ACADEMIC_CONCERNS),
      },
      {
        key: 'family_history',
        getValue: () =>
          Math.random() < CASE_DETAIL_PROBABILITIES.FAMILY_HISTORY
            ? randElement(FAMILY_HISTORY_CONDITIONS)
            : 'No significant history reported',
      },
      {
        key: 'interpreter_needed',
        getValue: () =>
          Math.random() < CASE_DETAIL_PROBABILITIES.INTERPRETER_NEEDED
            ? formatInterpreterNeed(randElement(INTERPRETER_LANGUAGES))
            : 'No',
      },
      {
        key: 'special_accommodations',
        getValue: () =>
          Math.random() < CASE_DETAIL_PROBABILITIES.SPECIAL_ACCOMMODATIONS
            ? randElement(SPECIAL_ACCOMMODATIONS)
            : 'Standard administration',
      },
    ];

    // Generate 3-5 case details per case
    const detailCount = randNumber({ min: 3, max: 5 });

    // Shuffle and select random case details
    const shuffledDetailKeys = [...caseDetailKeys].sort(
      () => Math.random() - 0.5
    );
    const selectedDetailKeys = shuffledDetailKeys.slice(0, detailCount);

    for (const detailKey of selectedDetailKeys) {
      const detailIndex = caseDetails.length;

      caseDetails.push({
        key: detailKey.key,
        value: detailKey.getValue(),
        isDeleted: false,
        deletedAt: null,
        deletedBy: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      caseDetailRelations.push({
        detailIndex,
        caseIndex: i,
      });
    }

    // Generate psychological testing data (70% of students have assessment sessions)
    if (
      Math.random() < PSYCH_TESTING_CONFIG.SESSION_PROBABILITY &&
      testBatteries.length > 0
    ) {
      const sessionCount = randNumber(
        PSYCH_TESTING_CONFIG.SESSIONS_PER_STUDENT
      );

      for (let sessionNum = 0; sessionNum < sessionCount; sessionNum++) {
        // Find a psychologist for this session
        const psychologists = usersByRole[RoleEnum.PSYCHOLOGIST];
        if (!psychologists || psychologists.length === 0) {
          continue;
        }

        const psychologist = randElement(psychologists);

        // Generate session date within the last 6 months
        const sessionDate = randBetweenDate({
          from: new Date(Date.now() - 180 * 24 * 60 * 60 * 1000), // 6 months ago
          to: new Date(),
        });

        // Create assessment session
        const sessionIndex = assessmentSessions.length;
        assessmentSessions.push({
          sessionDate,
          sessionType: randElement(Object.values(SessionTypeEnum)),
          sessionStatus: randElement([
            SessionStatusEnum.COMPLETED,
            SessionStatusEnum.IN_PROGRESS,
          ]),
          location: `Room ${randNumber({ min: 101, max: 299 })}`,
          sessionDuration: randNumber({ min: 60, max: 180 }), // 1-3 hours
          behavioralObservations: randSentence({
            length: randNumber({ min: 8, max: 15 }),
          }).join(' '),
          createdAt: new Date(),
          updatedAt: new Date(),
        });

        // Link session to student, case, and psychologist
        sessionRelations.push({
          sessionIndex,
          studentIndex: i,
          caseIndex: i, // Assuming one case per student
          psychologistId: psychologist.id,
        });

        // Generate test administrations for this session (1-2 batteries per session)
        const administrationCount = randNumber({
          min: 1,
          max: Math.min(2, testBatteries.length),
        });

        // Select random batteries without using the problematic randElement with count
        const shuffledBatteries = [...testBatteries].sort(
          () => Math.random() - 0.5
        );
        const selectedBatteries = shuffledBatteries.slice(
          0,
          administrationCount
        );

        for (
          let batteryIndex = 0;
          batteryIndex < selectedBatteries.length;
          batteryIndex++
        ) {
          const battery = selectedBatteries[batteryIndex];
          const administrationIndex = testAdministrations.length;

          testAdministrations.push({
            administrationOrder: batteryIndex + 1,
            adminStatus: randElement([
              AdminStatusEnum.COMPLETED,
              AdminStatusEnum.IN_PROGRESS,
            ]),
            administrationNotes: randElement([
              'All subtests administered according to protocol.',
              'Minor modifications made due to student needs.',
              'Standard administration completed successfully.',
              'Extended time provided as per accommodation.',
            ]),
            createdAt: new Date(),
            updatedAt: new Date(),
          });

          // Link administration to session and battery
          administrationRelations.push({
            administrationIndex,
            sessionIndex,
            batteryId: battery.id,
          });

          // Generate subtest scores for this administration
          const batterySubtests = testSubtests.filter(
            (st) => st.batteryId === battery.id
          );
          for (const subtest of batterySubtests) {
            const subtestScoreIndex = subtestScores.length;

            subtestScores.push({
              rawScore: randNumber({ min: 0, max: 50 }),
              scaledScore: randNumber({ min: 1, max: 19 }),
              percentileRank: `${randNumber({ min: 1, max: 99 })}`,
              ageEquivalent: `${Math.floor(randNumber({ min: 60, max: 240 }) / 12)};${randNumber({ min: 0, max: 11 })}`,
              gradeEquivalent: randFloat({ min: 1, max: 12.9 }).toFixed(1),
              descriptiveCategory: randElement([
                'Very Low',
                'Low',
                'Low Average',
                'Average',
                'High Average',
                'Superior',
                'Very Superior',
              ]),
              scoringNotes:
                Math.random() < 0.3
                  ? randElement([
                      'Score reflects typical performance for age group.',
                      'Performance was consistent throughout subtest.',
                      'Student showed excellent effort and focus.',
                      'Some difficulty noted but within normal range.',
                    ])
                  : null,
              createdAt: new Date(),
              updatedAt: new Date(),
            });

            // Link subtest score to administration and subtest
            subtestScoreRelations.push({
              scoreIndex: subtestScoreIndex,
              administrationIndex,
              subtestId: subtest.id,
            });
          }

          // Generate index scores for this administration
          const batteryIndices = testIndices.filter(
            (ti) => ti.batteryId === battery.id
          );
          for (const index of batteryIndices) {
            const indexScoreIndex = indexScores.length;

            indexScores.push({
              compositeScore: randNumber({ min: 70, max: 130 }),
              percentileRank: `${randNumber({ min: 1, max: 99 })}`,
              descriptiveCategory: randElement([
                'Very Low',
                'Low',
                'Low Average',
                'Average',
                'High Average',
                'Superior',
                'Very Superior',
              ]),
              interpretationNotes:
                Math.random() < 0.4
                  ? randElement([
                      'Index score reflects overall performance in this domain.',
                      'Performance was consistent across subtests.',
                      'Some variability noted within this index.',
                      'Strong performance in this cognitive area.',
                    ])
                  : null,
              createdAt: new Date(),
              updatedAt: new Date(),
            });

            // Link index score to administration and index
            indexScoreRelations.push({
              scoreIndex: indexScoreIndex,
              administrationIndex,
              indexId: index.id,
            });
          }
        }

        // Generate a document for this assessment session (generic PDF)
        const documentIndex = documents.length;
        const documentName = `Assessment_Report_${studentIdNumber}_${sessionDate.toISOString().split('T')[0]}.pdf`;

        documents.push({
          name: documentName,
          url: '/docs/sample-report.pdf', // Generic PDF from public folder
          category: DocumentCategoryEnum.ASSESSMENT,
          createdAt: new Date(),
          updatedAt: new Date(),
        });

        // Link document to student, session, and psychologist
        documentRelations.push({
          documentIndex,
          studentIndex: i,
          sessionIndex,
          uploadedUserId: psychologist.id,
        });
      }
    }
  }

  return {
    students,
    addresses,
    enrollments,
    parents,
    parentRelations,
    languageRelations,
    cases,
    caseAssignments,
    assessmentSessions,
    sessionRelations,
    testAdministrations,
    administrationRelations,
    subtestScores,
    subtestScoreRelations,
    indexScores,
    indexScoreRelations,
    documents,
    documentRelations,
    caseDetails,
    caseDetailRelations,
  };
}

async function insertStudentBatch(batch: StudentBatch) {
  const db = await createDatabaseClient({ admin: true });

  // biome-ignore lint/complexity/noExcessiveCognitiveComplexity: Not a big deal
  return await db.transaction(async (tx) => {
    // 1. Insert students in chunks
    const insertedStudents: { id: string; index: number }[] = [];
    const studentChunks = chunk(batch.students, CHUNK_SIZE);

    for (let chunkIndex = 0; chunkIndex < studentChunks.length; chunkIndex++) {
      const studentChunk = studentChunks[chunkIndex];
      // biome-ignore lint/nursery/noAwaitInLoop: Not a big deal
      const results = await tx
        .insert(studentsTable)
        .values(studentChunk)
        .returning({ id: studentsTable.id });

      results.forEach((result, index) => {
        insertedStudents.push({
          id: result.id,
          index: chunkIndex * CHUNK_SIZE + index,
        });
      });
    }

    // 2. Insert addresses with student IDs
    const addressData = batch.addresses.map((address, index) => ({
      ...address,
      studentId: insertedStudents[index].id,
    }));
    const addressChunks = chunk(addressData, CHUNK_SIZE);
    for (const addressChunk of addressChunks) {
      // biome-ignore lint/nursery/noAwaitInLoop: Not a big deal
      await tx.insert(addressesTable).values(addressChunk);
    }

    // 3. Insert student enrollments
    const enrollmentData = batch.enrollments.map((enrollment, index) => ({
      ...enrollment,
      studentId: insertedStudents[index].id,
    }));
    const enrollmentChunks = chunk(enrollmentData, CHUNK_SIZE);
    for (const enrollmentChunk of enrollmentChunks) {
      // biome-ignore lint/nursery/noAwaitInLoop: Not a big deal
      await tx.insert(studentEnrollmentsTable).values(enrollmentChunk);
    }

    // 4. Insert parents in chunks
    const insertedParents: { id: string; index: number }[] = [];
    const parentChunks = chunk(batch.parents, CHUNK_SIZE);

    for (let chunkIndex = 0; chunkIndex < parentChunks.length; chunkIndex++) {
      const parentChunk = parentChunks[chunkIndex];
      // biome-ignore lint/nursery/noAwaitInLoop: Not a big deal
      const results = await tx
        .insert(parentsTable)
        .values(parentChunk)
        .returning({ id: parentsTable.id });

      results.forEach((result, index) => {
        insertedParents.push({
          id: result.id,
          index: chunkIndex * CHUNK_SIZE + index,
        });
      });
    }

    // 5. Insert student-parent relationships
    const parentRelationData = batch.parentRelations.map((relation) => ({
      studentId: insertedStudents[relation.studentIndex].id,
      parentId: insertedParents[relation.parentIndex].id,
      isPrimaryContact: false, // Will be randomly set to true for some relationships
      hasPickupAuthorization: true, // Default to true, can be randomized later
    }));
    const relationChunks = chunk(parentRelationData, CHUNK_SIZE);
    for (const relationChunk of relationChunks) {
      // biome-ignore lint/nursery/noAwaitInLoop: Not a big deal
      await tx.insert(studentParentsTable).values(relationChunk);
    }

    // 6. Insert student languages
    const languageData = batch.languageRelations.map((relation) => ({
      studentId: insertedStudents[relation.studentIndex].id,
      languageId: relation.languageId,
      isPrimary: relation.isPrimary,
    }));
    const languageChunks = chunk(languageData, CHUNK_SIZE);
    for (const languageChunk of languageChunks) {
      // biome-ignore lint/nursery/noAwaitInLoop: Not a big deal
      await tx.insert(studentLanguagesTable).values(languageChunk);
    }

    // 7. Insert cases
    const insertedCases: { id: string; index: number }[] = [];
    const caseData = batch.cases.map((caseItem, index) => ({
      ...caseItem,
      studentId: insertedStudents[index].id,
    }));
    const caseChunks = chunk(caseData, CHUNK_SIZE);

    for (let chunkIndex = 0; chunkIndex < caseChunks.length; chunkIndex++) {
      const caseChunk = caseChunks[chunkIndex];
      // biome-ignore lint/nursery/noAwaitInLoop: Not a big deal
      const results = await tx
        .insert(casesTable)
        .values(caseChunk)
        .returning({ id: casesTable.id });

      results.forEach((result, index) => {
        insertedCases.push({
          id: result.id,
          index: chunkIndex * CHUNK_SIZE + index,
        });
      });
    }

    // 8. Insert case assignments
    const assignmentData: Array<{
      userId: string;
      caseId: string;
      isDeleted: boolean;
      deletedAt: Date | null;
      deletedBy: string | null;
    }> = [];
    for (const assignment of batch.caseAssignments) {
      const caseId = insertedCases[assignment.studentIndex].id;
      for (const userId of assignment.userIds) {
        assignmentData.push({
          userId,
          caseId,
          isDeleted: false,
          deletedAt: null,
          deletedBy: null,
        });
      }
    }
    const assignmentChunks = chunk(assignmentData, CHUNK_SIZE);
    for (const assignmentChunk of assignmentChunks) {
      // biome-ignore lint/nursery/noAwaitInLoop: Not a big deal
      await tx.insert(caseAssignmentsTable).values(assignmentChunk);
    }

    // 9. Insert assessment sessions
    const insertedSessions: { id: string; index: number }[] = [];
    if (batch.assessmentSessions.length > 0) {
      const sessionData = batch.sessionRelations.map((relation) => ({
        ...batch.assessmentSessions[relation.sessionIndex],
        studentId: insertedStudents[relation.studentIndex].id,
        caseId: insertedCases[relation.caseIndex].id,
        psychologistId: relation.psychologistId,
      }));
      const sessionChunks = chunk(sessionData, CHUNK_SIZE);

      for (
        let chunkIndex = 0;
        chunkIndex < sessionChunks.length;
        chunkIndex++
      ) {
        const sessionChunk = sessionChunks[chunkIndex];
        // biome-ignore lint/nursery/noAwaitInLoop: Not a big deal
        const results = await tx
          .insert(assessmentSessionsTable)
          .values(sessionChunk)
          .returning({ id: assessmentSessionsTable.id });

        results.forEach((result, index) => {
          insertedSessions.push({
            id: result.id,
            index: chunkIndex * CHUNK_SIZE + index,
          });
        });
      }
    }

    // 10. Insert test administrations
    const insertedAdministrations: { id: string; index: number }[] = [];
    if (batch.testAdministrations.length > 0) {
      const administrationData = batch.administrationRelations.map(
        (relation) => ({
          ...batch.testAdministrations[relation.administrationIndex],
          sessionId: insertedSessions[relation.sessionIndex].id,
          batteryId: relation.batteryId,
        })
      );
      const administrationChunks = chunk(administrationData, CHUNK_SIZE);

      for (
        let chunkIndex = 0;
        chunkIndex < administrationChunks.length;
        chunkIndex++
      ) {
        const administrationChunk = administrationChunks[chunkIndex];
        // biome-ignore lint/nursery/noAwaitInLoop: Not a big deal
        const results = await tx
          .insert(testAdministrationsTable)
          .values(administrationChunk)
          .returning({ id: testAdministrationsTable.id });

        results.forEach((result, index) => {
          insertedAdministrations.push({
            id: result.id,
            index: chunkIndex * CHUNK_SIZE + index,
          });
        });
      }
    }

    // 11. Insert subtest scores
    if (batch.subtestScores.length > 0) {
      const subtestScoreData = batch.subtestScoreRelations.map((relation) => ({
        ...batch.subtestScores[relation.scoreIndex],
        administrationId:
          insertedAdministrations[relation.administrationIndex].id,
        subtestId: relation.subtestId,
      }));
      const subtestScoreChunks = chunk(subtestScoreData, CHUNK_SIZE);
      for (const subtestScoreChunk of subtestScoreChunks) {
        // biome-ignore lint/nursery/noAwaitInLoop: Not a big deal
        await tx.insert(subtestScoresTable).values(subtestScoreChunk);
      }
    }

    // 12. Insert index scores
    if (batch.indexScores.length > 0) {
      const indexScoreData = batch.indexScoreRelations.map((relation) => ({
        ...batch.indexScores[relation.scoreIndex],
        administrationId:
          insertedAdministrations[relation.administrationIndex].id,
        indexId: relation.indexId,
      }));
      const indexScoreChunks = chunk(indexScoreData, CHUNK_SIZE);
      for (const indexScoreChunk of indexScoreChunks) {
        // biome-ignore lint/nursery/noAwaitInLoop: Not a big deal
        await tx.insert(indexScoresTable).values(indexScoreChunk);
      }
    }

    // 13. Insert documents
    if (batch.documents.length > 0) {
      const documentData = batch.documentRelations.map((relation) => ({
        ...batch.documents[relation.documentIndex],
        studentId: insertedStudents[relation.studentIndex].id,
        uploadedUserId: relation.uploadedUserId,
        assessmentSessionId: insertedSessions[relation.sessionIndex].id,
      }));
      const documentChunks = chunk(documentData, CHUNK_SIZE);
      for (const documentChunk of documentChunks) {
        // biome-ignore lint/nursery/noAwaitInLoop: Not a big deal
        await tx.insert(documentsTable).values(documentChunk);
      }
    }

    // 14. Insert case details
    if (batch.caseDetails.length > 0) {
      const caseDetailData = batch.caseDetailRelations.map((relation) => ({
        ...batch.caseDetails[relation.detailIndex],
        caseId: insertedCases[relation.caseIndex].id,
      }));
      const caseDetailChunks = chunk(caseDetailData, CHUNK_SIZE);
      for (const caseDetailChunk of caseDetailChunks) {
        // biome-ignore lint/nursery/noAwaitInLoop: Not a big deal
        await tx.insert(caseDetailsTable).values(caseDetailChunk);
      }
    }

    return insertedStudents.length;
  });
}

/*
 * -----------------------------------------------------------------------------
 * SECTION: Main Function
 * -----------------------------------------------------------------------------
 */
async function main() {
  const startTime = Date.now();

  logger.info(
    {
      studentsCount: STUDENT_CONFIG.STUDENTS_COUNT,
      batchSize: BATCH_SIZE,
      chunkSize: CHUNK_SIZE,
    },
    'Starting optimized student seeding process'
  );

  try {
    // Get existing data
    const {
      users,
      schools,
      languages,
      testBatteries,
      testSubtests,
      testIndices,
    } = await getExistingData();

    // Validate we have required data
    if (schools.length === 0) {
      throw new Error(
        'No schools found in database. Please seed districts and schools first.'
      );
    }

    if (languages.length === 0) {
      throw new Error(
        'No languages found in database. Please seed languages first.'
      );
    }

    // Group users by role for case assignments
    const usersByRole = users.reduce(
      (acc, user) => {
        if (!acc[user.role]) {
          acc[user.role] = [];
        }
        acc[user.role].push(user);
        return acc;
      },
      {} as Record<string, UserType[]>
    );

    // Pre-generate random selectors for better performance
    const selectors = createRandomSelectors(STUDENT_CONFIG.STUDENTS_COUNT);

    // Process students in batches
    let totalCreated = 0;
    const batches = Math.ceil(STUDENT_CONFIG.STUDENTS_COUNT / BATCH_SIZE);

    for (let batchIndex = 0; batchIndex < batches; batchIndex++) {
      const batchStartIndex = batchIndex * BATCH_SIZE;
      const currentBatchSize = Math.min(
        BATCH_SIZE,
        STUDENT_CONFIG.STUDENTS_COUNT - batchStartIndex
      );

      // Generate batch data
      const batch = generateStudentBatch(
        currentBatchSize,
        schools,
        languages,
        usersByRole,
        selectors,
        batchStartIndex,
        testBatteries,
        testSubtests,
        testIndices
      );

      // Insert batch
      // biome-ignore lint/nursery/noAwaitInLoop: Not a big deal
      const created = await insertStudentBatch(batch);
      totalCreated += created;
    }

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    logger.info(
      {
        totalStudentsCreated: totalCreated,
        targetCount: STUDENT_CONFIG.STUDENTS_COUNT,
        duration: `${duration}s`,
        studentsPerSecond: Math.round(totalCreated / duration),
      },
      'Optimized student seeding process completed'
    );
  } catch (error) {
    logger.error({ error }, 'Fatal error in optimized student seeding');
    throw error;
  }
}

main().catch((error) => {
  logger.error({ error }, 'Fatal error in optimized student seeding');
  process.exit(1);
});
