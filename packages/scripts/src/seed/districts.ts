import { dbAdmin } from '@lilypad/db/client';
import {
  addressesTable,
  districtPreferencesTable,
  districtsTable,
  schoolsTable,
} from '@lilypad/db/schema';
import { logger } from '@lilypad/shared/logger';
import { DISTRICTS } from '../data/districts';

async function main() {
  logger.info('Starting to seed districts...');

  await dbAdmin.transaction(async (tx) => {
    for (const district of DISTRICTS) {
      // biome-ignore lint/nursery/noAwaitInLoop: Not a big deal
      const [address] = await tx
        .insert(addressesTable)
        .values({
          address: district.address,
          address2: district.address2,
          city: district.city,
          state: district.state,
          zipcode: district.zipcode,
        })
        .returning();

      const [districtData] = await tx
        .insert(districtsTable)
        .values({
          name: district.name,
          slug: district.slug,
          ncesId: district.nces_id,
          stateId: district.state_id,
          website: district.website,
          county: district.county,
          invoiceEmail: district.invoiceEmail,
          addressId: address.id,
        })
        .returning();

      for (const pref of district.preferences) {
        try {
          // biome-ignore lint/nursery/noAwaitInLoop: Not a big deal
          await tx.insert(districtPreferencesTable).values({
            districtId: districtData.id,
            category: pref.category,
            key: pref.key,
            type: pref.type,
            value: pref.value,
          });
        } catch (error) {
          logger.error(
            { error, districtId: districtData.id, prefKey: pref.key },
            'Error creating district preference'
          );
        }
      }

      for (const school of district.schools) {
        // biome-ignore lint/nursery/noAwaitInLoop: Not a big deal
        const [schoolAddress] = await tx
          .insert(addressesTable)
          .values({
            address: school.address,
            address2: school.address2,
            city: school.city,
            state: school.state,
            zipcode: school.zipcode,
          })
          .returning();

        await tx
          .insert(schoolsTable)
          .values({
            name: school.name,
            slug: school.slug,
            ncesId: school.nces_id,
            type: school.type,
            website: school.website,
            addressId: schoolAddress.id,
            districtId: districtData.id,
          })
          .returning();
      }
    }
  });

  logger.info('Districts seeding completed');
  process.exit(0);
}

main();
