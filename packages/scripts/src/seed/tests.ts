import { and, createDatabaseClient, eq } from '@lilypad/db/client';
import {
  type IndexSubtestMapping,
  indexSubtestMappingsTable,
  type Subtest,
  subtestsTable,
  type TestBattery,
  type TestIndex,
  testBatteriesTable,
  testIndicesTable,
} from '@lilypad/db/schema';
import { logger } from '@lilypad/shared/logger';
import {
  INDEX_SUBTEST_MAPPINGS,
  SUBTESTS,
  TEST_BATTERIES,
  TEST_CATEGORIES,
  TEST_INDICES,
  type TestCategory,
} from '../data/tests';

async function seedTestBatteries(categories: TestCategory[]) {
  const db = await createDatabaseClient({ admin: true });

  return await db.transaction(async (tx) => {
    const insertedBatteries: TestBattery[] = [];

    for (const batteryData of TEST_BATTERIES) {
      try {
        const category = categories.find(
          (c) => c.name === batteryData.categoryName
        );
        if (!category) {
          logger.error(
            { categoryName: batteryData.categoryName },
            'Category not found for battery'
          );
          continue;
        }

        // biome-ignore lint/nursery/noAwaitInLoop: Not a big deal
        const [existingBattery] = await tx
          .select()
          .from(testBatteriesTable)
          .where(eq(testBatteriesTable.code, batteryData.code))
          .limit(1);

        if (existingBattery) {
          insertedBatteries.push(existingBattery);
          continue;
        }

        const [insertedBattery] = await tx
          .insert(testBatteriesTable)
          .values({
            category: category.categoryType,
            name: batteryData.name,
            code: batteryData.code,
            version: batteryData.version,
            publisher: batteryData.publisher,
            ageRangeMin: batteryData.ageRangeMin,
            ageRangeMax: batteryData.ageRangeMax,
            administrationTime: batteryData.administrationTime,
            description: batteryData.description,
            normingInformation: batteryData.normingInformation,
            isActive: batteryData.isActive,
          })
          .returning();

        if (!insertedBattery) {
          throw new Error(`Failed to insert battery: ${batteryData.code}`);
        }

        insertedBatteries.push(insertedBattery);
      } catch (error) {
        logger.error(
          { error, batteryCode: batteryData.code },
          'Error creating test battery'
        );
        throw error;
      }
    }
    return insertedBatteries;
  });
}

async function seedTestIndices(batteries: Array<{ id: string; code: string }>) {
  const db = await createDatabaseClient({ admin: true });

  return await db.transaction(async (tx) => {
    const insertedIndices: TestIndex[] = [];

    for (const indexData of TEST_INDICES) {
      try {
        const battery = batteries.find((b) => b.code === indexData.batteryCode);
        if (!battery) {
          logger.error(
            { batteryCode: indexData.batteryCode },
            'Battery not found for index'
          );
          continue;
        }

        // biome-ignore lint/nursery/noAwaitInLoop: Not a big deal
        const [existingIndex] = await tx
          .select()
          .from(testIndicesTable)
          .where(
            and(
              eq(testIndicesTable.code, indexData.code),
              eq(testIndicesTable.batteryId, battery.id)
            )
          )
          .limit(1);

        if (existingIndex) {
          insertedIndices.push(existingIndex);
          continue;
        }

        const [insertedIndex] = await tx
          .insert(testIndicesTable)
          .values({
            batteryId: battery.id,
            name: indexData.name,
            code: indexData.code,
            indexType: indexData.indexType,
            description: indexData.description,
            scoreRangeMin: indexData.scoreRangeMin,
            scoreRangeMax: indexData.scoreRangeMax,
            meanScore: indexData.meanScore,
            standardDeviation: indexData.standardDeviation,
            interpretiveGuidelines: indexData.interpretiveGuidelines,
            sortOrder: indexData.sortOrder,
            isActive: indexData.isActive,
          })
          .returning();

        if (!insertedIndex) {
          throw new Error(`Failed to insert index: ${indexData.code}`);
        }

        insertedIndices.push({ ...insertedIndex, batteryId: battery.id });
      } catch (error) {
        logger.error(
          { error, indexCode: indexData.code },
          'Error creating test index'
        );
        throw error;
      }
    }
    return insertedIndices;
  });
}

async function seedSubtests(batteries: Array<{ id: string; code: string }>) {
  const db = await createDatabaseClient({ admin: true });

  return await db.transaction(async (tx) => {
    const insertedSubtests: Subtest[] = [];

    for (const subtestData of SUBTESTS) {
      try {
        const battery = batteries.find(
          (b) => b.code === subtestData.batteryCode
        );
        if (!battery) {
          logger.error(
            { batteryCode: subtestData.batteryCode },
            'Battery not found for subtest'
          );
          continue;
        }

        // biome-ignore lint/nursery/noAwaitInLoop: Not a big deal
        const [existingSubtest] = await tx
          .select()
          .from(subtestsTable)
          .where(
            and(
              eq(subtestsTable.code, subtestData.code),
              eq(subtestsTable.batteryId, battery.id)
            )
          )
          .limit(1);

        if (existingSubtest) {
          insertedSubtests.push(existingSubtest);
          continue;
        }

        const [insertedSubtest] = await tx
          .insert(subtestsTable)
          .values({
            batteryId: battery.id,
            name: subtestData.name,
            code: subtestData.code,
            subtestType: subtestData.subtestType,
            description: subtestData.description,
            measuredAbilities: subtestData.measuredAbilities,
            timeLimitMinutes: subtestData.timeLimitMinutes,
            scoreRangeMin: subtestData.scoreRangeMin,
            scoreRangeMax: subtestData.scoreRangeMax,
            meanScore: subtestData.meanScore,
            standardDeviation: subtestData.standardDeviation,
            administrationInstructions: subtestData.administrationInstructions,
            sortOrder: subtestData.sortOrder,
            isActive: subtestData.isActive,
          })
          .returning();

        if (!insertedSubtest) {
          throw new Error(`Failed to insert subtest: ${subtestData.code}`);
        }

        insertedSubtests.push({ ...insertedSubtest, batteryId: battery.id });
      } catch (error) {
        logger.error(
          { error, subtestCode: subtestData.code },
          'Error creating subtest'
        );
        throw error;
      }
    }

    return insertedSubtests;
  });
}

async function seedIndexSubtestMappings(
  indices: Array<{ id: string; code: string; batteryId: string }>,
  subtests: Array<{ id: string; code: string; batteryId: string }>,
  batteries: Array<{ id: string; code: string }>
) {
  const db = await createDatabaseClient({ admin: true });

  // biome-ignore lint/complexity/noExcessiveCognitiveComplexity: Not a big deal
  return await db.transaction(async (tx) => {
    const insertedMappings: IndexSubtestMapping[] = [];

    // Process mappings in batches for better performance
    const batchSize = 50;
    // biome-ignore lint/suspicious/noEvolvingTypes: Not a big deal
    const mappingBatches = [];

    for (let i = 0; i < INDEX_SUBTEST_MAPPINGS.length; i += batchSize) {
      mappingBatches.push(INDEX_SUBTEST_MAPPINGS.slice(i, i + batchSize));
    }

    for (const batch of mappingBatches) {
      // biome-ignore lint/suspicious/noEvolvingTypes: Not a big deal
      const mappingValues = [];

      for (const mappingData of batch) {
        try {
          // Find the battery first using the batteryCode from the mapping
          const battery = batteries.find(
            (b) => b.code === mappingData.batteryCode
          );
          if (!battery) {
            logger.error(
              { batteryCode: mappingData.batteryCode },
              'Battery not found for mapping'
            );
            continue;
          }

          // Find index within the specific battery
          const index = indices.find(
            (i) =>
              i.code === mappingData.indexCode && i.batteryId === battery.id
          );

          // Find subtest within the specific battery
          const subtest = subtests.find(
            (s) =>
              s.code === mappingData.subtestCode && s.batteryId === battery.id
          );

          if (!index) {
            logger.error(
              {
                indexCode: mappingData.indexCode,
                batteryCode: mappingData.batteryCode,
              },
              'Index not found for mapping in specified battery'
            );
            continue;
          }

          if (!subtest) {
            logger.error(
              {
                subtestCode: mappingData.subtestCode,
                batteryCode: mappingData.batteryCode,
              },
              'Subtest not found for mapping in specified battery'
            );
            continue;
          }

          // Verify that the index and subtest belong to the same battery
          // This should always pass now since we matched within battery, but keep as safety check
          if (index.batteryId !== subtest.batteryId) {
            logger.error(
              {
                indexCode: mappingData.indexCode,
                subtestCode: mappingData.subtestCode,
                batteryCode: mappingData.batteryCode,
                indexBatteryId: index.batteryId,
                subtestBatteryId: subtest.batteryId,
              },
              'Index and subtest belong to different batteries - this should not happen'
            );
            continue;
          }

          // biome-ignore lint/nursery/noAwaitInLoop: Not a big deal
          const [existingMapping] = await tx
            .select()
            .from(indexSubtestMappingsTable)
            .where(
              and(
                eq(indexSubtestMappingsTable.indexId, index.id),
                eq(indexSubtestMappingsTable.subtestId, subtest.id)
              )
            )
            .limit(1);

          if (existingMapping) {
            insertedMappings.push(existingMapping);
            continue;
          }

          mappingValues.push({
            indexId: index.id,
            subtestId: subtest.id,
            weight: mappingData.weight.toString(),
            isRequired: mappingData.isRequired,
          });
        } catch (error) {
          logger.error(
            {
              error,
              indexCode: mappingData.indexCode,
              subtestCode: mappingData.subtestCode,
              batteryCode: mappingData.batteryCode,
            },
            'Error processing mapping'
          );
        }
      }

      // Bulk insert the batch if we have values
      if (mappingValues.length > 0) {
        try {
          const insertedBatch = await tx
            .insert(indexSubtestMappingsTable)
            .values(mappingValues)
            .returning();

          insertedMappings.push(...insertedBatch);
        } catch (error) {
          logger.error(
            { error, batchSize: mappingValues.length },
            'Error inserting mapping batch'
          );
          throw error;
        }
      }
    }

    return insertedMappings;
  });
}

async function main() {
  try {
    logger.info('Starting psychological testing data seeding...');

    // Step 1: Seed test batteries
    const batteries = await seedTestBatteries(TEST_CATEGORIES);

    // Step 3: Seed test indices
    const indices = await seedTestIndices(batteries);

    // Step 4: Seed subtests
    const subtests = await seedSubtests(batteries);

    // Step 5: Seed index-subtest mappings
    const mappings = await seedIndexSubtestMappings(
      indices,
      subtests,
      batteries
    );

    logger.info(
      {
        totalBatteries: batteries.length,
        totalIndices: indices.length,
        totalSubtests: subtests.length,
        totalMappings: mappings.length,
      },
      'Psychological testing data seeding completed successfully'
    );

    process.exit(0);
  } catch (error) {
    logger.error({ error }, 'Error during psychological testing data seeding');
    process.exit(1);
  }
}

main();
