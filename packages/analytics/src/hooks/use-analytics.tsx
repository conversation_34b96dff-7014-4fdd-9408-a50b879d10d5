'use client';

import { usePathname, useSearchParams } from 'next/navigation';
import React from 'react';

import { default as AnalyticsProviderImpl } from '../provider/posthog.client';
import type { AnalyticsProvider as AnalyticsProviderInterface } from '../provider/types';

function useTrackPageView(): void {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  React.useEffect(() => {
    const url = [pathname, searchParams.toString()].filter(Boolean).join('?');
    AnalyticsProviderImpl.trackPageView(url);
  }, [pathname, searchParams]);
}

export const AnalyticsContext = React.createContext<AnalyticsProviderInterface>(
  AnalyticsProviderImpl
);

export function AnalyticsProvider(props: React.PropsWithChildren) {
  useTrackPageView();
  return (
    <AnalyticsContext.Provider value={AnalyticsProviderImpl}>
      {props.children}
    </AnalyticsContext.Provider>
  );
}

export function useAnalytics(): AnalyticsProviderInterface {
  return React.useContext(AnalyticsContext);
}
