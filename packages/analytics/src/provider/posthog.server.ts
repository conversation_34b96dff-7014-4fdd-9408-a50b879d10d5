import type { PostHog as ServerPostHog } from 'posthog-node';

import type { AnalyticsProvider } from './types';
import { keys } from '../../keys';

class PostHogAnalyticsServerProvider implements AnalyticsProvider {
  private serverPostHog: ServerPostHog | undefined;
  private userId: string | undefined;
  private isInitialized = false;

  async identify(
    userIdentifier: string,
    traits?: Record<string, string>
  ): Promise<void> {
    await this.initialize();
    this.userId = userIdentifier;

    const server = await this.initializeOrGetServerPostHog();
    server.capture({
      event: '$identify',
      distinctId: userIdentifier,
      properties: traits,
    });
  }

  async trackPageView(url: string): Promise<void> {
    await this.initialize();

    if (!this.userId) {
      throw new Error(
        'Please identify the user using the identify method before tracking page views'
      );
    }
    const server = await this.initializeOrGetServerPostHog();
    server.capture({
      event: '$pageview',
      distinctId: this.userId,
      properties: { $current_url: url },
    });
  }

  async trackEvent(
    eventName: string,
    eventProperties?: Record<string, string | string[]>
  ): Promise<void> {
    await this.initialize();

    if (!this.userId) {
      throw new Error(
        'Please identify the user using the identify method before tracking events'
      );
    }
    const server = await this.initializeOrGetServerPostHog();
    server.capture({
      event: eventName,
      distinctId: this.userId,
      properties: eventProperties,
    });
  }

  private async initialize(): Promise<void> {
    if (this.isInitialized) {
      return Promise.resolve();
    }

    const posthogKey = keys().NEXT_PUBLIC_ANALYTICS_POSTHOG_KEY;
    if (!posthogKey) {
      throw new Error('PostHog key not provided, skipping initialization');
    }

    const posthogHost = keys().NEXT_PUBLIC_ANALYTICS_POSTHOG_HOST;
    if (!posthogHost) {
      throw new Error('PostHog host not provided, skipping initialization');
    }

    await this.initializeOrGetServerPostHog();
    this.isInitialized = true;
  }

  private async initializeOrGetServerPostHog(): Promise<ServerPostHog> {
    if (!this.serverPostHog) {
      const { PostHog } = await import('posthog-node');

      const posthogHost = keys().NEXT_PUBLIC_ANALYTICS_POSTHOG_HOST;
      const posthogKey = keys().NEXT_PUBLIC_ANALYTICS_POSTHOG_KEY;

      if (!posthogKey) {
        throw new Error('PostHog key not provided, skipping initialization');
      }

      this.serverPostHog = new PostHog(posthogKey, {
        host: posthogHost,
        flushAt: 1,
        flushInterval: 0,
      });
    }
    return this.serverPostHog;
  }
}

export default new PostHogAnalyticsServerProvider();
