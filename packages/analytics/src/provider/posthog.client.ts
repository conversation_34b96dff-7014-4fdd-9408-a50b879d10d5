import type { PostHog as ClientPostHog } from 'posthog-js';

import type { AnalyticsProvider } from './types';
import { keys } from '../../keys';

class PostHogAnalyticsClientProvider implements AnalyticsProvider {
  private clientPostHog: ClientPostHog | undefined;
  private isInitialized = false;

  async identify(
    userIdentifier: string,
    traits?: Record<string, string>
  ): Promise<void> {
    await this.initialize();

    const client = await this.initializeOrGetClientPostHog();
    client.identify(userIdentifier, traits);
  }

  async trackPageView(url: string): Promise<void> {
    await this.initialize();

    const client = await this.initializeOrGetClientPostHog();
    client.capture('$pageview', { $current_url: url });
  }

  async trackEvent(
    eventName: string,
    eventProperties?: Record<string, string | string[]>
  ): Promise<void> {
    await this.initialize();

    const client = await this.initializeOrGetClientPostHog();
    client.capture(eventName, eventProperties);
  }

  private async initialize(): Promise<void> {
    if (this.isInitialized) {
      return Promise.resolve();
    }

    const posthogKey = keys().NEXT_PUBLIC_ANALYTICS_POSTHOG_KEY;
    if (!posthogKey) {
      throw new Error('PostHog key not provided, skipping initialization');
    }

    const posthogHost = keys().NEXT_PUBLIC_ANALYTICS_POSTHOG_HOST;
    if (!posthogHost) {
      throw new Error('PostHog host not provided, skipping initialization');
    }

    await this.initializeOrGetClientPostHog();
    this.isInitialized = true;
  }

  private async initializeOrGetClientPostHog(): Promise<ClientPostHog> {
    if (!this.clientPostHog) {
      const { posthog } = await import('posthog-js');

      const posthogKey = keys().NEXT_PUBLIC_ANALYTICS_POSTHOG_KEY;
      const posthogHost = keys().NEXT_PUBLIC_ANALYTICS_POSTHOG_HOST;

      if (!posthogKey) {
        throw new Error('PostHog key not provided, skipping initialization');
      }

      posthog.init(posthogKey, {
        api_host: posthogHost,
        ui_host: posthogHost,
        persistence: 'localStorage+cookie',
        person_profiles: 'always',
        capture_pageview: false,
        capture_pageleave: true,
      });
      this.clientPostHog = posthog;
    }
    return this.clientPostHog;
  }
}

export default new PostHogAnalyticsClientProvider();
