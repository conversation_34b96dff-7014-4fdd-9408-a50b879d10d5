import { createEnv } from '@t3-oss/env-nextjs';
import { z } from 'zod';

export const keys = () =>
  createEnv({
    client: {
      NEXT_PUBLIC_ANALYTICS_POSTHOG_KEY: z.string().optional(),
      NEXT_PUBLIC_ANALYTICS_POSTHOG_HOST: z.string().optional(),
    },
    runtimeEnv: {
      NEXT_PUBLIC_ANALYTICS_POSTHOG_KEY:
        process.env.NEXT_PUBLIC_ANALYTICS_POSTHOG_KEY,
      NEXT_PUBLIC_ANALYTICS_POSTHOG_HOST:
        process.env.NEXT_PUBLIC_ANALYTICS_POSTHOG_HOST,
    },
  });
