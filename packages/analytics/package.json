{"name": "@lilypad/analytics", "private": true, "version": "0.0.0", "type": "module", "scripts": {"clean": "git clean -xdf node_modules", "format": "biome format --write .", "lint": "biome lint --write .", "typecheck": "tsc --noEmit"}, "devDependencies": {"@lilypad/typescript": "workspace:*", "@types/node": "^22.15.3", "@types/react": "19.1.0", "@types/react-dom": "19.1.1"}, "dependencies": {"@t3-oss/env-nextjs": "^0.13.4", "next": "^15.3.0", "posthog-js": "^1.245.0", "posthog-node": "^4.17.1", "react": "^19.1.0", "react-dom": "^19.1.0", "zod": "^3.25.7"}, "exports": {"./keys": "./keys.ts", "./hooks": "./src/hooks/use-analytics.tsx", "./client": "./src/provider/client.ts", "./server": "./src/provider/server.ts"}}