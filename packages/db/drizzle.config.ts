import { defineConfig } from 'drizzle-kit';

import { keys } from './keys';

export default defineConfig({
  dialect: 'postgresql',
  out: '../../supabase/migrations',
  schema: './src/schema/index.ts',
  dbCredentials: {
    url: keys().ADMIN_DATABASE_URL,
  },
  schemaFilter: 'public',
  tablesFilter: '*',
  casing: 'snake_case',
  migrations: {
    prefix: 'timestamp',
    schema: 'public',
  },
  entities: {
    roles: {
      provider: 'supabase',
    },
  },
  breakpoints: true,
  strict: true,
  verbose: true,
});
