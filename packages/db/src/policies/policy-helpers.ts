/**
 * @fileoverview RLS Policy Helpers for Supabase/Drizzle
 *
 * This file contains helper functions that generate SQL expressions for common
 * Row Level Security (RLS) policy conditions. These helpers promote code reuse
 * and consistency across all table policies.
 *
 * @example
 * ```typescript
 * import { userHasAnyRole } from './helpers';
 *
 * // Use in a policy
 * export const schoolsInsertPolicy = pgPolicy(
 *	"authorized_roles_can_create_schools",
 *	{
 *		for: "insert",
 *		to: authenticatedRole,
 *		withCheck: sql`
 *			${isUserSuperUser()}
 *			OR
 *			(
 *				${userHasAnyRole(["SPECIAL_ED_DIRECTOR", "SCHOOL_COORDINATOR", "CASE_MANAGER"])}
 *				AND schools.district_id = ANY(${getUserDistrictIds()})
 *			)
 *		`,
 *	},
 * );
 * ```
 *
 * Key Design Principles:
 * - All helpers use Supabase's auth.uid() for user identification
 * - District-based isolation is enforced throughout
 * - Soft delete support (is_deleted checks) where applicable
 * - Raw SQL for performance and to avoid circular dependencies
 *
 * @performance
 * - Uses EXISTS instead of array operations where possible
 * - Optimized condition ordering (cheapest checks first)
 * - Leverages indexes for efficient lookups
 */

import { type SQL, sql } from 'drizzle-orm';

/**
 * Helper function to get the current authenticated user ID
 * @performance O(1) - Direct auth context lookup
 * @returns SQL expression for current user ID from auth.uid()
 */
export const getCurrentUserId = () => sql`auth.uid()`;

/**
 * Helper function to check if user has any of the specified roles
 * @performance Uses EXISTS for short-circuit evaluation
 * @param roles - Array of role names to check
 * @param userId - The user UUID to check (defaults to current user)
 * @returns SQL expression for role check
 */
export const userHasAnyRole = (roles: string[], userId?: SQL<unknown>) => {
  const userIdExpr = userId || getCurrentUserId();

  if (roles.length === 0) {
    return sql`false`;
  }

  // Use EXISTS for better performance - PostgreSQL can short-circuit
  // Create a SQL string with the actual role names embedded as literals
  const roleList = roles.map((role) => `'${role}'`).join(', ');

  return sql`
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = ${userIdExpr}
      AND role_name IN (${sql.raw(roleList)})
    )
  `;
};

/**
 * Helper function to check if user is a super user
 * @performance Uses EXISTS with indexed lookups
 * @param userId - The user UUID to check (defaults to current user)
 * @returns SQL expression for super user check
 */
export const isUserSuperUser = (userId?: SQL<unknown>) => {
  const userIdExpr = userId || getCurrentUserId();
  // Optimized to use denormalized role_name for direct comparison
  return sql`
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = ${userIdExpr}
      AND role_name = 'SUPER_USER'
    )
  `;
};

/**
 * Helper function to check if user belongs to a specific district
 * @performance Uses EXISTS for efficient lookup
 * @param districtId - The district UUID to check
 * @param userId - The user UUID (defaults to current user)
 * @returns SQL expression for district membership check
 */
export const userBelongsToDistrict = (
  districtId: SQL<unknown>,
  userId?: SQL<unknown>
) => {
  const userIdExpr = userId || getCurrentUserId();
  return sql`
    EXISTS (
      SELECT 1
      FROM user_districts
      WHERE user_id = ${userIdExpr}
      AND district_id = ${districtId}
    )
  `;
};

/**
 * Helper function to check if user belongs to a specific school
 * @performance Uses EXISTS for efficient lookup
 * @param schoolId - The school UUID to check
 * @param userId - The user UUID (defaults to current user)
 * @returns SQL expression for school membership check
 */
export const userBelongsToSchool = (
  schoolId: SQL<unknown>,
  userId?: SQL<unknown>
) => {
  const userIdExpr = userId || getCurrentUserId();
  return sql`
    EXISTS (
      SELECT 1
      FROM user_schools
      WHERE user_id = ${userIdExpr}
      AND school_id = ${schoolId}
    )
  `;
};

/**
 * Helper function to check if user has district-level access
 * District-level roles: SPECIAL_ED_DIRECTOR, CASE_MANAGER, CLINICAL_DIRECTOR
 * @performance Uses EXISTS with role name index
 * @param userId - The user UUID (defaults to current user)
 * @returns SQL expression for district-level access check
 */
export const hasDistrictLevelAccess = (userId?: SQL<unknown>) => {
  const userIdExpr = userId || getCurrentUserId();
  return sql`
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = ${userIdExpr}
      AND role_name IN ('SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR')
    )
  `;
};

/**
 * Helper function to check if user has case-level access only
 * Case-level only roles: PSYCHOLOGIST, PROCTOR
 * @performance Uses EXISTS with role name index
 * @param userId - The user UUID (defaults to current user)
 * @returns SQL expression for case-level only access check
 */
export const hasCaseLevelAccessOnly = (userId?: SQL<unknown>) => {
  const userIdExpr = userId || getCurrentUserId();
  return sql`
    EXISTS (
      SELECT 1
      FROM user_roles
      WHERE user_id = ${userIdExpr}
      AND role_name IN ('PSYCHOLOGIST', 'PROCTOR')
    )
  `;
};

/**
 * Helper function to check if user is assigned to a specific case
 * @performance Uses EXISTS with composite index on (user_id, case_id, is_deleted)
 * @param caseId - The case UUID to check
 * @param userId - The user UUID (defaults to current user)
 * @returns SQL expression for case assignment check
 */
export const isUserAssignedToCase = (
  caseId: SQL<unknown>,
  userId?: SQL<unknown>
) => {
  const userIdExpr = userId || getCurrentUserId();
  return sql`
    EXISTS (
      SELECT 1
      FROM case_assignments ca
      WHERE ca.user_id = ${userIdExpr}
      AND ca.case_id = ${caseId}
      AND ca.is_deleted = false
    )
  `;
};

/**
 * Helper function to check if user can access a specific student
 * Based on role-specific access rules:
 * - SUPER_USER: all students
 * - District-level roles: all students in their districts
 * - PSYCHOLOGIST/PROCTOR: only assigned students
 * - School-level roles: all students in their schools
 *
 * @performance Optimized condition ordering - cheapest checks first
 * - Super user check is performed first (cheapest)
 * - District-level roles can access all students in their districts
 * - Case-level only roles can access assigned students
 * - School-level roles can access students in their schools
 *
 * @param studentId - The student UUID to check
 * @param userId - The user UUID (defaults to current user)
 * @returns SQL expression for student access check
 */
export const canUserAccessStudent = (
  studentId: SQL<unknown>,
  userId?: SQL<unknown>
) => {
  const userIdExpr = userId || getCurrentUserId();
  return sql`
    (
      ${isUserSuperUser(userIdExpr)}
      OR
      (
        ${hasDistrictLevelAccess(userIdExpr)}
        AND EXISTS (
          SELECT 1
          FROM student_enrollments se
          JOIN schools s ON s.id = se.school_id
          JOIN user_districts ud ON ud.district_id = s.district_id
          WHERE se.student_id = ${studentId}
          AND ud.user_id = ${userIdExpr}
        )
      )
      OR
      (
        ${hasCaseLevelAccessOnly(userIdExpr)}
        AND EXISTS (
          SELECT 1
          FROM cases c
          JOIN case_assignments ca ON ca.case_id = c.id
          WHERE c.student_id = ${studentId}
          AND ca.user_id = ${userIdExpr}
          AND ca.is_deleted = false
          AND c.is_deleted = false
        )
      )
      OR
      (
        NOT ${hasDistrictLevelAccess(userIdExpr)}
        AND NOT ${hasCaseLevelAccessOnly(userIdExpr)}
        AND EXISTS (
          SELECT 1
          FROM student_enrollments se
          JOIN user_schools us ON us.school_id = se.school_id
          WHERE se.student_id = ${studentId}
          AND us.user_id = ${userIdExpr}
        )
      )
    )
  `;
};

/**
 * Helper function to check if user can manage district preferences
 * Allowed roles: SPECIAL_ED_DIRECTOR, SCHOOL_COORDINATOR, CASE_MANAGER
 * @performance Checks super user first, then role + district membership
 * @param districtId - The district UUID to check
 * @param userId - The user UUID (defaults to current user)
 * @returns SQL expression for district preference management check
 */
export const canUserManageDistrictPreferences = (
  districtId: SQL<unknown>,
  userId?: SQL<unknown>
) => {
  const userIdExpr = userId || getCurrentUserId();
  return sql`
    (
      ${isUserSuperUser(userIdExpr)}
      OR
      (
        ${userHasAnyRole(['SPECIAL_ED_DIRECTOR', 'SCHOOL_COORDINATOR', 'CASE_MANAGER'], userIdExpr)}
        AND EXISTS (
          SELECT 1
          FROM user_districts ud
          WHERE ud.user_id = ${userIdExpr}
          AND ud.district_id = ${districtId}
        )
      )
    )
  `;
};

/**
 * Helper function to check if user can manage users/invitations
 * Allowed roles: SPECIAL_ED_DIRECTOR, SCHOOL_COORDINATOR, CASE_MANAGER, SUPER_USER
 * @performance Single EXISTS check with role names
 * @param userId - The user UUID (defaults to current user)
 * @returns SQL expression for user management check
 */
export const canUserManageUsers = (userId?: SQL<unknown>) => {
  const userIdExpr = userId || getCurrentUserId();
  return userHasAnyRole(
    ['SPECIAL_ED_DIRECTOR', 'SCHOOL_COORDINATOR', 'CASE_MANAGER', 'SUPER_USER'],
    userIdExpr
  );
};

/**
 * Helper function to check if user can perform assessment operations
 * Allowed roles: CASE_MANAGER, CLINICAL_DIRECTOR, PSYCHOLOGIST, ASSISTANT
 * @performance Single EXISTS check with role names
 * @param userId - The user UUID (defaults to current user)
 * @returns SQL expression for assessment operation check
 */
export const canUserPerformAssessments = (userId?: SQL<unknown>) => {
  const userIdExpr = userId || getCurrentUserId();
  return userHasAnyRole(
    ['CASE_MANAGER', 'CLINICAL_DIRECTOR', 'PSYCHOLOGIST', 'ASSISTANT'],
    userIdExpr
  );
};

/**
 * Helper function to check if user can view all assessments
 * Allowed roles: SPECIAL_ED_DIRECTOR, SCHOOL_COORDINATOR (SELECT only)
 * @performance Single EXISTS check with role names
 * @param userId - The user UUID (defaults to current user)
 * @returns SQL expression for assessment viewing check
 */
export const canUserViewAllAssessments = (userId?: SQL<unknown>) => {
  const userIdExpr = userId || getCurrentUserId();
  return userHasAnyRole(
    ['SPECIAL_ED_DIRECTOR', 'SCHOOL_COORDINATOR'],
    userIdExpr
  );
};
