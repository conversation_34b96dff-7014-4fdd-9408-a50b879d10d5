/**
 * @fileoverview Composable Policy Builders for RLS
 *
 * This file contains higher-level policy builders that reduce code duplication
 * and improve maintainability of RLS policies. These builders follow common
 * patterns used across multiple tables.
 *
 * @performance
 *   - Optimized condition ordering (cheapest checks first)
 *   - Uses EXISTS instead of array operations where possible
 *   - Follows PostgreSQL query optimization best practices
 */

import { type SQL, sql } from 'drizzle-orm';
import { pgPolicy } from 'drizzle-orm/pg-core';
import { authenticatedRole } from 'drizzle-orm/supabase';
import {
  canUserManageDistrictPreferences,
  getCurrentUserId,
  isUserSuperUser,
} from './policy-helpers';

/**
 * @policy-builder createSelectOnlyPolicy
 * @description Creates read-only policies
 * @performance Minimal overhead for read operations
 * @usage For lookup tables or read-only data
 */
export const createSelectOnlyPolicy = (
  policyPrefix: string,
  condition: SQL<unknown> = sql`true`
) => {
  return pgPolicy(`${policyPrefix}_select`, {
    for: 'select',
    to: authenticatedRole,
    using: condition,
  });
};

/**
 * @policy-builder createDistrictManagementPolicy
 * @description Creates policies for district-level management features
 * @performance Optimized for management operations
 * @usage For district preferences, settings, etc.
 */
export const createDistrictManagementPolicy = (
  policyPrefix: string,
  tableName: string,
  districtColumn: SQL<unknown> = sql`${sql.identifier(tableName)}.district_id`
) => {
  return {
    select: pgPolicy(`${policyPrefix}_select`, {
      for: 'select',
      to: authenticatedRole,
      using: sql`
        ${isUserSuperUser()}
        OR
        EXISTS (
          SELECT 1 FROM user_districts ud 
          WHERE ud.user_id = ${getCurrentUserId()} 
          AND ud.district_id = ${districtColumn}
        )
      `,
    }),

    all: pgPolicy(`${policyPrefix}_all`, {
      for: 'all',
      to: authenticatedRole,
      using: canUserManageDistrictPreferences(districtColumn),
    }),
  };
};

/**
 * @policy-builder createScopedAccessPolicy
 * @description Creates policies with complex scoping rules
 * @performance Uses EXISTS for efficient permission checks
 * @usage For entities with complex access patterns
 */
export const createScopedAccessPolicy = (
  policyPrefix: string,
  selectCondition: SQL<unknown>,
  modifyCondition: SQL<unknown>
) => {
  return {
    select: pgPolicy(`${policyPrefix}_select`, {
      for: 'select',
      to: authenticatedRole,
      using: selectCondition,
    }),

    insert: pgPolicy(`${policyPrefix}_insert`, {
      for: 'insert',
      to: authenticatedRole,
      withCheck: modifyCondition,
    }),

    update: pgPolicy(`${policyPrefix}_update`, {
      for: 'update',
      to: authenticatedRole,
      using: modifyCondition,
    }),

    delete: pgPolicy(`${policyPrefix}_delete`, {
      for: 'delete',
      to: authenticatedRole,
      using: isUserSuperUser(),
    }),
  };
};

/**
 * @policy-builder createAuthenticatedOnlyPolicy
 * @description Creates policies that only require authentication
 * @performance Minimal overhead - just checks auth status
 * @usage For public data that requires login
 */
export const createAuthenticatedOnlyPolicy = (
  policyPrefix: string,
  operation: 'select' | 'all' = 'select'
) => {
  return pgPolicy(`${policyPrefix}_${operation}`, {
    for: operation,
    to: authenticatedRole,
    using: sql`auth.uid() IS NOT NULL`,
  });
};
