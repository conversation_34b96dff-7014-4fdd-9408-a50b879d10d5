# RLS Permissions Documentation

This document outlines all Row Level Security (RLS) permissions implemented in the database. Permissions are organized by table and role.

## Roles Overview

### System Role
- **SUPER_USER**: Full system access, can perform any operation on any data

### District-Level Roles (Access all data within assigned districts)
- **SPECIAL_ED_DIRECTOR**: District management and student oversight
- **CASE_MANAGER**: Manages cases and assessments across the district
- **CLINICAL_DIRECTOR**: Clinical oversight and assessment management

### School-Level Roles (Access data within assigned schools)
- **SCHOOL_COORDINATOR**: School management within districts
- **SCHOOL_ADMIN**: Administrative access within schools
- **ASSISTANT**: Support role with limited school-level access

### Case-Level Roles (Access only assigned cases)
- **PSYCHOLOGIST**: Performs assessments on assigned students
- **PROCTOR**: Assists with assessments on assigned cases

## Permissions by Table

### System Tables

#### roles
- **READ**: All authenticated users

#### permissions
- **READ**: All authenticated users

#### role_permissions
- **READ**: All authenticated users

#### user_roles
- **READ**: All authenticated users
- **ALL OPERATIONS**: All authenticated users (actual restrictions at app layer)

### District Management

#### districts
- **READ**: All authenticated users
- **CREATE**: SUPER_USER only
- **UPDATE**: 
  - All Districts: SUPER_USER,
  - Own Districts: SPECIAL_ED_DIRECTOR, SCHOOL_COORDINATOR, CASE_MANAGER
- **DELETE**: SUPER_USER only

#### district_preferences
- **READ**: Users in the district
- **ALL OPERATIONS**: 
  - All Districts: SUPER_USER,
  - Own Districts: SPECIAL_ED_DIRECTOR, SCHOOL_COORDINATOR, CASE_MANAGER

### Schools

#### schools
- **READ**: Users in the district
- **CREATE**: 
  - All Districts: SUPER_USER,
  - In their Districts: SPECIAL_ED_DIRECTOR, SCHOOL_COORDINATOR, CASE_MANAGER
- **UPDATE**: 
  - All Districts: SUPER_USER,
  - In their Districts: SPECIAL_ED_DIRECTOR, SCHOOL_COORDINATOR, CASE_MANAGER
- **DELETE**: SUPER_USER only

### Users

#### users
- **READ**: Own profile, users in shared districts, or SUPER_USER
- **CREATE**: Not allowed (use invitation system)
- **UPDATE**: Own profile, SUPER_USER, or user managers in shared districts
- **DELETE**: SUPER_USER only

#### user_districts
- **READ**: Own assignments or SUPER_USER
- **ALL OPERATIONS**: SUPER_USER or user managers for own assignments

### Students

#### students
- **READ**: 
  - District roles: All students in assigned districts
  - School roles: Students in assigned schools  
  - Case-level roles: Only assigned students
- **CREATE**:
  - District roles: Can create in district schools
  - ASSISTANT: Can create in assigned schools
  - PSYCHOLOGIST/PROCTOR: Cannot create
- **UPDATE**:
  - District/School roles: Students they can access
  - PSYCHOLOGIST: Only assigned students
- **DELETE**: SUPER_USER only

#### student_enrollments
- **READ**: Users who can access the school
- **ALL OPERATIONS**: Users with school access (except case-level roles)

#### student_parents
- **READ**: Users who can access the student
- **ALL OPERATIONS**: Users with student access (except case-level roles)

#### student_languages
- **READ**: Users who can access the student
- **ALL OPERATIONS**: Users with student access (except case-level roles)

### Parents

#### parents
- **READ**: Users who can access associated students
- **CREATE**: All authenticated users
- **UPDATE**: Users who can access associated students
- **DELETE**: SUPER_USER only

### Cases & Assessments

#### cases
- **READ**:
  - District roles: All cases in districts
  - School roles: Cases for students in schools
  - Case-level roles: Only assigned cases
- **CREATE**: All roles except case-level roles
- **UPDATE**: Users with case access
- **DELETE**: SUPER_USER only

#### case_assignments
- **READ**: Assigned user, district management roles
- **CREATE/UPDATE**: SUPER_USER, SPECIAL_ED_DIRECTOR, CASE_MANAGER, CLINICAL_DIRECTOR
- **DELETE**: SUPER_USER only

#### plans
- **READ/ALL OPERATIONS**: Same as case access

#### assessment_sessions
- **READ**: 
  - SPECIAL_ED_DIRECTOR, SCHOOL_COORDINATOR: All sessions (read-only)
  - Assessment roles: Sessions for accessible students
- **CREATE**: CASE_MANAGER, CLINICAL_DIRECTOR, PSYCHOLOGIST, ASSISTANT (for accessible students)
- **UPDATE**: Assessment roles for accessible students
- **DELETE**: SUPER_USER only

#### test_administrations, subtest_scores, index_scores
- **READ**: Same as assessment_sessions

### Documents & Tasks

#### documents
- **READ**: Users with student access
- **CREATE**: Users with student access (as uploader)
- **UPDATE**: Uploader or SUPER_USER
- **DELETE**: SUPER_USER only

#### tasks
- **READ**: Assignee, creator, district members
- **CREATE**: Users in district (with appropriate case/student access)
- **UPDATE**: Assignee, creator, or SUPER_USER
- **DELETE**: SUPER_USER only

### Scheduling

#### availabilities
- **READ**: Own availability, district members' availability
- **ALL OPERATIONS**: Own availability or SUPER_USER

#### district_availabilities, district_blocked_dates
- **READ**: District members
- **ALL OPERATIONS**: District management roles

### User Management

#### invitations
- **READ**: Recipients (by email), user managers in district
- **CREATE**: User managers in their districts
- **UPDATE**: Recipients, user managers in district
- **DELETE**: SUPER_USER only

#### invitation_schools
- **READ**: Same as parent invitation
- **ALL OPERATIONS**: User managers in district

### Reference Tables

#### languages, test_batteries, test_indices, subtests, index_subtest_mappings
- **READ**: All authenticated users

### Special Access

#### addresses
- **READ/ALL OPERATIONS**: Based on parent entity (district, school, student, or parent)

#### join_requests
- **READ**: SUPER_USER only
- **CREATE**: Anonymous users
- **ALL OPERATIONS**: SUPER_USER only

## Key Access Patterns

1. **District Isolation**: Most data is isolated by district membership
2. **Hierarchical Access**: District > School > Student/Case
3. **Role-Based Restrictions**: Different roles have different scopes
4. **Soft Deletes**: Most entities use soft delete flags instead of hard deletion
5. **Audit Trail**: Critical operations restricted to maintain history

## Special Permissions

### User Management Roles
Can manage users and invitations: SPECIAL_ED_DIRECTOR, SCHOOL_COORDINATOR, CASE_MANAGER, SUPER_USER

### Assessment Roles  
Can perform assessments: CASE_MANAGER, CLINICAL_DIRECTOR, PSYCHOLOGIST, ASSISTANT

### View-Only Assessment Access
Can view but not modify assessments: SPECIAL_ED_DIRECTOR, SCHOOL_COORDINATOR 