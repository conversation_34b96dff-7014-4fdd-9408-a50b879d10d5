import { sql } from 'drizzle-orm';
import { pgPolicy } from 'drizzle-orm/pg-core';
import { anonRole, authenticatedRole } from 'drizzle-orm/supabase';
import {
  createAuthenticatedOnlyPolicy,
  createDistrictManagementPolicy,
  createSelectOnlyPolicy,
} from './policy-builders';
import {
  canUserAccessStudent,
  canUserManageDistrictPreferences,
  canUserManageUsers,
  canUserPerformAssessments,
  canUserViewAllAssessments,
  getCurrentUserId,
  hasCaseLevelAccessOnly,
  hasDistrictLevelAccess,
  isUserSuperUser,
  userBelongsToDistrict,
  userHasAnyRole,
} from './policy-helpers';

/*
 * -------------------------------------------------------
 * IMPORTANT: Circular Dependency Prevention
 * -------------------------------------------------------
 * These RLS policies are carefully designed to avoid circular dependencies
 * that can cause "infinite recursion detected" errors in PostgreSQL.
 *
 * Key principles:
 * 1. Never reference a table within its own policy (no self-joins)
 * 2. Avoid indirect circular references (A → B → A)
 * 3. Use IN clauses instead of EXISTS when checking access lists
 * 4. Join through foreign key relationships, not through the target table
 *
 * Common patterns that cause issues:
 * - students policy checking student_enrollments.student_id = students.id
 * - parents policy joining through students table
 * - cases policy joining through students table
 *
 * Instead, we:
 * - Get lists of accessible IDs and check if target ID is in the list
 * - Join directly on foreign key columns without going through the referenced table
 * -------------------------------------------------------
 */

/*
 * -------------------------------------------------------
 * POLICY: Roles
 * -------------------------------------------------------
 */

/**
 * @policy rolesSelectPolicy
 * @description Allows all authenticated users to view system roles
 * @access All authenticated users
 * @security This is a system lookup table that all users need access to for role-based UI rendering
 * @performance O(1) - Simple authentication check only
 */
export const rolesSelectPolicy = createSelectOnlyPolicy(
  'all_users_can_view_roles'
);

/*
 * -------------------------------------------------------
 * POLICY: Permissions
 * -------------------------------------------------------
 */

/**
 * @policy permissionsSelectPolicy
 * @description Allows all authenticated users to view system permissions
 * @access All authenticated users
 * @security This is a system lookup table that all users need access to for permission-based UI rendering
 * @performance O(1) - Simple authentication check only
 */
export const permissionsSelectPolicy = createSelectOnlyPolicy(
  'all_users_can_view_permissions'
);

/*
 * -------------------------------------------------------
 * POLICY: User Roles
 * -------------------------------------------------------
 */

/**
 * @policy userRolesSelectPolicy
 * @description Allows all authenticated users to view user-role associations
 * @access All authenticated users
 * @security Users need to see role assignments to determine access levels in the UI
 * @performance O(1) - Simple authentication check only
 */
export const userRolesSelectPolicy = createSelectOnlyPolicy(
  'all_users_can_view_user_roles'
);

/**
 * @policy userRolesAllPolicy
 * @description Allows all authenticated users to manage user-role associations
 * @access All authenticated users with appropriate permissions
 * @security Role management is controlled at the application layer with additional permission checks
 * @performance O(1) - Simple authentication check only
 * @note This policy is permissive because actual role assignment is restricted by application logic
 */
export const userRolesAllPolicy = createAuthenticatedOnlyPolicy(
  'all_users_can_manage_user_roles',
  'all'
);

/*
 * -------------------------------------------------------
 * POLICY: Role Permissions
 * -------------------------------------------------------
 */

/**
 * @policy rolePermissionsSelectPolicy
 * @description Allows all authenticated users to view role-permission mappings
 * @access All authenticated users
 * @security This is a system lookup table that all users need access to for permission checking
 * @performance O(1) - Simple authentication check only
 */
export const rolePermissionsSelectPolicy = createSelectOnlyPolicy(
  'all_users_can_view_role_permissions'
);

/*
 * -------------------------------------------------------
 * POLICY: Districts
 * -------------------------------------------------------
 */

/**
 * @policy districtsSelectPolicy
 * @description Allows all authenticated users to view district records
 * @access All authenticated users
 * @security Districts are foundational entities that users need to see for navigation and context
 * @performance O(1) - Simple authentication check only
 * @note Districts contain public information like names and addresses
 */
export const districtsSelectPolicy = createAuthenticatedOnlyPolicy(
  'all_users_can_view_districts'
);

/**
 * @policy districtsInsertPolicy
 * @description Restricts district creation to super users only
 * @access SUPER_USER role only
 * @security Districts are top-level organizational units that should only be created by system administrators
 * @performance O(log n) - Single role check with indexed lookup
 * @note Regular users cannot create new districts - this must be done through administrative processes
 */
export const districtsInsertPolicy = pgPolicy(
  'only_superuser_can_create_districts',
  {
    for: 'insert',
    to: authenticatedRole,
    withCheck: isUserSuperUser(),
  }
);

/**
 * @policy districtsUpdatePolicy
 * @description Allows authorized district management roles to update district information
 * @access
 *   - SUPER_USER: Can update any district
 *   - SPECIAL_ED_DIRECTOR: Can update assigned districts
 *   - SCHOOL_COORDINATOR: Can update assigned districts
 *   - CASE_MANAGER: Can update assigned districts
 * @security District updates are sensitive and must be restricted to authorized personnel
 * @performance O(log n) - Role check with district membership verification
 * @note Updates might include district preferences, contact info, or operational settings
 */
export const districtsUpdatePolicy = pgPolicy(
  'authorized_roles_can_update_districts',
  {
    for: 'update',
    to: authenticatedRole,
    using: canUserManageDistrictPreferences(sql`id`),
  }
);

/**
 * @policy districtsDeletePolicy
 * @description Restricts district deletion to super users only
 * @access SUPER_USER role only
 * @security Districts contain critical data and relationships - deletion is extremely restricted
 * @performance O(log n) - Single role check with indexed lookup
 * @note District deletion would cascade to many related entities - handle with extreme caution
 */
export const districtsDeletePolicy = pgPolicy(
  'only_superusers_can_delete_districts',
  {
    for: 'delete',
    to: authenticatedRole,
    using: isUserSuperUser(),
  }
);

/*
 * -------------------------------------------------------
 * POLICY: District Preferences
 * -------------------------------------------------------
 */

/**
 * @policy districtPreferencesSelectPolicy
 * @description Allows users to view preferences for districts they belong to
 * @access Users assigned to the district
 * @security District preferences may contain operational settings that should only be visible to district members
 * @performance O(log n) - EXISTS check on indexed user_districts table
 * @note Preferences might include scheduling settings, notification preferences, etc.
 */
const districtPreferencesPolicies = createDistrictManagementPolicy(
  'users_can_manage_district_preferences',
  'district_preferences'
);

export const districtPreferencesSelectPolicy =
  districtPreferencesPolicies.select;

/**
 * @policy districtPreferencesUpdatePolicy
 * @description Allows authorized roles to manage district preferences
 * @access
 *   - SUPER_USER: Can manage all district preferences
 *   - SPECIAL_ED_DIRECTOR: Can manage preferences for assigned districts
 *   - SCHOOL_COORDINATOR: Can manage preferences for assigned districts
 *   - CASE_MANAGER: Can manage preferences for assigned districts
 * @security Only users with district management permissions can modify preferences
 * @performance O(log n) - Role and district membership checks with indexed lookups
 * @note This includes creating, updating, and deleting preference records
 */
export const districtPreferencesUpdatePolicy = districtPreferencesPolicies.all;

/*
 * -------------------------------------------------------
 * POLICY: Addresses
 * -------------------------------------------------------
 */

/**
 * @policy addressesSelectPolicy
 * @description Controls read access to address records based on entity type
 * @access
 *   - SUPER_USER: Can view all addresses
 *   - District addresses: Viewable by users assigned to that district
 *   - School addresses: Viewable by users in the school's district
 *   - Student addresses: Viewable by users who can access the student
 *   - Parent addresses: Viewable by users who can access associated students
 * @security Addresses contain PII and must be protected based on parent entity access rules
 * @performance O(log n) to O(n) - Varies based on entity type and relationship complexity
 * @note Address visibility follows the same rules as the parent entity
 */
export const addressesSelectPolicy = pgPolicy(
  'users_can_view_addresses_based_on_access',
  {
    for: 'select',
    to: authenticatedRole,
    using: sql`
			${isUserSuperUser()}
			OR
			(district_id IS NOT NULL AND ${userBelongsToDistrict(sql`district_id`)})
			OR
			(school_id IS NOT NULL AND EXISTS (
				SELECT 1 FROM schools s 
				WHERE s.id = school_id 
				AND EXISTS (
					SELECT 1 FROM user_districts ud
					WHERE ud.user_id = ${getCurrentUserId()}
					AND ud.district_id = s.district_id
				)
			))
			OR
			(student_id IS NOT NULL AND ${canUserAccessStudent(sql`student_id`)})
			OR
			(parent_id IS NOT NULL AND EXISTS (
				SELECT 1 FROM student_parents sp
				WHERE sp.parent_id = parent_id
				AND ${canUserAccessStudent(sql`sp.student_id`)}
			))
		`,
  }
);

/**
 * @policy addressesAllPolicy
 * @description Controls create, update, and delete operations on address records
 * @access
 *   - SUPER_USER: Can manage all addresses
 *   - District addresses: Manageable by users with district management permissions
 *   - School addresses: Manageable by users with district management permissions for that school
 *   - Student addresses: Manageable by users who can access the student
 *   - Parent addresses: Manageable by users who can access associated students
 * @security Address modifications follow the same authorization as the parent entity
 * @performance O(log n) to O(n) - Complex checks based on entity relationships
 * @note Address changes should be logged for audit purposes as they contain sensitive information
 */
export const addressesAllPolicy = pgPolicy(
  'users_can_manage_addresses_for_accessible_entities',
  {
    for: 'all',
    to: authenticatedRole,
    using: sql`
			${isUserSuperUser()}
			OR
			(district_id IS NOT NULL AND ${canUserManageDistrictPreferences(sql`district_id`)})
			OR
			(school_id IS NOT NULL AND EXISTS (
				SELECT 1 FROM schools s 
				WHERE s.id = school_id 
				AND ${canUserManageDistrictPreferences(sql`s.district_id`)}
			))
			OR
			(student_id IS NOT NULL AND ${canUserAccessStudent(sql`student_id`)})
			OR
			(parent_id IS NOT NULL AND EXISTS (
				SELECT 1 FROM student_parents sp
				WHERE sp.parent_id = parent_id
				AND ${canUserAccessStudent(sql`sp.student_id`)}
			))
		`,
  }
);

/*
 * -------------------------------------------------------
 * POLICY: Schools
 * -------------------------------------------------------
 */

/**
 * @policy schoolsSelectPolicy
 * @description Allows users to view schools within their assigned districts
 * @access
 *   - SUPER_USER: Can view all schools across all districts
 *   - All other roles: Can view schools in their assigned districts only
 * @security Schools are district-scoped entities visible to all district members
 * @performance O(log n) - EXISTS check on indexed user_districts table
 * @note School information is generally non-sensitive but district-isolated for data segregation
 */
export const schoolsSelectPolicy = pgPolicy(
  'users_can_view_schools_in_their_districts',
  {
    for: 'select',
    to: authenticatedRole,
    using: sql`
			${isUserSuperUser()}
			OR
			EXISTS (
				SELECT 1 FROM user_districts ud
				WHERE ud.user_id = ${getCurrentUserId()}
				AND ud.district_id = district_id
			)
		`,
  }
);

/**
 * @policy schoolsInsertPolicy
 * @description Allows authorized district management roles to create new schools
 * @access
 *   - SUPER_USER: Can create schools in any district
 *   - SPECIAL_ED_DIRECTOR: Can create schools in assigned districts
 *   - SCHOOL_COORDINATOR: Can create schools in assigned districts
 *   - CASE_MANAGER: Can create schools in assigned districts
 * @security School creation is restricted to district management roles to maintain data integrity
 * @performance O(log n) - Role check and district membership verification
 * @note New schools must be associated with a valid district and include required information
 */
export const schoolsInsertPolicy = pgPolicy(
  'authorized_roles_can_create_schools',
  {
    for: 'insert',
    to: authenticatedRole,
    withCheck: sql`
			${isUserSuperUser()}
			OR
			(
				${userHasAnyRole(['SPECIAL_ED_DIRECTOR', 'SCHOOL_COORDINATOR', 'CASE_MANAGER'])}
				AND EXISTS (
					SELECT 1 FROM user_districts ud
					WHERE ud.user_id = ${getCurrentUserId()}
					AND ud.district_id = district_id
				)
			)
		`,
  }
);

/**
 * @policy schoolsUpdatePolicy
 * @description Allows authorized district management roles to update school information
 * @access
 *   - SUPER_USER: Can update any school
 *   - SPECIAL_ED_DIRECTOR: Can update schools in assigned districts
 *   - SCHOOL_COORDINATOR: Can update schools in assigned districts
 *   - CASE_MANAGER: Can update schools in assigned districts
 * @security School updates are restricted to prevent unauthorized modifications to educational facilities
 * @performance O(log n) - Role and district membership checks with indexed lookups
 * @note Updates might include school details, contact information, or operational status
 */
export const schoolsUpdatePolicy = pgPolicy(
  'authorized_roles_can_update_schools',
  {
    for: 'update',
    to: authenticatedRole,
    using: sql`
			${isUserSuperUser()}
			OR
			(
				${userHasAnyRole(['SPECIAL_ED_DIRECTOR', 'SCHOOL_COORDINATOR', 'CASE_MANAGER'])}
				AND EXISTS (
					SELECT 1 FROM user_districts ud
					WHERE ud.user_id = ${getCurrentUserId()}
					AND ud.district_id = district_id
				)
			)
		`,
  }
);

/**
 * @policy schoolsDeletePolicy
 * @description Restricts school deletion to super users only
 * @access SUPER_USER role only
 * @security School deletion has significant impact on students, staff, and records
 * @performance O(log n) - Single role check with indexed lookup
 * @note School deletion should be extremely rare and may require data migration procedures
 */
export const schoolsDeletePolicy = pgPolicy(
  'only_superuser_can_delete_schools',
  {
    for: 'delete',
    to: authenticatedRole,
    using: isUserSuperUser(),
  }
);

/*
 * -------------------------------------------------------
 * POLICY: Students
 * -------------------------------------------------------
 */

/**
 * @policy studentsSelectPolicy
 * @description Controls read access to student records based on complex role-based rules
 * @access
 *   - SUPER_USER: Can view all students across all districts
 *   - SPECIAL_ED_DIRECTOR: Can view all students in assigned districts
 *   - CASE_MANAGER: Can view all students in assigned districts
 *   - CLINICAL_DIRECTOR: Can view all students in assigned districts
 *   - SCHOOL_COORDINATOR: Can view students in assigned schools
 *   - SCHOOL_ADMIN: Can view students in assigned schools
 *   - ASSISTANT: Can view students in assigned schools
 *   - PSYCHOLOGIST: Can only view students they are assigned to through cases
 *   - PROCTOR: Can only view students they are assigned to through cases
 * @security Student records contain sensitive PII and must be strictly access-controlled
 * @performance O(n) - Complex joins based on role type, may benefit from materialized views
 * @note Access patterns vary significantly by role - district-wide vs school-specific vs case-specific
 */
export const studentsSelectPolicy = pgPolicy(
  'users_can_view_students_based_on_role',
  {
    for: 'select',
    to: authenticatedRole,
    using: sql`
			${isUserSuperUser()}
			OR
			(
				${hasDistrictLevelAccess()}
				AND EXISTS (
					SELECT 1
					FROM student_enrollments se
					JOIN schools s ON s.id = se.school_id
					JOIN user_districts ud ON ud.district_id = s.district_id
					WHERE se.student_id = students.id
					AND ud.user_id = ${getCurrentUserId()}
				)
			)
			OR
			(
				${hasCaseLevelAccessOnly()}
				AND EXISTS (
					SELECT 1
					FROM case_assignments ca
					JOIN cases c ON c.id = ca.case_id
					WHERE c.student_id = students.id
					AND ca.user_id = ${getCurrentUserId()}
					AND ca.is_deleted = false
					AND c.is_deleted = false
				)
			)
			OR
			(
				 NOT ${hasDistrictLevelAccess()}
				AND NOT ${hasCaseLevelAccessOnly()}
				AND EXISTS (
					SELECT 1
					FROM student_enrollments se
					JOIN user_schools us ON us.school_id = se.school_id
					WHERE se.student_id = students.id
					AND us.user_id = ${getCurrentUserId()}
				)
			)
		`,
  }
);

/**
 * @policy studentsInsertPolicy
 * @description Controls who can create new student records
 * @access
 *   - SUPER_USER: Can create students in any school
 *   - SPECIAL_ED_DIRECTOR: Can create students in schools within assigned districts
 *   - CASE_MANAGER: Can create students in schools within assigned districts
 *   - CLINICAL_DIRECTOR: Can create students in schools within assigned districts
 *   - ASSISTANT: Can create students in assigned schools only
 * @security Student creation must enforce district/school boundaries
 * @performance O(log n) - Role checks with school/district verification
 * @note PSYCHOLOGIST and PROCTOR roles cannot create students - they only work with existing ones
 */
export const studentsInsertPolicy = pgPolicy(
  'authorized_roles_can_create_students',
  {
    for: 'insert',
    to: authenticatedRole,
    withCheck: sql`
			${isUserSuperUser()}
			OR
			(
				${userHasAnyRole(['SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR'])}
				AND EXISTS (
					SELECT 1 FROM schools s
					JOIN user_districts ud ON ud.district_id = s.district_id
					WHERE s.id = primary_school_id
					AND ud.user_id = ${getCurrentUserId()}
				)
			)
			OR
			(
				${userHasAnyRole(['ASSISTANT'])}
				AND EXISTS (
						SELECT 1 FROM user_schools us
						WHERE us.user_id = ${getCurrentUserId()}
						AND us.school_id = primary_school_id
					)
			)
		`,
  }
);

/**
 * @policy studentsUpdatePolicy
 * @description Controls who can update existing student records
 * @access
 *   - SUPER_USER: Can update any student
 *   - SPECIAL_ED_DIRECTOR: Can update students in assigned districts
 *   - CASE_MANAGER: Can update students in assigned districts
 *   - CLINICAL_DIRECTOR: Can update students in assigned districts
 *   - ASSISTANT: Can update students in assigned schools
 *   - SCHOOL_COORDINATOR: Can update students in assigned schools
 *   - SCHOOL_ADMIN: Can update students in assigned schools
 *   - PSYCHOLOGIST: Can update students they're assigned to through cases
 * @security Updates to student records should be audited and may require additional validation
 * @performance O(n) - Complex access checks based on role and assignment type
 * @note Different roles may have different field-level update permissions enforced at application layer
 */
export const studentsUpdatePolicy = pgPolicy(
  'authorized_roles_can_update_students',
  {
    for: 'update',
    to: authenticatedRole,
    using: sql`
			${isUserSuperUser()}
			OR
			(
				${userHasAnyRole(['SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR'])}
				AND EXISTS (
					SELECT 1 FROM student_enrollments se
					JOIN schools s ON s.id = se.school_id
					JOIN user_districts ud ON ud.district_id = s.district_id
					WHERE se.student_id = students.id
					AND ud.user_id = ${getCurrentUserId()}
				)
			)
			OR
			(
				${userHasAnyRole(['ASSISTANT', 'SCHOOL_COORDINATOR', 'SCHOOL_ADMIN'])}
				AND EXISTS (
					SELECT 1 FROM student_enrollments se
					JOIN user_schools us ON us.school_id = se.school_id
					WHERE se.student_id = students.id
					AND us.user_id = ${getCurrentUserId()}
				)
			)
			OR
			(
				${userHasAnyRole(['PSYCHOLOGIST'])}
				AND EXISTS (
					SELECT 1 FROM cases c
					JOIN case_assignments ca ON ca.case_id = c.id
					WHERE c.student_id = students.id
					AND ca.user_id = ${getCurrentUserId()}
					AND ca.is_deleted = false
					AND c.is_deleted = false
				)
			)
		`,
  }
);

/**
 * @policy studentsDeletePolicy
 * @description Restricts student deletion to super users only
 * @access SUPER_USER role only
 * @security Student records should use soft delete to maintain historical data and relationships
 * @performance O(log n) - Single role check
 * @note Hard deletion of student records should be extremely rare due to legal retention requirements
 */
export const studentsDeletePolicy = pgPolicy(
  'only_superuser_can_delete_students',
  {
    for: 'delete',
    to: authenticatedRole,
    using: isUserSuperUser(),
  }
);

/*
 * -------------------------------------------------------
 * POLICY: Student Enrollments
 * -------------------------------------------------------
 */

/**
 * @policy studentEnrollmentsSelectPolicy
 * @description Controls read access to student enrollment records
 * @access Users who can view the student can also view their enrollment history
 * @security Enrollment data reveals student location and school history
 * @performance O(n) - Inherits complexity from student access checks
 * @note Enrollments track which schools a student has attended over time
 */
export const studentEnrollmentsSelectPolicy = pgPolicy(
  'users_can_view_enrollments_for_accessible_students',
  {
    for: 'select',
    to: authenticatedRole,
    using: sql`
			${isUserSuperUser()}
			OR
			EXISTS (
				SELECT 1
				FROM user_districts ud
				JOIN schools s ON s.district_id = ud.district_id
				WHERE s.id = student_enrollments.school_id
				AND ud.user_id = ${getCurrentUserId()}
			)
			OR
			EXISTS (
				SELECT 1
				FROM user_schools us
				WHERE us.school_id = student_enrollments.school_id
				AND us.user_id = ${getCurrentUserId()}
			)
		`,
  }
);

/**
 * @policy studentEnrollmentsAllPolicy
 * @description Controls create, update, and delete operations on enrollment records
 * @access
 *   - SUPER_USER: Can manage all enrollments
 *   - Users with student access (except case-level only roles) can manage enrollments
 * @security Case-level roles (PSYCHOLOGIST, PROCTOR) cannot modify enrollment data
 * @performance O(n) - Complex student access check plus role verification
 * @note Enrollment changes affect student's school association and should be logged
 */
export const studentEnrollmentsAllPolicy = pgPolicy(
  'users_can_manage_enrollments_for_accessible_students',
  {
    for: 'all',
    to: authenticatedRole,
    using: sql`
			${isUserSuperUser()}
			OR
			(
				NOT ${hasCaseLevelAccessOnly()}
				AND (
					EXISTS (
						SELECT 1
						FROM user_districts ud
						JOIN schools s ON s.district_id = ud.district_id
						WHERE s.id = student_enrollments.school_id
						AND ud.user_id = ${getCurrentUserId()}
					)
					OR
					EXISTS (
						SELECT 1
						FROM user_schools us
						WHERE us.school_id = student_enrollments.school_id
						AND us.user_id = ${getCurrentUserId()}
					)
				)
			)
		`,
  }
);

/*
 * -------------------------------------------------------
 * POLICY: User Districts
 * -------------------------------------------------------
 */

/**
 * @policy userDistrictsSelectPolicy
 * @description Controls visibility of user-district assignments
 * @access
 *   - SUPER_USER: Can view all user-district associations
 *   - Users can view their own district assignments
 *   - Users can view other users' assignments in shared districts
 * @security District membership information is needed for proper access control
 * @performance O(log n) - Efficient EXISTS check on indexed table
 * @note This table is critical for enforcing district-based data isolation
 */
export const userDistrictsSelectPolicy = pgPolicy(
  'users_can_view_district_associations',
  {
    for: 'select',
    to: authenticatedRole,
    using: sql`
			${isUserSuperUser()}
			OR
			user_districts.user_id = ${getCurrentUserId()}
		`,
  }
);

/**
 * @policy userDistrictsAllPolicy
 * @description Controls management of user-district assignments
 * @access
 *   - SUPER_USER: Can manage all assignments
 *   - SPECIAL_ED_DIRECTOR: Can manage assignments within their districts
 *   - SCHOOL_COORDINATOR: Can manage assignments within their districts
 *   - CASE_MANAGER: Can manage assignments within their districts
 * @security Only authorized roles can assign/remove users to/from districts
 * @performance O(log n) - Role check plus district membership verification
 * @note Changes to district assignments affect user's data access scope immediately
 */
export const userDistrictsAllPolicy = pgPolicy(
  'authorized_roles_can_manage_district_associations',
  {
    for: 'all',
    to: authenticatedRole,
    using: sql`
			${isUserSuperUser()}
			OR
			(
				${canUserManageUsers()}
				AND user_districts.user_id = ${getCurrentUserId()}
			)
		`,
  }
);

/*
 * -------------------------------------------------------
 * POLICY: Users
 * -------------------------------------------------------
 */

/**
 * @policy usersSelectPolicy
 * @description Controls read access to user profiles and information
 * @access
 *   - Users can always view their own profile
 *   - SUPER_USER: Can view all user profiles
 *   - Other users: Can view profiles of users in shared districts
 * @security User profiles contain PII but are needed for collaboration features
 * @performance O(1) for self-access, O(log n) for district-based access
 * @note Profile visibility enables team collaboration within district boundaries
 */
export const usersSelectPolicy = pgPolicy(
  'users_can_view_users_in_their_scope',
  {
    for: 'select',
    to: authenticatedRole,
    using: sql`
			id = ${getCurrentUserId()}
			OR
			${isUserSuperUser()}
			OR
			EXISTS (
				SELECT 1 
				FROM user_districts ud_current
				JOIN user_districts ud_target ON ud_current.district_id = ud_target.district_id
				WHERE ud_current.user_id = ${getCurrentUserId()}
				AND ud_target.user_id = users.id
			)
		`,
  }
);

/**
 * @policy usersInsertPolicy
 * @description Prevents direct user creation through the users table
 * @access No direct access - users must be created through the invitation system
 * @security User creation is handled through Supabase Auth and invitation workflow
 * @performance O(1) - Always returns false
 * @note This policy ensures users are properly onboarded with role assignments
 */
export const usersInsertPolicy = pgPolicy('users_cannot_be_created_directly', {
  for: 'insert',
  to: authenticatedRole,
  withCheck: sql`false`,
});

/**
 * @policy usersUpdatePolicy
 * @description Controls who can update user profiles
 * @access
 *   - Users can update their own profile
 *   - SUPER_USER: Can update any user profile
 *   - SPECIAL_ED_DIRECTOR: Can update users in their districts
 *   - SCHOOL_COORDINATOR: Can update users in their districts
 *   - CASE_MANAGER: Can update users in their districts
 * @security Profile updates may include role changes and must be carefully controlled
 * @performance O(1) for self-update, O(log n) for authorized role updates
 * @note Some fields may have additional application-level restrictions
 */
export const usersUpdatePolicy = pgPolicy('authorized_roles_can_update_users', {
  for: 'update',
  to: authenticatedRole,
  using: sql`
		id = ${getCurrentUserId()}
		OR
		${isUserSuperUser()}
		OR
		(
			${canUserManageUsers()}
			AND EXISTS (
				SELECT 1 
				FROM user_districts ud_current
				JOIN user_districts ud_target ON ud_current.district_id = ud_target.district_id
				WHERE ud_current.user_id = ${getCurrentUserId()}
				AND ud_target.user_id = users.id
			)
		)
	`,
});

/**
 * @policy usersDeletePolicy
 * @description Restricts user deletion to super users only
 * @access SUPER_USER role only
 * @security User deletion has cascading effects and should be avoided
 * @performance O(log n) - Single role check
 * @note Prefer deactivating users over deletion to maintain audit trails
 */
export const usersDeletePolicy = pgPolicy('only_superuser_can_delete_users', {
  for: 'delete',
  to: authenticatedRole,
  using: isUserSuperUser(),
});

/*
 * -------------------------------------------------------
 * POLICY: Cases
 * -------------------------------------------------------
 */

/**
 * @policy casesSelectPolicy
 * @description Controls read access to case records based on role-specific rules
 * @access
 *   - SUPER_USER: Can view all cases
 *   - District-level roles: Can view all cases in their districts
 *   - School-level roles: Can view cases for students in their schools
 *   - Case-level roles (PSYCHOLOGIST, PROCTOR): Can only view assigned cases
 * @security Cases contain sensitive assessment and intervention data
 * @performance O(n) - Complex access rules with multiple join patterns
 * @note Case visibility determines access to related assessments, plans, and documents
 */
export const casesSelectPolicy = pgPolicy(
  'users_can_view_cases_based_on_role',
  {
    for: 'select',
    to: authenticatedRole,
    using: sql`
			${isUserSuperUser()}
			OR
			(
				${hasCaseLevelAccessOnly()}
				AND EXISTS (
					SELECT 1
					FROM case_assignments ca
					WHERE ca.user_id = ${getCurrentUserId()}
					AND ca.case_id = cases.id
					AND ca.is_deleted = false
				)
			)
			OR
			(
				${hasDistrictLevelAccess()}
				AND EXISTS (
					SELECT 1
					FROM student_enrollments se
					JOIN schools s ON s.id = se.school_id
					JOIN user_districts ud ON ud.district_id = s.district_id
					WHERE se.student_id = student_id
					AND ud.user_id = ${getCurrentUserId()}
				)
			)
			OR
			(
				NOT ${hasDistrictLevelAccess()}
				AND NOT ${hasCaseLevelAccessOnly()}
				AND EXISTS (
					SELECT 1
					FROM student_enrollments se
					JOIN user_schools us ON us.school_id = se.school_id
					WHERE se.student_id = student_id
					AND us.user_id = ${getCurrentUserId()}
				)
			)
		`,
  }
);

/**
 * @policy casesInsertPolicy
 * @description Controls who can create new case records
 * @access
 *   - SUPER_USER: Can create cases for any student
 *   - All roles except case-level only roles can create cases for accessible students
 * @security Case creation initiates assessment workflows and must be properly authorized
 * @performance O(n) - Student access check plus role verification
 * @note PSYCHOLOGIST and PROCTOR cannot create cases - they are assigned to existing ones
 */
export const casesInsertPolicy = pgPolicy('authorized_roles_can_create_cases', {
  for: 'insert',
  to: authenticatedRole,
  withCheck: sql`
		${isUserSuperUser()}
		OR
		(
			NOT ${hasCaseLevelAccessOnly()}
			AND (
				(
					${hasDistrictLevelAccess()}
					AND EXISTS (
						SELECT 1
						FROM student_enrollments se
						JOIN schools s ON s.id = se.school_id
						JOIN user_districts ud ON ud.district_id = s.district_id
						WHERE se.student_id = student_id
						AND ud.user_id = ${getCurrentUserId()}
					)
				)
				OR
				(
					NOT ${hasDistrictLevelAccess()}
						AND NOT ${hasCaseLevelAccessOnly()}
						AND EXISTS (
							SELECT 1
							FROM student_enrollments se
							JOIN user_schools us ON us.school_id = se.school_id
							WHERE se.student_id = student_id
							AND us.user_id = ${getCurrentUserId()}
						)
				)
			)
		)
	`,
});

/**
 * @policy casesUpdatePolicy
 * @description Controls who can update case information
 * @access Any user with case access can update it
 * @security Case updates should be audited as they affect assessment outcomes
 * @performance O(n) - Inherits complexity from case access check
 * @note Different roles may have different field-level permissions at application layer
 */
export const casesUpdatePolicy = pgPolicy('users_can_update_accessible_cases', {
  for: 'update',
  to: authenticatedRole,
  using: sql`
		${isUserSuperUser()}
		OR
		(
			${hasCaseLevelAccessOnly()}
			AND EXISTS (
				SELECT 1
				FROM case_assignments ca
				WHERE ca.user_id = ${getCurrentUserId()}
				AND ca.case_id = cases.id
				AND ca.is_deleted = false
			)
		)
		OR
		(
			${hasDistrictLevelAccess()}
			AND EXISTS (
				SELECT 1
				FROM student_enrollments se
				JOIN schools s ON s.id = se.school_id
				JOIN user_districts ud ON ud.district_id = s.district_id
				WHERE se.student_id = student_id
				AND ud.user_id = ${getCurrentUserId()}
			)
		)
		OR
		(
			NOT ${hasDistrictLevelAccess()}
				AND NOT ${hasCaseLevelAccessOnly()}
				AND EXISTS (
					SELECT 1
					FROM student_enrollments se
					JOIN user_schools us ON us.school_id = se.school_id
					WHERE se.student_id = student_id
					AND us.user_id = ${getCurrentUserId()}
				)
		)
	`,
});

/**
 * @policy casesDeletePolicy
 * @description Restricts case deletion to super users only
 * @access SUPER_USER role only
 * @security Cases should use soft delete to maintain assessment history
 * @performance O(log n) - Single role check
 * @note Case deletion affects related assessments, assignments, and documents
 */
export const casesDeletePolicy = pgPolicy('only_superuser_can_delete_cases', {
  for: 'delete',
  to: authenticatedRole,
  using: isUserSuperUser(),
});

/*
 * -------------------------------------------------------
 * POLICY: Case Assignments
 * -------------------------------------------------------
 */

/**
 * @policy caseAssignmentsSelectPolicy
 * @description Controls read access to case assignment records
 * @access
 *   - SUPER_USER: Can view all case assignments
 *   - Assigned user: Can view their own assignments
 *   - District management roles: Can view all assignments in their districts
 * @security Case assignments reveal who is working on which cases
 * @performance O(1) for self-access, O(log n) for district access
 * @note Simplified to avoid circular dependency with cases table
 */
export const caseAssignmentsSelectPolicy = pgPolicy(
  'users_can_view_case_assignments_based_on_role',
  {
    for: 'select',
    to: authenticatedRole,
    using: sql`
			${isUserSuperUser()}
			OR
			user_id = ${getCurrentUserId()}
			OR
			(
				${userHasAnyRole(['SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR'])}
				AND EXISTS (
					SELECT 1
					FROM user_districts ud
					WHERE ud.user_id = ${getCurrentUserId()}
				)
			)
		`,
  }
);

/**
 * @policy caseAssignmentsInsertPolicy
 * @description Controls who can create new case assignments
 * @access
 *   - SUPER_USER: Can create any assignment
 *   - SPECIAL_ED_DIRECTOR, CASE_MANAGER, CLINICAL_DIRECTOR: Can create assignments if they have district access
 * @security Case assignment creation affects who can access sensitive case data
 * @performance O(log n) - Simple district membership check
 * @note Simplified to avoid circular dependency - actual case access is validated at application layer
 */
export const caseAssignmentsInsertPolicy = pgPolicy(
  'authorized_roles_can_create_case_assignments',
  {
    for: 'insert',
    to: authenticatedRole,
    withCheck: sql`
			${isUserSuperUser()}
			OR
			(
				${userHasAnyRole(['SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR'])}
				AND EXISTS (
					SELECT 1
					FROM user_districts ud
					WHERE ud.user_id = ${getCurrentUserId()}
				)
			)
		`,
  }
);

/**
 * @policy caseAssignmentsUpdatePolicy
 * @description Controls who can update case assignments (e.g., soft delete)
 * @access
 *   - SUPER_USER: Can update any assignment
 *   - District management roles: Can update assignments if they have district access
 * @security Updates typically involve soft deletion or reassignment
 * @performance O(log n) - Simple district membership check
 * @note Simplified to avoid circular dependency - actual case access is validated at application layer
 */
export const caseAssignmentsUpdatePolicy = pgPolicy(
  'authorized_roles_can_update_case_assignments',
  {
    for: 'update',
    to: authenticatedRole,
    using: sql`
			${isUserSuperUser()}
			OR
			(
				${userHasAnyRole(['SPECIAL_ED_DIRECTOR', 'CASE_MANAGER', 'CLINICAL_DIRECTOR'])}
				AND EXISTS (
					SELECT 1
					FROM user_districts ud
					WHERE ud.user_id = ${getCurrentUserId()}
				)
			)
		`,
  }
);

/**
 * @policy caseAssignmentsDeletePolicy
 * @description Restricts hard deletion of case assignments to super users only
 * @access SUPER_USER role only
 * @security Prefer soft deletion to maintain assignment history
 * @performance O(log n) - Single role check
 * @note Hard deletion should be rare - use soft delete (is_deleted flag) instead
 */
export const caseAssignmentsDeletePolicy = pgPolicy(
  'only_superuser_can_delete_case_assignments',
  {
    for: 'delete',
    to: authenticatedRole,
    using: isUserSuperUser(),
  }
);

/*
 * -------------------------------------------------------
 * POLICY: Parents
 * -------------------------------------------------------
 */

/**
 * @policy parentsSelectPolicy
 * @description Controls read access to parent/guardian records
 * @access
 *   - SUPER_USER: Can view all parents
 *   - Other users: Can view parents of students they have access to
 * @security Parent records contain contact information and PII
 * @performance O(n) - Requires join through student_parents relationship
 * @note Parent visibility is derived from student access permissions
 */
export const parentsSelectPolicy = pgPolicy(
  'users_can_view_parents_based_on_student_access',
  {
    for: 'select',
    to: authenticatedRole,
    using: sql`
			${isUserSuperUser()}
			OR
			EXISTS (
				SELECT 1 FROM student_parents sp
				WHERE sp.parent_id = parents.id
				AND ${canUserAccessStudent(sql`sp.student_id`)}
			)
		`,
  }
);

/**
 * @policy parentsInsertPolicy
 * @description Allows any authenticated user to create parent records
 * @access All authenticated users
 * @security Parent-student relationships are controlled separately
 * @performance O(1) - Simple authentication check
 * @note Actual parent-student linking is controlled by student_parents policies
 */
export const parentsInsertPolicy = pgPolicy(
  'users_can_create_parents_for_accessible_students',
  {
    for: 'insert',
    to: authenticatedRole,
    withCheck: sql`${getCurrentUserId()} IS NOT NULL`,
  }
);

/**
 * @policy parentsUpdatePolicy
 * @description Controls who can update parent information
 * @access
 *   - SUPER_USER: Can update any parent record
 *   - Other users: Can update parents of students they have access to
 * @security Parent information updates should be logged for audit purposes
 * @performance O(n) - Requires student access check through relationship
 * @note Updates might include contact information, preferences, or consent status
 */
export const parentsUpdatePolicy = pgPolicy(
  'users_can_update_parents_for_accessible_students',
  {
    for: 'update',
    to: authenticatedRole,
    using: sql`
			${isUserSuperUser()}
			OR
			EXISTS (
				SELECT 1 FROM student_parents sp
				WHERE sp.parent_id = parents.id
				AND ${canUserAccessStudent(sql`sp.student_id`)}
			)
		`,
  }
);

/**
 * @policy parentsDeletePolicy
 * @description Restricts parent deletion to super users only
 * @access SUPER_USER role only
 * @security Parent records may be referenced in consent forms and communications
 * @performance O(log n) - Single role check
 * @note Consider soft delete to maintain communication history
 */
export const parentsDeletePolicy = pgPolicy(
  'only_superuser_can_delete_parents',
  {
    for: 'delete',
    to: authenticatedRole,
    using: isUserSuperUser(),
  }
);

/*
 * -------------------------------------------------------
 * POLICY: Student Parents
 * -------------------------------------------------------
 */

/**
 * @policy studentParentsSelectPolicy
 * @description Controls read access to student-parent relationship records
 * @access Users who can view the student can also view their parent relationships
 * @security Relationship data reveals family connections and must be protected
 * @performance O(n) - Inherits complexity from student access check
 * @note This junction table links students to their parents/guardians
 */
export const studentParentsSelectPolicy = pgPolicy(
  'users_can_view_student_parent_relationships',
  {
    for: 'select',
    to: authenticatedRole,
    using: sql`
			${isUserSuperUser()}
			OR
			${canUserAccessStudent(sql`student_parents.student_id`)}
		`,
  }
);

/**
 * @policy studentParentsAllPolicy
 * @description Controls management of student-parent relationships
 * @access
 *   - SUPER_USER: Can manage all relationships
 *   - Users with student access (except case-level only roles) can manage relationships
 * @security Case-level roles cannot modify family relationships
 * @performance O(n) - Student access check plus role verification
 * @note Changes affect emergency contacts and parent communication permissions
 */
export const studentParentsAllPolicy = pgPolicy(
  'users_can_manage_student_parent_relationships',
  {
    for: 'all',
    to: authenticatedRole,
    using: sql`
			${isUserSuperUser()}
			OR
			(
				${canUserAccessStudent(sql`student_parents.student_id`)}
				AND NOT ${hasCaseLevelAccessOnly()}
			)
		`,
  }
);

/*
 * -------------------------------------------------------
 * POLICY: Plans
 * -------------------------------------------------------
 */

/**
 * @policy plansSelectPolicy
 * @description Controls read access to intervention and education plans
 * @access
 *   - SUPER_USER: Can view all plans
 *   - PSYCHOLOGIST/PROCTOR: Can view plans for assigned cases
 *   - District-level roles: Can view plans for students in their districts
 *   - School-level roles: Can view plans for students in their schools
 * @security Plans contain sensitive intervention strategies and goals
 * @performance O(n) - Complex joins through students table
 * @note Simplified to avoid circular dependency with cases table
 */
export const plansSelectPolicy = pgPolicy(
  'users_can_view_plans_for_accessible_cases',
  {
    for: 'select',
    to: authenticatedRole,
    using: sql`
			${isUserSuperUser()}
			OR
			(
				${hasCaseLevelAccessOnly()}
				AND EXISTS (
					SELECT 1
					FROM case_assignments ca
					WHERE ca.user_id = ${getCurrentUserId()}
					AND ca.case_id = plans.case_id
					AND ca.is_deleted = false
				)
			)
			OR
			(
				${hasDistrictLevelAccess()}
				AND EXISTS (
					SELECT 1
					FROM student_enrollments se
					JOIN schools s ON s.id = se.school_id
					JOIN user_districts ud ON ud.district_id = s.district_id
					WHERE se.student_id = plans.student_id
					AND ud.user_id = ${getCurrentUserId()}
				)
			)
			OR
			(
				NOT ${hasDistrictLevelAccess()}
				AND NOT ${hasCaseLevelAccessOnly()}
				AND EXISTS (
					SELECT 1
					FROM student_enrollments se
					JOIN user_schools us ON us.school_id = se.school_id
					WHERE se.student_id = plans.student_id
					AND us.user_id = ${getCurrentUserId()}
				)
			)
		`,
  }
);

/**
 * @policy plansAllPolicy
 * @description Controls creation, update, and deletion of plans
 * @access Same as select policy - users who can view plans can also manage them
 * @security Plan modifications should be tracked for compliance purposes
 * @performance O(n) - Complex joins through students table
 * @note Simplified to avoid circular dependency with cases table
 */
export const plansAllPolicy = pgPolicy(
  'users_can_manage_plans_for_accessible_cases',
  {
    for: 'all',
    to: authenticatedRole,
    using: sql`
			${isUserSuperUser()}
			OR
			(
				${hasCaseLevelAccessOnly()}
				AND EXISTS (
					SELECT 1
					FROM case_assignments ca
					WHERE ca.user_id = ${getCurrentUserId()}
					AND ca.case_id = plans.case_id
					AND ca.is_deleted = false
				)
			)
			OR
			(
				${hasDistrictLevelAccess()}
				AND EXISTS (
					SELECT 1
					FROM student_enrollments se
					JOIN schools s ON s.id = se.school_id
					JOIN user_districts ud ON ud.district_id = s.district_id
					WHERE se.student_id = plans.student_id
					AND ud.user_id = ${getCurrentUserId()}
				)
			)
			OR
			(
				NOT ${hasDistrictLevelAccess()}
				AND NOT ${hasCaseLevelAccessOnly()}
				AND EXISTS (
					SELECT 1
					FROM student_enrollments se
					JOIN user_schools us ON us.school_id = se.school_id
					WHERE se.student_id = plans.student_id
					AND us.user_id = ${getCurrentUserId()}
				)
			)
		`,
  }
);

/*
 * -------------------------------------------------------
 * POLICY: Tasks
 * -------------------------------------------------------
 */

/**
 * @policy tasksSelectPolicy
 * @description Controls read access to task assignments and to-dos
 * @access
 *   - Task assignee: Can view tasks assigned to them
 *   - Task creator: Can view tasks they created
 *   - SUPER_USER: Can view all tasks
 *   - District members: Can view all tasks in their districts
 * @security Tasks may contain action items related to student cases
 * @performance O(1) for direct assignments, O(log n) for district checks
 * @note Task visibility promotes accountability and workload transparency
 */
export const tasksSelectPolicy = pgPolicy(
  'users_can_view_tasks_in_their_scope',
  {
    for: 'select',
    to: authenticatedRole,
    using: sql`
			assigned_to_id = ${getCurrentUserId()}
			OR
			assigned_by_id = ${getCurrentUserId()}
			OR
			${isUserSuperUser()}
			OR
			EXISTS (
				SELECT 1 FROM user_districts ud
				WHERE ud.user_id = ${getCurrentUserId()}
				AND ud.district_id = district_id
			)
		`,
  }
);

/**
 * @policy tasksInsertPolicy
 * @description Controls who can create task assignments
 * @access
 *   - Users can create tasks within their districts
 *   - Case/student-related tasks require appropriate access
 * @security Task creation must validate district membership and optional associations
 * @performance O(log n) - District check plus optional case/student checks
 * @note Tasks can be general district tasks or linked to specific cases/students
 */
export const tasksInsertPolicy = pgPolicy(
  'users_can_create_tasks_in_their_scope',
  {
    for: 'insert',
    to: authenticatedRole,
    withCheck: sql`
			${getCurrentUserId()} IS NOT NULL
			AND EXISTS (
				SELECT 1 FROM user_districts ud
				WHERE ud.user_id = ${getCurrentUserId()}
				AND ud.district_id = district_id
			)
			AND (
				case_id IS NULL
				OR (
					${isUserSuperUser()}
					OR
					(
						${hasCaseLevelAccessOnly()}
						AND EXISTS (
							SELECT 1
							FROM case_assignments ca
							WHERE ca.user_id = ${getCurrentUserId()}
							AND ca.case_id = case_id
							AND ca.is_deleted = false
						)
					)
					OR
					(
						EXISTS (
							SELECT 1
							FROM user_districts ud
							WHERE ud.user_id = ${getCurrentUserId()}
							AND ud.district_id = district_id
						)
					)
				)
			)
			AND (
				student_id IS NULL
				OR ${canUserAccessStudent(sql`student_id`)}
			)
		`,
  }
);

/**
 * @policy tasksUpdatePolicy
 * @description Controls who can modify task details
 * @access
 *   - Task assignee: Can update their assigned tasks (e.g., status)
 *   - Task creator: Can update tasks they created
 *   - SUPER_USER: Can update any task
 * @security Both assignees and creators can update to enable collaboration
 * @performance O(1) - Direct ID comparisons with early exit
 * @note Updates typically include status changes, notes, or reassignments
 */
export const tasksUpdatePolicy = pgPolicy('users_can_update_assigned_tasks', {
  for: 'update',
  to: authenticatedRole,
  using: sql`
			assigned_to_id = ${getCurrentUserId()}
			OR
			assigned_by_id = ${getCurrentUserId()}
			OR
			${isUserSuperUser()}
		`,
});

/**
 * @policy tasksDeletePolicy
 * @description Restricts task deletion to super users only
 * @access SUPER_USER role only
 * @security Tasks should be marked complete rather than deleted
 * @performance O(log n) - Single role check
 * @note Task history is important for accountability and workload tracking
 */
export const tasksDeletePolicy = pgPolicy('only_superuser_can_delete_tasks', {
  for: 'delete',
  to: authenticatedRole,
  using: isUserSuperUser(),
});

/*
 * -------------------------------------------------------
 * POLICY: Documents
 * -------------------------------------------------------
 */

/**
 * @policy documentsSelectPolicy
 * @description Controls read access to student-related documents
 * @access
 *   - SUPER_USER: Can view all documents
 *   - Other users: Can view documents for students they have access to
 * @security Documents may contain sensitive educational or medical records
 * @performance O(n) - Inherits complexity from student access check
 * @note Document types might include reports, assessments, consent forms, etc.
 */
export const documentsSelectPolicy = pgPolicy(
  'users_can_view_documents_for_accessible_students',
  {
    for: 'select',
    to: authenticatedRole,
    using: sql`
			${isUserSuperUser()}
			OR
			${canUserAccessStudent(sql`student_id`)}
		`,
  }
);

/**
 * @policy documentsInsertPolicy
 * @description Controls who can upload new documents
 * @access Users with student access can upload documents for those students
 * @security Enforces that uploader is recorded and has appropriate access
 * @performance O(n) - Student access check required
 * @note Document uploads should be scanned and validated at application layer
 */
export const documentsInsertPolicy = pgPolicy(
  'users_can_upload_documents_for_accessible_students',
  {
    for: 'insert',
    to: authenticatedRole,
    withCheck: sql`
			${getCurrentUserId()} IS NOT NULL
			AND ${canUserAccessStudent(sql`student_id`)}
		`,
  }
);

/**
 * @policy documentsUpdatePolicy
 * @description Controls who can modify document metadata
 * @access
 *   - SUPER_USER: Can update any document
 *   - Document uploader: Can update their own documents
 * @security Only uploaders can modify their documents to maintain integrity
 * @performance O(1) - Direct uploader check or super user verification
 * @note Updates typically involve metadata changes, not document content
 */
export const documentsUpdatePolicy = pgPolicy(
  'users_can_update_their_documents',
  {
    for: 'update',
    to: authenticatedRole,
    using: sql`
			${isUserSuperUser()}
			OR
			uploaded_user_id = ${getCurrentUserId()}
		`,
  }
);

/**
 * @policy documentsDeletePolicy
 * @description Restricts document deletion to super users only
 * @access SUPER_USER role only
 * @security Documents are part of the permanent student record
 * @performance O(log n) - Single role check
 * @note Document retention policies may legally prevent deletion
 */
export const documentsDeletePolicy = pgPolicy(
  'only_superuser_can_delete_documents',
  {
    for: 'delete',
    to: authenticatedRole,
    using: isUserSuperUser(),
  }
);

/*
 * -------------------------------------------------------
 * POLICY: Test Batteries
 * -------------------------------------------------------
 */

/**
 * @policy testBatteriesSelectPolicy
 * @description Allows all authenticated users to view available test batteries
 * @access All authenticated users
 * @security Test battery metadata is not sensitive - contains test names and versions
 * @performance O(1) - Simple authentication check
 * @note This is a reference table for available psychological tests
 */
export const testBatteriesSelectPolicy = pgPolicy(
  'all_users_can_view_test_batteries',
  {
    for: 'select',
    to: authenticatedRole,
    using: sql`true`,
  }
);

/*
 * -------------------------------------------------------
 * POLICY: Test Indices
 * -------------------------------------------------------
 */

/**
 * @policy testIndicesSelectPolicy
 * @description Allows all authenticated users to view test index definitions
 * @access All authenticated users
 * @security Index definitions are test metadata, not student data
 * @performance O(1) - Simple authentication check
 * @note Indices define composite scores within test batteries
 */
export const testIndicesSelectPolicy = pgPolicy(
  'all_users_can_view_test_indices',
  {
    for: 'select',
    to: authenticatedRole,
    using: sql`true`,
  }
);

/*
 * -------------------------------------------------------
 * POLICY: Subtests
 * -------------------------------------------------------
 */

/**
 * @policy subtestsSelectPolicy
 * @description Allows all authenticated users to view subtest definitions
 * @access All authenticated users
 * @security Subtest metadata is not sensitive - contains test structure
 * @performance O(1) - Simple authentication check
 * @note Subtests are individual components within test batteries
 */
export const subtestsSelectPolicy = pgPolicy('all_users_can_view_subtests', {
  for: 'select',
  to: authenticatedRole,
  using: sql`true`,
});

/*
 * -------------------------------------------------------
 * POLICY: Index Subtest Mappings
 * -------------------------------------------------------
 */

/**
 * @policy indexSubtestMappingsSelectPolicy
 * @description Allows all authenticated users to view index-subtest relationships
 * @access All authenticated users
 * @security Mapping data is test structure information, not results
 * @performance O(1) - Simple authentication check
 * @note Defines which subtests contribute to which indices
 */
export const indexSubtestMappingsSelectPolicy = pgPolicy(
  'all_users_can_view_index_subtest_mappings',
  {
    for: 'select',
    to: authenticatedRole,
    using: sql`true`,
  }
);

/*
 * -------------------------------------------------------
 * POLICY: Assessment Sessions
 * -------------------------------------------------------
 */

/**
 * @policy assessmentSessionsSelectPolicy
 * @description Controls read access to psychological assessment sessions
 * @access
 *   - SUPER_USER: Can view all sessions
 *   - SPECIAL_ED_DIRECTOR, SCHOOL_COORDINATOR: Can view all assessments (read-only)
 *   - CASE_MANAGER, CLINICAL_DIRECTOR, PSYCHOLOGIST, ASSISTANT: Can view sessions for accessible students
 * @security Assessment sessions contain sensitive psychological evaluation data
 * @performance O(n) - Complex role and student access checks
 * @note Sessions track when and by whom assessments were conducted
 */
export const assessmentSessionsSelectPolicy = pgPolicy(
  'users_can_view_assessment_sessions_based_on_role',
  {
    for: 'select',
    to: authenticatedRole,
    using: sql`
			${isUserSuperUser()}
			OR
			${canUserViewAllAssessments()}
			OR
			(
				${canUserPerformAssessments()}
				AND ${canUserAccessStudent(sql`assessment_sessions.student_id`)}
			)
		`,
  }
);

/**
 * @policy assessmentSessionsInsertPolicy
 * @description Controls who can create new assessment sessions
 * @access Assessment-authorized roles with student access
 * @security Enforces that the creator is recorded as the psychologist
 * @performance O(n) - Role and student access verification
 * @note Session creation initiates the assessment workflow
 */
export const assessmentSessionsInsertPolicy = pgPolicy(
  'authorized_roles_can_create_assessment_sessions',
  {
    for: 'insert',
    to: authenticatedRole,
    withCheck: sql`
			${canUserPerformAssessments()}
			AND ${canUserAccessStudent(sql`assessment_sessions.student_id`)}
			AND assessment_sessions.psychologist_id = ${getCurrentUserId()}
		`,
  }
);

/**
 * @policy assessmentSessionsUpdatePolicy
 * @description Controls who can modify assessment sessions
 * @access
 *   - SUPER_USER: Can update any session
 *   - Assessment-authorized roles: Can update sessions for accessible students
 * @security Assessment data modifications should be tracked for validity
 * @performance O(n) - Role and student access checks
 * @note Updates might include status changes or session metadata
 */
export const assessmentSessionsUpdatePolicy = pgPolicy(
  'authorized_roles_can_update_assessment_sessions',
  {
    for: 'update',
    to: authenticatedRole,
    using: sql`
			${isUserSuperUser()}
			OR
			(
				${canUserPerformAssessments()}
				AND ${canUserAccessStudent(sql`assessment_sessions.student_id`)}
			)
		`,
  }
);

/**
 * @policy assessmentSessionsDeletePolicy
 * @description Restricts assessment session deletion to super users only
 * @access SUPER_USER role only
 * @security Assessment data must be preserved for clinical and legal reasons
 * @performance O(log n) - Single role check
 * @note Assessment records should never be deleted in normal operations
 */
export const assessmentSessionsDeletePolicy = pgPolicy(
  'only_superuser_can_delete_assessment_sessions',
  {
    for: 'delete',
    to: authenticatedRole,
    using: isUserSuperUser(),
  }
);

/*
 * -------------------------------------------------------
 * POLICY: Test Administrations
 * -------------------------------------------------------
 */

/**
 * @policy testAdministrationsSelectPolicy
 * @description Controls read access to individual test administration records
 * @access Same as assessment sessions - based on role and student access
 * @security Test administrations contain actual test instance data
 * @performance O(n) - Requires join through sessions to verify student access
 * @note Each administration represents one test battery given during a session
 */
export const testAdministrationsSelectPolicy = pgPolicy(
  'users_can_view_test_administrations_based_on_role',
  {
    for: 'select',
    to: authenticatedRole,
    using: sql`
			${isUserSuperUser()}
			OR
			${canUserViewAllAssessments()}
			OR
			(
				${canUserPerformAssessments()}
				AND EXISTS (
					SELECT 1 FROM assessment_sessions a
					WHERE a.id = test_administrations.session_id
					AND ${canUserAccessStudent(sql`a.student_id`)}
				)
			)
		`,
  }
);

/*
 * -------------------------------------------------------
 * POLICY: Subtest Scores
 * -------------------------------------------------------
 */

/**
 * @policy subtestScoresSelectPolicy
 * @description Controls read access to individual subtest scores
 * @access Same as test administrations - requires appropriate role and student access
 * @security Subtest scores are sensitive psychological data
 * @performance O(n) - Multiple joins required to verify access
 * @note Raw scores and scaled scores for individual test components
 */
export const subtestScoresSelectPolicy = pgPolicy(
  'users_can_view_subtest_scores_based_on_role',
  {
    for: 'select',
    to: authenticatedRole,
    using: sql`
			${isUserSuperUser()}
			OR
			${canUserViewAllAssessments()}
			OR
			(
				${canUserPerformAssessments()}
				AND EXISTS (
					SELECT 1 FROM test_administrations ta
					JOIN assessment_sessions a ON a.id = ta.session_id
					WHERE ta.id = subtest_scores.administration_id
					AND ${canUserAccessStudent(sql`a.student_id`)}
				)
			)
		`,
  }
);

/*
 * -------------------------------------------------------
 * POLICY: Index Scores
 * -------------------------------------------------------
 */

/**
 * @policy indexScoresSelectPolicy
 * @description Controls read access to composite index scores
 * @access Same as test administrations - requires appropriate role and student access
 * @security Index scores represent overall cognitive/achievement areas
 * @performance O(n) - Multiple joins required to verify access
 * @note Composite scores calculated from multiple subtests
 */
export const indexScoresSelectPolicy = pgPolicy(
  'users_can_view_index_scores_based_on_role',
  {
    for: 'select',
    to: authenticatedRole,
    using: sql`
			${isUserSuperUser()}
			OR
			${canUserViewAllAssessments()}
			OR
			(
				${canUserPerformAssessments()}
				AND EXISTS (
					SELECT 1 FROM test_administrations ta
					JOIN assessment_sessions a ON a.id = ta.session_id
					WHERE ta.id = index_scores.administration_id
					AND ${canUserAccessStudent(sql`a.student_id`)}
				)
			)
		`,
  }
);

/*
 * -------------------------------------------------------
 * POLICY: District Scheduling
 * -------------------------------------------------------
 */

/**
 * @policy districtAvailabilitiesSelectPolicy
 * @description Controls read access to district scheduling availability
 * @access Users in the district can view availability settings
 * @security Availability data affects scheduling but isn't sensitive
 * @performance O(log n) - EXISTS check on indexed user_districts table
 * @note Defines when districts are available for assessments/services
 */
export const districtAvailabilitiesSelectPolicy = pgPolicy(
  'users_can_view_district_availabilities',
  {
    for: 'select',
    to: authenticatedRole,
    using: sql`
			${isUserSuperUser()}
			OR
			EXISTS (
				SELECT 1 FROM user_districts ud
				WHERE ud.user_id = ${getCurrentUserId()}
				AND ud.district_id = district_id
			)
		`,
  }
);

/**
 * @policy districtAvailabilitiesUpdatePolicy
 * @description Controls management of district availability settings
 * @access District management roles can modify availability
 * @security Only authorized roles can affect district-wide scheduling
 * @performance O(log n) - Role and district management check
 * @note Changes affect when services can be scheduled district-wide
 */
export const districtAvailabilitiesUpdatePolicy = pgPolicy(
  'authorized_roles_can_manage_district_availabilities',
  {
    for: 'all',
    to: authenticatedRole,
    using: sql`
			${canUserManageDistrictPreferences(sql`district_id`)}
		`,
  }
);

/**
 * @policy districtBlockedDatesSelectPolicy
 * @description Controls read access to district blocked date settings
 * @access Users in the district can view blocked dates
 * @security Blocked dates are scheduling information, not sensitive
 * @performance O(log n) - EXISTS check on indexed user_districts table
 * @note Defines holidays, breaks, and other non-service dates
 */
export const districtBlockedDatesSelectPolicy = pgPolicy(
  'users_can_view_district_blocked_dates',
  {
    for: 'select',
    to: authenticatedRole,
    using: sql`
			${isUserSuperUser()}
			OR
			EXISTS (
				SELECT 1 FROM user_districts ud
				WHERE ud.user_id = ${getCurrentUserId()}
				AND ud.district_id = district_id
			)
		`,
  }
);

/**
 * @policy districtBlockedDatesUpdatePolicy
 * @description Controls management of district blocked dates
 * @access District management roles can modify blocked dates
 * @security Only authorized roles can block district-wide scheduling
 * @performance O(log n) - Role and district management check
 * @note Changes prevent scheduling during specified dates
 */
export const districtBlockedDatesUpdatePolicy = pgPolicy(
  'authorized_roles_can_manage_district_blocked_dates',
  {
    for: 'all',
    to: authenticatedRole,
    using: sql`
			${canUserManageDistrictPreferences(sql`district_id`)}
		`,
  }
);

/*
 * -------------------------------------------------------
 * POLICY: Invitations
 * -------------------------------------------------------
 */

/**
 * @policy invitationsSelectPolicy
 * @description Controls read access to user invitations
 * @access
 *   - Recipients: Can view their own invitations (by email)
 *   - SUPER_USER: Can view all invitations
 *   - User managers in district: Can view invitations they can manage
 * @security Invitations contain email addresses and role assignments
 * @performance O(1) for email match, O(log n) for management access
 * @note Email check is performed first as it's indexed and most specific
 */
export const invitationsSelectPolicy = pgPolicy('users_can_view_invitations', {
  for: 'select',
  to: authenticatedRole,
  using: sql`
			email = auth.email()
			OR
			${isUserSuperUser()}
			OR
			(
				${canUserManageUsers()}
				AND EXISTS (
					SELECT 1 FROM user_districts ud
					WHERE ud.user_id = ${getCurrentUserId()}
					AND ud.district_id = district_id
				)
			)
		`,
});

/**
 * @policy invitationsInsertPolicy
 * @description Controls who can create new user invitations
 * @access User management roles within their districts
 * @security Enforces inviter tracking and district boundaries
 * @performance O(log n) - Role check and district membership verification
 * @note Invitations are the only way to add new users to the system
 */
export const invitationsInsertPolicy = pgPolicy(
  'authorized_roles_can_create_invitations',
  {
    for: 'insert',
    to: authenticatedRole,
    withCheck: sql`
			${canUserManageUsers()}
			AND EXISTS (
				SELECT 1 FROM user_districts ud
				WHERE ud.user_id = ${getCurrentUserId()}
				AND ud.district_id = district_id
			)
			AND inviter_id = ${getCurrentUserId()}
		`,
  }
);

/**
 * @policy invitationsUpdatePolicy
 * @description Controls who can modify invitation details
 * @access
 *   - Recipients: Can update their own invitations (e.g., accept/decline)
 *   - SUPER_USER: Can update any invitation
 *   - User managers in district: Can update invitations they manage
 * @security Updates might include status changes or role modifications
 * @performance O(1) for email match, O(log n) for management access
 * @note Recipients need update access to accept invitations
 */
export const invitationsUpdatePolicy = pgPolicy(
  'users_can_update_relevant_invitations',
  {
    for: 'update',
    to: authenticatedRole,
    using: sql`
			email = auth.email()
			OR
			${isUserSuperUser()}
			OR
			(
				${canUserManageUsers()}
				AND EXISTS (
					SELECT 1 FROM user_districts ud
					WHERE ud.user_id = ${getCurrentUserId()}
					AND ud.district_id = district_id
				)
			)
		`,
  }
);

/**
 * @policy invitationsDeletePolicy
 * @description Restricts invitation deletion to super users only
 * @access SUPER_USER role only
 * @security Invitations should expire rather than be deleted
 * @performance O(log n) - Single role check
 * @note Maintaining invitation history helps with audit and troubleshooting
 */
export const invitationsDeletePolicy = pgPolicy(
  'only_superuser_can_delete_invitations',
  {
    for: 'delete',
    to: authenticatedRole,
    using: isUserSuperUser(),
  }
);

/*
 * -------------------------------------------------------
 * POLICY: Invitation Schools
 * -------------------------------------------------------
 */

/**
 * @policy invitationSchoolsSelectPolicy
 * @description Controls read access to school assignments within invitations
 * @access Same as parent invitation - recipients and managers
 * @security School assignments determine user's initial access scope
 * @performance O(n) - Requires join to invitations table for access check
 * @note Defines which schools an invited user will have access to
 */
export const invitationSchoolsSelectPolicy = pgPolicy(
  'users_can_view_invitation_schools',
  {
    for: 'select',
    to: authenticatedRole,
    using: sql`
			${isUserSuperUser()}
			OR
			EXISTS (
				SELECT 1 FROM invitations i
				WHERE i.id = invitation_id
				AND (
					i.email = auth.email()
					OR
					(
						${canUserManageUsers()}
						AND EXISTS (
							SELECT 1 FROM user_districts ud
							WHERE ud.user_id = ${getCurrentUserId()}
							AND ud.district_id = i.district_id
						)
					)
				)
			)
		`,
  }
);

/**
 * @policy invitationSchoolsAllPolicy
 * @description Controls management of school assignments in invitations
 * @access User managers within the invitation's district
 * @security Only managers can assign schools to new users
 * @performance O(n) - Multiple joins for invitation and district access
 * @note School assignments affect the invited user's initial permissions
 */
export const invitationSchoolsAllPolicy = pgPolicy(
  'users_can_manage_invitation_schools',
  {
    for: 'all',
    to: authenticatedRole,
    using: sql`
			${isUserSuperUser()}
			OR
			EXISTS (
				SELECT 1 FROM invitations i
				WHERE i.id = invitation_id
				AND ${canUserManageUsers()}
				AND EXISTS (
					SELECT 1 FROM user_districts ud
					WHERE ud.user_id = ${getCurrentUserId()}
						AND ud.district_id = i.district_id
				)
			)
		`,
  }
);

/*
 * -------------------------------------------------------
 * POLICY: Languages
 * -------------------------------------------------------
 */

/**
 * @policy languagesSelectPolicy
 * @description Allows all authenticated users to view the languages reference table
 * @access All authenticated users
 * @security Language list is public reference data
 * @performance O(1) - Simple authentication check
 * @note Contains ISO language codes and names for student language tracking
 */
export const languagesSelectPolicy = pgPolicy('all_users_can_view_languages', {
  for: 'select',
  to: authenticatedRole,
  using: sql`true`,
});

/*
 * -------------------------------------------------------
 * POLICY: Student Languages
 * -------------------------------------------------------
 */

/**
 * @policy studentLanguagesSelectPolicy
 * @description Controls read access to student language proficiency records
 * @access Users who can view the student can see their language information
 * @security Language data may be relevant for educational planning
 * @performance O(n) - Inherits complexity from student access check
 * @note Tracks home languages and English proficiency levels
 */
export const studentLanguagesSelectPolicy = pgPolicy(
  'users_can_view_student_languages',
  {
    for: 'select',
    to: authenticatedRole,
    using: sql`
			${isUserSuperUser()}
			OR
			${canUserAccessStudent(sql`student_id`)}
		`,
  }
);

/**
 * @policy studentLanguagesAllPolicy
 * @description Controls management of student language records
 * @access
 *   - SUPER_USER: Can manage all language records
 *   - Users with student access (except case-level only) can manage languages
 * @security Case-level roles cannot modify language information
 * @performance O(n) - Student access check plus role verification
 * @note Language information affects service delivery and accommodations
 */
export const studentLanguagesAllPolicy = pgPolicy(
  'users_can_manage_student_languages',
  {
    for: 'all',
    to: authenticatedRole,
    using: sql`
			${isUserSuperUser()}
			OR
			(
				${canUserAccessStudent(sql`student_id`)}
				AND NOT ${hasCaseLevelAccessOnly()}
			)
		`,
  }
);

/*
 * -------------------------------------------------------
 * POLICY: Availabilities
 * -------------------------------------------------------
 */

/**
 * @policy availabilitiesSelectPolicy
 * @description Controls read access to user availability schedules
 * @access
 *   - SUPER_USER: Can view all availabilities
 *   - Users can view their own availability
 *   - District members can view availabilities of users in shared districts
 * @security Availability data enables scheduling coordination
 * @performance O(1) for self-access, O(log n) for district-based access
 * @note Used for scheduling assessments and meetings
 */
export const availabilitiesSelectPolicy = pgPolicy(
  'users_can_view_availabilities',
  {
    for: 'select',
    to: authenticatedRole,
    using: sql`
			${isUserSuperUser()}
			OR
			user_id = ${getCurrentUserId()}
			OR
			EXISTS (
				SELECT 1 
				FROM user_districts ud_current
				JOIN user_districts ud_target ON ud_current.district_id = ud_target.district_id
				WHERE ud_current.user_id = ${getCurrentUserId()}
				AND ud_target.user_id = availabilities.user_id
			)
		`,
  }
);

/**
 * @policy availabilitiesAllPolicy
 * @description Controls management of user availability schedules
 * @access
 *   - SUPER_USER: Can manage any availability
 *   - Users can only manage their own availability
 * @security Users control their own schedules for privacy
 * @performance O(1) - Direct user ID comparison
 * @note Changes affect when users can be scheduled for services
 */
export const availabilitiesAllPolicy = pgPolicy(
  'users_can_manage_their_availabilities',
  {
    for: 'all',
    to: authenticatedRole,
    using: sql`
			${isUserSuperUser()}
			OR
			user_id = ${getCurrentUserId()}
		`,
  }
);

/*
 * -------------------------------------------------------
 * POLICY: Join Requests
 * -------------------------------------------------------
 */

/**
 * @policy joinRequestsSelectPolicy
 * @description Controls read access to join requests
 * @access
 *   - SUPER_USER: Can view all join requests
 */
export const joinRequestsSelectPolicy = pgPolicy(
  'users_can_view_join_requests',
  {
    for: 'select',
    to: authenticatedRole,
    using: sql`
		${isUserSuperUser()}
	`,
  }
);

/**
 * @policy joinRequestsInsertPolicy
 * @description Controls who can create new join requests
 * @access Anon users can create new join requests
 * @security Join requests are public and require no authentication
 * @performance O(1) - Simple authentication check
 * @note Join requests are public and require no authentication
 */
export const joinRequestsInsertPolicy = pgPolicy(
  'users_can_manage_join_requests',
  {
    for: 'insert',
    to: anonRole,
    withCheck: sql`
		true
	`,
  }
);

/**
 * @policy joinRequestsUpdatePolicy
 * @description Controls who can update join requests
 * @access
 *   - SUPER_USER: Can manage any join request
 */

export const joinRequestsAllPolicy = pgPolicy(
  'superuser_can_manage_join_requests',
  {
    for: 'all',
    to: authenticatedRole,
    using: sql`
			${isUserSuperUser()}
		`,
  }
);

// --------------------------------------------------------------------------------------
// POLICY: Feedback
// --------------------------------------------------------------------------------------

/**
 * @policy feedbackPolicies
 * @description Controls who can create, update, and delete feedback data
 * @access
 *   - Anyone can view feedback data
 *   - Authenticated users can create feedback (linked to their user ID)
 *   - Users can update/delete their own feedback
 *   - SUPER_USER can update/delete any feedback for moderation purposes
 * @security Feedback must be associated with the authenticated user on creation
 * @performance O(1) for user checks, O(log n) for super user role check
 */
export const feedbackPolicies = [
  pgPolicy('Anyone can get feedback data', {
    for: 'select',
    using: sql`true`,
  }),
  pgPolicy('Only authenticated user can create feedback data', {
    for: 'insert',
    to: authenticatedRole,
    withCheck: sql`user_id = ${getCurrentUserId()}`,
  }),
  pgPolicy(
    'Users can update their own feedback or super users can update any',
    {
      for: 'update',
      to: authenticatedRole,
      using: sql`user_id = ${getCurrentUserId()} OR ${isUserSuperUser()}`,
    }
  ),
  pgPolicy(
    'Users can delete their own feedback or super users can delete any',
    {
      for: 'delete',
      to: authenticatedRole,
      using: sql`user_id = ${getCurrentUserId()} OR ${isUserSuperUser()}`,
    }
  ),
];

export const feedbackFilePolicies = [
  pgPolicy('Anyone can get feedback file data', {
    for: 'select',
    using: sql`true`,
  }),
  pgPolicy('Only authenticated user can create feedback file data', {
    for: 'insert',
    to: authenticatedRole,
    withCheck: sql`EXISTS (
      SELECT 1 FROM feedback f
      WHERE f.id = feedback_files.feedback_id AND f.user_id = ${getCurrentUserId()}
    )`,
  }),
  pgPolicy(
    'Users can delete their own feedback files or super users can delete any',
    {
      for: 'delete',
      to: authenticatedRole,
      using: sql`
			${isUserSuperUser()}
			OR
			EXISTS (
				SELECT 1 FROM feedback f
				WHERE f.id = feedback_files.feedback_id AND f.user_id = ${getCurrentUserId()}
			)`,
    }
  ),
];

// --------------------------------------------------------------------------------------
// POLICY: Notifications
// --------------------------------------------------------------------------------------

/**
 * @policy notificationPolicies
 * @description Controls who can get, update, and delete notifications
 * @access
 *   - Only authenticated users can get their own notifications
 *   - Only authenticated users can update their own notifications
 *   - Only authenticated users can delete their own notifications
 */
export const notificationPolicies = [
  pgPolicy('only_authenticated_users_can_get_their_own_notifications', {
    for: 'select',
    to: authenticatedRole,
    using: sql`auth.uid() = user_id`,
  }),
  pgPolicy('only_authenticated_users_can_update_their_own_notifications', {
    for: 'update',
    to: authenticatedRole,
    using: sql`auth.uid() = user_id`,
  }),
  pgPolicy('only_authenticated_users_can_delete_their_own_notifications', {
    for: 'delete',
    to: authenticatedRole,
    using: sql`auth.uid() = user_id`,
  }),
];

/**
 * @policy userNotificationPreferencePolicies
 * @description Controls who can get, update, and delete user notification preferences
 * @access
 *   - Only authenticated users can get their own notification preferences
 *   - Only authenticated users can update their own notification preferences
 */
export const userNotificationPreferencePolicies = [
  pgPolicy(
    'only_authenticated_users_can_get_their_own_notification_preferences',
    {
      for: 'select',
      to: authenticatedRole,
      using: sql`auth.uid() = user_id`,
    }
  ),
  pgPolicy(
    'only_authenticated_users_can_update_their_own_notification_preferences',
    {
      for: 'update',
      to: authenticatedRole,
      using: sql`auth.uid() = user_id`,
    }
  ),
];

export const allPolicies = [
  // Basic system tables
  rolesSelectPolicy,
  permissionsSelectPolicy,
  rolePermissionsSelectPolicy,
  userRolesSelectPolicy,
  userRolesAllPolicy,

  // Districts and preferences
  districtsSelectPolicy,
  districtsInsertPolicy,
  districtsUpdatePolicy,
  districtsDeletePolicy,
  districtPreferencesSelectPolicy,
  districtPreferencesUpdatePolicy,

  // Addresses
  addressesSelectPolicy,
  addressesAllPolicy,

  // Schools
  schoolsSelectPolicy,
  schoolsInsertPolicy,
  schoolsUpdatePolicy,
  schoolsDeletePolicy,

  // Students and enrollments
  studentsSelectPolicy,
  studentsInsertPolicy,
  studentsUpdatePolicy,
  studentsDeletePolicy,
  studentEnrollmentsSelectPolicy,
  studentEnrollmentsAllPolicy,

  // Users and districts
  usersSelectPolicy,
  usersInsertPolicy,
  usersUpdatePolicy,
  usersDeletePolicy,
  userDistrictsSelectPolicy,
  userDistrictsAllPolicy,

  // Languages
  languagesSelectPolicy,
  studentLanguagesSelectPolicy,
  studentLanguagesAllPolicy,

  // Availabilities
  availabilitiesSelectPolicy,
  availabilitiesAllPolicy,

  // Parents
  parentsSelectPolicy,
  parentsInsertPolicy,
  parentsUpdatePolicy,
  parentsDeletePolicy,
  studentParentsSelectPolicy,
  studentParentsAllPolicy,

  // Cases and plans
  casesSelectPolicy,
  casesInsertPolicy,
  casesUpdatePolicy,
  casesDeletePolicy,
  caseAssignmentsSelectPolicy,
  caseAssignmentsInsertPolicy,
  caseAssignmentsUpdatePolicy,
  caseAssignmentsDeletePolicy,
  plansSelectPolicy,
  plansAllPolicy,

  // Tasks
  tasksSelectPolicy,
  tasksInsertPolicy,
  tasksUpdatePolicy,
  tasksDeletePolicy,

  // Documents
  documentsSelectPolicy,
  documentsInsertPolicy,
  documentsUpdatePolicy,
  documentsDeletePolicy,

  // Psychological Testing
  testBatteriesSelectPolicy,
  testIndicesSelectPolicy,
  subtestsSelectPolicy,
  indexSubtestMappingsSelectPolicy,
  assessmentSessionsSelectPolicy,
  assessmentSessionsInsertPolicy,
  assessmentSessionsUpdatePolicy,
  assessmentSessionsDeletePolicy,
  testAdministrationsSelectPolicy,
  subtestScoresSelectPolicy,
  indexScoresSelectPolicy,

  // District scheduling
  districtAvailabilitiesSelectPolicy,
  districtAvailabilitiesUpdatePolicy,
  districtBlockedDatesSelectPolicy,
  districtBlockedDatesUpdatePolicy,

  // Invitations
  invitationsSelectPolicy,
  invitationsInsertPolicy,
  invitationsUpdatePolicy,
  invitationsDeletePolicy,
  invitationSchoolsSelectPolicy,
  invitationSchoolsAllPolicy,

  // Join Requests
  joinRequestsSelectPolicy,
  joinRequestsInsertPolicy,
  joinRequestsAllPolicy,

  // Feedback
  feedbackPolicies,
  feedbackFilePolicies,

  // Notifications
  notificationPolicies,
  userNotificationPreferencePolicies,
];
