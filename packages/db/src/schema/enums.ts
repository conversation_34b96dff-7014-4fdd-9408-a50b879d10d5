/** biome-ignore-all lint/style/noEnum: Enums can be used as types */
import { pgEnum } from 'drizzle-orm/pg-core';

/*
 * -------------------------------------------------------
 * SECTION: Enums
 * -------------------------------------------------------
 */

export enum SchoolGradeEnum {
  PRESCHOOL = 'PK',
  KINDERGARTEN = 'K',
  FIRST_GRADE = '1',
  SECOND_GRADE = '2',
  THIRD_GRADE = '3',
  FOURTH_GRADE = '4',
  FIFTH_GRADE = '5',
  SIXTH_GRADE = '6',
  SEVENTH_GRADE = '7',
  EIGHTH_GRADE = '8',
  NINTH_GRADE = '9',
  TENTH_GRADE = '10',
  ELEVENTH_GRADE = '11',
  TWELFTH_GRADE = '12',
  UNGRADED = 'U',
  POST_GRADUATE = 'PG',
}

export const SchoolGradeEnumMap = {
  [SchoolGradeEnum.PRESCHOOL]: 'Preschool',
  [SchoolGradeEnum.KINDERGARTEN]: 'Kindergarten',
  [SchoolGradeEnum.FIRST_GRADE]: '1st Grade',
  [SchoolGradeEnum.SECOND_GRADE]: '2nd Grade',
  [SchoolGradeEnum.THIRD_GRADE]: '3rd Grade',
  [SchoolGradeEnum.FOURTH_GRADE]: '4th Grade',
  [SchoolGradeEnum.FIFTH_GRADE]: '5th Grade',
  [SchoolGradeEnum.SIXTH_GRADE]: '6th Grade',
  [SchoolGradeEnum.SEVENTH_GRADE]: '7th Grade',
  [SchoolGradeEnum.EIGHTH_GRADE]: '8th Grade',
  [SchoolGradeEnum.NINTH_GRADE]: '9th Grade',
  [SchoolGradeEnum.TENTH_GRADE]: '10th Grade',
  [SchoolGradeEnum.ELEVENTH_GRADE]: '11th Grade',
  [SchoolGradeEnum.TWELFTH_GRADE]: '12th Grade',
  [SchoolGradeEnum.UNGRADED]: 'Ungraded',
  [SchoolGradeEnum.POST_GRADUATE]: 'Post Graduate',
};

export enum GenderEnum {
  MALE = 'MALE',
  FEMALE = 'FEMALE',
  NON_BINARY = 'NON_BINARY',
  PREFER_NOT_TO_SAY = 'PREFER_NOT_TO_SAY',
  OTHER = 'OTHER',
}

export const GenderEnumMap = {
  [GenderEnum.MALE]: 'Male',
  [GenderEnum.FEMALE]: 'Female',
  [GenderEnum.NON_BINARY]: 'Non-Binary',
  [GenderEnum.PREFER_NOT_TO_SAY]: 'Prefer Not to Say',
  [GenderEnum.OTHER]: 'Other',
};

export enum RoleEnum {
  SUPER_USER = 'SUPER_USER',
  SPECIAL_ED_DIRECTOR = 'SPECIAL_ED_DIRECTOR',
  SCHOOL_COORDINATOR = 'SCHOOL_COORDINATOR',
  SCHOOL_ADMIN = 'SCHOOL_ADMIN',
  PROCTOR = 'PROCTOR',
  CASE_MANAGER = 'CASE_MANAGER',
  CLINICAL_DIRECTOR = 'CLINICAL_DIRECTOR',
  PSYCHOLOGIST = 'PSYCHOLOGIST',
  ASSISTANT = 'ASSISTANT',
}

export const RoleEnumMap = {
  [RoleEnum.SUPER_USER]: 'Super User',
  [RoleEnum.SPECIAL_ED_DIRECTOR]: 'Special Education Director',
  [RoleEnum.SCHOOL_COORDINATOR]: 'School Coordinator',
  [RoleEnum.SCHOOL_ADMIN]: 'School Administrator',
  [RoleEnum.PROCTOR]: 'Proctor',
  [RoleEnum.CASE_MANAGER]: 'Case Manager',
  [RoleEnum.CLINICAL_DIRECTOR]: 'Clinical Director',
  [RoleEnum.PSYCHOLOGIST]: 'Psychologist',
  [RoleEnum.ASSISTANT]: 'Assistant',
};

// Source: http://nces.ed.gov/programs/edge/docs/EDGE_SDGRF_2014_UserDoc.pdf
export enum DistrictTypeEnum {
  ELEMENTARY_DISTRICT = 'ELEMENTARY_DISTRICT', // Regular local district whose highest grade is ≤ 8 (aka "Elementary" or "K-8" district).
  SECONDARY_DISTRICT = 'SECONDARY_DISTRICT', // Regular local district that only operates grades 9–12 (a "High-School" district).
  UNIFIED_DISTRICT = 'UNIFIED_DISTRICT', // Regular local district that covers the full K–12 span.
  SUPERVISORY_UNION_ADMIN = 'SUPERVISORY_UNION_ADMIN', // Agency that provides a shared superintendent/central office for multiple member districts (NCES Type 3).
  REGIONAL_SERVICE_AGENCY = 'REGIONAL_SERVICE_AGENCY', // County superintendent, BOCES, ESC, ESD, RESA, etc. (NCES Type 4).
  STATE_OPERATED_AGENCY = 'STATE_OPERATED_AGENCY', // State-run LEA (schools for the deaf/blind, juvenile-justice schools, etc.—NCES Type 5).
  FEDERAL_OPERATED_AGENCY = 'FEDERAL_OPERATED_AGENCY', // Federal agency (e.g., Bureau of Indian Education, Bureau of Juvenile Justice, etc. - NCES Type 6).
  CHARTER_LEA = 'CHARTER_LEA', // Independent charter organization reported as its own district/LEA (NCES Type 7).
  OTHER_EDUCATION_AGENCY = 'OTHER_EDUCATION_AGENCY', // Any public-school LEA that doesn't fit Types 1-7 (NCES Type 8).
  SPECIALIZED_PUBLIC_DISTRICT = 'SPECIALIZED_PUBLIC_DISTRICT', // Single-purpose or multi-county districts (e.g., magnet, career-technical, etc. - NCES Type 8).
}

export const DistrictTypeMap = {
  [DistrictTypeEnum.ELEMENTARY_DISTRICT]: 'Elementary District',
  [DistrictTypeEnum.SECONDARY_DISTRICT]: 'Secondary District',
  [DistrictTypeEnum.UNIFIED_DISTRICT]: 'Unified District',
  [DistrictTypeEnum.SUPERVISORY_UNION_ADMIN]: 'Supervisory Union Admin',
  [DistrictTypeEnum.REGIONAL_SERVICE_AGENCY]: 'Regional Service Agency',
  [DistrictTypeEnum.STATE_OPERATED_AGENCY]: 'State Operated Agency',
  [DistrictTypeEnum.FEDERAL_OPERATED_AGENCY]: 'Federal Operated Agency',
  [DistrictTypeEnum.CHARTER_LEA]: 'Charter LEA',
  [DistrictTypeEnum.OTHER_EDUCATION_AGENCY]: 'Other Education Agency',
  [DistrictTypeEnum.SPECIALIZED_PUBLIC_DISTRICT]: 'Specialized Public District',
};

// Source: https://nces.ed.gov/ccd/pdf/psu061cgen.pdf
export enum SchoolTypeEnum {
  REGULAR_PUBLIC_PRIMARY = 'REGULAR_PUBLIC_PRIMARY', // District-run public primary school that offers the standard curriculum (base code 1).
  REGULAR_PUBLIC_MIDDLE = 'REGULAR_PUBLIC_MIDDLE', // District-run public middle school that offers the standard curriculum (base code 1).
  REGULAR_PUBLIC_HIGH = 'REGULAR_PUBLIC_HIGH', // District-run public high school that offers the standard curriculum (base code 1).
  REGULAR_PUBLIC_UNIFIED = 'REGULAR_PUBLIC_UNIFIED', // District-run public school that offers the standard curriculum for all grades (base code 11).
  SPECIAL_ED_PUBLIC = 'SPECIAL_ED_PUBLIC', // Public school that primarily serves students with disabilities (code 2).
  VOCATIONAL_PUBLIC = 'VOCATIONAL_PUBLIC', // Public career/technical (CTE) or vocational high school (code 3).
  ALTERNATIVE_PUBLIC = 'ALTERNATIVE_PUBLIC', // Public alternative/other school for at-risk or non-traditional students (code 4).
  REPORTABLE_PROGRAM = 'REPORTABLE_PROGRAM', // Self-contained program that has an NCES ID but is not a standalone school (code 5).
  PUBLIC_CHARTER = 'PUBLIC_CHARTER', // Any public school whose charter gives it statutory autonomy (overlay flag).
  MAGNET_PUBLIC = 'MAGNET_PUBLIC', // 	Public school or program designed to attract a diverse mix of students around a theme (overlay flag).
  VIRTUAL_PUBLIC = 'VIRTUAL_PUBLIC', // 	"Full-virtual" public school—no physical campus; all instruction online (overlay flag).
  DODEA_SCHOOL = 'DODEA_SCHOOL', // School operated by the Department of Defense Education Activity (domestic or overseas).
  BIE_SCHOOL = 'BIE_SCHOOL', // 	School directly funded by the Bureau of Indian Education.
  PRIVATE_CATHOLIC = 'PRIVATE_CATHOLIC', // 	Catholic elementary/secondary school (parochial, diocesan, or private).
  PRIVATE_OTHER_RELIGIOUS = 'PRIVATE_OTHER_RELIGIOUS', // Non-Catholic religious school (e.g., Lutheran, Jewish, Islamic, Christian).
  PRIVATE_NONSECTARIAN = 'PRIVATE_NONSECTARIAN', // Independent or other non-religious private school.
}

export const SchoolTypeEnumMap = {
  [SchoolTypeEnum.REGULAR_PUBLIC_PRIMARY]: 'Regular Public Primary',
  [SchoolTypeEnum.REGULAR_PUBLIC_MIDDLE]: 'Regular Public Middle',
  [SchoolTypeEnum.REGULAR_PUBLIC_HIGH]: 'Regular Public High',
  [SchoolTypeEnum.REGULAR_PUBLIC_UNIFIED]: 'Regular Public Unified',
  [SchoolTypeEnum.SPECIAL_ED_PUBLIC]: 'Special Ed Public',
  [SchoolTypeEnum.VOCATIONAL_PUBLIC]: 'Vocational Public',
  [SchoolTypeEnum.ALTERNATIVE_PUBLIC]: 'Alternative Public',
  [SchoolTypeEnum.REPORTABLE_PROGRAM]: 'Reportable Program',
  [SchoolTypeEnum.PUBLIC_CHARTER]: 'Public Charter',
  [SchoolTypeEnum.MAGNET_PUBLIC]: 'Magnet Public',
  [SchoolTypeEnum.VIRTUAL_PUBLIC]: 'Virtual Public',
  [SchoolTypeEnum.DODEA_SCHOOL]: 'DOD/EA School',
  [SchoolTypeEnum.BIE_SCHOOL]: 'BIE School',
  [SchoolTypeEnum.PRIVATE_CATHOLIC]: 'Private Catholic',
  [SchoolTypeEnum.PRIVATE_OTHER_RELIGIOUS]: 'Private Other Religious',
  [SchoolTypeEnum.PRIVATE_NONSECTARIAN]: 'Private Nonsectarian',
};

export enum AddressTypeEnum {
  PHYSICAL = 'PHYSICAL',
  POSTAL = 'POSTAL',
  BILLING = 'BILLING',
  OTHER = 'OTHER',
}

export const AddressTypeEnumMap = {
  [AddressTypeEnum.PHYSICAL]: 'Physical',
  [AddressTypeEnum.POSTAL]: 'Postal',
  [AddressTypeEnum.BILLING]: 'Billing',
  [AddressTypeEnum.OTHER]: 'Other',
};

export enum DayOfWeekEnum {
  SUNDAY = 'SUNDAY',
  MONDAY = 'MONDAY',
  TUESDAY = 'TUESDAY',
  WEDNESDAY = 'WEDNESDAY',
  THURSDAY = 'THURSDAY',
  FRIDAY = 'FRIDAY',
  SATURDAY = 'SATURDAY',
}

export enum EventStatusEnum {
  SCHEDULED = 'SCHEDULED',
  CANCELLED = 'CANCELLED',
  COMPLETED = 'COMPLETED',
  RESCHEDULED = 'RESCHEDULED',
  NO_SHOW = 'NO_SHOW',
}

export enum EventTypeEnum {
  EVALUATION = 'EVALUATION',
  IEP_MEETING = 'IEP_MEETING',
  OBSERVATION_MEETING = 'OBSERVATION_MEETING',
  GENERAL_MEETING = 'GENERAL_MEETING',
}

export enum AttendeeStatusEnum {
  PENDING = 'PENDING',
  ACCEPTED = 'ACCEPTED',
  DECLINED = 'DECLINED',
  TENTATIVE = 'TENTATIVE',
}

export enum InvitationStatusEnum {
  PENDING = 'PENDING',
  ACCEPTED = 'ACCEPTED',
  REJECTED = 'REJECTED',
  EXPIRED = 'EXPIRED',
}

export enum TaskStageEnum {
  ONBOARDING = 'ONBOARDING',
  PRE_EVALUATION = 'PRE_EVALUATION',
  EVALUATION = 'EVALUATION',
  POST_EVALUATION = 'POST_EVALUATION',
}

export const TaskStageEnumMap = {
  [TaskStageEnum.ONBOARDING]: 'Onboarding',
  [TaskStageEnum.PRE_EVALUATION]: 'Pre-Evaluation',
  [TaskStageEnum.EVALUATION]: 'Evaluation',
  [TaskStageEnum.POST_EVALUATION]: 'Post-Evaluation',
};

export enum TaskTypeEnum {
  // ONBOARDING STAGE
  ASSIGN_PSYCHOLOGIST = 'ASSIGN_PSYCHOLOGIST',
  REASSIGN_PSYCHOLOGIST = 'REASSIGN_PSYCHOLOGIST',
  REVIEW_DISTRICT_ASSIGNMENT = 'REVIEW_DISTRICT_ASSIGNMENT',
  SHIP_EVALUATION_MATERIALS = 'SHIP_EVALUATION_MATERIALS',

  // PRE_EVALUATION STAGE
  UPDATE_AVAILABILITY = 'UPDATE_AVAILABILITY',
  COMPLETE_REFERRAL_FORM = 'COMPLETE_REFERRAL_FORM',
  SCHEDULE_STUDENT_EVALUATIONS = 'SCHEDULE_STUDENT_EVALUATIONS',
  GENERATE_CALENDAR_INVITES = 'GENERATE_CALENDAR_INVITES',
  CREATE_EVALUATION_PLAN = 'CREATE_EVALUATION_PLAN',
  PREPARE_RATING_SCALES = 'PREPARE_RATING_SCALES',
  REVIEW_AND_SEND_RATING_SCALES = 'REVIEW_AND_SEND_RATING_SCALES',
  MONITOR_RATING_SCALES = 'MONITOR_RATING_SCALES',
  PREPARE_ASSESSMENT_MATERIALS = 'PREPARE_ASSESSMENT_MATERIALS',
  PREPARE_FOR_EVALUATION = 'PREPARE_FOR_EVALUATION',

  // EVALUATION STAGE
  JOIN_EVALUATION_AS_PROCTOR = 'JOIN_EVALUATION_AS_PROCTOR',
  JOIN_EVALUATION_AS_PSYCHOLOGIST = 'JOIN_EVALUATION_AS_PSYCHOLOGIST',
  MARK_EVALUATION_COMPLETE = 'MARK_EVALUATION_COMPLETE',
  COMPLETE_STUDENT_INTERVIEW = 'COMPLETE_STUDENT_INTERVIEW',
  UPDLOAD_PROTOCOLS = 'UPLOAD_PROTOCOLS',
  UPDATE_ASSESSMENT_SCORES = 'UPDATE_ASSESSMENT_SCORES',

  // POST_EVALUATION STAGE
  GENERATE_REPORT_DRAFT = 'GENERATE_REPORT_DRAFT',
  FINALIZE_EVALUATION_REPORT = 'FINALIZE_EVALUATION_REPORT',
  SCORE_REPORT_QUALITY = 'SCORE_REPORT_QUALITY',
  REVIEW_FINAL_REPORT = 'REVIEW_FINAL_REPORT',
  MARK_REPORT_RECEIVED = 'MARK_REPORT_RECEIVED',
  SCHEDULE_IEP_MEETING = 'SCHEDULE_IEP_MEETING',
  PREPARE_FOR_IEP_MEETING = 'PREPARE_FOR_IEP_MEETING',
  SEND_MEETING_INVITATIONS = 'SEND_MEETING_INVITATIONS',
  COMPLETE_IEP_MEETING = 'COMPLETE_IEP_MEETING',
}

export const TaskTypeEnumMap = {
  [TaskTypeEnum.ASSIGN_PSYCHOLOGIST]: {
    name: 'Assign psychologist to school district',
    description:
      'Pair school with a psychologist that matches the needs of the school and preferences for scheduling',
    stage: TaskStageEnum.ONBOARDING,
  },
  [TaskTypeEnum.REASSIGN_PSYCHOLOGIST]: {
    name: 'Reassign psychologist to school district',
    description:
      'Reassign school to a different psychologist that matches the needs of the school and preferences for scheduling',
    stage: TaskStageEnum.ONBOARDING,
  },
  [TaskTypeEnum.REVIEW_DISTRICT_ASSIGNMENT]: {
    name: 'Review school district assignment',
    description:
      'Review the onboarding form and demographics of the assigned school district, and accept/reject school assignment based on that',
    stage: TaskStageEnum.ONBOARDING,
  },
  [TaskTypeEnum.SHIP_EVALUATION_MATERIALS]: {
    name: 'Ship evaluation materials',
    description:
      'Ship package to school district with a document camera and physical materials required for the evaluations',
    stage: TaskStageEnum.ONBOARDING,
  },
  [TaskTypeEnum.UPDATE_AVAILABILITY]: {
    name: 'Update your availability',
    description: 'Update your calendar status and availability',
    stage: TaskStageEnum.PRE_EVALUATION,
  },
  [TaskTypeEnum.COMPLETE_REFERRAL_FORM]: {
    name: 'Complete referral form',
    description:
      'Complete the referral form for the school district and upload background information and other relevant documents',
    stage: TaskStageEnum.PRE_EVALUATION,
  },
  [TaskTypeEnum.SCHEDULE_STUDENT_EVALUATIONS]: {
    name: 'Schedule student evaluations',
    description:
      "Use scheduling process to book students into the psychologist's calendar. Enter backup students (if any). Select who should be kept up to date for evaluation from school team.",
    stage: TaskStageEnum.PRE_EVALUATION,
  },
  [TaskTypeEnum.GENERATE_CALENDAR_INVITES]: {
    name: 'Generate calendar invite',
    description: 'Generate a calendar invite for the student evaluation',
    stage: TaskStageEnum.PRE_EVALUATION,
  },
  [TaskTypeEnum.CREATE_EVALUATION_PLAN]: {
    name: 'Create evaluation plan',
    description:
      'Review the list of upcoming evaluations, review student information and submit the evaluation plan',
    stage: TaskStageEnum.PRE_EVALUATION,
  },
  [TaskTypeEnum.PREPARE_RATING_SCALES]: {
    name: 'Prepare rating scales',
    description:
      'Rating scales must be prepared to be sent to teachers and parents.',
    stage: TaskStageEnum.PRE_EVALUATION,
  },
  [TaskTypeEnum.REVIEW_AND_SEND_RATING_SCALES]: {
    name: 'Review and send rating scales',
    description:
      'Review rating scales and send to parents and teachers (as required)',
    stage: TaskStageEnum.PRE_EVALUATION,
  },
  [TaskTypeEnum.MONITOR_RATING_SCALES]: {
    name: 'Monitor rating scales',
    description:
      'Track rating scale completion and send reminders to parents and teachers (as required)',
    stage: TaskStageEnum.PRE_EVALUATION,
  },
  [TaskTypeEnum.PREPARE_ASSESSMENT_MATERIALS]: {
    name: 'Prepare assessment materials',
    description:
      'Set up assessments for the Psychologist including setting up Pearson + other platforms and marks student "evaluation ready".',
    stage: TaskStageEnum.PRE_EVALUATION,
  },
  [TaskTypeEnum.PREPARE_FOR_EVALUATION]: {
    name: 'Prepare for upcoming evaluation',
    description:
      'Review a generated summary of all information relating to the evaluation and student file to ensure well prepared for upcoming evaluation (Includes all backup students)',
    stage: TaskStageEnum.PRE_EVALUATION,
  },
  [TaskTypeEnum.JOIN_EVALUATION_AS_PROCTOR]: {
    name: 'Join upcoming evaluation session with student',
    description: 'Bring student from their classroom to the evaluation room',
    stage: TaskStageEnum.EVALUATION,
  },
  [TaskTypeEnum.JOIN_EVALUATION_AS_PSYCHOLOGIST]: {
    name: 'Join upcoming evaluation session',
    description: 'Join the evaluation zoom meeting from invite',
    stage: TaskStageEnum.EVALUATION,
  },
  [TaskTypeEnum.MARK_EVALUATION_COMPLETE]: {
    name: 'Mark evaluation as complete',
    description: 'Mark the evaluation as complete or request more time',
    stage: TaskStageEnum.EVALUATION,
  },
  [TaskTypeEnum.COMPLETE_STUDENT_INTERVIEW]: {
    name: 'Complete student interview form',
    description:
      'Input notes directly into student interview form on Google Drive',
    stage: TaskStageEnum.EVALUATION,
  },
  [TaskTypeEnum.UPDLOAD_PROTOCOLS]: {
    name: 'Upload protocols used during evaluation',
    description:
      'Upload any paper forms that were used during the evaluation to the student folder.',
    stage: TaskStageEnum.EVALUATION,
  },
  [TaskTypeEnum.UPDATE_ASSESSMENT_SCORES]: {
    name: 'Update assessment scores',
    description:
      'Update Q-interactive with any remaining scores so that the report is ready to be drafted',
    stage: TaskStageEnum.EVALUATION,
  },
  [TaskTypeEnum.GENERATE_REPORT_DRAFT]: {
    name: 'Generate report draft',
    description:
      'Once all notes are submitted generate first draft of the evaluation report and submit to psychologist for review',
    stage: TaskStageEnum.POST_EVALUATION,
  },
  [TaskTypeEnum.FINALIZE_EVALUATION_REPORT]: {
    name: 'Finalize evaluation report',
    description: 'Review first draft and complete final report',
    stage: TaskStageEnum.POST_EVALUATION,
  },
  [TaskTypeEnum.SCORE_REPORT_QUALITY]: {
    name: 'Score report quality',
    description:
      'Review completed evaluation report and score based on quality of report',
    stage: TaskStageEnum.POST_EVALUATION,
  },
  [TaskTypeEnum.REVIEW_FINAL_REPORT]: {
    name: 'Review evaluation final report',
    description:
      'Review final evaluation report and provide edits as necessary',
    stage: TaskStageEnum.POST_EVALUATION,
  },
  [TaskTypeEnum.MARK_REPORT_RECEIVED]: {
    name: 'Final report completed',
    description:
      'Mark the report as "Received" to allow the submitted report to be reviewed prior to IEP meeting and send to parents (as required).',
    stage: TaskStageEnum.POST_EVALUATION,
  },
  [TaskTypeEnum.SCHEDULE_IEP_MEETING]: {
    name: 'Schedule IEP meeting',
    description:
      'Schedule IEP meeting (if not already scheduled) and use meeting scheduling form to book directly into the psychologist calendar',
    stage: TaskStageEnum.POST_EVALUATION,
  },
  [TaskTypeEnum.PREPARE_FOR_IEP_MEETING]: {
    name: 'Prepare for IEP meeting',
    description:
      'You have received an evaluation summary and you are requested to review evaluation to prepare for upcoming meeting',
    stage: TaskStageEnum.POST_EVALUATION,
  },
  [TaskTypeEnum.SEND_MEETING_INVITATIONS]: {
    name: 'Send meeting invitations',
    description: 'Generate calendar invites and share with relevant people',
    stage: TaskStageEnum.POST_EVALUATION,
  },
  [TaskTypeEnum.COMPLETE_IEP_MEETING]: {
    name: 'Complete IEP meeting',
    description:
      'Mark the meeting as "Completed" on platform and close the case.',
    stage: TaskStageEnum.POST_EVALUATION,
  },
} as const;

export enum TaskStatusEnum {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  BLOCKED = 'BLOCKED',
  REJECTED = 'REJECTED',
}

export const TaskStatusEnumMap = {
  [TaskStatusEnum.PENDING]: 'Pending',
  [TaskStatusEnum.IN_PROGRESS]: 'In Progress',
  [TaskStatusEnum.COMPLETED]: 'Completed',
  [TaskStatusEnum.CANCELLED]: 'Cancelled',
  [TaskStatusEnum.BLOCKED]: 'Blocked',
  [TaskStatusEnum.REJECTED]: 'Rejected',
};

export enum TaskPriorityEnum {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  URGENT = 'URGENT',
}

export const TaskPriorityEnumMap = {
  [TaskPriorityEnum.LOW]: 'Low',
  [TaskPriorityEnum.MEDIUM]: 'Medium',
  [TaskPriorityEnum.HIGH]: 'High',
  [TaskPriorityEnum.URGENT]: 'Urgent',
};

export enum TaskHistoryActionEnum {
  CREATED = 'CREATED',
  ASSIGNED = 'ASSIGNED',
  REASSIGNED = 'REASSIGNED',
  STATUS_CHANGED = 'STATUS_CHANGED',
  PRIORITY_CHANGED = 'PRIORITY_CHANGED',
  DUE_DATE_CHANGED = 'DUE_DATE_CHANGED',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  NOTES_ADDED = 'NOTES_ADDED',
  REOPENED = 'REOPENED',
  REJECTED = 'REJECTED',
}

export const TaskHistoryActionEnumMap = {
  [TaskHistoryActionEnum.CREATED]: 'Created',
  [TaskHistoryActionEnum.ASSIGNED]: 'Assigned',
  [TaskHistoryActionEnum.REASSIGNED]: 'Reassigned',
  [TaskHistoryActionEnum.STATUS_CHANGED]: 'Status Changed',
  [TaskHistoryActionEnum.PRIORITY_CHANGED]: 'Priority Changed',
  [TaskHistoryActionEnum.DUE_DATE_CHANGED]: 'Due Date Changed',
  [TaskHistoryActionEnum.COMPLETED]: 'Completed',
  [TaskHistoryActionEnum.CANCELLED]: 'Cancelled',
  [TaskHistoryActionEnum.NOTES_ADDED]: 'Notes Added',
  [TaskHistoryActionEnum.REOPENED]: 'Reopened',
  [TaskHistoryActionEnum.REJECTED]: 'Rejected',
} as const;

export enum CaseStatusEnum {
  READY_FOR_EVALUATION = 'READY_FOR_EVALUATION',
  EVALUATION_IN_PROGRESS = 'EVALUATION_IN_PROGRESS',
  REPORT_IN_PROGRESS = 'REPORT_IN_PROGRESS',
  AWAITING_MEETING = 'AWAITING_MEETING',
  MEETING_COMPLETE = 'MEETING_COMPLETE',
}

export enum CasePriorityEnum {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  URGENT = 'URGENT',
}

export const CasePriorityEnumMap = {
  [CasePriorityEnum.LOW]: 'Low',
  [CasePriorityEnum.MEDIUM]: 'Medium',
  [CasePriorityEnum.HIGH]: 'High',
  [CasePriorityEnum.URGENT]: 'Urgent',
};

export enum CaseTypeEnum {
  INITIAL_EVALUATION = 'INITIAL_EVALUATION',
  TRIENNIAL_EVALUATION = 'TRIENNIAL_EVALUATION',
  REEVALUATION = 'REEVALUATION',
  INDEPENDENT_EVALUATION = 'INDEPENDENT_EVALUATION',
  CHANGE_OF_PLACEMENT = 'CHANGE_OF_PLACEMENT',
  DISCIPLINE_EVALUATION = 'DISCIPLINE_EVALUATION',
  TRANSITION_EVALUATION = 'TRANSITION_EVALUATION',
}

export const CaseTypeEnumMap = {
  [CaseTypeEnum.INITIAL_EVALUATION]: 'Initial Evaluation',
  [CaseTypeEnum.TRIENNIAL_EVALUATION]: 'Triennial Evaluation',
  [CaseTypeEnum.REEVALUATION]: 'Reevaluation',
  [CaseTypeEnum.INDEPENDENT_EVALUATION]: 'Independent Evaluation',
  [CaseTypeEnum.CHANGE_OF_PLACEMENT]: 'Change of Placement',
  [CaseTypeEnum.DISCIPLINE_EVALUATION]: 'Discipline Evaluation',
  [CaseTypeEnum.TRANSITION_EVALUATION]: 'Transition Evaluation',
};

export enum IepStatusEnum {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}

export enum EnrollmentStatusEnum {
  ENROLLED = 'ENROLLED',
  WITHDRAWN = 'WITHDRAWN',
  TRANSFERRED = 'TRANSFERRED',
  GRADUATED = 'GRADUATED',
  SUSPENDED = 'SUSPENDED',
  EXPELLED = 'EXPELLED',
  INACTIVE = 'INACTIVE',
}

export const EnrollmentStatusEnumMap = {
  [EnrollmentStatusEnum.ENROLLED]: 'Enrolled',
  [EnrollmentStatusEnum.WITHDRAWN]: 'Withdrawn',
  [EnrollmentStatusEnum.TRANSFERRED]: 'Transferred',
  [EnrollmentStatusEnum.GRADUATED]: 'Graduated',
  [EnrollmentStatusEnum.SUSPENDED]: 'Suspended',
  [EnrollmentStatusEnum.EXPELLED]: 'Expelled',
  [EnrollmentStatusEnum.INACTIVE]: 'Inactive',
};

export enum ParentRelationshipEnum {
  MOTHER = 'MOTHER',
  FATHER = 'FATHER',
  STEP_MOTHER = 'STEP_MOTHER',
  STEP_FATHER = 'STEP_FATHER',
  GUARDIAN = 'GUARDIAN',
  ADOPTIVE_MOTHER = 'ADOPTIVE_MOTHER',
  ADOPTIVE_FATHER = 'ADOPTIVE_FATHER',
  GRANDMOTHER = 'GRANDMOTHER',
  GRANDFATHER = 'GRANDFATHER',
  AUNT = 'AUNT',
  UNCLE = 'UNCLE',
  FOSTER_MOTHER = 'FOSTER_MOTHER',
  FOSTER_FATHER = 'FOSTER_FATHER',
  OTHER_RELATIVE = 'OTHER_RELATIVE',
  NON_RELATIVE = 'NON_RELATIVE',
  UNKNOWN = 'UNKNOWN',
}

export const ParentRelationshipEnumMap = {
  [ParentRelationshipEnum.MOTHER]: 'Mother',
  [ParentRelationshipEnum.FATHER]: 'Father',
  [ParentRelationshipEnum.STEP_MOTHER]: 'Step Mother',
  [ParentRelationshipEnum.STEP_FATHER]: 'Step Father',
  [ParentRelationshipEnum.GUARDIAN]: 'Guardian',
  [ParentRelationshipEnum.ADOPTIVE_MOTHER]: 'Adoptive Mother',
  [ParentRelationshipEnum.ADOPTIVE_FATHER]: 'Adoptive Father',
  [ParentRelationshipEnum.GRANDMOTHER]: 'Grandmother',
  [ParentRelationshipEnum.GRANDFATHER]: 'Grandfather',
  [ParentRelationshipEnum.AUNT]: 'Aunt',
  [ParentRelationshipEnum.UNCLE]: 'Uncle',
  [ParentRelationshipEnum.FOSTER_MOTHER]: 'Foster Mother',
  [ParentRelationshipEnum.FOSTER_FATHER]: 'Foster Father',
  [ParentRelationshipEnum.OTHER_RELATIVE]: 'Other Relative',
  [ParentRelationshipEnum.NON_RELATIVE]: 'Non-Relative',
  [ParentRelationshipEnum.UNKNOWN]: 'Unknown',
};

export enum PlanTypeEnum {
  IEP = 'IEP',
  PLAN_504 = '504',
  BIP = 'BIP',
  SST = 'SST',
}

export const PlanTypeEnumMap = {
  [PlanTypeEnum.IEP]: 'IEP',
  [PlanTypeEnum.PLAN_504]: '504 Plan',
  [PlanTypeEnum.BIP]: 'Behavior Intervention Plan (BIP)',
  [PlanTypeEnum.SST]: 'Student Study Team (SST)',
} as const;

export enum PlanStatusEnum {
  PENDING = 'PENDING',
  ACTIVE = 'ACTIVE',
  CANCELLED = 'CANCELLED',
}

export const PlanStatusEnumMap = {
  [PlanStatusEnum.PENDING]: 'Pending',
  [PlanStatusEnum.ACTIVE]: 'Active',
  [PlanStatusEnum.CANCELLED]: 'Cancelled',
} as const;

export enum DocumentCategoryEnum {
  BACKGROUND = 'BACKGROUND',
  ASSESSMENT = 'ASSESSMENT',
}

export enum PreferenceCategoryEnum {
  NOTIFICATIONS = 'NOTIFICATIONS',
  USER_MANAGEMENT = 'USER_MANAGEMENT',
  WORKFLOW = 'WORKFLOW',
  REPORTING = 'REPORTING',
  INTEGRATIONS = 'INTEGRATIONS',
  EVALUATION = 'EVALUATION',
  SECURITY = 'SECURITY',
}

export enum PreferenceTypeEnum {
  BOOLEAN = 'BOOLEAN',
  STRING = 'STRING',
  NUMBER = 'NUMBER',
  JSON = 'JSON',
}

export enum AvailabilityTypeEnum {
  EVALUATION = 'EVALUATION',
  MEETING = 'MEETING',
  CONSULTATION = 'CONSULTATION',
  ALL = 'ALL',
}

export const AvailabilityTypeEnumMap = {
  [AvailabilityTypeEnum.EVALUATION]: 'Evaluation',
  [AvailabilityTypeEnum.MEETING]: 'Meeting',
  [AvailabilityTypeEnum.CONSULTATION]: 'Consultation',
  [AvailabilityTypeEnum.ALL]: 'All Activities',
};

export enum BlockedDateTypeEnum {
  HOLIDAY = 'HOLIDAY',
  STAFF_TRAINING = 'STAFF_TRAINING',
  DISTRICT_CLOSURE = 'DISTRICT_CLOSURE',
  MAINTENANCE = 'MAINTENANCE',
  SPECIAL_EVENT = 'SPECIAL_EVENT',
  VACATION = 'VACATION',
  WEATHER = 'WEATHER',
  EMERGENCY = 'EMERGENCY',
  OTHER = 'OTHER',
}

export const BlockedDateTypeEnumMap = {
  [BlockedDateTypeEnum.HOLIDAY]: 'Holiday',
  [BlockedDateTypeEnum.STAFF_TRAINING]: 'Staff Training',
  [BlockedDateTypeEnum.DISTRICT_CLOSURE]: 'District Closure',
  [BlockedDateTypeEnum.MAINTENANCE]: 'Maintenance',
  [BlockedDateTypeEnum.SPECIAL_EVENT]: 'Special Event',
  [BlockedDateTypeEnum.VACATION]: 'Vacation',
  [BlockedDateTypeEnum.WEATHER]: 'Weather',
  [BlockedDateTypeEnum.EMERGENCY]: 'Emergency',
  [BlockedDateTypeEnum.OTHER]: 'Other',
};

export enum NotificationCategoryTypeEnum {
  GENERAL = 'GENERAL',
  WORKFLOW = 'WORKFLOW',
  ADMINISTRATIVE = 'ADMINISTRATIVE',
}

export const NotificationCategoryTypeEnumMap = {
  [NotificationCategoryTypeEnum.GENERAL]: 'General',
  [NotificationCategoryTypeEnum.WORKFLOW]: 'Workflow',
  [NotificationCategoryTypeEnum.ADMINISTRATIVE]: 'Administrative',
};

export enum NotificationTypeEnum {
  // General types
  GENERAL = 'GENERAL',
  SYSTEM_UPDATE = 'SYSTEM_UPDATE',
  REMINDER = 'REMINDER',

  // Workflow types
  TASK_ASSIGNED = 'TASK_ASSIGNED',
  TASK_ACCEPTED = 'TASK_ACCEPTED',
  TASK_REJECTED = 'TASK_REJECTED',
  TASK_COMPLETED = 'TASK_COMPLETED',
  TASK_CANCELLED = 'TASK_CANCELLED',
  TASK_DUE_SOON = 'TASK_DUE_SOON',
  TASK_OVERDUE = 'TASK_OVERDUE',
  EVALUATION_SCHEDULED = 'EVALUATION_SCHEDULED',
  RATING_SCALES_REMINDER = 'RATING_SCALES_REMINDER',
  UPCOMING_EVALUATION_REMINDER = 'UPCOMING_EVALUATION_REMINDER',
  MEETING_SCHEDULED = 'MEETING_SCHEDULED',
  DEADLINE_APPROACHING = 'DEADLINE_APPROACHING',
  APPROVAL_NEEDED = 'APPROVAL_NEEDED',
  REPORT_READY = 'REPORT_READY',

  // Administrative types
  POLICY_UPDATE = 'POLICY_UPDATE',
  MAINTENANCE_ALERT = 'MAINTENANCE_ALERT',
  SECURITY_ALERT = 'SECURITY_ALERT',
}

export enum FeedbackTypeEnum {
  BUG = 'BUG',
  FEATURE_REQUEST = 'FEATURE_REQUEST',
  GENERAL = 'GENERAL',
}

export const FeedbackTypeEnumMap = {
  [FeedbackTypeEnum.BUG]: 'Bug',
  [FeedbackTypeEnum.FEATURE_REQUEST]: 'Feature Request',
  [FeedbackTypeEnum.GENERAL]: 'General',
};

export enum FeedbackIssueTypeEnum {
  VISUAL = 'VISUAL',
  FUNCTIONALITY = 'FUNCTIONALITY',
  PERFORMANCE = 'PERFORMANCE',
  SECURITY = 'SECURITY',
  DATA = 'DATA',
  USABILITY = 'USABILITY',
  ACCESSIBILITY = 'ACCESSIBILITY',
  OTHER = 'OTHER',
}

export const FeedbackIssueTypeEnumMap = {
  [FeedbackIssueTypeEnum.VISUAL]: 'Visual',
  [FeedbackIssueTypeEnum.FUNCTIONALITY]: 'Functionality',
  [FeedbackIssueTypeEnum.PERFORMANCE]: 'Performance',
  [FeedbackIssueTypeEnum.SECURITY]: 'Security',
  [FeedbackIssueTypeEnum.DATA]: 'Data',
  [FeedbackIssueTypeEnum.USABILITY]: 'Usability',
  [FeedbackIssueTypeEnum.ACCESSIBILITY]: 'Accessibility',
  [FeedbackIssueTypeEnum.OTHER]: 'Other',
};

export enum FeedbackStatusEnum {
  OPEN = 'OPEN',
  IN_PROGRESS = 'IN_PROGRESS',
  RESOLVED = 'RESOLVED',
  RELEASED = 'RELEASED',
  REJECTED = 'REJECTED',
}

export const FeedbackStatusEnumMap = {
  [FeedbackStatusEnum.OPEN]: 'Open',
  [FeedbackStatusEnum.IN_PROGRESS]: 'In Progress',
  [FeedbackStatusEnum.RESOLVED]: 'Resolved',
  [FeedbackStatusEnum.RELEASED]: 'Released',
  [FeedbackStatusEnum.REJECTED]: 'Rejected',
};

/*
 * -------------------------------------------------------
 * SECTION: Psychological Testing Enums
 * -------------------------------------------------------
 */

export enum TestCategoryEnum {
  COGNITIVE_ASSESSMENT = 'COGNITIVE_ASSESSMENT',
  ACADEMIC_ACHIEVEMENT = 'ACADEMIC_ACHIEVEMENT',
  SOCIAL_EMOTIONAL_ASSESSMENT = 'SOCIAL_EMOTIONAL_ASSESSMENT',
  NEUROPSYCHOLOGICAL_ASSESSMENT = 'NEUROPSYCHOLOGICAL_ASSESSMENT',
  ADAPTIVE_BEHAVIOR = 'ADAPTIVE_BEHAVIOR',
}

export const TestCategoryEnumMap = {
  [TestCategoryEnum.COGNITIVE_ASSESSMENT]: 'Cognitive Assessment',
  [TestCategoryEnum.ACADEMIC_ACHIEVEMENT]: 'Academic Achievement',
  [TestCategoryEnum.SOCIAL_EMOTIONAL_ASSESSMENT]: 'Social-Emotional Assessment',
  [TestCategoryEnum.NEUROPSYCHOLOGICAL_ASSESSMENT]:
    'Neuropsychological Assessment',
  [TestCategoryEnum.ADAPTIVE_BEHAVIOR]: 'Adaptive Behavior',
};

export enum IndexTypeEnum {
  PRIMARY = 'PRIMARY',
  COMPOSITE = 'COMPOSITE',
  SUPPLEMENTAL = 'SUPPLEMENTAL',
  PROCESS_SPECIFIC = 'PROCESS_SPECIFIC',
  ANCILLARY = 'ANCILLARY',
}

export const IndexTypeEnumMap = {
  [IndexTypeEnum.PRIMARY]: 'Primary',
  [IndexTypeEnum.COMPOSITE]: 'Composite',
  [IndexTypeEnum.SUPPLEMENTAL]: 'Supplemental',
  [IndexTypeEnum.PROCESS_SPECIFIC]: 'Process Specific',
  [IndexTypeEnum.ANCILLARY]: 'Ancillary',
};

export enum SubtestTypeEnum {
  CORE = 'CORE',
  SUPPLEMENTAL = 'SUPPLEMENTAL',
  PROCESS_SPECIFIC = 'PROCESS_SPECIFIC',
  OPTIONAL = 'OPTIONAL',
  ANCILLARY = 'ANCILLARY',
  RATING_SCALE = 'RATING_SCALE',
}

export const SubtestTypeEnumMap = {
  [SubtestTypeEnum.CORE]: 'Core',
  [SubtestTypeEnum.SUPPLEMENTAL]: 'Supplemental',
  [SubtestTypeEnum.PROCESS_SPECIFIC]: 'Process Specific',
  [SubtestTypeEnum.OPTIONAL]: 'Optional',
  [SubtestTypeEnum.ANCILLARY]: 'Ancillary',
  [SubtestTypeEnum.RATING_SCALE]: 'Rating Scale',
};

export enum SessionTypeEnum {
  INITIAL_EVALUATION = 'INITIAL_EVALUATION',
  TRIENNIAL_EVALUATION = 'TRIENNIAL_EVALUATION',
  REEVALUATION = 'REEVALUATION',
  INDEPENDENT_EVALUATION = 'INDEPENDENT_EVALUATION',
  PROGRESS_MONITORING = 'PROGRESS_MONITORING',
  DIAGNOSTIC_CLARIFICATION = 'DIAGNOSTIC_CLARIFICATION',
  COMPREHENSIVE_EVALUATION = 'COMPREHENSIVE_EVALUATION',
}

export const SessionTypeEnumMap = {
  [SessionTypeEnum.INITIAL_EVALUATION]: 'Initial Evaluation',
  [SessionTypeEnum.TRIENNIAL_EVALUATION]: 'Triennial Evaluation',
  [SessionTypeEnum.REEVALUATION]: 'Reevaluation',
  [SessionTypeEnum.INDEPENDENT_EVALUATION]: 'Independent Evaluation',
  [SessionTypeEnum.PROGRESS_MONITORING]: 'Progress Monitoring',
  [SessionTypeEnum.DIAGNOSTIC_CLARIFICATION]: 'Diagnostic Clarification',
  [SessionTypeEnum.COMPREHENSIVE_EVALUATION]: 'Comprehensive Evaluation',
};

export enum SessionStatusEnum {
  SCHEDULED = 'SCHEDULED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  RESCHEDULED = 'RESCHEDULED',
  INCOMPLETE = 'INCOMPLETE',
}

export const SessionStatusEnumMap = {
  [SessionStatusEnum.SCHEDULED]: 'Scheduled',
  [SessionStatusEnum.IN_PROGRESS]: 'In Progress',
  [SessionStatusEnum.COMPLETED]: 'Completed',
  [SessionStatusEnum.CANCELLED]: 'Cancelled',
  [SessionStatusEnum.RESCHEDULED]: 'Rescheduled',
  [SessionStatusEnum.INCOMPLETE]: 'Incomplete',
};

export enum AdminStatusEnum {
  PLANNED = 'PLANNED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  DISCONTINUED = 'DISCONTINUED',
  INVALID = 'INVALID',
  PARTIAL = 'PARTIAL',
}

export const AdminStatusEnumMap = {
  [AdminStatusEnum.PLANNED]: 'Planned',
  [AdminStatusEnum.IN_PROGRESS]: 'In Progress',
  [AdminStatusEnum.COMPLETED]: 'Completed',
  [AdminStatusEnum.DISCONTINUED]: 'Discontinued',
  [AdminStatusEnum.INVALID]: 'Invalid',
  [AdminStatusEnum.PARTIAL]: 'Partial',
};

/*
 * -------------------------------------------------------
 * SECTION: PG Enums
 * -------------------------------------------------------
 */
export const roleEnum = pgEnum('role', RoleEnum);
export const genderEnum = pgEnum('gender', GenderEnum);
export const addressTypeEnum = pgEnum('address_type', AddressTypeEnum);
export const districtTypeEnum = pgEnum('district_type', DistrictTypeEnum);
export const schoolTypeEnum = pgEnum('school_type', SchoolTypeEnum);
export const dayOfWeekEnum = pgEnum('day_of_week', DayOfWeekEnum);
export const eventStatusEnum = pgEnum('event_status', EventStatusEnum);
export const eventTypeEnum = pgEnum('event_type', EventTypeEnum);
export const attendeeStatusEnum = pgEnum('attendee_status', AttendeeStatusEnum);
export const invitationStatusEnum = pgEnum(
  'invitation_status',
  InvitationStatusEnum
);
export const taskTypeEnum = pgEnum('task_type', TaskTypeEnum);
export const taskStatusEnum = pgEnum('task_status', TaskStatusEnum);
export const taskPriorityEnum = pgEnum('task_priority', TaskPriorityEnum);
export const taskHistoryActionEnum = pgEnum(
  'task_history_action',
  TaskHistoryActionEnum
);
export const caseStatusEnum = pgEnum('case_status', CaseStatusEnum);
export const casePriorityEnum = pgEnum('case_priority', CasePriorityEnum);
export const caseTypeEnum = pgEnum('case_type', CaseTypeEnum);
export const enrollmentStatusEnum = pgEnum(
  'enrollment_status',
  EnrollmentStatusEnum
);
export const parentRelationshipEnum = pgEnum(
  'parent_relationship',
  ParentRelationshipEnum
);
export const iepStatusEnum = pgEnum('iep_status', IepStatusEnum);
export const planTypeEnum = pgEnum('plan_type', PlanTypeEnum);
export const planStatusEnum = pgEnum('plan_status', PlanStatusEnum);
export const documentCategoryEnum = pgEnum(
  'document_category',
  DocumentCategoryEnum
);
export const preferenceCategoryEnum = pgEnum(
  'preference_category',
  PreferenceCategoryEnum
);
export const preferenceTypeEnum = pgEnum('preference_type', PreferenceTypeEnum);

// Psychological Testing pgEnums
export const testCategoryEnum = pgEnum('test_category', TestCategoryEnum);
export const indexTypeEnum = pgEnum('index_type', IndexTypeEnum);
export const subtestTypeEnum = pgEnum('subtest_type', SubtestTypeEnum);
export const sessionTypeEnum = pgEnum('session_type', SessionTypeEnum);
export const sessionStatusEnum = pgEnum('session_status', SessionStatusEnum);
export const adminStatusEnum = pgEnum('admin_status', AdminStatusEnum);

// District Scheduling pgEnums
export const availabilityTypeEnum = pgEnum(
  'availability_type',
  AvailabilityTypeEnum
);
export const blockedDateTypeEnum = pgEnum(
  'blocked_date_type',
  BlockedDateTypeEnum
);

// Notification Category pgEnums
export const notificationCategoryTypeEnum = pgEnum(
  'notification_category_type',
  NotificationCategoryTypeEnum
);
// Feedback pgEnums
export const feedbackTypeEnum = pgEnum('feedback_type', FeedbackTypeEnum);
export const feedbackIssueTypeEnum = pgEnum(
  'issue_type',
  FeedbackIssueTypeEnum
);
export const feedbackStatusEnum = pgEnum('status', FeedbackStatusEnum);
