/** biome-ignore-all lint/performance/noNamespaceImport: Ignore */
import type { SQL } from 'drizzle-orm';
import { relations, sql } from 'drizzle-orm';
import {
  boolean,
  date,
  foreignKey,
  index,
  integer,
  jsonb,
  pgTable,
  text,
  timestamp,
  unique,
  uniqueIndex,
  uuid,
  varchar,
} from 'drizzle-orm/pg-core';
import * as Policies from '../policies/policies';
import { authUsersTable } from './auth';
import * as Enums from './enums';

/*
 * -------------------------------------------------------
 * SECTION: Tables
 * -------------------------------------------------------
 */
export const usersTable = pgTable(
  'users',
  {
    id: uuid('id').primaryKey(),
    firstName: varchar('first_name', { length: 255 }).notNull(),
    middleName: varchar('middle_name', { length: 255 }),
    lastName: varchar('last_name', { length: 255 }).notNull(),
    email: varchar('email', { length: 255 }).notNull(),
    fullName: varchar('full_name', { length: 255 })
      .notNull()
      .generatedAlwaysAs(
        (): SQL =>
          sql`concat_names(${usersTable.firstName}, ${usersTable.middleName}, ${usersTable.lastName})`
      ),
    avatar: text('avatar'),
    isOnboarded: boolean('is_onboarded').notNull().default(true),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },

  (table) => [
    foreignKey({
      columns: [table.id],
      foreignColumns: [authUsersTable.id],
    }).onDelete('cascade'),
    index('user_name_idx').on(table.fullName),
    index('user_email_idx').on(table.email),
    Policies.usersSelectPolicy,
    Policies.usersInsertPolicy,
    Policies.usersUpdatePolicy,
    Policies.usersDeletePolicy,
  ]
);

export const rolesTable = pgTable(
  'roles',
  {
    id: uuid('id').primaryKey().default(sql`uuid_generate_v7()`),
    name: Enums.roleEnum('name').notNull(),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [index('role_name_idx').on(table.name), Policies.rolesSelectPolicy]
);

export const permissionsTable = pgTable(
  'permissions',
  {
    id: uuid('id').primaryKey().default(sql`uuid_generate_v7()`),
    name: varchar('name', { length: 255 }).notNull(),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    index('permission_name_idx').on(table.name),
    Policies.permissionsSelectPolicy,
  ]
);

export const rolePermissionsTable = pgTable(
  'role_permissions',
  {
    id: uuid('id').primaryKey().default(sql`uuid_generate_v7()`),
    roleId: uuid('role_id')
      .notNull()
      .references(() => rolesTable.id),
    permissionId: uuid('permission_id')
      .notNull()
      .references(() => permissionsTable.id),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    index('role_permission_role_idx').on(table.roleId),
    index('role_permission_permission_idx').on(table.permissionId),
    Policies.rolePermissionsSelectPolicy,
  ]
);

// Define addressesTable first without foreign key references to avoid circular deps
export const addressesTable = pgTable(
  'addresses',
  {
    id: uuid('id').primaryKey().default(sql`uuid_generate_v7()`),
    type: Enums.addressTypeEnum('type')
      .notNull()
      .default(Enums.AddressTypeEnum.PHYSICAL),
    address: varchar('address', { length: 255 }).notNull(),
    address2: varchar('address2', { length: 255 }),
    city: varchar('city', { length: 255 }).notNull(),
    state: varchar('state', { length: 255 }).notNull(),
    zipcode: varchar('zipcode', { length: 255 }).notNull(),
    // Polymorphic reference fields - will be constrained via foreign keys below
    studentId: uuid('student_id'),
    districtId: uuid('district_id'),
    schoolId: uuid('school_id'),
    parentId: uuid('parent_id'),
    isPrimary: boolean('is_primary').notNull().default(false),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    index('address_student_idx').on(table.studentId),
    index('address_district_idx').on(table.districtId),
    index('address_school_idx').on(table.schoolId),
    index('address_parent_idx').on(table.parentId),
    index('address_zipcode_idx').on(table.zipcode),
    index('address_type_idx').on(table.type),
    // Partial indexes for performance optimization on polymorphic queries
    index('address_student_not_null_idx')
      .on(table.studentId)
      .where(sql`student_id IS NOT NULL`),
    index('address_district_not_null_idx')
      .on(table.districtId)
      .where(sql`district_id IS NOT NULL`),
    index('address_school_not_null_idx')
      .on(table.schoolId)
      .where(sql`school_id IS NOT NULL`),
    index('address_parent_not_null_idx')
      .on(table.parentId)
      .where(sql`parent_id IS NOT NULL`),
    Policies.addressesSelectPolicy,
    Policies.addressesAllPolicy,
  ]
);

export const districtsTable = pgTable(
  'districts',
  {
    id: uuid('id').primaryKey().default(sql`uuid_generate_v7()`),
    name: varchar('name', { length: 255 }).notNull(),
    slug: varchar('slug', { length: 255 }).notNull(),
    type: Enums.districtTypeEnum('type')
      .notNull()
      .default(Enums.DistrictTypeEnum.UNIFIED_DISTRICT),
    website: varchar('website', { length: 255 }).notNull(),
    ncesId: varchar('nces_id', { length: 255 }).notNull(),
    stateId: varchar('state_id', { length: 255 }).notNull(),
    county: varchar('county', { length: 255 }).notNull(),
    numSchools: integer('num_schools'),
    numStudents: integer('num_students'),
    invoiceEmail: varchar('invoice_email', { length: 255 }).notNull(),
    addressId: uuid('address_id')
      .notNull()
      .references(() => addressesTable.id),
  },
  () => [
    Policies.districtsSelectPolicy,
    Policies.districtsInsertPolicy,
    Policies.districtsUpdatePolicy,
    Policies.districtsDeletePolicy,
  ]
);

export const schoolsTable = pgTable(
  'schools',
  {
    id: uuid('id').primaryKey().default(sql`uuid_generate_v7()`),
    name: varchar('name', { length: 255 }).notNull(),
    slug: varchar('slug', { length: 255 }).notNull(),
    type: Enums.schoolTypeEnum('type').notNull(),
    website: varchar('website', { length: 255 }),
    ncesId: varchar('nces_id', { length: 255 }).notNull(),
    districtId: uuid('district_id')
      .notNull()
      .references(() => districtsTable.id),
    addressId: uuid('address_id')
      .notNull()
      .references(() => addressesTable.id),
  },
  (table) => [
    index('school_district_idx').on(table.districtId),
    Policies.schoolsSelectPolicy,
    Policies.schoolsInsertPolicy,
    Policies.schoolsUpdatePolicy,
    Policies.schoolsDeletePolicy,
  ]
);

export const studentsTable = pgTable(
  'students',
  {
    id: uuid('id').primaryKey().default(sql`uuid_generate_v7()`),
    firstName: varchar('first_name', { length: 255 }).notNull(),
    middleName: varchar('middle_name', { length: 255 }),
    lastName: varchar('last_name', { length: 255 }).notNull(),
    fullName: varchar('full_name', { length: 255 }).generatedAlwaysAs(
      (): SQL =>
        sql`concat_names(${studentsTable.firstName}, ${studentsTable.middleName}, ${studentsTable.lastName})`
    ),
    preferredName: varchar('preferred_name', { length: 255 }).notNull(),
    studentIdNumber: varchar('student_id_number', { length: 50 }).notNull(),
    dateOfBirth: date('date_of_birth').notNull(),
    dateOfConsent: date('date_of_consent').notNull(),
    grade: varchar('grade', { length: 2 }).notNull(),
    gender: Enums.genderEnum('gender').notNull(),
    primarySchoolId: uuid('primary_school_id').references(
      () => schoolsTable.id
    ),
    enrollmentStatus: Enums.enrollmentStatusEnum('enrollment_status')
      .notNull()
      .default(Enums.EnrollmentStatusEnum.ENROLLED),
    specialNeedsIndicator: boolean('special_needs_indicator')
      .notNull()
      .default(false),
    emergencyContactName: varchar('emergency_contact_name', { length: 255 }),
    emergencyContactPhone: varchar('emergency_contact_phone', { length: 255 }),
    isDeleted: boolean('is_deleted').notNull().default(false),
    deletedAt: timestamp('deleted_at', { withTimezone: true }),
    deletedBy: uuid('deleted_by').references(() => usersTable.id),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    uniqueIndex('student_id_school_unique').on(
      table.studentIdNumber,
      table.primarySchoolId
    ),
    index('student_enrollment_status_idx').on(table.enrollmentStatus),
    index('student_special_needs_idx').on(table.specialNeedsIndicator),
    index('student_deleted_idx').on(table.isDeleted),
    index('student_name_idx').on(table.fullName),
    Policies.studentsSelectPolicy,
    Policies.studentsInsertPolicy,
    Policies.studentsUpdatePolicy,
    Policies.studentsDeletePolicy,
  ]
);

export const userDistrictsTable = pgTable(
  'user_districts',
  {
    id: uuid('id').primaryKey().default(sql`uuid_generate_v7()`),
    districtId: uuid('district_id')
      .notNull()
      .references(() => districtsTable.id),
    userId: uuid('user_id')
      .notNull()
      .references(() => usersTable.id),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    uniqueIndex('user_district_unique').on(table.userId, table.districtId),
    index('user_district_user_idx').on(table.userId),
    index('user_district_district_idx').on(table.districtId),
    Policies.userDistrictsSelectPolicy,
    Policies.userDistrictsAllPolicy,
  ]
);

export const districtPreferencesTable = pgTable(
  'district_preferences',
  {
    id: uuid('id').primaryKey().default(sql`uuid_generate_v7()`),
    districtId: uuid('district_id')
      .notNull()
      .references(() => districtsTable.id, { onDelete: 'cascade' }),
    category: Enums.preferenceCategoryEnum('category').notNull(),
    key: varchar('key', { length: 255 }).notNull(),
    type: Enums.preferenceTypeEnum('type').notNull(),
    value: text('value').notNull(),
    lastModifiedBy: uuid('last_modified_by').references(() => usersTable.id),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    uniqueIndex('district_preference_unique').on(table.districtId, table.key),
    index('district_pref_district_idx').on(table.districtId),
    index('district_pref_category_idx').on(table.category),
    index('district_pref_key_idx').on(table.key),
    Policies.districtPreferencesSelectPolicy,
    Policies.districtPreferencesUpdatePolicy,
  ]
);

export const userRolesTable = pgTable(
  'user_roles',
  {
    id: uuid('id').primaryKey().default(sql`uuid_generate_v7()`),
    userId: uuid('user_id')
      .notNull()
      .references(() => usersTable.id),
    roleId: uuid('role_id')
      .notNull()
      .references(() => rolesTable.id),
    roleName: Enums.roleEnum('role_name').notNull(),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    uniqueIndex('user_role_unique').on(table.userId, table.roleId),
    index('user_role_role_idx').on(table.roleId),
    index('user_role_user_name_idx').on(table.userId, table.roleName),
    Policies.userRolesSelectPolicy,
    Policies.userRolesAllPolicy,
  ]
);

export const userSchoolsTable = pgTable(
  'user_schools',
  {
    id: uuid('id').primaryKey().default(sql`uuid_generate_v7()`),
    userId: uuid('user_id').references(() => usersTable.id, {
      onDelete: 'cascade',
    }),
    schoolId: uuid('school_id').references(() => schoolsTable.id, {
      onDelete: 'cascade',
    }),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    uniqueIndex('user_school_unique').on(table.userId, table.schoolId),
    index('user_school_user_idx').on(table.userId),
    index('user_school_school_idx').on(table.schoolId),
  ]
);

export const studentEnrollmentsTable = pgTable(
  'student_enrollments',
  {
    id: uuid('id').primaryKey().default(sql`uuid_generate_v7()`),
    studentId: uuid('student_id')
      .notNull()
      .references(() => studentsTable.id, { onDelete: 'cascade' }),
    schoolId: uuid('school_id')
      .notNull()
      .references(() => schoolsTable.id, { onDelete: 'cascade' }),
    startDate: date('start_date').defaultNow(),
    endDate: date('end_date'),
    districtId: uuid('district_id').references(() => districtsTable.id, {
      onDelete: 'cascade',
    }),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    index('student_enrollment_student_idx').on(table.studentId),
    index('student_enrollment_school_idx').on(table.schoolId),
    index('student_enrollment_district_idx').on(table.districtId),
    uniqueIndex('student_enrollment_unique').on(
      table.studentId,
      table.schoolId
    ),
    Policies.studentEnrollmentsSelectPolicy,
    Policies.studentEnrollmentsAllPolicy,
  ]
);

export const languagesTable = pgTable(
  'languages',
  {
    id: uuid('id').primaryKey().default(sql`uuid_generate_v7()`),
    name: varchar('name', { length: 25 }).notNull(),
    code: varchar('code', { length: 10 }).notNull().unique(),
    emoji: varchar('emoji', { length: 10 }).notNull(),
  },
  (table) => [
    uniqueIndex('language_code_unique').on(table.code),
    index('language_name_idx').on(table.name),
    Policies.languagesSelectPolicy,
  ]
);

export const studentLanguagesTable = pgTable(
  'student_languages',
  {
    id: uuid('id').primaryKey().default(sql`uuid_generate_v7()`),
    isPrimary: boolean('is_primary').notNull().default(false),
    studentId: uuid('student_id')
      .notNull()
      .references(() => studentsTable.id),
    languageId: uuid('language_id')
      .notNull()
      .references(() => languagesTable.id),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    uniqueIndex('student_language_unique').on(
      table.studentId,
      table.languageId
    ),
    index('student_language_primary_idx').on(table.isPrimary),
    Policies.studentLanguagesSelectPolicy,
    Policies.studentLanguagesAllPolicy,
  ]
);

export const availabilitiesTable = pgTable(
  'availabilities',
  {
    id: uuid('id').primaryKey().default(sql`uuid_generate_v7()`),
    userId: uuid('user_id')
      .notNull()
      .references(() => usersTable.id),
    day: Enums.dayOfWeekEnum('day').notNull(),
    startTime: timestamp('start_time', { withTimezone: true }).notNull(),
    endTime: timestamp('end_time', { withTimezone: true }).notNull(),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    index('availability_user_idx').on(table.userId),
    Policies.availabilitiesSelectPolicy,
    Policies.availabilitiesAllPolicy,
  ]
);

export const parentsTable = pgTable(
  'parents',
  {
    id: uuid('id').primaryKey().default(sql`uuid_generate_v7()`),
    firstName: varchar('first_name', { length: 255 }).notNull(),
    middleName: varchar('middle_name', { length: 255 }),
    lastName: varchar('last_name', { length: 255 }).notNull(),
    fullName: varchar('full_name', { length: 255 })
      .notNull()
      .generatedAlwaysAs(
        (): SQL =>
          sql`concat_names(${parentsTable.firstName}, ${parentsTable.middleName}, ${parentsTable.lastName})`
      ),
    primaryEmail: varchar('primary_email', { length: 255 }),
    secondaryEmail: varchar('secondary_email', { length: 255 }),
    primaryPhone: varchar('primary_phone', { length: 255 }),
    secondaryPhone: varchar('secondary_phone', { length: 255 }),
    relationshipType: Enums.parentRelationshipEnum('relationship_type')
      .notNull()
      .default(Enums.ParentRelationshipEnum.UNKNOWN),
    isDeleted: boolean('is_deleted').notNull().default(false),
    deletedAt: timestamp('deleted_at', { withTimezone: true }),
    deletedBy: uuid('deleted_by').references(() => usersTable.id),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    index('parent_name_idx').on(table.fullName),
    index('parent_email_idx').on(table.primaryEmail),
    index('parent_deleted_idx').on(table.isDeleted),
    Policies.parentsSelectPolicy,
    Policies.parentsInsertPolicy,
    Policies.parentsUpdatePolicy,
    Policies.parentsDeletePolicy,
  ]
);

export const studentParentsTable = pgTable(
  'student_parents',
  {
    id: uuid('id').primaryKey().default(sql`uuid_generate_v7()`),
    parentId: uuid('parent_id')
      .notNull()
      .references(() => parentsTable.id),
    studentId: uuid('student_id')
      .notNull()
      .references(() => studentsTable.id),
    isPrimaryContact: boolean('is_primary_contact').notNull().default(false),
    hasPickupAuthorization: boolean('has_pickup_authorization')
      .notNull()
      .default(true),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    uniqueIndex('student_parent_unique').on(table.studentId, table.parentId),
    index('student_parent_primary_idx').on(table.isPrimaryContact),
    Policies.studentParentsSelectPolicy,
    Policies.studentParentsAllPolicy,
  ]
);

export const casesTable = pgTable(
  'cases',
  {
    id: uuid('id').primaryKey().default(sql`uuid_generate_v7()`),
    status: Enums.caseStatusEnum('status').notNull(),
    priority: Enums.casePriorityEnum('priority')
      .notNull()
      .default(Enums.CasePriorityEnum.MEDIUM),
    caseType: Enums.caseTypeEnum('case_type').notNull(),
    studentId: uuid('student_id')
      .notNull()
      .references(() => studentsTable.id),
    isActive: boolean('is_active').notNull(),
    iepStatus: Enums.iepStatusEnum('iep_status').notNull(),
    iepStartDate: timestamp('iep_start_date', { withTimezone: true }).notNull(),
    iepEndDate: timestamp('iep_end_date', { withTimezone: true }).notNull(),
    referralDate: timestamp('referral_date', { withTimezone: true }),
    evaluationDueDate: timestamp('evaluation_due_date', { withTimezone: true }),
    meetingDate: timestamp('meeting_date', { withTimezone: true }),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
    isDeleted: boolean('is_deleted').notNull().default(false),
    deletedAt: timestamp('deleted_at', { withTimezone: true }),
    deletedBy: uuid('deleted_by').references(() => usersTable.id),
  },
  (table) => [
    index('case_priority_status_idx').on(table.priority, table.status),
    index('case_student_idx').on(table.studentId),
    index('case_type_idx').on(table.caseType),
    index('case_active_idx').on(table.isActive),
    index('case_deleted_idx').on(table.isDeleted),
    index('case_eval_due_date_idx').on(table.evaluationDueDate),
    // Composite index for RLS policy performance optimization
    index('case_student_deleted_idx').on(table.studentId, table.isDeleted),
    Policies.casesSelectPolicy,
    Policies.casesInsertPolicy,
    Policies.casesUpdatePolicy,
    Policies.casesDeletePolicy,
  ]
);

export const plansTable = pgTable(
  'plans',
  {
    id: uuid('id').primaryKey().default(sql`uuid_generate_v7()`),
    studentId: uuid('student_id')
      .notNull()
      .references(() => studentsTable.id),
    caseId: uuid('case_id')
      .notNull()
      .references(() => casesTable.id),
    type: Enums.planTypeEnum('type').notNull(),
    status: Enums.planStatusEnum('status').notNull(),
    expirationDate: timestamp('expiration_date', {
      withTimezone: true,
    }).notNull(),
    isDeleted: boolean('is_deleted').notNull().default(false),
    deletedAt: timestamp('deleted_at', { withTimezone: true }),
    deletedBy: uuid('deleted_by').references(() => usersTable.id),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    index('plan_student_idx').on(table.studentId),
    index('plan_case_idx').on(table.caseId),
    index('plan_type_status_idx').on(table.type, table.status),
    index('plan_expiration_idx').on(table.expirationDate),
    index('plan_deleted_idx').on(table.isDeleted),
    Policies.plansSelectPolicy,
    Policies.plansAllPolicy,
  ]
);

export const documentsTable = pgTable(
  'documents',
  {
    id: uuid('id').primaryKey().default(sql`uuid_generate_v7()`),
    studentId: uuid('student_id')
      .notNull()
      .references(() => studentsTable.id),
    // TODO: Add foreign keys in migration after psychological testing tables are created
    // These reference tables defined in psychological-testing.ts to avoid circular imports
    assessmentSessionId: uuid('assessment_session_id'),
    testAdministrationId: uuid('test_administration_id'),
    category: Enums.documentCategoryEnum('category').notNull(),
    name: varchar('name', { length: 255 }).notNull(),
    url: text('url').notNull(),
    uploadedUserId: uuid('uploaded_user_id')
      .notNull()
      .references(() => usersTable.id),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    index('document_student_idx').on(table.studentId),
    index('document_category_idx').on(table.category),
    index('document_session_idx').on(table.assessmentSessionId),
    index('document_test_admin_idx').on(table.testAdministrationId),
    Policies.documentsSelectPolicy,
    Policies.documentsInsertPolicy,
    Policies.documentsUpdatePolicy,
    Policies.documentsDeletePolicy,
  ]
);

export const caseAssignmentsTable = pgTable(
  'case_assignments',
  {
    id: uuid('id').primaryKey().default(sql`uuid_generate_v7()`),
    userId: uuid('user_id')
      .notNull()
      .references(() => usersTable.id),
    caseId: uuid('case_id')
      .notNull()
      .references(() => casesTable.id),
    isDeleted: boolean('is_deleted').notNull().default(false),
    deletedAt: timestamp('deleted_at', { withTimezone: true }),
    deletedBy: uuid('deleted_by').references(() => usersTable.id),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    uniqueIndex('case_assignment_unique').on(table.userId, table.caseId),
    index('case_assignment_deleted_idx').on(table.isDeleted),
    index('case_assignment_user_deleted_idx').on(table.userId, table.isDeleted),
    index('case_assignment_case_idx').on(table.caseId),
    // Composite index for RLS policy performance optimization
    index('case_assignment_user_case_deleted_idx').on(
      table.userId,
      table.caseId,
      table.isDeleted
    ),
    Policies.caseAssignmentsSelectPolicy,
    Policies.caseAssignmentsInsertPolicy,
    Policies.caseAssignmentsUpdatePolicy,
    Policies.caseAssignmentsDeletePolicy,
  ]
);

export const caseDetailsTable = pgTable(
  'case_details',
  {
    id: uuid('id').primaryKey().default(sql`uuid_generate_v7()`),
    caseId: uuid('case_id')
      .notNull()
      .references(() => casesTable.id),
    key: varchar('key', { length: 255 }).notNull(),
    value: text('value').notNull(),
    isDeleted: boolean('is_deleted').notNull().default(false),
    deletedAt: timestamp('deleted_at', { withTimezone: true }),
    deletedBy: uuid('deleted_by').references(() => usersTable.id),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    index('case_detail_key_idx').on(table.key),
    index('case_detail_deleted_idx').on(table.isDeleted),
  ]
);

export const invitationsTable = pgTable(
  'invitations',
  {
    id: uuid('id').primaryKey().default(sql`uuid_generate_v7()`),
    roleId: uuid('role_id')
      .notNull()
      .references(() => rolesTable.id),
    districtId: uuid('district_id')
      .notNull()
      .references(() => districtsTable.id),
    inviterId: uuid('inviter_id')
      .notNull()
      .references(() => usersTable.id),
    firstName: varchar('first_name', { length: 255 }).notNull(),
    lastName: varchar('last_name', { length: 255 }).notNull(),
    email: varchar('email', { length: 255 }).notNull(),
    status: Enums.invitationStatusEnum('status')
      .notNull()
      .default(Enums.InvitationStatusEnum.PENDING),
    token: varchar('token').notNull(),
    expiresAt: timestamp('expires_at', { withTimezone: true })
      .notNull()
      .default(sql`(NOW() + INTERVAL '7 days')`), // Default 7-day expiry
    acceptedAt: timestamp('accepted_at', { withTimezone: true }),
    rejectedAt: timestamp('rejected_at', { withTimezone: true }),
    invitedById: uuid('invited_by_id').references(() => usersTable.id), // Who created this invitation (different from inviter for system invites)
    metadata: text('metadata'), // JSON field for additional invitation data
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    index('invitation_status_idx').on(table.status),
    index('invitation_email_idx').on(table.email),
    index('invitation_district_idx').on(table.districtId),
    index('invitation_expires_idx').on(table.expiresAt),
    index('invitation_token_idx').on(table.token),
    // Compound index for active invitations lookup
    index('invitation_active_idx').on(
      table.email,
      table.districtId,
      table.status
    ),
    Policies.invitationsSelectPolicy,
    Policies.invitationsInsertPolicy,
    Policies.invitationsUpdatePolicy,
    Policies.invitationsDeletePolicy,
  ]
);

export const invitationSchoolsTable = pgTable(
  'invitation_schools',
  {
    id: uuid('id').primaryKey().default(sql`uuid_generate_v7()`),
    invitationId: uuid('invitation_id')
      .notNull()
      .references(() => invitationsTable.id, { onDelete: 'cascade' }),
    schoolId: uuid('school_id')
      .notNull()
      .references(() => schoolsTable.id, { onDelete: 'cascade' }),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    uniqueIndex('invitation_school_unique').on(
      table.invitationId,
      table.schoolId
    ),
    index('invitation_schools_invitation_idx').on(table.invitationId),
    index('invitation_schools_school_idx').on(table.schoolId),
    Policies.invitationSchoolsSelectPolicy,
    Policies.invitationSchoolsAllPolicy,
  ]
);

export const tasksTable = pgTable(
  'tasks',
  {
    id: uuid('id').primaryKey().default(sql`uuid_generate_v7()`),
    taskType: Enums.taskTypeEnum('task_type').notNull(),
    caseId: uuid('case_id').references(() => casesTable.id),
    studentId: uuid('student_id').references(() => studentsTable.id),
    schoolId: uuid('school_id').references(() => schoolsTable.id),
    districtId: uuid('district_id').references(() => districtsTable.id),
    assignedToId: uuid('assigned_to_id')
      .notNull()
      .references(() => usersTable.id),
    assignedById: uuid('assigned_by_id')
      .notNull()
      .references(() => usersTable.id),
    status: Enums.taskStatusEnum('status')
      .notNull()
      .default(Enums.TaskStatusEnum.PENDING),
    priority: Enums.taskPriorityEnum('priority')
      .notNull()
      .default(Enums.TaskPriorityEnum.MEDIUM),
    dueDate: timestamp('due_date', { withTimezone: true }),
    notes: text('notes').notNull().default(''),
    reason: text('reason'),
    metadata: jsonb('metadata'),
    completedAt: timestamp('completed_at', { withTimezone: true }),
    rejectedAt: timestamp('rejected_at', { withTimezone: true }),
    cancelledAt: timestamp('cancelled_at', { withTimezone: true }),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    index('task_assigned_to_idx').on(table.assignedToId),
    index('task_status_priority_idx').on(table.status, table.priority),
    index('task_due_date_idx').on(table.dueDate),
    index('task_case_idx').on(table.caseId),
    index('task_student_idx').on(table.studentId),
    index('task_district_idx').on(table.districtId),
    index('task_type_idx').on(table.taskType),
    index('task_completed_idx').on(table.completedAt),
    index('task_assigned_status_due_idx').on(
      table.assignedToId,
      table.status,
      table.dueDate
    ),
    index('task_district_priority_idx').on(
      table.districtId,
      table.priority,
      table.createdAt
    ),
    index('task_active_idx').on(table.assignedToId, table.dueDate),
    index('task_overdue_idx').on(table.dueDate, table.status),
    Policies.tasksSelectPolicy,
    Policies.tasksInsertPolicy,
    Policies.tasksUpdatePolicy,
    Policies.tasksDeletePolicy,
  ]
);

export const taskDependenciesTable = pgTable(
  'task_dependencies',
  {
    id: uuid('id').primaryKey().default(sql`uuid_generate_v7()`),
    predecessorTaskId: uuid('predecessor_task_id')
      .notNull()
      .references(() => tasksTable.id),
    successorTaskId: uuid('successor_task_id')
      .notNull()
      .references(() => tasksTable.id),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    index('task_dependencies_successor_idx').on(table.successorTaskId),
  ]
);

export const taskHistoryTable = pgTable('task_history', {
  id: uuid('id').primaryKey().default(sql`uuid_generate_v7()`),
  taskId: uuid('task_id')
    .notNull()
    .references(() => tasksTable.id),
  userId: uuid('user_id')
    .notNull()
    .references(() => usersTable.id),
  action: Enums.taskHistoryActionEnum('action').notNull(),
  previousStatus: Enums.taskStatusEnum('previous_status'),
  newStatus: Enums.taskStatusEnum('new_status'),
  createdAt: timestamp('created_at', { withTimezone: true })
    .notNull()
    .defaultNow(),
});

export const districtAvailabilitiesTable = pgTable(
  'district_availabilities',
  {
    id: uuid('id').primaryKey().default(sql`uuid_generate_v7()`),
    districtId: uuid('district_id')
      .notNull()
      .references(() => districtsTable.id, { onDelete: 'cascade' }),
    type: Enums.availabilityTypeEnum('type')
      .notNull()
      .default(Enums.AvailabilityTypeEnum.EVALUATION),
    day: Enums.dayOfWeekEnum('day').notNull(),
    startTime: timestamp('start_time', { withTimezone: false }).notNull(), // Time only
    endTime: timestamp('end_time', { withTimezone: false }).notNull(), // Time only
    timeZone: varchar('time_zone', { length: 255 }).notNull(), // e.g., "America/Los_Angeles"
    isActive: boolean('is_active').notNull().default(true),
    notes: text('notes'),
    createdBy: uuid('created_by')
      .notNull()
      .references(() => usersTable.id),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    index('district_avail_district_idx').on(table.districtId),
    index('district_avail_day_idx').on(table.day),
    index('district_avail_type_idx').on(table.type),
    index('district_avail_active_idx').on(table.isActive),
    uniqueIndex('district_avail_unique').on(
      table.districtId,
      table.type,
      table.day,
      table.startTime,
      table.endTime
    ),
    Policies.districtAvailabilitiesSelectPolicy,
    Policies.districtAvailabilitiesUpdatePolicy,
  ]
);

export const districtBlockedDatesTable = pgTable(
  'district_blocked_dates',
  {
    id: uuid('id').primaryKey().default(sql`uuid_generate_v7()`),
    districtId: uuid('district_id')
      .notNull()
      .references(() => districtsTable.id, { onDelete: 'cascade' }),
    title: varchar('title', { length: 255 }).notNull(), // e.g., "Thanksgiving Holiday"
    description: text('description'),
    blockType: Enums.blockedDateTypeEnum('block_type')
      .notNull()
      .default(Enums.BlockedDateTypeEnum.OTHER),
    startDate: date('start_date').notNull(),
    endDate: date('end_date').notNull(), // Same as startDate for single day blocks
    isRecurring: boolean('is_recurring').notNull().default(false),
    recurrencePattern: text('recurrence_pattern'), // JSON string for recurring rules
    isActive: boolean('is_active').notNull().default(true),
    affectsTypes: text('affects_types').array(), // Array of AvailabilityTypeEnum values
    createdBy: uuid('created_by')
      .notNull()
      .references(() => usersTable.id),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    index('district_blocked_district_idx').on(table.districtId),
    index('district_blocked_dates_idx').on(table.startDate, table.endDate),
    index('district_blocked_type_idx').on(table.blockType),
    index('district_blocked_active_idx').on(table.isActive),
    index('district_blocked_recurring_idx').on(table.isRecurring),
    Policies.districtBlockedDatesSelectPolicy,
    Policies.districtBlockedDatesUpdatePolicy,
  ]
);

export const joinRequestsTable = pgTable(
  'join_requests',
  {
    id: uuid('id').primaryKey().default(sql`uuid_generate_v7()`),
    firstName: varchar('first_name', { length: 255 }).notNull(),
    lastName: varchar('last_name', { length: 255 }).notNull(),
    email: varchar('email', { length: 255 }).notNull(),
    phone: varchar('phone', { length: 255 }).notNull(),
    districtName: varchar('district_name', { length: 255 }).notNull(),
    message: text('message'),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
  },
  () => [
    Policies.joinRequestsSelectPolicy,
    Policies.joinRequestsInsertPolicy,
    Policies.joinRequestsAllPolicy,
  ]
);

export const feedbackTable = pgTable(
  'feedback',
  {
    id: uuid('id').primaryKey().default(sql`uuid_generate_v7()`),
    userId: uuid('user_id')
      .notNull()
      .references(() => usersTable.id, { onDelete: 'cascade' }),
    rating: integer(),
    type: Enums.feedbackTypeEnum('type').notNull(),
    status: Enums.feedbackStatusEnum('status')
      .notNull()
      .default(Enums.FeedbackStatusEnum.OPEN),
    title: varchar(),
    description: text(),
    issueType: Enums.feedbackIssueTypeEnum('issue_type')
      .notNull()
      .default(Enums.FeedbackIssueTypeEnum.OTHER),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    index('idx_feedback_rating').on(table.rating),
    index('idx_feedback_type').on(table.type),
    index('idx_feedback_status').on(table.status),
    index('idx_feedback_issue_type').on(table.issueType),
    ...Policies.feedbackPolicies,
  ]
);

export const feedbackFileTable = pgTable(
  'feedback_files',
  {
    id: uuid('id').primaryKey().default(sql`uuid_generate_v7()`),
    feedbackId: uuid('feedback_id')
      .notNull()
      .references(() => feedbackTable.id, { onDelete: 'cascade' }),
    fileUrl: varchar('file_url').notNull(),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  () => [...Policies.feedbackFilePolicies]
);

// biome-ignore format: Custom formatted
export const notificationsTable = pgTable(
  'notifications',
  {
		id: uuid("id").primaryKey().default(sql`uuid_generate_v7()`),
    userId: uuid('user_id').notNull().references(() => usersTable.id, { onDelete: 'cascade' }),
    type: varchar("type", { length: 100 }).notNull(),
    content: text("content").notNull(),
    metadata: jsonb("metadata"),
    isRead: boolean('is_read').notNull().default(false),
		isArchived: boolean('is_archived').notNull().default(false),
    category: Enums.notificationCategoryTypeEnum("category").notNull().default(Enums.NotificationCategoryTypeEnum.GENERAL),
    readAt: timestamp('read_at', { withTimezone: true }),
		archivedAt: timestamp('archived_at', { withTimezone: true }),
    expiresAt: timestamp('expires_at', { withTimezone: true }).default(sql`now() + interval '1 month'`),
    createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
  },
  (table) => [
    index('idx_notification_user_read').on(table.userId, table.isRead, table.expiresAt),
    index('idx_notification_user_archived').on(table.userId, table.isArchived, table.expiresAt),
    ...Policies.notificationPolicies,
  ]
);

// biome-ignore format: Custom formatted
export const userNotificationPreferencesTable = pgTable(
  'user_notification_preferences',
  {
		id: uuid("id").primaryKey().default(sql`uuid_generate_v7()`),
    userId: uuid('user_id').notNull().references(() => usersTable.id, { onDelete: 'cascade' }),
    isInAppNotificationsEnabled: boolean('is_in_app_notifications_enabled').notNull().default(false),
    isPushNotificationsEnabled: boolean('is_push_notifications_enabled').notNull().default(false),
    isEmailEnabled: boolean('is_email_enabled').notNull().default(false),
    createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().$onUpdate(() => new Date()),
  },
  (table) => [
    unique('unique_user_notification_preferences').on(table.userId),
    ...Policies.userNotificationPreferencePolicies,
  ]
);

/*
 * -------------------------------------------------------
 * SECTION: Relations
 * -------------------------------------------------------
 */

export const districtsRelations = relations(
  districtsTable,
  ({ many, one }) => ({
    schools: many(schoolsTable),
    userDistricts: many(userDistrictsTable),
    invitations: many(invitationsTable),
    studentEnrollments: many(studentEnrollmentsTable),
    tasks: many(tasksTable),
    preferences: many(districtPreferencesTable),
    availabilities: many(districtAvailabilitiesTable),
    blockedDates: many(districtBlockedDatesTable),
    address: one(addressesTable, {
      fields: [districtsTable.addressId],
      references: [addressesTable.id],
    }),
  })
);

export const addressesRelations = relations(
  addressesTable,
  ({ one, many }) => ({
    // Polymorphic relationships - only one should be populated per address
    student: one(studentsTable, {
      fields: [addressesTable.studentId],
      references: [studentsTable.id],
    }),
    district: one(districtsTable, {
      fields: [addressesTable.districtId],
      references: [districtsTable.id],
    }),
    school: one(schoolsTable, {
      fields: [addressesTable.schoolId],
      references: [schoolsTable.id],
    }),
    parent: one(parentsTable, {
      fields: [addressesTable.parentId],
      references: [parentsTable.id],
    }),
    // Reverse relations
    districtsUsingThisAddress: many(districtsTable),
    schoolsUsingThisAddress: many(schoolsTable),
  })
);

export const schoolsRelations = relations(schoolsTable, ({ one, many }) => ({
  district: one(districtsTable, {
    fields: [schoolsTable.districtId],
    references: [districtsTable.id],
  }),
  address: one(addressesTable, {
    fields: [schoolsTable.addressId],
    references: [addressesTable.id],
  }),
  studentEnrollments: many(studentEnrollmentsTable),
  tasks: many(tasksTable),
  userSchools: many(userSchoolsTable),
  invitationSchools: many(invitationSchoolsTable),
}));

export const studentsRelations = relations(studentsTable, ({ many }) => ({
  addresses: many(addressesTable),
  studentEnrollments: many(studentEnrollmentsTable),
  studentLanguages: many(studentLanguagesTable),
  studentParents: many(studentParentsTable),
  cases: many(casesTable),
  plans: many(plansTable),
  documents: many(documentsTable),
  tasks: many(tasksTable),
}));

export const usersRelations = relations(usersTable, ({ many }) => ({
  userDistricts: many(userDistrictsTable),
  userRoles: many(userRolesTable),
  availabilities: many(availabilitiesTable),
  caseAssignments: many(caseAssignmentsTable),
  documents: many(documentsTable),
  invitations: many(invitationsTable),
  tasks: many(tasksTable),
  taskHistory: many(taskHistoryTable),
  userSchools: many(userSchoolsTable),
  feedback: many(feedbackTable),
}));

export const studentEnrollmentsRelations = relations(
  studentEnrollmentsTable,
  ({ one }) => ({
    student: one(studentsTable, {
      fields: [studentEnrollmentsTable.studentId],
      references: [studentsTable.id],
    }),
    school: one(schoolsTable, {
      fields: [studentEnrollmentsTable.schoolId],
      references: [schoolsTable.id],
    }),
    district: one(districtsTable, {
      fields: [studentEnrollmentsTable.districtId],
      references: [districtsTable.id],
    }),
  })
);

export const rolesRelations = relations(rolesTable, ({ many }) => ({
  userRoles: many(userRolesTable),
  rolePermissions: many(rolePermissionsTable),
  invitations: many(invitationsTable),
}));

export const userDistrictsRelations = relations(
  userDistrictsTable,
  ({ one }) => ({
    user: one(usersTable, {
      fields: [userDistrictsTable.userId],
      references: [usersTable.id],
      relationName: 'user_district_user',
    }),
    district: one(districtsTable, {
      fields: [userDistrictsTable.districtId],
      references: [districtsTable.id],
    }),
  })
);

export const districtPreferencesRelations = relations(
  districtPreferencesTable,
  ({ one }) => ({
    district: one(districtsTable, {
      fields: [districtPreferencesTable.districtId],
      references: [districtsTable.id],
    }),
    lastModifiedByUser: one(usersTable, {
      fields: [districtPreferencesTable.lastModifiedBy],
      references: [usersTable.id],
    }),
  })
);

export const userSchoolsRelations = relations(userSchoolsTable, ({ one }) => ({
  user: one(usersTable, {
    fields: [userSchoolsTable.userId],
    references: [usersTable.id],
    relationName: 'user_school_user',
  }),
  school: one(schoolsTable, {
    fields: [userSchoolsTable.schoolId],
    references: [schoolsTable.id],
  }),
}));

export const userRolesRelations = relations(userRolesTable, ({ one }) => ({
  user: one(usersTable, {
    fields: [userRolesTable.userId],
    references: [usersTable.id],
    relationName: 'user_role_user',
  }),
  role: one(rolesTable, {
    fields: [userRolesTable.roleId],
    references: [rolesTable.id],
  }),
}));

export const permissionsRelations = relations(permissionsTable, ({ many }) => ({
  rolePermissions: many(rolePermissionsTable),
}));

export const rolePermissionsRelations = relations(
  rolePermissionsTable,
  ({ one }) => ({
    role: one(rolesTable, {
      fields: [rolePermissionsTable.roleId],
      references: [rolesTable.id],
    }),
    permission: one(permissionsTable, {
      fields: [rolePermissionsTable.permissionId],
      references: [permissionsTable.id],
    }),
  })
);

export const languagesRelations = relations(languagesTable, ({ many }) => ({
  studentLanguages: many(studentLanguagesTable),
}));

export const studentLanguagesRelations = relations(
  studentLanguagesTable,
  ({ one }) => ({
    student: one(studentsTable, {
      fields: [studentLanguagesTable.studentId],
      references: [studentsTable.id],
    }),
    language: one(languagesTable, {
      fields: [studentLanguagesTable.languageId],
      references: [languagesTable.id],
    }),
  })
);

export const availabilitiesRelations = relations(
  availabilitiesTable,
  ({ one }) => ({
    user: one(usersTable, {
      fields: [availabilitiesTable.userId],
      references: [usersTable.id],
      relationName: 'user_availability',
    }),
  })
);

export const parentsRelations = relations(parentsTable, ({ many }) => ({
  studentParents: many(studentParentsTable),
  addresses: many(addressesTable),
}));

export const studentParentsRelations = relations(
  studentParentsTable,
  ({ one }) => ({
    student: one(studentsTable, {
      fields: [studentParentsTable.studentId],
      references: [studentsTable.id],
    }),
    parent: one(parentsTable, {
      fields: [studentParentsTable.parentId],
      references: [parentsTable.id],
    }),
  })
);

export const casesRelations = relations(casesTable, ({ one, many }) => ({
  student: one(studentsTable, {
    fields: [casesTable.studentId],
    references: [studentsTable.id],
  }),
  plans: many(plansTable),
  caseAssignments: many(caseAssignmentsTable),
  caseDetails: many(caseDetailsTable),
  tasks: many(tasksTable),
}));

export const plansRelations = relations(plansTable, ({ one }) => ({
  student: one(studentsTable, {
    fields: [plansTable.studentId],
    references: [studentsTable.id],
  }),
  case: one(casesTable, {
    fields: [plansTable.caseId],
    references: [casesTable.id],
  }),
}));

export const documentsRelations = relations(documentsTable, ({ one }) => ({
  student: one(studentsTable, {
    fields: [documentsTable.studentId],
    references: [studentsTable.id],
  }),
  uploadedUser: one(usersTable, {
    fields: [documentsTable.uploadedUserId],
    references: [usersTable.id],
    relationName: 'document_uploaded_by',
  }),
}));

export const caseAssignmentsRelations = relations(
  caseAssignmentsTable,
  ({ one }) => ({
    user: one(usersTable, {
      fields: [caseAssignmentsTable.userId],
      references: [usersTable.id],
      relationName: 'user_case_assignment',
    }),
    case: one(casesTable, {
      fields: [caseAssignmentsTable.caseId],
      references: [casesTable.id],
    }),
  })
);

export const caseDetailsRelations = relations(caseDetailsTable, ({ one }) => ({
  case: one(casesTable, {
    fields: [caseDetailsTable.caseId],
    references: [casesTable.id],
  }),
}));

export const invitationsRelations = relations(
  invitationsTable,
  ({ one, many }) => ({
    role: one(rolesTable, {
      fields: [invitationsTable.roleId],
      references: [rolesTable.id],
    }),
    district: one(districtsTable, {
      fields: [invitationsTable.districtId],
      references: [districtsTable.id],
    }),
    inviter: one(usersTable, {
      fields: [invitationsTable.inviterId],
      references: [usersTable.id],
      relationName: 'invitation_created_by',
    }),
    invitedBy: one(usersTable, {
      fields: [invitationsTable.invitedById],
      references: [usersTable.id],
      relationName: 'invitation_invited_by',
    }),
    // Many-to-many relationship with schools
    invitationSchools: many(invitationSchoolsTable),
  })
);

export const invitationSchoolsRelations = relations(
  invitationSchoolsTable,
  ({ one }) => ({
    invitation: one(invitationsTable, {
      fields: [invitationSchoolsTable.invitationId],
      references: [invitationsTable.id],
    }),
    school: one(schoolsTable, {
      fields: [invitationSchoolsTable.schoolId],
      references: [schoolsTable.id],
    }),
  })
);

export const tasksRelations = relations(tasksTable, ({ one, many }) => ({
  case: one(casesTable, {
    fields: [tasksTable.caseId],
    references: [casesTable.id],
  }),
  student: one(studentsTable, {
    fields: [tasksTable.studentId],
    references: [studentsTable.id],
  }),
  school: one(schoolsTable, {
    fields: [tasksTable.schoolId],
    references: [schoolsTable.id],
  }),
  district: one(districtsTable, {
    fields: [tasksTable.districtId],
    references: [districtsTable.id],
  }),
  assignedTo: one(usersTable, {
    fields: [tasksTable.assignedToId],
    references: [usersTable.id],
    relationName: 'task_assigned_to',
  }),
  assignedBy: one(usersTable, {
    fields: [tasksTable.assignedById],
    references: [usersTable.id],
    relationName: 'task_assigned_by',
  }),
  predecessorTasks: many(taskDependenciesTable, {
    relationName: 'successor_tasks',
  }),
  successorTasks: many(taskDependenciesTable, {
    relationName: 'predecessor_tasks',
  }),
  history: many(taskHistoryTable),
}));

export const taskDependenciesRelations = relations(
  taskDependenciesTable,
  ({ one }) => ({
    predecessorTask: one(tasksTable, {
      fields: [taskDependenciesTable.predecessorTaskId],
      references: [tasksTable.id],
      relationName: 'predecessor_tasks',
    }),
    successorTask: one(tasksTable, {
      fields: [taskDependenciesTable.successorTaskId],
      references: [tasksTable.id],
      relationName: 'successor_tasks',
    }),
  })
);

export const taskHistoryRelations = relations(taskHistoryTable, ({ one }) => ({
  task: one(tasksTable, {
    fields: [taskHistoryTable.taskId],
    references: [tasksTable.id],
  }),
  user: one(usersTable, {
    fields: [taskHistoryTable.userId],
    references: [usersTable.id],
    relationName: 'user_task_history',
  }),
}));

export const districtAvailabilitiesRelations = relations(
  districtAvailabilitiesTable,
  ({ one }) => ({
    district: one(districtsTable, {
      fields: [districtAvailabilitiesTable.districtId],
      references: [districtsTable.id],
    }),
    createdByUser: one(usersTable, {
      fields: [districtAvailabilitiesTable.createdBy],
      references: [usersTable.id],
      relationName: 'district_availability_created_by',
    }),
  })
);

export const districtBlockedDatesRelations = relations(
  districtBlockedDatesTable,
  ({ one }) => ({
    district: one(districtsTable, {
      fields: [districtBlockedDatesTable.districtId],
      references: [districtsTable.id],
    }),
    createdByUser: one(usersTable, {
      fields: [districtBlockedDatesTable.createdBy],
      references: [usersTable.id],
      relationName: 'district_blocked_date_created_by',
    }),
  })
);

export const feedbackFileRelations = relations(
  feedbackFileTable,
  ({ one }) => ({
    feedback: one(feedbackTable, {
      fields: [feedbackFileTable.feedbackId],
      references: [feedbackTable.id],
    }),
  })
);

export const feedbackRelations = relations(feedbackTable, ({ one, many }) => ({
  user: one(usersTable, {
    fields: [feedbackTable.userId],
    references: [usersTable.id],
  }),
  files: many(feedbackFileTable),
}));
