import {
  boolean,
  integer,
  jsonb,
  pgSchema,
  text,
  timestamp,
  uuid,
  varchar,
} from 'drizzle-orm/pg-core';

const auth = pgSchema('auth');

type RawAppMetaData = {
  provider?: string;
  mfaSecret?: string;
  mfaRecoveryCodes?: string;
  mfaSetupSkipped?: boolean;
  requiresPasswordReset?: boolean;
  [key: string]: unknown;
};

export const authUsersTable = auth.table('users', {
  id: uuid().primaryKey().notNull(),
  email: varchar({ length: 255 }),
  phone: text().unique(),
  encryptedPassword: varchar({ length: 255 }),
  rawAppMetaData: jsonb().$type<RawAppMetaData>(),
  emailConfirmedAt: timestamp({ withTimezone: true }),
  phoneConfirmedAt: timestamp({ withTimezone: true }),
  lastSignInAt: timestamp({ withTimezone: true }),
  createdAt: timestamp({ withTimezone: true }),
  updatedAt: timestamp({ withTimezone: true }),
});

export const authRefreshTokensTable = auth.table('refresh_tokens', {
  id: integer().primaryKey().notNull(),
  userId: uuid().notNull(),
  instanceId: uuid().notNull(),
  token: varchar({ length: 255 }).notNull(),
  revoked: boolean().notNull().default(false),
  parent: varchar({ length: 255 }),
  createdAt: timestamp({ withTimezone: true }),
  updatedAt: timestamp({ withTimezone: true }),
});
