/** biome-ignore-all lint/performance/noNamespaceImport: Ignore */
import { relations, sql } from 'drizzle-orm';
import {
  boolean,
  decimal,
  index,
  integer,
  jsonb,
  pgTable,
  text,
  timestamp,
  uniqueIndex,
  uuid,
  varchar,
} from 'drizzle-orm/pg-core';
import { createInsertSchema, createSelectSchema } from 'drizzle-zod';
import * as Policies from '../policies/policies';
import * as Enums from './enums';
import {
  casesTable,
  documentsTable,
  studentsTable,
  usersTable,
} from './tables';

/*
 * -------------------------------------------------------
 * SECTION: Psychological Testing Tables
 * -------------------------------------------------------
 */

// Test Batteries (WISC-V, WIAT-III, etc.)
export const testBatteriesTable = pgTable(
  'test_batteries',
  {
    id: uuid('id').primaryKey().default(sql`uuid_generate_v7()`),
    category: Enums.testCategoryEnum('category').notNull(),
    name: varchar('name', { length: 255 }).notNull(),
    code: varchar('code', { length: 20 }).notNull().unique(), // e.g., 'WISCV', 'WIATIII'
    version: varchar('version', { length: 20 }), // e.g., '5.0', '4.0'
    publisher: varchar('publisher', { length: 255 }),
    ageRangeMin: integer('age_range_min'), // Minimum age in months
    ageRangeMax: integer('age_range_max'), // Maximum age in months
    administrationTime: integer('administration_time'), // Average time in minutes
    description: text('description'),
    normingInformation: jsonb('norming_information'), // Standardization data
    isActive: boolean('is_active').notNull().default(true),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    index('battery_category_idx').on(table.category),
    index('battery_age_range_idx').on(table.ageRangeMin, table.ageRangeMax),
    index('battery_active_idx').on(table.isActive),
    index('battery_code_idx').on(table.code),
    Policies.testBatteriesSelectPolicy,
  ]
);

// Test Indices/Composites (VCI, VSI, FRI, etc.)
export const testIndicesTable = pgTable(
  'test_indices',
  {
    id: uuid('id').primaryKey().default(sql`uuid_generate_v7()`),
    batteryId: uuid('battery_id')
      .notNull()
      .references(() => testBatteriesTable.id),
    name: varchar('name', { length: 255 }).notNull(),
    code: varchar('code', { length: 20 }).notNull(), // e.g., 'VCI', 'VSI', 'FRI'
    indexType: Enums.indexTypeEnum('index_type').notNull(),
    description: text('description'),
    scoreRangeMin: integer('score_range_min').default(40),
    scoreRangeMax: integer('score_range_max').default(160),
    meanScore: integer('mean_score').default(100),
    standardDeviation: integer('standard_deviation').default(15),
    interpretiveGuidelines: text('interpretive_guidelines'),
    sortOrder: integer('sort_order').default(0),
    isActive: boolean('is_active').notNull().default(true),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    uniqueIndex('index_battery_code_unique').on(table.batteryId, table.code),
    index('index_battery_idx').on(table.batteryId),
    index('index_type_idx').on(table.indexType),
    index('index_sort_idx').on(table.sortOrder),
    index('index_active_idx').on(table.isActive),
    Policies.testIndicesSelectPolicy,
  ]
);

// Subtests (BD, SI, MR, etc.)
export const subtestsTable = pgTable(
  'subtests',
  {
    id: uuid('id').primaryKey().default(sql`uuid_generate_v7()`),
    batteryId: uuid('battery_id')
      .notNull()
      .references(() => testBatteriesTable.id),
    name: varchar('name', { length: 255 }).notNull(),
    code: varchar('code', { length: 20 }).notNull(), // e.g., 'BD', 'SI', 'MR'
    subtestType: Enums.subtestTypeEnum('subtest_type').notNull(),
    description: text('description'),
    measuredAbilities: jsonb('measured_abilities'), // Array of cognitive abilities measured
    timeLimitMinutes: integer('time_limit_minutes'), // NULL if untimed
    scoreRangeMin: integer('score_range_min').default(1),
    scoreRangeMax: integer('score_range_max').default(19),
    meanScore: integer('mean_score').default(10),
    standardDeviation: integer('standard_deviation').default(3),
    administrationInstructions: text('administration_instructions'),
    sortOrder: integer('sort_order').default(0),
    isActive: boolean('is_active').notNull().default(true),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    uniqueIndex('subtest_battery_code_unique').on(table.batteryId, table.code),
    index('subtest_battery_idx').on(table.batteryId),
    index('subtest_type_idx').on(table.subtestType),
    index('subtest_sort_idx').on(table.sortOrder),
    index('subtest_active_idx').on(table.isActive),
    Policies.subtestsSelectPolicy,
  ]
);

// Many-to-many relationship between indices and subtests
export const indexSubtestMappingsTable = pgTable(
  'index_subtest_mappings',
  {
    id: uuid('id').primaryKey().default(sql`uuid_generate_v7()`),
    indexId: uuid('index_id')
      .notNull()
      .references(() => testIndicesTable.id),
    subtestId: uuid('subtest_id')
      .notNull()
      .references(() => subtestsTable.id),
    weight: decimal('weight', { precision: 3, scale: 2 }).default('1.00'), // Weighting factor
    isRequired: boolean('is_required').default(true), // Required for index calculation
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
  },
  (table) => [
    uniqueIndex('mapping_index_subtest_unique').on(
      table.indexId,
      table.subtestId
    ),
    index('mapping_index_idx').on(table.indexId),
    index('mapping_subtest_idx').on(table.subtestId),
    Policies.indexSubtestMappingsSelectPolicy,
  ]
);

// =============================================
// ASSESSMENT SESSION MANAGEMENT
// =============================================

// Assessment Sessions
export const assessmentSessionsTable = pgTable(
  'assessment_sessions',
  {
    id: uuid('id').primaryKey().default(sql`uuid_generate_v7()`),
    studentId: uuid('student_id')
      .notNull()
      .references(() => studentsTable.id),
    caseId: uuid('case_id').references(() => casesTable.id),
    psychologistId: uuid('psychologist_id')
      .notNull()
      .references(() => usersTable.id),
    sessionDate: timestamp('session_date', { withTimezone: true }).notNull(),
    sessionDuration: integer('session_duration'), // in minutes
    sessionType: Enums.sessionTypeEnum('session_type').notNull(),
    location: varchar('location', { length: 255 }),
    referralReason: text('referral_reason'),
    backgroundInformation: text('background_information'),
    behavioralObservations: text('behavioral_observations'),
    testingConditions: text('testing_conditions'),
    accommodationsProvided: jsonb('accommodations_provided'), // Array of accommodations
    environmentalFactors: text('environmental_factors'),
    sessionStatus: Enums.sessionStatusEnum('session_status').default(
      Enums.SessionStatusEnum.SCHEDULED
    ),
    validityConcerns: text('validity_concerns'),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    index('session_student_idx').on(table.studentId),
    index('session_psychologist_idx').on(table.psychologistId),
    index('session_case_idx').on(table.caseId),
    index('session_date_idx').on(table.sessionDate),
    index('session_status_idx').on(table.sessionStatus),
    index('session_type_idx').on(table.sessionType),
    Policies.assessmentSessionsSelectPolicy,
    Policies.assessmentSessionsInsertPolicy,
    Policies.assessmentSessionsUpdatePolicy,
    Policies.assessmentSessionsDeletePolicy,
  ]
);

// Test Administrations within sessions
export const testAdministrationsTable = pgTable(
  'test_administrations',
  {
    id: uuid('id').primaryKey().default(sql`uuid_generate_v7()`),
    sessionId: uuid('session_id')
      .notNull()
      .references(() => assessmentSessionsTable.id),
    batteryId: uuid('battery_id')
      .notNull()
      .references(() => testBatteriesTable.id),
    administrationOrder: integer('administration_order').notNull(),
    startTime: timestamp('start_time', { withTimezone: true }),
    endTime: timestamp('end_time', { withTimezone: true }),
    administrationNotes: text('administration_notes'),
    accommodationsUsed: jsonb('accommodations_used'),
    discontinuationNotes: text('discontinuation_notes'),
    validityIndicators: jsonb('validity_indicators'),
    adminStatus: Enums.adminStatusEnum('admin_status').default(
      Enums.AdminStatusEnum.PLANNED
    ),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    index('admin_session_idx').on(table.sessionId),
    index('admin_battery_idx').on(table.batteryId),
    index('admin_order_idx').on(table.administrationOrder),
    index('admin_status_idx').on(table.adminStatus),
    index('admin_start_time_idx').on(table.startTime),
    Policies.testAdministrationsSelectPolicy,
  ]
);

// =============================================
// COMPREHENSIVE SCORING TABLES
// =============================================

// Subtest Scores
export const subtestScoresTable = pgTable(
  'subtest_scores',
  {
    id: uuid('id').primaryKey().default(sql`uuid_generate_v7()`),
    administrationId: uuid('administration_id')
      .notNull()
      .references(() => testAdministrationsTable.id),
    subtestId: uuid('subtest_id')
      .notNull()
      .references(() => subtestsTable.id),
    rawScore: integer('raw_score'),
    scaledScore: integer('scaled_score'),
    percentileRank: decimal('percentile_rank', { precision: 5, scale: 2 }),
    confidenceIntervalLower: integer('confidence_interval_lower'),
    confidenceIntervalUpper: integer('confidence_interval_upper'),
    confidenceLevel: integer('confidence_level').default(95), // 90, 95, 99
    ageEquivalent: varchar('age_equivalent', { length: 20 }), // e.g., "8;6"
    gradeEquivalent: varchar('grade_equivalent', { length: 20 }), // e.g., "3.2"
    completionTimeMinutes: integer('completion_time_minutes'),
    discontinuedItem: integer('discontinued_item'),
    descriptiveCategory: varchar('descriptive_category', { length: 100 }), // "Very Low", "Low Average", etc.
    qualitativeDescriptor: varchar('qualitative_descriptor', { length: 100 }),
    strengthsIdentified: text('strengths_identified'),
    weaknessesIdentified: text('weaknesses_identified'),
    scoringNotes: text('scoring_notes'),
    isValid: boolean('is_valid').default(true),
    validityNotes: text('validity_notes'),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    uniqueIndex('score_admin_subtest_unique').on(
      table.administrationId,
      table.subtestId
    ),
    index('score_admin_idx').on(table.administrationId),
    index('score_subtest_idx').on(table.subtestId),
    index('score_scaled_idx').on(table.scaledScore),
    index('score_percentile_idx').on(table.percentileRank),
    index('score_valid_idx').on(table.isValid),
    Policies.subtestScoresSelectPolicy,
  ]
);

// Index/Composite Scores
export const indexScoresTable = pgTable(
  'index_scores',
  {
    id: uuid('id').primaryKey().default(sql`uuid_generate_v7()`),
    administrationId: uuid('administration_id')
      .notNull()
      .references(() => testAdministrationsTable.id),
    indexId: uuid('index_id')
      .notNull()
      .references(() => testIndicesTable.id),
    compositeScore: integer('composite_score'),
    percentileRank: decimal('percentile_rank', { precision: 5, scale: 2 }),
    confidenceIntervalLower: integer('confidence_interval_lower'),
    confidenceIntervalUpper: integer('confidence_interval_upper'),
    confidenceLevel: integer('confidence_level').default(95),
    descriptiveCategory: varchar('descriptive_category', { length: 100 }),
    qualitativeDescriptor: varchar('qualitative_descriptor', { length: 100 }),
    calculationMethod: varchar('calculation_method', { length: 100 }),
    componentSubtests: jsonb('component_subtests'), // Array of subtest IDs used
    interpretationNotes: text('interpretation_notes'),
    strengthsIdentified: text('strengths_identified'),
    weaknessesIdentified: text('weaknesses_identified'),
    isValid: boolean('is_valid').default(true),
    validityNotes: text('validity_notes'),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    uniqueIndex('index_score_admin_index_unique').on(
      table.administrationId,
      table.indexId
    ),
    index('index_score_admin_idx').on(table.administrationId),
    index('index_score_index_idx').on(table.indexId),
    index('index_score_composite_idx').on(table.compositeScore),
    index('index_score_percentile_idx').on(table.percentileRank),
    index('index_score_valid_idx').on(table.isValid),
    Policies.indexScoresSelectPolicy,
  ]
);

/*
 * -------------------------------------------------------
 * SECTION: Relations
 * -------------------------------------------------------
 */

export const testBatteriesRelations = relations(
  testBatteriesTable,
  ({ many }) => ({
    testIndices: many(testIndicesTable),
    subtests: many(subtestsTable),
    testAdministrations: many(testAdministrationsTable),
  })
);

export const testIndicesRelations = relations(
  testIndicesTable,
  ({ one, many }) => ({
    battery: one(testBatteriesTable, {
      fields: [testIndicesTable.batteryId],
      references: [testBatteriesTable.id],
    }),
    indexSubtestMappings: many(indexSubtestMappingsTable),
    indexScores: many(indexScoresTable),
  })
);

export const subtestsRelations = relations(subtestsTable, ({ one, many }) => ({
  battery: one(testBatteriesTable, {
    fields: [subtestsTable.batteryId],
    references: [testBatteriesTable.id],
  }),
  indexSubtestMappings: many(indexSubtestMappingsTable),
  subtestScores: many(subtestScoresTable),
}));

export const indexSubtestMappingsRelations = relations(
  indexSubtestMappingsTable,
  ({ one }) => ({
    index: one(testIndicesTable, {
      fields: [indexSubtestMappingsTable.indexId],
      references: [testIndicesTable.id],
    }),
    subtest: one(subtestsTable, {
      fields: [indexSubtestMappingsTable.subtestId],
      references: [subtestsTable.id],
    }),
  })
);

export const assessmentSessionsRelations = relations(
  assessmentSessionsTable,
  ({ one, many }) => ({
    student: one(studentsTable, {
      fields: [assessmentSessionsTable.studentId],
      references: [studentsTable.id],
    }),
    psychologist: one(usersTable, {
      fields: [assessmentSessionsTable.psychologistId],
      references: [usersTable.id],
      relationName: 'session_psychologist',
    }),
    case: one(casesTable, {
      fields: [assessmentSessionsTable.caseId],
      references: [casesTable.id],
    }),
    testAdministrations: many(testAdministrationsTable),
    documents: many(documentsTable),
  })
);

export const testAdministrationsRelations = relations(
  testAdministrationsTable,
  ({ one, many }) => ({
    session: one(assessmentSessionsTable, {
      fields: [testAdministrationsTable.sessionId],
      references: [assessmentSessionsTable.id],
    }),
    battery: one(testBatteriesTable, {
      fields: [testAdministrationsTable.batteryId],
      references: [testBatteriesTable.id],
    }),
    subtestScores: many(subtestScoresTable),
    indexScores: many(indexScoresTable),
    documents: many(documentsTable),
  })
);

export const subtestScoresRelations = relations(
  subtestScoresTable,
  ({ one }) => ({
    administration: one(testAdministrationsTable, {
      fields: [subtestScoresTable.administrationId],
      references: [testAdministrationsTable.id],
    }),
    subtest: one(subtestsTable, {
      fields: [subtestScoresTable.subtestId],
      references: [subtestsTable.id],
    }),
  })
);

export const indexScoresRelations = relations(indexScoresTable, ({ one }) => ({
  administration: one(testAdministrationsTable, {
    fields: [indexScoresTable.administrationId],
    references: [testAdministrationsTable.id],
  }),
  index: one(testIndicesTable, {
    fields: [indexScoresTable.indexId],
    references: [testIndicesTable.id],
  }),
}));

// Relations for documentsTable to connect with psychological testing
export const documentsTablePsychTestingRelations = relations(
  documentsTable,
  ({ one }) => ({
    assessmentSession: one(assessmentSessionsTable, {
      fields: [documentsTable.assessmentSessionId],
      references: [assessmentSessionsTable.id],
    }),
    testAdministration: one(testAdministrationsTable, {
      fields: [documentsTable.testAdministrationId],
      references: [testAdministrationsTable.id],
    }),
  })
);

/*
 * -------------------------------------------------------
 * SECTION: Zod Schemas
 * -------------------------------------------------------
 */
// Test Batteries
export const insertTestBatterySchema = createInsertSchema(testBatteriesTable);
export const selectTestBatterySchema = createSelectSchema(testBatteriesTable);
export const updateTestBatterySchema = insertTestBatterySchema.partial().omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

// Test Indices
export const insertTestIndexSchema = createInsertSchema(testIndicesTable);
export const selectTestIndexSchema = createSelectSchema(testIndicesTable);
export const updateTestIndexSchema = insertTestIndexSchema.partial().omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

// Subtests
export const insertSubtestSchema = createInsertSchema(subtestsTable);
export const selectSubtestSchema = createSelectSchema(subtestsTable);
export const updateSubtestSchema = insertSubtestSchema.partial().omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

// Assessment Sessions
export const insertAssessmentSessionSchema = createInsertSchema(
  assessmentSessionsTable
);
export const selectAssessmentSessionSchema = createSelectSchema(
  assessmentSessionsTable
);
export const updateAssessmentSessionSchema = insertAssessmentSessionSchema
  .partial()
  .omit({
    id: true,
    createdAt: true,
    updatedAt: true,
  });

// Test Administrations
export const insertTestAdministrationSchema = createInsertSchema(
  testAdministrationsTable
);
export const selectTestAdministrationSchema = createSelectSchema(
  testAdministrationsTable
);
export const updateTestAdministrationSchema = insertTestAdministrationSchema
  .partial()
  .omit({
    id: true,
    createdAt: true,
    updatedAt: true,
  });

// Subtest Scores
export const insertSubtestScoreSchema = createInsertSchema(subtestScoresTable);
export const selectSubtestScoreSchema = createSelectSchema(subtestScoresTable);
export const updateSubtestScoreSchema = insertSubtestScoreSchema
  .partial()
  .omit({
    id: true,
    createdAt: true,
    updatedAt: true,
  });

// Index Scores
export const insertIndexScoreSchema = createInsertSchema(indexScoresTable);
export const selectIndexScoreSchema = createSelectSchema(indexScoresTable);
export const updateIndexScoreSchema = insertIndexScoreSchema.partial().omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});
