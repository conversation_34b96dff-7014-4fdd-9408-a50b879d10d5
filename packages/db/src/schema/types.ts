import type { SQL } from 'drizzle-orm';
import type * as PsychologicalTesting from './psychological-testing';
import type * as Tables from './tables';

export interface QueryBuilderOpts {
  where?: SQL;
  orderBy?: SQL;
  distinct?: boolean;
  nullish?: boolean;
}

export type District = typeof Tables.districtsTable.$inferSelect;
export type NewDistrict = typeof Tables.districtsTable.$inferInsert;

export type Address = typeof Tables.addressesTable.$inferSelect;
export type NewAddress = typeof Tables.addressesTable.$inferInsert;

export type School = typeof Tables.schoolsTable.$inferSelect;
export type NewSchool = typeof Tables.schoolsTable.$inferInsert;

export type Student = typeof Tables.studentsTable.$inferSelect;
export type NewStudent = typeof Tables.studentsTable.$inferInsert;

export type User = typeof Tables.usersTable.$inferSelect;
export type NewUser = typeof Tables.usersTable.$inferInsert;

export type StudentEnrollment =
  typeof Tables.studentEnrollmentsTable.$inferSelect;
export type NewStudentEnrollment =
  typeof Tables.studentEnrollmentsTable.$inferInsert;

export type Role = typeof Tables.rolesTable.$inferSelect;
export type NewRole = typeof Tables.rolesTable.$inferInsert;

export type UserDistrict = typeof Tables.userDistrictsTable.$inferSelect;
export type NewUserDistrict = typeof Tables.userDistrictsTable.$inferInsert;

export type UserRole = typeof Tables.userRolesTable.$inferSelect;
export type NewUserRole = typeof Tables.userRolesTable.$inferInsert;

export type Permission = typeof Tables.permissionsTable.$inferSelect;
export type NewPermission = typeof Tables.permissionsTable.$inferInsert;

export type RolePermission = typeof Tables.rolePermissionsTable.$inferSelect;
export type NewRolePermission = typeof Tables.rolePermissionsTable.$inferInsert;

export type Language = typeof Tables.languagesTable.$inferSelect;
export type NewLanguage = typeof Tables.languagesTable.$inferInsert;

export type StudentLanguage = typeof Tables.studentLanguagesTable.$inferSelect;
export type NewStudentLanguage =
  typeof Tables.studentLanguagesTable.$inferInsert;

export type Availability = typeof Tables.availabilitiesTable.$inferSelect;
export type NewAvailability = typeof Tables.availabilitiesTable.$inferInsert;

export type Parent = typeof Tables.parentsTable.$inferSelect;
export type NewParent = typeof Tables.parentsTable.$inferInsert;

export type StudentParent = typeof Tables.studentParentsTable.$inferSelect;
export type NewStudentParent = typeof Tables.studentParentsTable.$inferInsert;

export type Case = typeof Tables.casesTable.$inferSelect;
export type NewCase = typeof Tables.casesTable.$inferInsert;

export type Plan = typeof Tables.plansTable.$inferSelect;
export type NewPlan = typeof Tables.plansTable.$inferInsert;

export type Document = typeof Tables.documentsTable.$inferSelect;
export type NewDocument = typeof Tables.documentsTable.$inferInsert;

export type CaseAssignment = typeof Tables.caseAssignmentsTable.$inferSelect;
export type NewCaseAssignment = typeof Tables.caseAssignmentsTable.$inferInsert;

export type CaseDetail = typeof Tables.caseDetailsTable.$inferSelect;
export type NewCaseDetail = typeof Tables.caseDetailsTable.$inferInsert;

export type Invitation = typeof Tables.invitationsTable.$inferSelect;
export type NewInvitation = typeof Tables.invitationsTable.$inferInsert;

export type InvitationSchool =
  typeof Tables.invitationSchoolsTable.$inferSelect;
export type NewInvitationSchool =
  typeof Tables.invitationSchoolsTable.$inferInsert;

export type Task = typeof Tables.tasksTable.$inferSelect;
export type NewTask = typeof Tables.tasksTable.$inferInsert;

export type TaskDependency = typeof Tables.taskDependenciesTable.$inferSelect;
export type NewTaskDependency =
  typeof Tables.taskDependenciesTable.$inferInsert;

export type TaskHistory = typeof Tables.taskHistoryTable.$inferSelect;
export type NewTaskHistory = typeof Tables.taskHistoryTable.$inferInsert;

export type DistrictAvailability =
  typeof Tables.districtAvailabilitiesTable.$inferSelect;
export type NewDistrictAvailability =
  typeof Tables.districtAvailabilitiesTable.$inferInsert;

export type DistrictBlockedDate =
  typeof Tables.districtBlockedDatesTable.$inferSelect;
export type NewDistrictBlockedDate =
  typeof Tables.districtBlockedDatesTable.$inferInsert;

export type Feedback = typeof Tables.feedbackTable.$inferSelect;
export type NewFeedback = typeof Tables.feedbackTable.$inferInsert;
export type FeedbackFile = typeof Tables.feedbackFileTable.$inferSelect;
export type NewFeedbackFile = typeof Tables.feedbackFileTable.$inferInsert;

export type DistrictPreference =
  typeof Tables.districtPreferencesTable.$inferSelect;
export type NewDistrictPreference =
  typeof Tables.districtPreferencesTable.$inferInsert;

export type JoinRequest = typeof Tables.joinRequestsTable.$inferSelect;
export type NewJoinRequest = typeof Tables.joinRequestsTable.$inferInsert;

export type Notification = typeof Tables.notificationsTable.$inferSelect;
export type NewNotification = typeof Tables.notificationsTable.$inferInsert;

export type UserNotificationPreference =
  typeof Tables.userNotificationPreferencesTable.$inferSelect;
export type NewUserNotificationPreference =
  typeof Tables.userNotificationPreferencesTable.$inferInsert;

/*
 * -------------------------------------------------------
 * SECTION: Psychological Testing Tables Types
 * -------------------------------------------------------
 */

export type TestBattery =
  typeof PsychologicalTesting.testBatteriesTable.$inferSelect;
export type NewTestBattery =
  typeof PsychologicalTesting.testBatteriesTable.$inferInsert;

export type TestIndex =
  typeof PsychologicalTesting.testIndicesTable.$inferSelect;
export type NewTestIndex =
  typeof PsychologicalTesting.testIndicesTable.$inferInsert;

export type Subtest = typeof PsychologicalTesting.subtestsTable.$inferSelect;
export type NewSubtest = typeof PsychologicalTesting.subtestsTable.$inferInsert;

export type IndexSubtestMapping =
  typeof PsychologicalTesting.indexSubtestMappingsTable.$inferSelect;
export type NewIndexSubtestMapping =
  typeof PsychologicalTesting.indexSubtestMappingsTable.$inferInsert;

export type AssessmentSession =
  typeof PsychologicalTesting.assessmentSessionsTable.$inferSelect;
export type NewAssessmentSession =
  typeof PsychologicalTesting.assessmentSessionsTable.$inferInsert;

export type TestAdministration =
  typeof PsychologicalTesting.testAdministrationsTable.$inferSelect;
export type NewTestAdministration =
  typeof PsychologicalTesting.testAdministrationsTable.$inferInsert;

export type SubtestScore =
  typeof PsychologicalTesting.subtestScoresTable.$inferSelect;
export type NewSubtestScore =
  typeof PsychologicalTesting.subtestScoresTable.$inferInsert;

export type IndexScore =
  typeof PsychologicalTesting.indexScoresTable.$inferSelect;
export type NewIndexScore =
  typeof PsychologicalTesting.indexScoresTable.$inferInsert;
