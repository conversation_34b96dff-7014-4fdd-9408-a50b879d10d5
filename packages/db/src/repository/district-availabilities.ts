import type { TransactionType } from '../config/client';
import {
  districtAvailabilitiesTable,
  type NewDistrictAvailability,
} from '../schema';

export class DistrictAvailabilitiesRepository {
  private tx: TransactionType;

  constructor(tx: TransactionType) {
    this.tx = tx;
  }

  async createAvailabilities(availabilities: NewDistrictAvailability[]) {
    if (availabilities.length === 0) {
      return 0;
    }

    await this.tx.insert(districtAvailabilitiesTable).values(availabilities);

    return availabilities.length;
  }
}
