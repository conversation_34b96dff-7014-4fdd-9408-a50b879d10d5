import { and, eq } from 'drizzle-orm';
import type { TransactionType } from '../config/client';
import { notificationsTable } from '../schema/tables';
import type { NewNotification } from '../schema/types';

export class NotificationsRepository {
  private readonly tx: TransactionType;

  constructor(tx: TransactionType) {
    this.tx = tx;
  }

  async createNotification(notification: NewNotification): Promise<void> {
    await this.tx.insert(notificationsTable).values(notification);
  }

  async markNotificationAsRead(
    userId: string,
    notificationId: string
  ): Promise<void> {
    await this.tx
      .update(notificationsTable)
      .set({ isRead: true, readAt: new Date() })
      .where(
        and(
          eq(notificationsTable.userId, userId),
          eq(notificationsTable.id, notificationId),
          eq(notificationsTable.isRead, false)
        )
      );
  }

  async markAllNotificationsAsRead(userId: string): Promise<void> {
    await this.tx
      .update(notificationsTable)
      .set({ isRead: true, readAt: new Date() })
      .where(
        and(
          eq(notificationsTable.userId, userId),
          eq(notificationsTable.isRead, false)
        )
      );
  }

  async archiveNotification(
    userId: string,
    notificationId: string
  ): Promise<void> {
    const now = new Date();
    await this.tx
      .update(notificationsTable)
      .set({
        isArchived: true,
        archivedAt: now,
        isRead: true,
        readAt: now,
      })
      .where(
        and(
          eq(notificationsTable.userId, userId),
          eq(notificationsTable.id, notificationId),
          eq(notificationsTable.isArchived, false)
        )
      );
  }

  async unarchiveNotification(
    userId: string,
    notificationId: string
  ): Promise<void> {
    await this.tx
      .update(notificationsTable)
      .set({ isArchived: false, archivedAt: null })
      .where(
        and(
          eq(notificationsTable.userId, userId),
          eq(notificationsTable.id, notificationId),
          eq(notificationsTable.isArchived, true)
        )
      );
  }

  async archiveReadNotifications(userId: string): Promise<void> {
    await this.tx
      .update(notificationsTable)
      .set({ isArchived: true, archivedAt: new Date() })
      .where(
        and(
          eq(notificationsTable.userId, userId),
          eq(notificationsTable.isRead, true),
          eq(notificationsTable.isArchived, false)
        )
      );
  }

  async archiveAllNotifications(userId: string): Promise<void> {
    const now = new Date();
    await this.tx
      .update(notificationsTable)
      .set({
        isArchived: true,
        archivedAt: now,
        isRead: true,
        readAt: now,
      })
      .where(
        and(
          eq(notificationsTable.userId, userId),
          eq(notificationsTable.isArchived, false)
        )
      );
  }

  async unarchiveAllNotifications(userId: string): Promise<void> {
    await this.tx
      .update(notificationsTable)
      .set({
        isArchived: false,
        archivedAt: null,
      })
      .where(
        and(
          eq(notificationsTable.userId, userId),
          eq(notificationsTable.isArchived, true)
        )
      );
  }

  async deleteArchivedNotifications(userId: string): Promise<void> {
    await this.tx
      .delete(notificationsTable)
      .where(
        and(
          eq(notificationsTable.userId, userId),
          eq(notificationsTable.isArchived, true)
        )
      );
  }
}
