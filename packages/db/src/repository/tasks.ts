import {
  aliasedTable,
  and,
  asc,
  between,
  count,
  desc,
  eq,
  gte,
  ilike,
  inArray,
  isNotNull,
  isNull,
  lt,
  lte,
  ne,
  notIlike,
  or,
  type SQL,
} from 'drizzle-orm';
import type { PgColumn } from 'drizzle-orm/pg-core';
import type { TransactionType } from '../config/client';
import {
  RoleEnum,
  TaskHistoryActionEnum,
  TaskPriorityEnum,
  TaskStatusEnum,
  TaskTypeEnum,
} from '../schema/enums';
import {
  casesTable,
  districtsTable,
  schoolsTable,
  studentsTable,
  taskDependenciesTable,
  taskHistoryTable,
  tasksTable,
  userRolesTable,
  usersTable,
} from '../schema/tables';
import type { NewTask } from '../schema/types';
import type {
  ActiveMonitorRatingScalesTask,
  GetTasksParams,
  TaskSortField,
  TaskWithRelations,
} from './types/tasks';

export interface TaskStats {
  total: number;
  pending: number;
  inProgress: number;
  completed: number;
  cancelled: number;
  blocked: number;
  rejected: number;
  overdue: number;
  dueToday: number;
  dueThisWeek: number;
}

export interface TaskDependencyWithTasks {
  id: string;
  predecessorTaskId: string;
  successorTaskId: string;
  createdAt: Date;
  predecessorTask: {
    id: string;
    taskType: TaskTypeEnum;
    status: TaskStatusEnum;
    priority: TaskPriorityEnum;
    dueDate: Date | null;
    completedAt: Date | null;
  };
  successorTask: {
    id: string;
    taskType: TaskTypeEnum;
    status: TaskStatusEnum;
    priority: TaskPriorityEnum;
    dueDate: Date | null;
    completedAt: Date | null;
  };
}

export class TasksRepository {
  private readonly tx: TransactionType;

  constructor(tx: TransactionType) {
    this.tx = tx;
  }

  private getAliases() {
    const assignee = aliasedTable(usersTable, 'assignee');
    const assigner = aliasedTable(usersTable, 'assigner');
    return { assignee, assigner };
  }

  private getTaskSelectFields(
    assignee: typeof usersTable,
    assigner: typeof usersTable
  ) {
    return {
      id: tasksTable.id,
      taskType: tasksTable.taskType,
      case: {
        id: casesTable.id,
        studentId: casesTable.studentId,
      },
      student: {
        id: studentsTable.id,
        fullName: studentsTable.fullName,
        studentIdNumber: studentsTable.studentIdNumber,
      },
      school: {
        id: schoolsTable.id,
        name: schoolsTable.name,
      },
      district: {
        id: districtsTable.id,
        name: districtsTable.name,
      },
      assignedTo: {
        id: assignee.id,
        avatar: assignee.avatar,
        fullName: assignee.fullName,
        email: assignee.email,
      },
      assignedBy: {
        id: assigner.id,
        avatar: assigner.avatar,
        fullName: assigner.fullName,
        email: assigner.email,
      },
      status: tasksTable.status,
      priority: tasksTable.priority,
      dueDate: tasksTable.dueDate,
      notes: tasksTable.notes,
      reason: tasksTable.reason,
      metadata: tasksTable.metadata,
      completedAt: tasksTable.completedAt,
      rejectedAt: tasksTable.rejectedAt,
      cancelledAt: tasksTable.cancelledAt,
      createdAt: tasksTable.createdAt,
      updatedAt: tasksTable.updatedAt,
    };
  }

  private buildOrderBy(sort: GetTasksParams['sort'] = []): SQL<unknown>[] {
    if (sort.length === 0) {
      return [asc(tasksTable.createdAt)];
    }

    const orderByConditions: SQL[] = [];

    for (const { id, desc: isDesc } of sort) {
      const column: Record<TaskSortField, PgColumn> = {
        dueDate: tasksTable.dueDate,
        priority: tasksTable.priority,
        createdAt: tasksTable.createdAt,
        completedAt: tasksTable.completedAt,
        rejectedAt: tasksTable.rejectedAt,
        cancelledAt: tasksTable.cancelledAt,
      };
      const orderBy = isDesc ? desc(column[id]) : asc(column[id]);
      if (orderBy) {
        orderByConditions.push(orderBy);
      }
    }

    return orderByConditions.length > 0
      ? orderByConditions
      : [asc(studentsTable.fullName)];
  }

  private buildFilterCondition(
    filter: NonNullable<GetTasksParams['filters']>[number]
  ): SQL | undefined {
    const { id, value, operator } = filter;
    const { assignee, assigner } = this.getAliases();

    switch (id) {
      case 'assignedTo': {
        const assignedTo: Record<string, SQL> = {
          iLike: ilike(assignee.fullName, `%${value}%`),
          notILike: notIlike(assignee.fullName, `%${value}%`),
          eq: eq(assignee.fullName, value as string),
          ne: ne(assignee.fullName, value as string),
          isEmpty: eq(assignee.fullName, ''),
          isNotEmpty: ne(assignee.fullName, ''),
        };
        return assignedTo[operator];
      }
      case 'assignedBy': {
        const assignedBy: Record<string, SQL> = {
          iLike: ilike(assigner.fullName, `%${value}%`),
          notILike: notIlike(assigner.fullName, `%${value}%`),
          eq: eq(assigner.fullName, value as string),
          ne: ne(assigner.fullName, value as string),
          isEmpty: eq(assigner.fullName, ''),
          isNotEmpty: ne(assigner.fullName, ''),
        };
        return assignedBy[operator];
      }
      case 'dueDate': {
        if (Array.isArray(value)) {
          const [from, to] = value;
          const fromDate = new Date(Number(from));
          const toDate = new Date(Number(to));
          fromDate.setHours(0, 0, 0, 0);
          toDate.setHours(23, 59, 59, 999);
          const map: Record<string, SQL> = {
            isBetween: between(tasksTable.dueDate, fromDate, toDate),
          };
          return map[operator];
        }

        const dueDate = new Date(Number(value as string));
        dueDate.setHours(0, 0, 0, 0);
        const nextDay = new Date(dueDate);
        nextDay.setDate(dueDate.getDate() + 1);
        nextDay.setMilliseconds(nextDay.getMilliseconds() - 1);
        const dueDateMap: Record<string, SQL | undefined> = {
          eq: between(tasksTable.dueDate, dueDate, nextDay),
          ne: or(
            lt(tasksTable.dueDate, dueDate),
            gte(tasksTable.dueDate, nextDay)
          ),
          lt: lt(tasksTable.dueDate, dueDate),
          gt: gte(tasksTable.dueDate, nextDay),
          lte: lt(tasksTable.dueDate, nextDay),
          gte: gte(tasksTable.dueDate, dueDate),
          isEmpty: isNull(tasksTable.dueDate),
          isNotEmpty: isNotNull(tasksTable.dueDate),
          isRelativeToToday: between(tasksTable.dueDate, new Date(), dueDate),
        };
        return dueDateMap[operator];
      }
      case 'status': {
        const status: Record<string, SQL> = {
          eq: eq(tasksTable.status, value as TaskStatusEnum),
          ne: ne(tasksTable.status, value as TaskStatusEnum),
        };
        return status[operator];
      }
      case 'priority': {
        const priority: Record<string, SQL> = {
          eq: eq(tasksTable.priority, value as TaskPriorityEnum),
          ne: ne(tasksTable.priority, value as TaskPriorityEnum),
        };
        return priority[operator];
      }
      case 'taskType': {
        const taskType: Record<string, SQL> = {
          eq: eq(tasksTable.taskType, value as TaskTypeEnum),
          ne: ne(tasksTable.taskType, value as TaskTypeEnum),
        };
        return taskType[operator];
      }
      // case 'caseId': {
      //   const caseId: Record<string, SQL> = {
      //     iLike: ilike(casesTable.id, `%${value}%`),
      //     notILike: notIlike(casesTable.id, `%${value}%`),
      //     eq: eq(casesTable.id, value as string),
      //     ne: ne(casesTable.id, value as string),
      //     isEmpty: eq(casesTable.id, ''),
      //     isNotEmpty: ne(casesTable.id, ''),
      //   };
      //   return caseId[operator];
      // }
      case 'studentName': {
        const studentName: Record<string, SQL> = {
          iLike: ilike(studentsTable.fullName, `%${value}%`),
          notILike: notIlike(studentsTable.fullName, `%${value}%`),
          eq: eq(studentsTable.fullName, value as string),
          ne: ne(studentsTable.fullName, value as string),
          isEmpty: eq(studentsTable.fullName, ''),
          isNotEmpty: ne(studentsTable.fullName, ''),
        };
        return studentName[operator];
      }
      case 'studentIdNumber': {
        const studentIdNumber: Record<string, SQL> = {
          iLike: ilike(studentsTable.studentIdNumber, `%${value}%`),
          notILike: notIlike(studentsTable.studentIdNumber, `%${value}%`),
          eq: eq(studentsTable.studentIdNumber, value as string),
          ne: ne(studentsTable.studentIdNumber, value as string),
          isEmpty: eq(studentsTable.studentIdNumber, ''),
          isNotEmpty: ne(studentsTable.studentIdNumber, ''),
        };
        return studentIdNumber[operator];
      }
      case 'districtName': {
        const districtName: Record<string, SQL> = {
          iLike: ilike(districtsTable.name, `%${value}%`),
          notILike: notIlike(districtsTable.name, `%${value}%`),
          eq: eq(districtsTable.name, value as string),
          ne: ne(districtsTable.name, value as string),
          isEmpty: eq(districtsTable.name, ''),
          isNotEmpty: ne(districtsTable.name, ''),
        };
        return districtName[operator];
      }
      case 'schoolName': {
        const schoolName: Record<string, SQL> = {
          iLike: ilike(schoolsTable.name, `%${value}%`),
          notILike: notIlike(schoolsTable.name, `%${value}%`),
          eq: eq(schoolsTable.name, value as string),
          ne: ne(schoolsTable.name, value as string),
          isEmpty: eq(schoolsTable.name, ''),
          isNotEmpty: ne(schoolsTable.name, ''),
        };
        return schoolName[operator];
      }
      default:
        return;
    }
  }

  private buildConditions(
    search: string | undefined,
    filters: GetTasksParams['filters'] = []
  ): SQL[] {
    const { assignee } = this.getAliases();

    const conditions: SQL[] = [];

    if (search?.trim()) {
      const searchConditions = or(
        ilike(tasksTable.notes, `%${search}%`),
        ilike(studentsTable.fullName, `%${search}%`),
        ilike(schoolsTable.name, `%${search}%`),
        ilike(districtsTable.name, `%${search}%`),
        ilike(assignee.fullName, `%${search}%`)
      );

      if (searchConditions) {
        conditions.push(searchConditions);
      }
    }

    for (const filter of filters) {
      const condition = this.buildFilterCondition(filter);
      if (condition) {
        conditions.push(condition);
      }
    }

    return conditions;
  }

  private buildWhereClause(
    conditions: SQL[],
    joinOperator: 'and' | 'or' = 'and'
  ): SQL | undefined {
    if (conditions.length === 0) {
      return;
    }

    if (conditions.length === 1) {
      return conditions[0];
    }

    const operationsMap = {
      and: and(...conditions),
      or: or(...conditions),
    };

    return operationsMap[joinOperator];
  }

  async getTasksByUserId(
    userId: string,
    params: GetTasksParams
  ): Promise<{
    tasks: TaskWithRelations[];
    totalCount: number;
  }> {
    const { assignee, assigner } = this.getAliases();
    const fields = this.getTaskSelectFields(assignee, assigner);
    const {
      // page = 1,
      // perPage = 10,
      search,
      filters = [],
      joinOperator = 'and',
      sort = [{ id: 'createdAt', desc: false }],
    } = params;

    const conditions: SQL[] = [
      eq(tasksTable.assignedToId, userId),
      ...this.buildConditions(search, filters),
    ];
    const whereClause = this.buildWhereClause(conditions, joinOperator);
    const orderBy = this.buildOrderBy(sort);
    // const offset = (page - 1) * perPage;

    const [total] = await this.tx
      .select({ totalCount: count() })
      .from(tasksTable)
      .innerJoin(assignee, eq(tasksTable.assignedToId, assignee.id))
      .leftJoin(districtsTable, eq(tasksTable.districtId, districtsTable.id))
      .leftJoin(schoolsTable, eq(tasksTable.schoolId, schoolsTable.id))
      .leftJoin(casesTable, eq(tasksTable.caseId, casesTable.id))
      .leftJoin(studentsTable, eq(tasksTable.studentId, studentsTable.id))
      .where(whereClause);

    const { totalCount } = total ?? {};
    if (!totalCount) {
      return {
        tasks: [],
        totalCount: 0,
      };
    }

    const tasks = await this.tx
      .select(fields)
      .from(tasksTable)
      .innerJoin(assignee, eq(tasksTable.assignedToId, assignee.id))
      .innerJoin(assigner, eq(tasksTable.assignedById, assigner.id))
      .leftJoin(districtsTable, eq(tasksTable.districtId, districtsTable.id))
      .leftJoin(schoolsTable, eq(tasksTable.schoolId, schoolsTable.id))
      .leftJoin(casesTable, eq(tasksTable.caseId, casesTable.id))
      .leftJoin(studentsTable, eq(tasksTable.studentId, studentsTable.id))
      .where(whereClause)
      .orderBy(...orderBy);
    // .limit(perPage)
    // .offset(offset);

    return {
      tasks,
      totalCount,
    };
  }

  async getTaskById(id: string): Promise<TaskWithRelations> {
    const { assignee, assigner } = this.getAliases();
    const fields = this.getTaskSelectFields(assignee, assigner);

    const [task] = await this.tx
      .select(fields)
      .from(tasksTable)
      .innerJoin(assignee, eq(tasksTable.assignedToId, assignee.id))
      .innerJoin(assigner, eq(tasksTable.assignedById, assigner.id))
      .leftJoin(districtsTable, eq(tasksTable.districtId, districtsTable.id))
      .leftJoin(schoolsTable, eq(tasksTable.schoolId, schoolsTable.id))
      .leftJoin(casesTable, eq(tasksTable.caseId, casesTable.id))
      .leftJoin(studentsTable, eq(tasksTable.studentId, studentsTable.id))
      .where(eq(tasksTable.id, id))
      .limit(1);

    if (!task) {
      throw new Error('TASK NOT FOUND');
    }

    return task;
  }

  async getTaskHistory(taskId: string) {
    return await this.tx
      .select({
        id: taskHistoryTable.id,
        action: taskHistoryTable.action,
        previousStatus: taskHistoryTable.previousStatus,
        newStatus: taskHistoryTable.newStatus,
        createdAt: taskHistoryTable.createdAt,
        user: {
          id: usersTable.id,
          fullName: usersTable.fullName,
          email: usersTable.email,
        },
      })
      .from(taskHistoryTable)
      .innerJoin(usersTable, eq(taskHistoryTable.userId, usersTable.id))
      .where(eq(taskHistoryTable.taskId, taskId))
      .orderBy(desc(taskHistoryTable.createdAt));
  }

  async getTaskDependencies(
    taskId: string
  ): Promise<TaskDependencyWithTasks[]> {
    const predecessorTask = aliasedTable(tasksTable, 'predecessorTask');
    const successorTask = aliasedTable(tasksTable, 'successorTask');

    return await this.tx
      .select({
        id: taskDependenciesTable.id,
        predecessorTaskId: taskDependenciesTable.predecessorTaskId,
        successorTaskId: taskDependenciesTable.successorTaskId,
        createdAt: taskDependenciesTable.createdAt,
        predecessorTask: {
          id: predecessorTask.id,
          taskType: predecessorTask.taskType,
          status: predecessorTask.status,
          priority: predecessorTask.priority,
          dueDate: predecessorTask.dueDate,
          completedAt: predecessorTask.completedAt,
        },
        successorTask: {
          id: successorTask.id,
          taskType: successorTask.taskType,
          status: successorTask.status,
          priority: successorTask.priority,
          dueDate: successorTask.dueDate,
          completedAt: successorTask.completedAt,
        },
      })
      .from(taskDependenciesTable)
      .innerJoin(
        predecessorTask,
        eq(taskDependenciesTable.predecessorTaskId, predecessorTask.id)
      )
      .innerJoin(
        successorTask,
        eq(taskDependenciesTable.successorTaskId, successorTask.id)
      )
      .where(
        or(
          eq(taskDependenciesTable.predecessorTaskId, taskId),
          eq(taskDependenciesTable.successorTaskId, taskId)
        )
      )
      .orderBy(desc(taskDependenciesTable.createdAt));
  }

  async getUrgentTasks(
    userId?: string,
    limit = 10
  ): Promise<TaskWithRelations[]> {
    const { assignee, assigner } = this.getAliases();
    const fields = this.getTaskSelectFields(assignee, assigner);

    const conditions = [
      eq(tasksTable.priority, TaskPriorityEnum.URGENT),
      inArray(tasksTable.status, [
        TaskStatusEnum.PENDING,
        TaskStatusEnum.IN_PROGRESS,
      ]),
    ];

    if (userId) {
      conditions.push(eq(tasksTable.assignedToId, userId));
    }

    const query = this.tx
      .select(fields)
      .from(tasksTable)
      .innerJoin(assignee, eq(tasksTable.assignedToId, assignee.id))
      .innerJoin(assigner, eq(tasksTable.assignedById, assigner.id))
      .leftJoin(districtsTable, eq(tasksTable.districtId, districtsTable.id))
      .leftJoin(schoolsTable, eq(tasksTable.schoolId, schoolsTable.id))
      .leftJoin(casesTable, eq(tasksTable.caseId, casesTable.id))
      .leftJoin(studentsTable, eq(tasksTable.studentId, studentsTable.id))
      .where(and(...conditions));

    return await query
      .orderBy(asc(tasksTable.dueDate), desc(tasksTable.createdAt))
      .limit(limit);
  }

  async getOverdueTasks(
    _userId?: string,
    limit = 10
  ): Promise<TaskWithRelations[]> {
    const { assignee, assigner } = this.getAliases();
    const fields = this.getTaskSelectFields(assignee, assigner);
    const now = new Date();

    const query = this.tx
      .select(fields)
      .from(tasksTable)
      .innerJoin(assignee, eq(tasksTable.assignedToId, assignee.id))
      .innerJoin(assigner, eq(tasksTable.assignedById, assigner.id))
      .leftJoin(districtsTable, eq(tasksTable.districtId, districtsTable.id))
      .leftJoin(schoolsTable, eq(tasksTable.schoolId, schoolsTable.id))
      .leftJoin(casesTable, eq(tasksTable.caseId, casesTable.id))
      .leftJoin(studentsTable, eq(tasksTable.studentId, studentsTable.id))
      .where(
        and(
          lte(tasksTable.dueDate, now),
          inArray(tasksTable.status, [
            TaskStatusEnum.PENDING,
            TaskStatusEnum.IN_PROGRESS,
          ])
        )
      );

    // if (userId) {
    //   query = query as PgSelectBase<
    //     typeof tasksTable,
    //     typeof fields,
    //     typeof aliases
    //   >;
    //   query = query.where(eq(tasksTable.assignedToId, userId));
    // }

    return await query.orderBy(asc(tasksTable.dueDate)).limit(limit);
  }

  async createTask(task: NewTask, dependedOnTaskId?: string): Promise<string> {
    const [newTask] = await this.tx
      .insert(tasksTable)
      .values(task)
      .returning({ id: tasksTable.id });

    if (!newTask) {
      throw new Error('FAILED TO CREATE TASK');
    }

    if (dependedOnTaskId) {
      await this.tx.insert(taskDependenciesTable).values({
        predecessorTaskId: dependedOnTaskId,
        successorTaskId: newTask.id,
      });
    }

    return newTask.id;
  }

  async createTasksBatch(tasks: NewTask[]): Promise<string[]> {
    if (tasks.length === 0) {
      return [];
    }

    const newTasks = await this.tx
      .insert(tasksTable)
      .values(tasks)
      .returning({ id: tasksTable.id });

    if (!newTasks || newTasks.length === 0) {
      throw new Error('FAILED TO CREATE TASKS');
    }

    return newTasks.map((task) => task.id);
  }

  async createTaskDependency(
    predecessorTaskId: string,
    successorTaskId: string
  ): Promise<string> {
    const [dependency] = await this.tx
      .insert(taskDependenciesTable)
      .values({
        predecessorTaskId,
        successorTaskId,
      })
      .returning({ id: taskDependenciesTable.id });

    if (!dependency) {
      throw new Error('FAILED TO CREATE TASK DEPENDENCY');
    }

    return dependency.id;
  }

  async updateTaskStatus(
    taskId: string,
    status: TaskStatusEnum,
    userId: string,
    notes?: string
  ): Promise<void> {
    // Get current task to log the change
    const [currentTask] = await this.tx
      .select({ status: tasksTable.status })
      .from(tasksTable)
      .where(eq(tasksTable.id, taskId))
      .limit(1);

    if (!currentTask) {
      throw new Error('TASK NOT FOUND');
    }

    // Log the status change
    await this.tx.insert(taskHistoryTable).values({
      taskId,
      userId,
      action: TaskHistoryActionEnum.STATUS_CHANGED,
      previousStatus: currentTask.status,
      newStatus: status,
    });

    // Update the task
    const updateData: {
      status: TaskStatusEnum;
      updatedAt: Date;
      notes?: string;
      completedAt?: Date | null;
      cancelledAt?: Date | null;
      rejectedAt?: Date | null;
    } = {
      status,
      updatedAt: new Date(),
    };

    if (notes) {
      updateData.notes = notes;
    }

    if (status === TaskStatusEnum.COMPLETED) {
      updateData.completedAt = new Date();
    }

    if (status === TaskStatusEnum.CANCELLED) {
      updateData.cancelledAt = new Date();
    }

    if (status === TaskStatusEnum.REJECTED) {
      updateData.rejectedAt = new Date();
    }

    await this.tx
      .update(tasksTable)
      .set(updateData)
      .where(eq(tasksTable.id, taskId));
  }

  async updateTaskPriority(
    taskId: string,
    priority: TaskPriorityEnum,
    userId: string,
    notes?: string
  ): Promise<void> {
    // Log the priority change
    await this.tx.insert(taskHistoryTable).values({
      taskId,
      userId,
      action: TaskHistoryActionEnum.PRIORITY_CHANGED,
    });

    // Update the task
    const updateData: {
      priority: TaskPriorityEnum;
      updatedAt: Date;
      notes?: string;
    } = {
      priority,
      updatedAt: new Date(),
    };

    if (notes) {
      updateData.notes = notes;
    }

    await this.tx
      .update(tasksTable)
      .set(updateData)
      .where(eq(tasksTable.id, taskId));
  }

  async updateTaskDueDate(
    taskId: string,
    dueDate: Date | null,
    userId: string,
    notes?: string
  ): Promise<void> {
    // Log the due date change
    await this.tx.insert(taskHistoryTable).values({
      taskId,
      userId,
      action: TaskHistoryActionEnum.DUE_DATE_CHANGED,
    });

    // Update the task
    const updateData: {
      dueDate: Date | null;
      updatedAt: Date;
      notes?: string;
    } = {
      dueDate,
      updatedAt: new Date(),
    };

    if (notes) {
      updateData.notes = notes;
    }

    await this.tx
      .update(tasksTable)
      .set(updateData)
      .where(eq(tasksTable.id, taskId));
  }

  async logTaskReassigned(params: {
    taskId: string;
    reassignedTo: string;
    reassignedBy: string;
    notes?: string;
  }): Promise<void> {
    await this.tx.insert(taskHistoryTable).values({
      taskId: params.taskId,
      userId: params.reassignedBy,
      action: TaskHistoryActionEnum.REASSIGNED,
      previousStatus: TaskStatusEnum.PENDING,
      newStatus: TaskStatusEnum.PENDING,
    });

    const [previousTask] = await this.tx
      .select()
      .from(tasksTable)
      .where(eq(tasksTable.id, params.taskId));

    if (!previousTask) {
      throw new Error('TASK NOT FOUND');
    }

    await this.createTask({
      assignedToId: params.reassignedTo,
      assignedById: params.reassignedBy,
      notes: params.notes,
      taskType: previousTask.taskType,
      caseId: previousTask.caseId,
      studentId: previousTask.studentId,
      schoolId: previousTask.schoolId,
      districtId: previousTask.districtId,
      dueDate: previousTask.dueDate,
      priority: previousTask.priority,
      status: previousTask.status,
      metadata: previousTask.metadata,
    });
  }

  async logTaskCompleted(taskId: string, completedBy: string): Promise<void> {
    await this.tx.insert(taskHistoryTable).values({
      taskId,
      userId: completedBy,
      action: TaskHistoryActionEnum.COMPLETED,
      previousStatus: TaskStatusEnum.IN_PROGRESS,
      newStatus: TaskStatusEnum.COMPLETED,
    });

    await this.tx
      .update(tasksTable)
      .set({
        status: TaskStatusEnum.COMPLETED,
        completedAt: new Date(),
      })
      .where(eq(tasksTable.id, taskId));
  }

  async logTaskRejected(
    taskId: string,
    rejectedBy: string,
    rejectionReason: string
  ): Promise<void> {
    await this.tx.insert(taskHistoryTable).values({
      taskId,
      userId: rejectedBy,
      action: TaskHistoryActionEnum.REJECTED,
      previousStatus: TaskStatusEnum.PENDING,
      newStatus: TaskStatusEnum.REJECTED,
    });

    await this.tx
      .update(tasksTable)
      .set({
        reason: rejectionReason,
        status: TaskStatusEnum.REJECTED,
        rejectedAt: new Date(),
      })
      .where(eq(tasksTable.id, taskId));
  }

  async canStartTask(taskId: string): Promise<boolean> {
    const dependencies = await this.tx
      .select({
        id: taskDependenciesTable.id,
        status: tasksTable.status,
      })
      .from(taskDependenciesTable)
      .innerJoin(
        tasksTable,
        eq(taskDependenciesTable.predecessorTaskId, tasksTable.id)
      )
      .where(eq(taskDependenciesTable.successorTaskId, taskId));

    return dependencies.every((dep) => dep.status === TaskStatusEnum.COMPLETED);
  }

  async getBlockingTasks(taskId: string): Promise<TaskWithRelations[]> {
    const { assignee, assigner } = this.getAliases();
    const fields = this.getTaskSelectFields(assignee, assigner);

    return await this.tx
      .select(fields)
      .from(taskDependenciesTable)
      .innerJoin(
        tasksTable,
        eq(taskDependenciesTable.predecessorTaskId, tasksTable.id)
      )
      .innerJoin(assignee, eq(tasksTable.assignedToId, assignee.id))
      .innerJoin(assigner, eq(tasksTable.assignedById, assigner.id))
      .leftJoin(districtsTable, eq(tasksTable.districtId, districtsTable.id))
      .leftJoin(schoolsTable, eq(tasksTable.schoolId, schoolsTable.id))
      .leftJoin(casesTable, eq(tasksTable.caseId, casesTable.id))
      .leftJoin(studentsTable, eq(tasksTable.studentId, studentsTable.id))
      .where(
        and(
          eq(taskDependenciesTable.successorTaskId, taskId),
          inArray(tasksTable.status, [
            TaskStatusEnum.PENDING,
            TaskStatusEnum.IN_PROGRESS,
            TaskStatusEnum.BLOCKED,
          ])
        )
      );
  }

  async validateTaskDependency(
    predecessorTaskId: string,
    successorTaskId: string
  ): Promise<{ isValid: boolean; reason?: string }> {
    // Check if tasks exist
    const [predecessorTask, successorTask] = await Promise.all([
      this.tx
        .select()
        .from(tasksTable)
        .where(eq(tasksTable.id, predecessorTaskId))
        .limit(1),
      this.tx
        .select()
        .from(tasksTable)
        .where(eq(tasksTable.id, successorTaskId))
        .limit(1),
    ]);

    if (!predecessorTask[0]) {
      return { isValid: false, reason: 'Predecessor task not found' };
    }

    if (!successorTask[0]) {
      return { isValid: false, reason: 'Successor task not found' };
    }

    // Check for circular dependencies
    const circularCheck = await this.checkCircularDependency(
      predecessorTaskId,
      successorTaskId
    );
    if (!circularCheck.isValid) {
      return circularCheck;
    }

    // Check if dependency already exists
    const existingDependency = await this.tx
      .select()
      .from(taskDependenciesTable)
      .where(
        and(
          eq(taskDependenciesTable.predecessorTaskId, predecessorTaskId),
          eq(taskDependenciesTable.successorTaskId, successorTaskId)
        )
      )
      .limit(1);

    if (existingDependency[0]) {
      return { isValid: false, reason: 'Dependency already exists' };
    }

    return { isValid: true };
  }

  async checkCircularDependency(
    predecessorTaskId: string,
    successorTaskId: string
  ): Promise<{ isValid: boolean; reason?: string }> {
    // Check if adding this dependency would create a circular reference
    // Get all dependencies in one query to avoid await in loop
    const allDependencies = await this.tx
      .select({
        predecessorTaskId: taskDependenciesTable.predecessorTaskId,
        successorTaskId: taskDependenciesTable.successorTaskId,
      })
      .from(taskDependenciesTable);

    // Build adjacency map
    const dependencyMap = new Map<string, string[]>();
    for (const dep of allDependencies) {
      if (!dependencyMap.has(dep.successorTaskId)) {
        dependencyMap.set(dep.successorTaskId, []);
      }
      dependencyMap.get(dep.successorTaskId)?.push(dep.predecessorTaskId);
    }

    // Check for circular dependency using DFS
    const visitedTasks = new Set<string>();
    const stack = [successorTaskId];

    while (stack.length > 0) {
      const currentTaskId = stack.pop();

      if (!currentTaskId || visitedTasks.has(currentTaskId)) {
        continue;
      }

      visitedTasks.add(currentTaskId);

      if (currentTaskId === predecessorTaskId) {
        return { isValid: false, reason: 'Would create circular dependency' };
      }

      // Add predecessors to stack
      const predecessors = dependencyMap.get(currentTaskId) || [];
      for (const pred of predecessors) {
        if (!visitedTasks.has(pred)) {
          stack.push(pred);
        }
      }
    }

    return { isValid: true };
  }

  async getActiveMonitorRatingScalesTasks(): Promise<
    ActiveMonitorRatingScalesTask[]
  > {
    return await this.tx
      .select({
        taskId: tasksTable.id,
        userId: usersTable.id,
        email: usersTable.email,
        fullName: usersTable.fullName,
        caseId: tasksTable.caseId,
        createdAt: tasksTable.createdAt,
        dueDate: tasksTable.dueDate,
      })
      .from(tasksTable)
      .innerJoin(usersTable, eq(tasksTable.assignedToId, usersTable.id))
      .innerJoin(
        userRolesTable,
        and(
          eq(userRolesTable.userId, usersTable.id),
          eq(userRolesTable.roleName, RoleEnum.ASSISTANT)
        )
      )
      .where(
        and(
          eq(tasksTable.taskType, TaskTypeEnum.MONITOR_RATING_SCALES),
          inArray(tasksTable.status, [
            TaskStatusEnum.PENDING,
            TaskStatusEnum.IN_PROGRESS,
          ])
        )
      );
  }
}
