import { and, eq, inArray, lt, sql } from 'drizzle-orm';
import type { TransactionType } from '../config/client';
import { availabilitiesTable, tasksTable, usersTable } from '../schema';
import { TaskStatusEnum, TaskTypeEnum } from '../schema/enums';
import type { StaleAvailabilityUser } from './types/user-availabilities';

export class UserAvailabilitiesRepository {
  private readonly tx: TransactionType;

  constructor(tx: TransactionType) {
    this.tx = tx;
  }

  async findUsersWithStaleAvailabilities(
    daysThreshold = 10
  ): Promise<StaleAvailabilityUser[]> {
    const thresholdDate = new Date();
    thresholdDate.setDate(thresholdDate.getDate() - daysThreshold);

    return await this.tx
      .select({
        userId: usersTable.id,
        fullName: usersTable.fullName,
        email: usersTable.email,
        lastUpdated: sql<Date>`MAX(${availabilitiesTable.updatedAt})`.as(
          'lastUpdated'
        ),
        daysSinceUpdate:
          sql<number>`EXTRACT(DAY FROM (NOW() - MAX(${availabilitiesTable.updatedAt})))`.as(
            'daysSinceUpdate'
          ),
      })
      .from(availabilitiesTable)
      .innerJoin(usersTable, eq(availabilitiesTable.userId, usersTable.id))
      .groupBy(usersTable.id, usersTable.fullName, usersTable.email)
      .having(lt(sql`MAX(${availabilitiesTable.updatedAt})`, thresholdDate))
      .orderBy(sql`MAX(${availabilitiesTable.updatedAt})`);
  }

  async findUsersWithStaleAvailabilitiesWithoutExistingTasks(
    daysThreshold = 10
  ): Promise<StaleAvailabilityUser[]> {
    const thresholdDate = new Date();
    thresholdDate.setDate(thresholdDate.getDate() - daysThreshold);

    return await this.tx
      .select({
        userId: usersTable.id,
        fullName: usersTable.fullName,
        email: usersTable.email,
        lastUpdated: sql<Date>`MAX(${availabilitiesTable.updatedAt})`.as(
          'lastUpdated'
        ),
        daysSinceUpdate:
          sql<number>`EXTRACT(DAY FROM (NOW() - MAX(${availabilitiesTable.updatedAt})))`.as(
            'daysSinceUpdate'
          ),
      })
      .from(availabilitiesTable)
      .innerJoin(usersTable, eq(availabilitiesTable.userId, usersTable.id))
      .leftJoin(
        tasksTable,
        and(
          eq(tasksTable.assignedToId, usersTable.id),
          eq(tasksTable.taskType, TaskTypeEnum.UPDATE_AVAILABILITY),
          inArray(tasksTable.status, [
            TaskStatusEnum.PENDING,
            TaskStatusEnum.IN_PROGRESS,
          ])
        )
      )
      .groupBy(usersTable.id, usersTable.fullName, usersTable.email)
      .having(
        and(
          lt(sql`MAX(${availabilitiesTable.updatedAt})`, thresholdDate),
          sql`COUNT(${tasksTable.id}) = 0`
        )
      )
      .orderBy(sql`MAX(${availabilitiesTable.updatedAt})`);
  }

  async getStaleAvailabilitiesCount(daysThreshold = 10): Promise<number> {
    const thresholdDate = new Date();
    thresholdDate.setDate(thresholdDate.getDate() - daysThreshold);

    const [result] = await this.tx
      .select({
        count: sql<number>`COUNT(DISTINCT ${usersTable.id})`.as('count'),
      })
      .from(availabilitiesTable)
      .innerJoin(usersTable, eq(availabilitiesTable.userId, usersTable.id))
      .where(
        lt(
          sql`(SELECT MAX(updated_at) FROM ${availabilitiesTable} WHERE user_id = ${usersTable.id})`,
          thresholdDate
        )
      );

    return result?.count || 0;
  }
}
