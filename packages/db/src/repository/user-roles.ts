import type { TransactionType } from '../config/client';
import { userRolesTable } from '../schema';
import type { RoleEnum } from '../schema/enums';

export class UserRolesRepository {
  private tx: TransactionType;
  constructor(tx: TransactionType) {
    this.tx = tx;
  }

  async createAssociation(userId: string, roleId: string, roleName: RoleEnum) {
    const [association] = await this.tx
      .insert(userRolesTable)
      .values({
        userId,
        roleId,
        roleName,
      })
      .returning();

    return association;
  }
}
