import type {
  Case,
  District,
  School,
  Student,
  Task,
  User,
} from '../../schema/types';

export interface GetTasksParams<F = TaskFilterField, S = TaskSortField> {
  page?: number;
  perPage?: number;
  search?: string;
  filters?: Array<{
    id: F;
    value: string | string[];
    variant: FilterVariants;
    operator: FilterOperators;
    filterId: string;
  }>;
  joinOperator?: 'and' | 'or';
  sort?: Array<{
    id: S;
    desc: boolean;
  }>;
}

export type TaskWithRelations = Omit<
  Task,
  | 'assignedById'
  | 'assignedToId'
  | 'caseId'
  | 'studentId'
  | 'schoolId'
  | 'districtId'
> & {
  district: Pick<District, 'id' | 'name'> | null;
  assignedTo: Pick<User, 'id' | 'avatar' | 'fullName' | 'email'>;
  assignedBy: Pick<User, 'id' | 'avatar' | 'fullName' | 'email'>;
  case: Pick<Case, 'id' | 'studentId'> | null;
  student: Pick<Student, 'id' | 'fullName' | 'studentIdNumber'> | null;
  school: Pick<School, 'id' | 'name'> | null;
};

export type TaskSortField =
  | 'dueDate'
  | 'priority'
  | 'createdAt'
  | 'completedAt'
  | 'rejectedAt'
  | 'cancelledAt';

export type TaskFilterField =
  | 'assignedTo'
  | 'assignedBy'
  | 'dueDate'
  | 'status'
  | 'priority'
  | 'taskType'
  | 'caseId'
  | 'studentName'
  | 'studentIdNumber'
  | 'districtName'
  | 'schoolName';

export type ActiveMonitorRatingScalesTask = {
  taskId: string;
  userId: string;
  email: string;
  fullName: string;
  caseId: string | null;
  createdAt: Date;
  dueDate: Date | null;
};

type FilterVariants =
  | 'number'
  | 'boolean'
  | 'text'
  | 'range'
  | 'date'
  | 'dateRange'
  | 'select'
  | 'multiSelect';

type FilterOperators =
  | 'iLike'
  | 'notILike'
  | 'eq'
  | 'ne'
  | 'isEmpty'
  | 'isNotEmpty'
  | 'lt'
  | 'lte'
  | 'gt'
  | 'gte'
  | 'isBetween'
  | 'isRelativeToToday'
  | 'inArray'
  | 'notInArray';
