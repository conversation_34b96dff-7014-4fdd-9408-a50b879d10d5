import type { District } from '@lilypad/db/schema';

export interface GetDistrictsParams {
  page?: number;
  perPage?: number;
  search?: string;
  filters?: Array<{
    id: string;
    value: unknown;
    isMulti?: boolean;
  }>;
  joinOperator?: 'and' | 'or';
  sort?: Array<{
    id: string;
    desc: boolean;
  }>;
}

export interface GetDistrictsResult {
  data: DistrictWithAddress[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface DistrictWithAddress extends District {
  address: string;
  city: string;
  state: string;
}

export type DistrictSortField =
  | 'name'
  | 'type'
  | 'county'
  | 'state'
  | 'city'
  | 'stateId';

export type DistrictFilterField =
  | 'name'
  | 'type'
  | 'county'
  | 'state'
  | 'city'
  | 'ncesId'
  | 'stateId';
