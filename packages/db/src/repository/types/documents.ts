import type { DocumentCategoryEnum } from '../../schema/enums';
import type { Document, User } from '../../schema/types';

export interface DocumentWithUser extends Document {
  uploadedUser: Pick<
    User,
    'id' | 'firstName' | 'lastName' | 'fullName' | 'email' | 'avatar'
  > & {
    userRoles: {
      role: {
        name: string;
      };
    }[];
  };
}

export interface DocumentTreeItem {
  type: 'folder' | 'document';
  id: string;
  name: string;
  children?: DocumentTreeItem[];
  // For folders
  parentId?: string | null;
  category?: DocumentCategoryEnum;
  isSystemFolder?: boolean;
  documentsCount?: number;
  // For documents
  document?: DocumentWithUser;
  folderId?: string | null;
}

export interface DocumentTreeNode {
  id: string;
  name: string;
  type: 'folder' | 'document';
  parentId?: string | null;
  children?: DocumentTreeNode[];
  // Folder specific
  category?: DocumentCategoryEnum;
  isSystemFolder?: boolean;
  documentsCount?: number;
  isExpanded?: boolean;
  // Document specific
  document?: DocumentWithUser;
  createdAt?: Date;
}
