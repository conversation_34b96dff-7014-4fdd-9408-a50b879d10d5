import type {
  AssessmentSession,
  Case,
  Document,
  IndexScore,
  IndexSubtestMapping,
  Subtest,
  SubtestScore,
  TestAdministration,
  TestBattery,
  TestIndex,
  User,
} from '../../schema';

// Simplified assessment session for lists/summaries
export type AssessmentSessionSummary = Pick<
  AssessmentSession,
  | 'id'
  | 'sessionDate'
  | 'sessionDuration'
  | 'sessionType'
  | 'location'
  | 'sessionStatus'
  | 'createdAt'
  | 'updatedAt'
> & {
  psychologist: Pick<
    User,
    'id' | 'firstName' | 'lastName' | 'fullName' | 'email' | 'avatar'
  > & {
    userRoles: {
      role: {
        name: string;
      };
    }[];
  };
  case: Pick<
    Case,
    | 'id'
    | 'status'
    | 'priority'
    | 'caseType'
    | 'iepStatus'
    | 'evaluationDueDate'
  > | null;
  testAdministrations: Array<
    Pick<TestAdministration, 'id' | 'administrationOrder' | 'adminStatus'> & {
      battery: Pick<TestBattery, 'id' | 'name' | 'code' | 'category'>;
    }
  >;
};

// Complete assessment session with all related data
export type AssessmentSessionWithFullDetails = AssessmentSession & {
  psychologist: Pick<
    User,
    'id' | 'firstName' | 'lastName' | 'fullName' | 'email' | 'avatar'
  > & {
    userRoles: {
      role: {
        name: string;
      };
    }[];
  };
  case?: Pick<
    Case,
    | 'id'
    | 'status'
    | 'priority'
    | 'caseType'
    | 'iepStatus'
    | 'iepStartDate'
    | 'iepEndDate'
    | 'referralDate'
    | 'evaluationDueDate'
  > | null;
  testAdministrations: TestAdministrationWithScores[];
  documents: Array<
    Document & {
      uploadedUser: Pick<
        User,
        'id' | 'firstName' | 'lastName' | 'fullName' | 'email'
      >;
    }
  >;
};

// Test administration with complete scoring data
export type TestAdministrationWithScores = TestAdministration & {
  battery: TestBatteryWithStructure;
  subtestScores: SubtestScoreWithSubtest[];
  indexScores: IndexScoreWithIndex[];
};

// Test battery with complete structure
export type TestBatteryWithStructure = TestBattery & {
  testIndices: Array<
    TestIndex & {
      indexSubtestMappings: IndexSubtestMappingWithSubtest[];
    }
  >;
  subtests: Subtest[];
};

// Index subtest mapping with subtest details
export type IndexSubtestMappingWithSubtest = IndexSubtestMapping & {
  subtest: Subtest;
};

// Subtest score with subtest details
export type SubtestScoreWithSubtest = SubtestScore & {
  subtest: Subtest;
};

// Index score with index details
export type IndexScoreWithIndex = IndexScore & {
  index: TestIndex;
};

// Test battery for selection/configuration interfaces
export type TestBatteryForSelection = TestBattery & {
  testIndices: TestIndex[];
  subtests: Subtest[];
};

// Score analysis helper types
export type ScoreInterpretation = {
  level:
    | 'Very Low'
    | 'Low'
    | 'Low Average'
    | 'Average'
    | 'High Average'
    | 'High'
    | 'Very High';
  percentile: number;
  strengthsWeaknesses: 'strength' | 'weakness' | 'average';
};

export type SubtestAnalysis = {
  subtest: Subtest;
  score: SubtestScore;
  interpretation: ScoreInterpretation & {
    ageEquivalent?: string;
    gradeEquivalent?: string;
  };
};

export type IndexAnalysis = {
  index: TestIndex;
  score: IndexScore;
  componentSubtests: SubtestScoreWithSubtest[];
  interpretation: ScoreInterpretation & {
    confidenceInterval: {
      lower: number;
      upper: number;
      level: number;
    };
  };
};

// Assessment report data structure
export type BatteryAnalysis = {
  battery: TestBattery;
  administration: TestAdministration;
  indexAnalyses: IndexAnalysis[];
  subtestAnalyses: SubtestAnalysis[];
  overallInterpretation: {
    cognitiveProfile: string;
    strengthAreas: string[];
    weaknessAreas: string[];
    recommendations: string[];
  };
};

export type AssessmentReport = {
  session: AssessmentSessionWithFullDetails;
  batteryAnalyses: BatteryAnalysis[];
  sessionSummary: {
    totalDuration: number;
    accommodationsUsed: string[];
    validityConcerns: string[];
    behavioralObservations: string;
    testingConditions: string;
  };
};
