import type { Case, Plan } from '../../schema/types';

/**
 * Core Plan type without soft deletion fields
 * Used for active plans display and operations
 */
type PlanCore = Omit<
  Plan,
  'studentId' | 'caseId' | 'isDeleted' | 'deletedAt' | 'deletedBy'
>;

/**
 * Plan with basic case information
 * Used when displaying plan context within case management
 */
export type PlanWithCase = PlanCore & {
  case: Pick<
    Case,
    | 'id'
    | 'status'
    | 'priority'
    | 'caseType'
    | 'isActive'
    | 'iepStatus'
    | 'iepStartDate'
    | 'iepEndDate'
    | 'referralDate'
    | 'evaluationDueDate'
    | 'meetingDate'
  >;
};
