import type { RoleEnum, TaskTypeEnum } from '../../schema/enums';
import type {
  Case,
  CaseAssignment,
  CaseDetail,
  User,
} from '../../schema/types';

export type CaseWithAssignmentsAndDetails = Omit<
  Case,
  'isDeleted' | 'deletedAt' | 'deletedBy'
> & {
  caseAssignments: (Omit<
    CaseAssignment,
    'isDeleted' | 'deletedAt' | 'deletedBy'
  > & {
    user: Pick<
      User,
      'id' | 'firstName' | 'lastName' | 'fullName' | 'email' | 'avatar'
    > & {
      userRoles: {
        role: {
          name: string;
        };
      }[];
    };
  })[];
  caseDetails: Omit<
    CaseDetail,
    'isDeleted' | 'deletedAt' | 'deletedBy' | 'createdBy' | 'updatedBy'
  >[];
};

export type CaseForUpcomingEvaluations = {
  caseId: string;
  userId: string;
  email: string;
  fullName: string;
  evaluationDueDate: string;
  roleName: RoleEnum;
};

export type CaseForPreparationTasks = {
  caseId: string;
  userId: string;
  email: string;
  fullName: string;
  evaluationDueDate: string;
};

export type CaseForJoinEvaluationTasks = {
  caseId: string;
  userId: string;
  email: string;
  fullName: string;
  evaluationDueDate: string;
  roleName: RoleEnum;
  taskType: TaskTypeEnum;
};

export type CaseForIEPMeetingTasks = {
  caseId: string;
  userId: string;
  email: string;
  fullName: string;
  meetingDate: string;
};
