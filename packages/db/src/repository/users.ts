import { eq } from 'drizzle-orm';
import type { TransactionType } from '../config/client';
import { usersTable } from '../schema';

export class UsersRepository {
  private tx: TransactionType;
  constructor(tx: TransactionType) {
    this.tx = tx;
  }

  async getUserInfo(
    userId: string
  ): Promise<{ fullName: string; email: string } | null> {
    const [user] = await this.tx
      .select({
        fullName: usersTable.fullName,
        email: usersTable.email,
      })
      .from(usersTable)
      .where(eq(usersTable.id, userId))
      .limit(1);

    return user || null;
  }

  async getUserIdByEmail(email: string): Promise<string> {
    const [user] = await this.tx
      .select({ id: usersTable.id })
      .from(usersTable)
      .where(eq(usersTable.email, email))
      .limit(1);

    if (!user) {
      throw new Error('USER NOT FOUND');
    }

    return user.id;
  }
}
