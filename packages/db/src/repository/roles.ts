import type { TransactionType } from '../config/client';
import { rolesTable } from '../schema';
import type { RoleEnum } from '../schema/enums';

export class RolesRepository {
  private tx: TransactionType;

  constructor(tx: TransactionType) {
    this.tx = tx;
  }

  async getAllRoles() {
    return await this.tx.select().from(rolesTable);
  }

  async getRoleMapping(): Promise<Map<RoleEnum, string>> {
    const roles = await this.getAllRoles();
    return new Map(roles.map((r) => [r.name, r.id]));
  }
}
