import { getSupabaseServerClient } from '@lilypad/supabase/server';
import { and, eq, inArray, like } from 'drizzle-orm';
import {
  type DatabaseType,
  type TransactionType,
  createDatabaseClient,
} from '../config/client';
import {
  RoleEnum,
  permissionsTable,
  rolePermissionsTable,
  rolesTable,
  userRolesTable,
} from '../schema';

//<editor-fold desc="Types">
export interface UserRoleWithName {
  id: string;
  name: RoleEnum;
  userId: string;
}

export interface UserPermission {
  id: string;
  name: string;
  roleId: string;
  roleName: RoleEnum;
}

export interface UserRolePermissions {
  userId: string;
  roles: UserRoleWithName[];
  permissions: UserPermission[];
}

interface BaseUserPermissionParams {
  db: DatabaseType | TransactionType;
  userId: string;
}

export interface AuthenticatedUser {
  id: string;
  email: string;
  rolePermissions: UserRolePermissions;
}

interface UserHasRoleParams extends BaseUserPermissionParams {
  role: RoleEnum | RoleEnum[];
}

interface UserHasPermissionParams extends BaseUserPermissionParams {
  permission: string;
}

interface UserHasPermsParams extends BaseUserPermissionParams {
  permissions: string[];
}

interface UserCanPerformActionParams extends BaseUserPermissionParams {
  resource: string;
  action: string;
}

interface RoleToUserParams extends BaseUserPermissionParams {
  roleId: string;
}
//</editor-fold>

//<editor-fold desc="Permission and Role Checks">
export async function getUserRolesAndPermissions(
  db: DatabaseType | TransactionType,
  userId: string
): Promise<UserRolePermissions> {
  const result = await db.transaction(async (tx) => {
    return await tx
      .select({
        roleId: rolesTable.id,
        roleName: rolesTable.name,
        permissionId: permissionsTable.id,
        permissionName: permissionsTable.name,
      })
      .from(userRolesTable)
      .innerJoin(rolesTable, eq(userRolesTable.roleId, rolesTable.id))
      .innerJoin(
        rolePermissionsTable,
        eq(rolesTable.id, rolePermissionsTable.roleId)
      )
      .innerJoin(
        permissionsTable,
        eq(rolePermissionsTable.permissionId, permissionsTable.id)
      )
      .where(eq(userRolesTable.userId, userId));
  });

  const rolesMap = new Map<string, UserRoleWithName>();
  const permissions: UserPermission[] = [];

  for (const row of result) {
    // Add role if not already present
    if (!rolesMap.has(row.roleId)) {
      rolesMap.set(row.roleId, {
        id: row.roleId,
        name: row.roleName as RoleEnum,
        userId,
      });
    }

    permissions.push({
      id: row.permissionId,
      name: row.permissionName,
      roleId: row.roleId,
      roleName: row.roleName as RoleEnum,
    });
  }

  return {
    userId,
    roles: Array.from(rolesMap.values()),
    permissions,
  };
}

export async function userHasRole(params: UserHasRoleParams): Promise<boolean> {
  const roles = Array.isArray(params.role) ? params.role : [params.role];

  const result = await params.db.transaction(async (tx) => {
    return await tx
      .select({ count: rolesTable.id })
      .from(userRolesTable)
      .innerJoin(rolesTable, eq(userRolesTable.roleId, rolesTable.id))
      .where(
        and(
          eq(userRolesTable.userId, params.userId),
          inArray(rolesTable.name, roles)
        )
      )
      .limit(1);
  });

  return result.length > 0;
}

export async function userHasPermission(
  params: UserHasPermissionParams
): Promise<boolean> {
  const result = await params.db.transaction(async (tx) => {
    return await tx
      .select({ count: permissionsTable.id })
      .from(userRolesTable)
      .innerJoin(rolesTable, eq(userRolesTable.roleId, rolesTable.id))
      .innerJoin(
        rolePermissionsTable,
        eq(rolesTable.id, rolePermissionsTable.roleId)
      )
      .innerJoin(
        permissionsTable,
        eq(rolePermissionsTable.permissionId, permissionsTable.id)
      )
      .where(
        and(
          eq(userRolesTable.userId, params.userId),
          eq(permissionsTable.name, params.permission)
        )
      )
      .limit(1);
  });

  return result.length > 0;
}

export async function userHasAnyPermission(
  params: UserHasPermsParams
): Promise<boolean> {
  const result = await params.db.transaction(async (tx) => {
    return await tx
      .select({ count: permissionsTable.id })
      .from(userRolesTable)
      .innerJoin(rolesTable, eq(userRolesTable.roleId, rolesTable.id))
      .innerJoin(
        rolePermissionsTable,
        eq(rolesTable.id, rolePermissionsTable.roleId)
      )
      .innerJoin(
        permissionsTable,
        eq(rolePermissionsTable.permissionId, permissionsTable.id)
      )
      .where(
        and(
          eq(userRolesTable.userId, params.userId),
          inArray(permissionsTable.name, params.permissions)
        )
      )
      .limit(1);
  });
  return result.length > 0;
}

export async function userHasAllPermissions(
  params: UserHasPermsParams
): Promise<boolean> {
  const result = await params.db.transaction(async (tx) => {
    return await tx
      .select({ permissionName: permissionsTable.name })
      .from(userRolesTable)
      .innerJoin(rolesTable, eq(userRolesTable.roleId, rolesTable.id))
      .innerJoin(
        rolePermissionsTable,
        eq(rolesTable.id, rolePermissionsTable.roleId)
      )
      .innerJoin(
        permissionsTable,
        eq(rolePermissionsTable.permissionId, permissionsTable.id)
      )
      .where(
        and(
          eq(userRolesTable.userId, params.userId),
          inArray(permissionsTable.name, params.permissions)
        )
      );
  });
  const foundPermissions = new Set(result.map((r) => r.permissionName));
  return params.permissions.every((permission) =>
    foundPermissions.has(permission)
  );
}

export async function userCanPerformAction(
  params: UserCanPerformActionParams
): Promise<boolean> {
  const { resource, action, db, userId } = params;
  const permission = `${resource}:${action}`;
  return await userHasPermission({ db, userId, permission });
}

export async function userCanAccessResource(
  db: DatabaseType | TransactionType,
  userId: string,
  resource: string
): Promise<boolean> {
  const result = await db.transaction(async (tx) => {
    return await tx
      .select({ count: permissionsTable.id })
      .from(userRolesTable)
      .innerJoin(rolesTable, eq(userRolesTable.roleId, rolesTable.id))
      .innerJoin(
        rolePermissionsTable,
        eq(rolesTable.id, rolePermissionsTable.roleId)
      )
      .innerJoin(
        permissionsTable,
        eq(rolePermissionsTable.permissionId, permissionsTable.id)
      )
      .where(
        and(
          eq(userRolesTable.userId, userId),
          // Use SQL LIKE to match resource prefix
          like(permissionsTable.name, `${resource}:%`)
        )
      )
      .limit(1);
  });
  return result.length > 0;
}

export async function getUserResourcePermissions(
  db: DatabaseType | TransactionType,
  userId: string,
  resource: string
): Promise<string[]> {
  const result = await db.transaction(async (tx) => {
    return await tx
      .select({ permissionName: permissionsTable.name })
      .from(userRolesTable)
      .innerJoin(rolesTable, eq(userRolesTable.roleId, rolesTable.id))
      .innerJoin(
        rolePermissionsTable,
        eq(rolesTable.id, rolePermissionsTable.roleId)
      )
      .innerJoin(
        permissionsTable,
        eq(rolePermissionsTable.permissionId, permissionsTable.id)
      )
      .where(
        and(
          eq(userRolesTable.userId, userId),
          // Use SQL LIKE to match resource prefix
          like(permissionsTable.name, `${resource}:%`)
        )
      );
  });
  return result
    .map((r) => r.permissionName)
    .filter((name) => name.startsWith(`${resource}:`))
    .map((name) => name.split(':')[1]); // Extract just the action part
}

export async function userIsSuperUser(
  params: BaseUserPermissionParams
): Promise<boolean> {
  return await userHasRole({ ...params, role: RoleEnum.SUPER_USER });
}

export async function userHasRoleOrHigher(
  params: BaseUserPermissionParams & { minimumRole: RoleEnum }
): Promise<boolean> {
  const roleHierarchy: RoleEnum[] = [
    RoleEnum.SUPER_USER,
    RoleEnum.SPECIAL_ED_DIRECTOR,
    RoleEnum.CLINICAL_DIRECTOR,
    RoleEnum.SCHOOL_COORDINATOR,
    RoleEnum.SCHOOL_ADMIN,
    RoleEnum.CASE_MANAGER,
    RoleEnum.PSYCHOLOGIST,
    RoleEnum.PROCTOR,
    RoleEnum.ASSISTANT,
  ];

  const minimumIndex = roleHierarchy.indexOf(params.minimumRole);
  if (minimumIndex === -1) {
    return false;
  }

  const eligibleRoles = roleHierarchy.slice(0, minimumIndex + 1);
  return await userHasRole({ ...params, role: eligibleRoles });
}
//</editor-fold>

//<editor-fold desc="User and Role Management">
export async function assignRoleToUser(
  params: RoleToUserParams
): Promise<void> {
  await params.db.transaction(async (tx) => {
    // First get the role name
    const [role] = await tx
      .select({ name: rolesTable.name })
      .from(rolesTable)
      .where(eq(rolesTable.id, params.roleId))
      .limit(1);

    if (!role) {
      throw new Error(`Role with ID ${params.roleId} not found`);
    }

    return tx
      .insert(userRolesTable)
      .values({
        userId: params.userId,
        roleId: params.roleId,
        roleName: role.name,
      })
      .onConflictDoNothing();
  });
}

export async function removeRoleFromUser(
  params: RoleToUserParams
): Promise<void> {
  await params.db.transaction(async (tx) => {
    return await tx
      .delete(userRolesTable)
      .where(
        and(
          eq(userRolesTable.userId, params.userId),
          eq(userRolesTable.roleId, params.roleId)
        )
      );
  });
}

export async function getAllRoles(
  db: DatabaseType | TransactionType
): Promise<Array<{ id: string; name: RoleEnum }>> {
  return await db.transaction(async (tx) => {
    return await tx
      .select({
        id: rolesTable.id,
        name: rolesTable.name,
      })
      .from(rolesTable);
  });
}

export async function getAllPermissions(
  db: DatabaseType | TransactionType
): Promise<Array<{ id: string; name: string }>> {
  return await db.transaction(async (tx) => {
    return await tx
      .select({
        id: permissionsTable.id,
        name: permissionsTable.name,
      })
      .from(permissionsTable);
  });
}
//</editor-fold>

//<editor-fold desc="Auth Utilities and Cache">
const userPermissionsCache = new Map<
  string,
  {
    data: UserRolePermissions;
    timestamp: number;
    ttl: number;
  }
>();

const CACHE_TTL = 5 * 60 * 1000; // 5 minutes in milliseconds

// Get current authenticated user with roles and permissions
export async function getCurrentUserWithPermissions(): Promise<AuthenticatedUser | null> {
  const supabase = await getSupabaseServerClient();
  const {
    data: { user },
    error,
  } = await supabase.auth.getUser();

  if (error || !user) {
    return null;
  }

  const cacheKey = user.id;
  const cached = userPermissionsCache.get(cacheKey);

  if (cached && Date.now() - cached.timestamp < cached.ttl) {
    return {
      id: user.id,
      email: user.email ?? '',
      rolePermissions: cached.data,
    };
  }

  const db = await createDatabaseClient();
  const rolePermissions = await getUserRolesAndPermissions(db, user.id);

  userPermissionsCache.set(cacheKey, {
    data: rolePermissions,
    timestamp: Date.now(),
    ttl: CACHE_TTL,
  });

  return {
    id: user.id,
    email: user.email ?? '',
    rolePermissions,
  };
}

// Clear cache for a specific user (useful after role changes)
export function clearUserPermissionsCache(userId: string): void {
  userPermissionsCache.delete(userId);
}

// Clear all cached permissions (useful for system-wide role changes)
export function clearAllPermissionsCache(): void {
  userPermissionsCache.clear();
}
//</editor-fold>

//<editor-fold desc="Server-side Authorization (require___)">
export async function requireAuth(): Promise<AuthenticatedUser> {
  const user = await getCurrentUserWithPermissions();
  if (!user) {
    throw new Error('Authentication required');
  }
  return user;
}

export async function requireRole(
  role: RoleEnum | RoleEnum[]
): Promise<AuthenticatedUser> {
  const user = await requireAuth();
  const db = await createDatabaseClient();
  const hasRole = await userHasRole({
    db,
    userId: user.id,
    role,
  });

  if (!hasRole) {
    const roleNames = Array.isArray(role) ? role.join(', ') : role;
    throw new Error(`Access denied. Required role(s): ${roleNames}`);
  }

  return user;
}

export async function requirePermission(
  permission: string
): Promise<AuthenticatedUser> {
  const user = await requireAuth();
  const db = await createDatabaseClient();
  const hasPermission = await userHasPermission({
    db,
    userId: user.id,
    permission,
  });

  if (!hasPermission) {
    const permissionNames = Array.isArray(permission)
      ? permission.join(', ')
      : permission;
    throw new Error(
      `Access denied. Required permission(s): ${permissionNames}`
    );
  }

  return user;
}

export async function requireAnyPermission(
  permissions: string[]
): Promise<AuthenticatedUser> {
  const user = await requireAuth();
  const db = await createDatabaseClient();
  const hasAnyPermission = await userHasAnyPermission({
    db,
    userId: user.id,
    permissions,
  });

  if (!hasAnyPermission) {
    throw new Error(
      `Access denied. Required any of permissions: ${permissions.join(', ')}`
    );
  }

  return user;
}

export async function requireAllPermissions(
  permissions: string[]
): Promise<AuthenticatedUser> {
  const user = await requireAuth();
  const db = await createDatabaseClient();
  const hasAllPermissions = await userHasAllPermissions({
    db,
    userId: user.id,
    permissions,
  });

  if (!hasAllPermissions) {
    throw new Error(
      `Access denied. Required all permissions: ${permissions.join(', ')}`
    );
  }

  return user;
}

export async function requireResourceAction(
  resource: string,
  action: string
): Promise<AuthenticatedUser> {
  const user = await requireAuth();
  const db = await createDatabaseClient();
  const canPerform = await userCanPerformAction({
    db,
    userId: user.id,
    resource,
    action,
  });

  if (!canPerform) {
    throw new Error(
      `Access denied. Required permission: ${resource}:${action}`
    );
  }

  return user;
}

export async function requireResourceAccess(
  resource: string
): Promise<AuthenticatedUser> {
  const user = await requireAuth();
  const db = await createDatabaseClient();
  const canAccess = await userCanAccessResource(db, user.id, resource);

  if (!canAccess) {
    throw new Error(`Access denied. No permissions for resource: ${resource}`);
  }

  return user;
}

export async function requireSuperUser(): Promise<AuthenticatedUser> {
  const user = await requireAuth();
  const db = await createDatabaseClient();
  const isSuperUser = await userIsSuperUser({
    db,
    userId: user.id,
  });

  if (!isSuperUser) {
    throw new Error('Access denied. Super user privileges required');
  }

  return user;
}

export async function requireRoleOrHigher(
  minimumRole: RoleEnum
): Promise<AuthenticatedUser> {
  const user = await requireAuth();
  const db = await createDatabaseClient();
  const hasRoleOrHigher = await userHasRoleOrHigher({
    db,
    userId: user.id,
    minimumRole,
  });

  if (!hasRoleOrHigher) {
    throw new Error(
      `Access denied. Required role level: ${minimumRole} or higher`
    );
  }

  return user;
}
//</editor-fold>

//<editor-fold desc="Client-side Authorization Checks (check___)">
export async function checkUserHasRole(
  role: RoleEnum | RoleEnum[]
): Promise<boolean> {
  try {
    const user = await getCurrentUserWithPermissions();
    if (!user) {
      return false;
    }

    const db = await createDatabaseClient();
    return await userHasRole({
      db,
      userId: user.id,
      role,
    });
  } catch {
    return false;
  }
}

export async function checkUserHasPermission(
  permission: string
): Promise<boolean> {
  try {
    const user = await getCurrentUserWithPermissions();
    if (!user) {
      return false;
    }

    const db = await createDatabaseClient();
    return await userHasPermission({
      db,
      userId: user.id,
      permission,
    });
  } catch {
    return false;
  }
}

export async function checkUserHasAnyPermission(
  permissions: string[]
): Promise<boolean> {
  try {
    const user = await getCurrentUserWithPermissions();
    if (!user) {
      return false;
    }

    const db = await createDatabaseClient();
    return await userHasAnyPermission({
      db,
      userId: user.id,
      permissions,
    });
  } catch {
    return false;
  }
}

export async function checkUserHasAllPermissions(
  permissions: string[]
): Promise<boolean> {
  try {
    const user = await getCurrentUserWithPermissions();
    if (!user) {
      return false;
    }

    const db = await createDatabaseClient();
    return await userHasAllPermissions({
      db,
      userId: user.id,
      permissions,
    });
  } catch {
    return false;
  }
}

export async function checkUserCanPerformAction(
  resource: string,
  action: string
): Promise<boolean> {
  try {
    const user = await getCurrentUserWithPermissions();
    if (!user) {
      return false;
    }

    const db = await createDatabaseClient();
    return await userCanPerformAction({
      db,
      userId: user.id,
      resource,
      action,
    });
  } catch {
    return false;
  }
}

export async function checkUserCanAccessResource(
  resource: string
): Promise<boolean> {
  try {
    const user = await getCurrentUserWithPermissions();
    if (!user) {
      return false;
    }

    const db = await createDatabaseClient();
    return await userCanAccessResource(db, user.id, resource);
  } catch {
    return false;
  }
}

export async function checkUserIsSuperUser(): Promise<boolean> {
  try {
    const user = await getCurrentUserWithPermissions();
    if (!user) {
      return false;
    }

    const db = await createDatabaseClient();
    return await userIsSuperUser({
      db,
      userId: user.id,
    });
  } catch {
    return false;
  }
}

export async function checkUserHasRoleOrHigher(
  minimumRole: RoleEnum
): Promise<boolean> {
  try {
    const user = await getCurrentUserWithPermissions();
    if (!user) {
      return false;
    }

    const db = await createDatabaseClient();
    return await userHasRoleOrHigher({
      db,
      userId: user.id,
      minimumRole,
    });
  } catch {
    return false;
  }
}

export async function getUserCurrentResourcePermissions(
  resource: string
): Promise<string[]> {
  try {
    const user = await getCurrentUserWithPermissions();
    if (!user) {
      return [];
    }

    const db = await createDatabaseClient();
    return await getUserResourcePermissions(db, user.id, resource);
  } catch {
    return [];
  }
}
//</editor-fold>
