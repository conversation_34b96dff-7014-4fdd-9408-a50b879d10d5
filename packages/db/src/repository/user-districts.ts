import type { TransactionType } from '../config/client';
import { userDistrictsTable } from '../schema';

export class UserDistrictsRepository {
  private tx: TransactionType;
  constructor(tx: TransactionType) {
    this.tx = tx;
  }

  async createAssociation(userId: string, districtId: string) {
    const [association] = await this.tx
      .insert(userDistrictsTable)
      .values({
        userId,
        districtId,
      })
      .returning();

    return association;
  }
}
