import { and, eq } from 'drizzle-orm';
import type { TransactionType } from '../config/client';
import { type DocumentCategoryEnum, documentsTable } from '../schema';

export interface CreateDocumentData {
  studentId: string;
  name: string;
  url: string;
  category: DocumentCategoryEnum;
  uploadedUserId: string;
}

export class DocumentsRepository {
  private tx: TransactionType;

  constructor(tx: TransactionType) {
    this.tx = tx;
  }

  /**
   * Create a single document
   */
  async createDocument(documentData: CreateDocumentData) {
    const [document] = await this.tx
      .insert(documentsTable)
      .values(documentData)
      .returning();

    return document;
  }

  /**
   * Create multiple documents in batch
   */
  async createDocuments(documentsData: CreateDocumentData[]) {
    if (documentsData.length === 0) {
      return [];
    }

    return await this.tx
      .insert(documentsTable)
      .values(documentsData)
      .returning();
  }

  /**
   * Get documents for a specific student
   */
  async getDocumentsByStudentId(studentId: string) {
    return await this.tx
      .select()
      .from(documentsTable)
      .where(eq(documentsTable.studentId, studentId));
  }

  /**
   * Get document by ID
   */
  async getDocumentById(id: string) {
    const [document] = await this.tx
      .select()
      .from(documentsTable)
      .where(and(eq(documentsTable.id, id)))
      .limit(1);

    return document || null;
  }

  /**
   * Soft delete a document
   */
  async deleteDocument(id: string) {
    const [document] = await this.tx
      .update(documentsTable)
      .set({
        updatedAt: new Date(),
      })
      .where(eq(documentsTable.id, id))
      .returning();

    return document;
  }
}
