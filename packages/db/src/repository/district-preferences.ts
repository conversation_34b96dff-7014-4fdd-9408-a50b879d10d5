import type { TransactionType } from '../config/client';
import {
  districtPreferencesTable,
  type NewDistrictPreference,
} from '../schema';

export class DistrictPreferencesRepository {
  private tx: TransactionType;

  constructor(tx: TransactionType) {
    this.tx = tx;
  }

  async createPreferences(preferences: NewDistrictPreference[]) {
    if (preferences.length === 0) {
      return 0;
    }

    await this.tx.insert(districtPreferencesTable).values(preferences);

    return preferences.length;
  }
}
