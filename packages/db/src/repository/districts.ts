import type { DistrictTypeEnum } from '@lilypad/db/enums';
import {
  and,
  asc,
  count,
  desc,
  eq,
  ilike,
  or,
  type SQL,
  sql,
} from 'drizzle-orm';
import type { TransactionType } from '../config/client';
import { addressesTable, districtsTable } from '../schema';
import type { District, NewDistrict } from '../schema/types';
import type {
  DistrictFilterField,
  DistrictSortField,
  DistrictWithAddress,
  GetDistrictsParams,
  GetDistrictsResult,
} from './types/districts';

export class DistrictsRepository {
  private tx: TransactionType;

  constructor(tx: TransactionType) {
    this.tx = tx;
  }

  private buildSearchConditions(search: string | undefined): SQL | undefined {
    if (!search?.trim()) {
      return;
    }

    const searchTerm = search.trim();
    return sql`(
			${ilike(districtsTable.name, `%${searchTerm}%`)} OR
			${ilike(districtsTable.county, `%${searchTerm}%`)} OR
			${ilike(districtsTable.ncesId, `%${searchTerm}%`)}
		)`;
  }

  private buildFilterConditions(
    filters: GetDistrictsParams['filters'] = []
  ): SQL[] {
    const conditions: SQL[] = [];

    for (const filter of filters) {
      if (!filter.value) {
        continue;
      }

      const condition = this.createFilterCondition(
        filter.id as DistrictFilterField,
        filter.value,
        filter.isMulti
      );

      if (condition) {
        conditions.push(condition);
      }
    }

    return conditions;
  }

  private createFilterCondition(
    field: DistrictFilterField,
    value: unknown,
    isMulti?: boolean
  ): SQL | undefined {
    switch (field) {
      case 'name':
        return ilike(districtsTable.name, `%${value}%`);

      case 'type':
        if (isMulti && Array.isArray(value)) {
          return sql`${districtsTable.type} = ANY(${value})`;
        }
        return eq(districtsTable.type, value as DistrictTypeEnum);

      case 'county':
        return ilike(districtsTable.county, `%${value}%`);

      case 'state':
        return ilike(addressesTable.state, `%${value}%`);

      case 'city':
        return ilike(addressesTable.city, `%${value}%`);

      case 'ncesId':
        return ilike(districtsTable.ncesId, `%${value}%`);

      case 'stateId':
        return ilike(districtsTable.stateId, `%${value}%`);

      default:
        return;
    }
  }

  private buildOrderByConditions(sort: GetDistrictsParams['sort'] = []): SQL[] {
    if (sort.length === 0) {
      return [asc(districtsTable.name)];
    }

    const orderByConditions: SQL[] = [];

    for (const sortItem of sort) {
      const orderBy = this.createOrderByCondition(
        sortItem.id as DistrictSortField,
        sortItem.desc
      );

      if (orderBy) {
        orderByConditions.push(orderBy);
      }
    }

    return orderByConditions.length > 0
      ? orderByConditions
      : [asc(districtsTable.name)];
  }

  private createOrderByCondition(
    field: DistrictSortField,
    descending: boolean
  ): SQL | undefined {
    const sortFn = descending ? desc : asc;

    switch (field) {
      case 'name':
        return sortFn(districtsTable.name);
      case 'type':
        return sortFn(districtsTable.type);
      case 'county':
        return sortFn(districtsTable.county);
      case 'state':
        return sortFn(addressesTable.state);
      case 'city':
        return sortFn(addressesTable.city);
      case 'stateId':
        return sortFn(districtsTable.stateId);
      default:
        return;
    }
  }

  private buildWhereClause(
    conditions: SQL[],
    joinOperator: 'and' | 'or' = 'and'
  ): SQL | undefined {
    if (conditions.length === 0) {
      return;
    }

    if (conditions.length === 1) {
      return conditions[0];
    }

    return joinOperator === 'or' ? or(...conditions) : and(...conditions);
  }

  private getDistrictWithAddressSelect() {
    return {
      // District fields
      id: districtsTable.id,
      name: districtsTable.name,
      slug: districtsTable.slug,
      type: districtsTable.type,
      website: districtsTable.website,
      ncesId: districtsTable.ncesId,
      stateId: districtsTable.stateId,
      county: districtsTable.county,
      numSchools: districtsTable.numSchools,
      numStudents: districtsTable.numStudents,
      invoiceEmail: districtsTable.invoiceEmail,
      addressId: districtsTable.addressId,
      // Address fields
      address: addressesTable.address,
      city: addressesTable.city,
      state: addressesTable.state,
    };
  }

  private async executeDistrictQuery(
    whereClause: SQL | undefined,
    orderByConditions: SQL[],
    limit: number,
    offset: number
  ): Promise<{ data: unknown[]; total: number }> {
    const baseQuery = this.tx
      .select(this.getDistrictWithAddressSelect())
      .from(districtsTable)
      .innerJoin(
        addressesTable,
        eq(districtsTable.addressId, addressesTable.id)
      );

    const countQuery = this.tx
      .select({ total: count() })
      .from(districtsTable)
      .innerJoin(
        addressesTable,
        eq(districtsTable.addressId, addressesTable.id)
      );

    const [data, [{ total }]] = await Promise.all([
      whereClause
        ? baseQuery
            .where(whereClause)
            .orderBy(...orderByConditions)
            .limit(limit)
            .offset(offset)
        : baseQuery
            .orderBy(...orderByConditions)
            .limit(limit)
            .offset(offset),
      whereClause ? countQuery.where(whereClause) : countQuery,
    ]);

    return { data, total };
  }

  async getDistricts(params: GetDistrictsParams): Promise<GetDistrictsResult> {
    const {
      page = 1,
      perPage = 10,
      search,
      filters = [],
      joinOperator = 'and',
      sort = [{ id: 'name', desc: true }],
    } = params;

    const conditions: SQL[] = [];

    const searchCondition = this.buildSearchConditions(search);
    if (searchCondition) {
      conditions.push(searchCondition);
    }

    const filterConditions = this.buildFilterConditions(filters);
    conditions.push(...filterConditions);

    const whereClause = this.buildWhereClause(conditions, joinOperator);
    const orderByConditions = this.buildOrderByConditions(sort);

    const offset = (page - 1) * perPage;

    const result = await this.executeDistrictQuery(
      whereClause,
      orderByConditions,
      perPage,
      offset
    );

    return {
      data: result.data as DistrictWithAddress[],
      pagination: {
        page,
        limit: perPage,
        total: result.total,
        pages: Math.ceil(result.total / perPage),
      },
    };
  }

  async getDistrictById(id: string): Promise<District | null> {
    const [district] = await this.tx
      .select()
      .from(districtsTable)
      .where(eq(districtsTable.id, id))
      .limit(1);

    return district || null;
  }

  async getDistrictBySlug(slug: string): Promise<District | null> {
    const [district] = await this.tx
      .select()
      .from(districtsTable)
      .where(eq(districtsTable.slug, slug))
      .limit(1);

    return district || null;
  }

  async getDistrictWithAddress(
    id: string
  ): Promise<DistrictWithAddress | null> {
    const [district] = await this.tx
      .select(this.getDistrictWithAddressSelect())
      .from(districtsTable)
      .innerJoin(
        addressesTable,
        eq(districtsTable.addressId, addressesTable.id)
      )
      .where(eq(districtsTable.id, id))
      .limit(1);

    return (district as DistrictWithAddress) || null;
  }

  async createDistrict(data: NewDistrict) {
    const [district] = await this.tx
      .insert(districtsTable)
      .values(data)
      .returning();

    if (!district) {
      throw new Error('Failed to create district');
    }

    return district;
  }

  async findBySlug(slug: string) {
    const [district] = await this.tx
      .select()
      .from(districtsTable)
      .where(eq(districtsTable.slug, slug))
      .limit(1);

    return district || null;
  }

  async exists(slug: string): Promise<boolean> {
    const district = await this.findBySlug(slug);
    return district !== null;
  }
}
