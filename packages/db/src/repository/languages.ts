import { asc } from 'drizzle-orm';
import type { TransactionType } from '../config/client';
import { languagesTable } from '../schema';

export class LanguagesRepository {
  private tx: TransactionType;

  constructor(tx: TransactionType) {
    this.tx = tx;
  }

  async getAllLanguages() {
    return await this.tx
      .select({
        id: languagesTable.id,
        name: languagesTable.name,
        code: languagesTable.code,
        emoji: languagesTable.emoji,
      })
      .from(languagesTable)
      .orderBy(asc(languagesTable.name));
  }
}
