import type {
  EnrollmentStatusEnum,
  GenderEnum,
  SchoolGradeEnum,
} from '@lilypad/db/enums';
import {
  addressesTable,
  districtsTable,
  languagesTable,
  parentsTable,
  schoolsTable,
  studentLanguagesTable,
  studentParentsTable,
  studentsTable,
} from '@lilypad/db/schema';
import type { Student } from '@lilypad/db/types';
import {
  and,
  asc,
  count,
  desc,
  eq,
  ilike,
  or,
  type SQL,
  sql,
} from 'drizzle-orm';
import type { TransactionType } from '../config/client';
import type {
  GetStudentsParams,
  GetStudentsResult,
  StudentFilterField,
  StudentSortField,
  StudentWithSchoolAndDistrict,
} from './types/students';

class StudentRepository {
  private tx: TransactionType;

  constructor(tx: TransactionType) {
    this.tx = tx;
  }

  private buildSearchConditions(search: string | undefined): SQL | undefined {
    if (!search?.trim()) {
      return;
    }

    const searchTerm = search.trim();
    return sql`(
			${ilike(studentsTable.fullName, `%${searchTerm}%`)} OR
			${ilike(studentsTable.studentIdNumber, `%${searchTerm}%`)} OR
			${ilike(schoolsTable.name, `%${searchTerm}%`)} OR
			${ilike(districtsTable.name, `%${searchTerm}%`)}
		)`;
  }

  private buildFilterConditions(
    filters: GetStudentsParams['filters'] = []
  ): SQL[] {
    const conditions: SQL[] = [];

    for (const filter of filters) {
      if (!filter.value) {
        continue;
      }

      const condition = this.createFilterCondition(
        filter.id as StudentFilterField,
        filter.value,
        filter.isMulti
      );

      if (condition) {
        conditions.push(condition);
      }
    }

    return conditions;
  }

  private createFilterCondition(
    field: StudentFilterField,
    value: unknown,
    isMulti?: boolean
  ): SQL | undefined {
    switch (field) {
      case 'fullName':
        return ilike(studentsTable.fullName, `%${value}%`);

      case 'studentIdNumber':
        return ilike(studentsTable.studentIdNumber, `%${value}%`);

      case 'grade':
        if (isMulti && Array.isArray(value)) {
          return sql`${studentsTable.grade} = ANY(${value})`;
        }
        return eq(studentsTable.grade, value as string);

      case 'gender':
        if (isMulti && Array.isArray(value)) {
          return sql`${studentsTable.gender} = ANY(${value})`;
        }
        return eq(studentsTable.gender, value as GenderEnum);

      case 'enrollmentStatus':
        if (isMulti && Array.isArray(value)) {
          return sql`${studentsTable.enrollmentStatus} = ANY(${value})`;
        }
        return eq(
          studentsTable.enrollmentStatus,
          value as EnrollmentStatusEnum
        );

      case 'specialNeedsIndicator':
        return eq(studentsTable.specialNeedsIndicator, value as boolean);

      case 'emergencyContactName':
        return ilike(studentsTable.emergencyContactName, `%${value}%`);

      case 'emergencyContactPhone':
        return ilike(studentsTable.emergencyContactPhone, `%${value}%`);

      case 'dateOfBirth':
        return eq(studentsTable.dateOfBirth, value as string);

      case 'dateOfConsent':
        return eq(studentsTable.dateOfConsent, value as string);

      case 'schoolName':
        return ilike(schoolsTable.name, `%${value}%`);

      case 'districtName':
        return ilike(districtsTable.name, `%${value}%`);

      default:
        return;
    }
  }

  private buildOrderByConditions(sort: GetStudentsParams['sort'] = []): SQL[] {
    if (sort.length === 0) {
      return [asc(studentsTable.fullName)];
    }

    const orderByConditions: SQL[] = [];

    for (const sortItem of sort) {
      const orderBy = this.createOrderByCondition(
        sortItem.id as StudentSortField,
        sortItem.desc
      );

      if (orderBy) {
        orderByConditions.push(orderBy);
      }
    }

    return orderByConditions.length > 0
      ? orderByConditions
      : [asc(studentsTable.fullName)];
  }

  private createOrderByCondition(
    field: StudentSortField,
    descending: boolean
  ): SQL | undefined {
    const sortFn = descending ? desc : asc;

    switch (field) {
      case 'fullName':
        return sortFn(studentsTable.fullName);
      case 'studentIdNumber':
        return sortFn(studentsTable.studentIdNumber);
      case 'grade':
        return sortFn(studentsTable.grade);
      case 'gender':
        return sortFn(studentsTable.gender);
      case 'enrollmentStatus':
        return sortFn(studentsTable.enrollmentStatus);
      case 'schoolName':
        return sortFn(schoolsTable.name);
      case 'districtName':
        return sortFn(districtsTable.name);
      case 'dateOfBirth':
        return sortFn(studentsTable.dateOfBirth);
      case 'dateOfConsent':
        return sortFn(studentsTable.dateOfConsent);
      default:
        return;
    }
  }

  private buildWhereClause(
    conditions: SQL[],
    joinOperator: 'and' | 'or' = 'and'
  ): SQL | undefined {
    if (conditions.length === 0) {
      return;
    }

    if (conditions.length === 1) {
      return conditions[0];
    }

    return joinOperator === 'or' ? or(...conditions) : and(...conditions);
  }

  private getStudentWithSchoolAndDistrictSelect() {
    return {
      // Student fields
      id: studentsTable.id,
      firstName: studentsTable.firstName,
      middleName: studentsTable.middleName,
      lastName: studentsTable.lastName,
      fullName: studentsTable.fullName,
      preferredName: studentsTable.preferredName,
      studentIdNumber: studentsTable.studentIdNumber,
      dateOfBirth: studentsTable.dateOfBirth,
      dateOfConsent: studentsTable.dateOfConsent,
      grade: studentsTable.grade,
      gender: studentsTable.gender,
      primarySchoolId: studentsTable.primarySchoolId,
      enrollmentStatus: studentsTable.enrollmentStatus,
      specialNeedsIndicator: studentsTable.specialNeedsIndicator,
      emergencyContactName: studentsTable.emergencyContactName,
      emergencyContactPhone: studentsTable.emergencyContactPhone,
      isDeleted: studentsTable.isDeleted,
      deletedAt: studentsTable.deletedAt,
      deletedBy: studentsTable.deletedBy,
      createdAt: studentsTable.createdAt,
      updatedAt: studentsTable.updatedAt,
      // School and District fields
      schoolName: schoolsTable.name,
      districtName: districtsTable.name,
      schoolId: schoolsTable.id,
      districtId: districtsTable.id,
    };
  }

  async getStudents(params: GetStudentsParams): Promise<GetStudentsResult> {
    const {
      page = 1,
      perPage = 10,
      search,
      filters = [],
      joinOperator = 'and',
      sort = [{ id: 'fullName', desc: false }],
    } = params;

    // Always filter out soft-deleted students
    const conditions: SQL[] = [eq(studentsTable.isDeleted, false)];

    const searchCondition = this.buildSearchConditions(search);
    if (searchCondition) {
      conditions.push(searchCondition);
    }

    const filterConditions = this.buildFilterConditions(filters);
    conditions.push(...filterConditions);

    const whereClause = this.buildWhereClause(conditions, joinOperator);
    const orderByConditions = this.buildOrderByConditions(sort);

    const offset = (page - 1) * perPage;

    const result = await this.executeStudentQuery(
      whereClause,
      orderByConditions,
      perPage,
      offset
    );

    return {
      data: result.data as StudentWithSchoolAndDistrict[],
      pagination: {
        page,
        limit: perPage,
        total: result.total,
        pages: Math.ceil(result.total / perPage),
      },
    };
  }

  private async executeStudentQuery(
    whereClause: SQL | undefined,
    orderByConditions: SQL[],
    limit: number,
    offset: number
  ): Promise<{ data: unknown[]; total: number }> {
    const baseQuery = this.tx
      .select(this.getStudentWithSchoolAndDistrictSelect())
      .from(studentsTable)
      .leftJoin(
        schoolsTable,
        eq(studentsTable.primarySchoolId, schoolsTable.id)
      )
      .leftJoin(districtsTable, eq(schoolsTable.districtId, districtsTable.id));

    const [data, [{ total }]] = await Promise.all([
      baseQuery
        .where(whereClause)
        .orderBy(...orderByConditions)
        .limit(limit)
        .offset(offset),

      this.tx
        .select({ total: count() })
        .from(studentsTable)
        .leftJoin(
          schoolsTable,
          eq(studentsTable.primarySchoolId, schoolsTable.id)
        )
        .leftJoin(
          districtsTable,
          eq(schoolsTable.districtId, districtsTable.id)
        )
        .where(whereClause),
    ]);

    return { data, total };
  }

  async getStudentById(id: string): Promise<Student | null> {
    const [student] = await this.tx
      .select()
      .from(studentsTable)
      .where(and(eq(studentsTable.id, id), eq(studentsTable.isDeleted, false)))
      .limit(1);

    return student || null;
  }

  async getStudentByStudentIdNumber(
    studentIdNumber: string
  ): Promise<Student | null> {
    const [student] = await this.tx
      .select()
      .from(studentsTable)
      .where(
        and(
          eq(studentsTable.studentIdNumber, studentIdNumber),
          eq(studentsTable.isDeleted, false)
        )
      )
      .limit(1);

    return student || null;
  }

  async getStudentProfileById(id: string) {
    const [studentData] = await this.tx
      .select({
        id: studentsTable.id,
        firstName: studentsTable.firstName,
        middleName: studentsTable.middleName,
        lastName: studentsTable.lastName,
        fullName: studentsTable.fullName,
        preferredName: studentsTable.preferredName,
        studentIdNumber: studentsTable.studentIdNumber,
        dateOfBirth: studentsTable.dateOfBirth,
        dateOfConsent: studentsTable.dateOfConsent,
        grade: studentsTable.grade,
        gender: studentsTable.gender,
        primarySchoolId: studentsTable.primarySchoolId,
        enrollmentStatus: studentsTable.enrollmentStatus,
        specialNeedsIndicator: studentsTable.specialNeedsIndicator,
        emergencyContactName: studentsTable.emergencyContactName,
        emergencyContactPhone: studentsTable.emergencyContactPhone,
        createdAt: studentsTable.createdAt,
        updatedAt: studentsTable.updatedAt,
        primarySchool: {
          id: schoolsTable.id,
          name: schoolsTable.name,
          district: districtsTable.name,
        },
      })
      .from(studentsTable)
      .leftJoin(
        schoolsTable,
        eq(studentsTable.primarySchoolId, schoolsTable.id)
      )
      .leftJoin(districtsTable, eq(schoolsTable.districtId, districtsTable.id))
      .where(and(eq(studentsTable.id, id), eq(studentsTable.isDeleted, false)))
      .limit(1);

    const languages = await this.tx
      .select({
        id: languagesTable.id,
        name: languagesTable.name,
        code: languagesTable.code,
        emoji: languagesTable.emoji,
        isPrimary: studentLanguagesTable.isPrimary,
      })
      .from(studentLanguagesTable)
      .innerJoin(
        languagesTable,
        eq(studentLanguagesTable.languageId, languagesTable.id)
      )
      .where(eq(studentLanguagesTable.studentId, id));

    const parents = await this.tx
      .select({
        id: parentsTable.id,
        firstName: parentsTable.firstName,
        middleName: parentsTable.middleName,
        lastName: parentsTable.lastName,
        fullName: parentsTable.fullName,
        primaryEmail: parentsTable.primaryEmail,
        secondaryEmail: parentsTable.secondaryEmail,
        primaryPhone: parentsTable.primaryPhone,
        secondaryPhone: parentsTable.secondaryPhone,
        relationshipType: parentsTable.relationshipType,
        isPrimaryContact: studentParentsTable.isPrimaryContact,
        hasPickupAuthorization: studentParentsTable.hasPickupAuthorization,
      })
      .from(studentParentsTable)
      .innerJoin(
        parentsTable,
        eq(studentParentsTable.parentId, parentsTable.id)
      )
      .where(
        and(
          eq(studentParentsTable.studentId, id),
          eq(parentsTable.isDeleted, false)
        )
      );

    const addresses = await this.tx
      .select({
        id: addressesTable.id,
        type: addressesTable.type,
        address: addressesTable.address,
        address2: addressesTable.address2,
        city: addressesTable.city,
        state: addressesTable.state,
        zipcode: addressesTable.zipcode,
        isPrimary: addressesTable.isPrimary,
      })
      .from(addressesTable)
      .where(eq(addressesTable.studentId, id));

    return {
      ...studentData,
      languages,
      parents,
      addresses,
    };
  }

  /**
   * Create multiple students in batch
   */
  async createStudents(
    studentsData: Array<{
      studentIdNumber: string;
      firstName: string;
      lastName: string;
      preferredName: string;
      dateOfBirth: string;
      dateOfConsent: string;
      gender: GenderEnum;
      grade: SchoolGradeEnum;
      primarySchoolId: string;
      specialNeedsIndicator: boolean;
    }>
  ) {
    if (studentsData.length === 0) {
      return [];
    }

    return await this.tx
      .insert(studentsTable)
      .values(studentsData)
      .returning({ id: studentsTable.id });
  }
}

export { StudentRepository };
