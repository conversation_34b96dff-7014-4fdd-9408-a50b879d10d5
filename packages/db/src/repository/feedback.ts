import type { DatabaseType, TransactionType } from '@lilypad/db/client';
import { FeedbackStatusEnum } from '@lilypad/db/enums';
import { feedbackFileTable, feedbackTable } from '@lilypad/db/schema';
import type {
  Feedback,
  FeedbackFile,
  NewFeedback,
  NewFeedbackFile,
} from '@lilypad/db/schema/types';
import { eq } from 'drizzle-orm';

export type FeedbackWithFiles = Feedback & {
  files?: FeedbackFile[];
};

class FeedbackRepository {
  private db: DatabaseType | TransactionType;

  constructor(db: DatabaseType | TransactionType) {
    this.db = db;
  }

  /**
   * Creates a new feedback entry with optional file attachments
   * @param params - The feedback data to create
   * @param fileUrls - Optional array of file URLs to attach to the feedback
   * @returns The created feedback with its files
   */
  async createFeedback(
    params: Omit<NewFeedback, 'status'> & {
      status?: FeedbackStatusEnum;
    },
    fileUrls?: string[]
  ): Promise<FeedbackWithFiles> {
    return await this.db.transaction(async (tx) => {
      // Create feedback with default status if not provided
      const [feedback] = await tx
        .insert(feedbackTable)
        .values({
          ...params,
          status: params.status || FeedbackStatusEnum.OPEN,
        })
        .returning();

      if (!feedback) {
        throw new Error('Failed to create feedback');
      }

      let files: FeedbackFile[] = [];

      // Create feedback files if provided
      if (fileUrls && fileUrls.length > 0) {
        files = await this.createFeedbackFiles(tx, feedback.id, fileUrls);
      }

      return {
        ...feedback,
        files,
      };
    });
  }

  /**
   * Creates feedback file records
   * @param tx - The transaction to use
   * @param feedbackId - The ID of the feedback to attach files to
   * @param fileUrls - Array of file URLs to attach
   * @returns The created feedback file records
   */
  private async createFeedbackFiles(
    tx: TransactionType,
    feedbackId: string,
    fileUrls: string[]
  ): Promise<FeedbackFile[]> {
    const fileRecords: NewFeedbackFile[] = fileUrls.map((url) => ({
      feedbackId,
      fileUrl: url,
    }));

    const files = await tx
      .insert(feedbackFileTable)
      .values(fileRecords)
      .returning();

    return files;
  }

  /**
   * Retrieves feedback by ID with optional file attachments
   * @param id - The feedback ID
   * @returns The feedback with its files or null if not found
   */
  async getFeedbackById(id: string): Promise<FeedbackWithFiles | null> {
    return await this.db.transaction(async (tx) => {
      // @ts-expect-error - TODO: fix this type issue with drizzle query
      const feedback = await tx.query.feedbackTable.findFirst({
        where: eq(feedbackTable.id, id),
        with: {
          files: true,
        },
      });

      return feedback || null;
    });
  }

  /**
   * Retrieves all feedback for a specific user
   * @param userId - The ID of the user
   * @returns Array of feedback entries with their files
   */
  async getFeedbackByUserId(userId: string): Promise<FeedbackWithFiles[]> {
    return await this.db.transaction(async (tx) => {
      // @ts-expect-error - TODO: fix this type issue with drizzle query
      return await tx.query.feedbackTable.findMany({
        where: eq(feedbackTable.userId, userId),
        with: {
          files: true,
        },
        // @ts-expect-error - TODO: fix this type issue with drizzle query
        orderBy: (feedback: Feedback, { desc }) => [desc(feedback.createdAt)],
      });
    });
  }

  /**
   * Updates the status of a feedback entry
   * @param id - The feedback ID
   * @param status - The new status
   * @returns The updated feedback or null if not found
   */
  async updateFeedbackStatus(
    id: string,
    status: FeedbackStatusEnum
  ): Promise<Feedback | null> {
    const [updated] = await this.db.transaction(async (tx) => {
      return await tx
        .update(feedbackTable)
        .set({
          status,
          updatedAt: new Date(),
        })
        .where(eq(feedbackTable.id, id))
        .returning();
    });

    return updated || null;
  }

  /**
   * Deletes a feedback entry and its associated files
   * @param id - The feedback ID to delete
   */
  async deleteFeedback(id: string): Promise<void> {
    await this.db.transaction(async (tx) => {
      // Files will be cascade deleted due to foreign key constraint
      await tx.delete(feedbackTable).where(eq(feedbackTable.id, id));
    });
  }
}

export { FeedbackRepository };
