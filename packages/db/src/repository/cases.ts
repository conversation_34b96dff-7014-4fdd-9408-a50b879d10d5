import type { TransactionType } from '@lilypad/db/client';
import {
  caseAssignmentsTable,
  caseDetailsTable,
  casesTable,
  rolesTable,
  schoolsTable,
  studentEnrollmentsTable,
  tasksTable,
  userDistrictsTable,
  userRolesTable,
  userSchoolsTable,
  usersTable,
} from '@lilypad/db/schema';
import {
  RoleEnum,
  TaskStatusEnum,
  TaskTypeEnum,
} from '@lilypad/db/schema/enums';
import type { Case, NewCase } from '@lilypad/db/schema/types';
import { and, eq, gte, inArray, isNotNull, isNull, lte } from 'drizzle-orm';
import type {
  CaseForIEPMeetingTasks,
  CaseForJoinEvaluationTasks,
  CaseForPreparationTasks,
  CaseForUpcomingEvaluations,
  CaseWithAssignmentsAndDetails,
} from './types/cases';

class CaseRepository {
  private tx: TransactionType;

  constructor(tx: TransactionType) {
    this.tx = tx;
  }

  /**
   * Creates a new case using admin privileges
   * @param params - The case data to create
   */
  async create(params: NewCase): Promise<void> {
    await this.tx.insert(casesTable).values(params);
  }

  /**
   * Retrieves cases based on user's role and access permissions
   * Implements the same filtering logic as RLS policies but at query level for better performance
   * @param userId - The ID of the user requesting cases
   * @param userRoles - Array of role names the user has
   * @returns Array of cases the user has access to
   */
  async getCasesByUserAccess(
    userId: string,
    userRoles: string[]
  ): Promise<CaseWithAssignmentsAndDetails[]> {
    // Super users get all cases
    if (userRoles.includes('SUPER_ADMIN')) {
      return await this.getAllCasesWithDetails(this.tx);
    }

    // Case-level access only roles (PSYCHOLOGIST, PROCTOR)
    const caseLevelOnlyRoles = ['PSYCHOLOGIST', 'PROCTOR'];
    const hasCaseLevelAccessOnly =
      userRoles.some((role) => caseLevelOnlyRoles.includes(role)) &&
      !userRoles.some((role) => !caseLevelOnlyRoles.includes(role));

    if (hasCaseLevelAccessOnly) {
      return await this.getCasesAssignedToUser(this.tx, userId);
    }

    // District-level and school-level access
    const districtLevelRoles = [
      'SPECIAL_ED_DIRECTOR',
      'CASE_MANAGER',
      'CLINICAL_DIRECTOR',
    ];
    const hasDistrictLevelAccess = userRoles.some((role) =>
      districtLevelRoles.includes(role)
    );

    if (hasDistrictLevelAccess) {
      return await this.getCasesByDistrictAccess(this.tx, userId);
    }

    // School-level access
    return await this.getCasesBySchoolAccess(this.tx, userId);
  }

  /**
   * Get all cases with details for super admin
   */
  private async getAllCasesWithDetails(
    tx: TransactionType
  ): Promise<CaseWithAssignmentsAndDetails[]> {
    const cases = await tx
      .select({
        id: casesTable.id,
        status: casesTable.status,
        priority: casesTable.priority,
        caseType: casesTable.caseType,
        studentId: casesTable.studentId,
        isActive: casesTable.isActive,
        iepStatus: casesTable.iepStatus,
        iepStartDate: casesTable.iepStartDate,
        iepEndDate: casesTable.iepEndDate,
        referralDate: casesTable.referralDate,
        evaluationDueDate: casesTable.evaluationDueDate,
        meetingDate: casesTable.meetingDate,
        createdAt: casesTable.createdAt,
        updatedAt: casesTable.updatedAt,
      })
      .from(casesTable)
      .where(eq(casesTable.isDeleted, false));

    return await this.enrichCasesWithDetails(tx, cases);
  }

  /**
   * Get cases assigned to a specific user
   */
  private async getCasesAssignedToUser(
    tx: TransactionType,
    userId: string
  ): Promise<CaseWithAssignmentsAndDetails[]> {
    const cases = await tx
      .select({
        id: casesTable.id,
        status: casesTable.status,
        priority: casesTable.priority,
        caseType: casesTable.caseType,
        studentId: casesTable.studentId,
        isActive: casesTable.isActive,
        iepStatus: casesTable.iepStatus,
        iepStartDate: casesTable.iepStartDate,
        iepEndDate: casesTable.iepEndDate,
        referralDate: casesTable.referralDate,
        evaluationDueDate: casesTable.evaluationDueDate,
        meetingDate: casesTable.meetingDate,
        createdAt: casesTable.createdAt,
        updatedAt: casesTable.updatedAt,
      })
      .from(casesTable)
      .innerJoin(
        caseAssignmentsTable,
        and(
          eq(caseAssignmentsTable.caseId, casesTable.id),
          eq(caseAssignmentsTable.userId, userId),
          eq(caseAssignmentsTable.isDeleted, false)
        )
      )
      .where(eq(casesTable.isDeleted, false));

    return await this.enrichCasesWithDetails(tx, cases);
  }

  /**
   * Get cases by district access
   */
  private async getCasesByDistrictAccess(
    tx: TransactionType,
    userId: string
  ): Promise<CaseWithAssignmentsAndDetails[]> {
    const cases = await tx
      .select({
        id: casesTable.id,
        status: casesTable.status,
        priority: casesTable.priority,
        caseType: casesTable.caseType,
        studentId: casesTable.studentId,
        isActive: casesTable.isActive,
        iepStatus: casesTable.iepStatus,
        iepStartDate: casesTable.iepStartDate,
        iepEndDate: casesTable.iepEndDate,
        referralDate: casesTable.referralDate,
        evaluationDueDate: casesTable.evaluationDueDate,
        meetingDate: casesTable.meetingDate,
        createdAt: casesTable.createdAt,
        updatedAt: casesTable.updatedAt,
      })
      .from(casesTable)
      .innerJoin(
        studentEnrollmentsTable,
        eq(studentEnrollmentsTable.studentId, casesTable.studentId)
      )
      .innerJoin(
        schoolsTable,
        eq(schoolsTable.id, studentEnrollmentsTable.schoolId)
      )
      .innerJoin(
        userDistrictsTable,
        and(
          eq(userDistrictsTable.districtId, schoolsTable.districtId),
          eq(userDistrictsTable.userId, userId)
        )
      )
      .where(eq(casesTable.isDeleted, false));

    return await this.enrichCasesWithDetails(tx, cases);
  }

  /**
   * Get cases by school access
   */
  private async getCasesBySchoolAccess(
    tx: TransactionType,
    userId: string
  ): Promise<CaseWithAssignmentsAndDetails[]> {
    const cases = await tx
      .select({
        id: casesTable.id,
        status: casesTable.status,
        priority: casesTable.priority,
        caseType: casesTable.caseType,
        studentId: casesTable.studentId,
        isActive: casesTable.isActive,
        iepStatus: casesTable.iepStatus,
        iepStartDate: casesTable.iepStartDate,
        iepEndDate: casesTable.iepEndDate,
        referralDate: casesTable.referralDate,
        evaluationDueDate: casesTable.evaluationDueDate,
        meetingDate: casesTable.meetingDate,
        createdAt: casesTable.createdAt,
        updatedAt: casesTable.updatedAt,
      })
      .from(casesTable)
      .innerJoin(
        studentEnrollmentsTable,
        eq(studentEnrollmentsTable.studentId, casesTable.studentId)
      )
      .innerJoin(
        userSchoolsTable,
        and(
          eq(userSchoolsTable.schoolId, studentEnrollmentsTable.schoolId),
          eq(userSchoolsTable.userId, userId)
        )
      )
      .where(eq(casesTable.isDeleted, false));

    return await this.enrichCasesWithDetails(tx, cases);
  }

  /**
   * Enrich cases with assignments and details
   */
  private async enrichCasesWithDetails(
    tx: TransactionType,
    cases: Pick<
      Case,
      | 'id'
      | 'status'
      | 'priority'
      | 'caseType'
      | 'studentId'
      | 'isActive'
      | 'iepStatus'
      | 'iepStartDate'
      | 'iepEndDate'
      | 'referralDate'
      | 'evaluationDueDate'
      | 'meetingDate'
      | 'createdAt'
      | 'updatedAt'
    >[]
  ): Promise<CaseWithAssignmentsAndDetails[]> {
    if (cases.length === 0) {
      return [];
    }

    const caseIds = cases.map((c) => c.id);

    // Get case assignments with user details
    const caseAssignments = await tx
      .select({
        id: caseAssignmentsTable.id,
        userId: caseAssignmentsTable.userId,
        caseId: caseAssignmentsTable.caseId,
        createdAt: caseAssignmentsTable.createdAt,
        updatedAt: caseAssignmentsTable.updatedAt,
        user: {
          id: usersTable.id,
          firstName: usersTable.firstName,
          lastName: usersTable.lastName,
          fullName: usersTable.fullName,
          email: usersTable.email,
          avatar: usersTable.avatar,
        },
      })
      .from(caseAssignmentsTable)
      .innerJoin(usersTable, eq(usersTable.id, caseAssignmentsTable.userId))
      .where(
        and(
          inArray(caseAssignmentsTable.caseId, caseIds),
          eq(caseAssignmentsTable.isDeleted, false)
        )
      );

    // Get user roles for the assigned users
    const userIds = [...new Set(caseAssignments.map((ca) => ca.userId))];
    const userRoles =
      userIds.length > 0
        ? await tx
            .select({
              userId: userRolesTable.userId,
              role: {
                id: rolesTable.id,
                name: rolesTable.name,
              },
            })
            .from(userRolesTable)
            .innerJoin(rolesTable, eq(rolesTable.id, userRolesTable.roleId))
            .where(inArray(userRolesTable.userId, userIds))
        : [];

    // Get case details
    const caseDetails = await tx
      .select({
        id: caseDetailsTable.id,
        caseId: caseDetailsTable.caseId,
        key: caseDetailsTable.key,
        value: caseDetailsTable.value,
        createdAt: caseDetailsTable.createdAt,
        updatedAt: caseDetailsTable.updatedAt,
      })
      .from(caseDetailsTable)
      .where(
        and(
          inArray(caseDetailsTable.caseId, caseIds),
          eq(caseDetailsTable.isDeleted, false)
        )
      );

    // Group user roles by user ID
    const userRolesByUserId = userRoles.reduce(
      (acc, ur) => {
        if (!acc[ur.userId]) {
          acc[ur.userId] = [];
        }
        acc[ur.userId].push(ur.role);
        return acc;
      },
      {} as Record<string, { id: string; name: string }[]>
    );

    // Group assignments by case ID
    const assignmentsByCaseId = caseAssignments.reduce(
      (acc, ca) => {
        if (!acc[ca.caseId]) {
          acc[ca.caseId] = [];
        }
        acc[ca.caseId].push({
          ...ca,
          user: {
            ...ca.user,
            userRoles: (userRolesByUserId[ca.userId] || []).map((role) => ({
              role,
            })),
          },
        });
        return acc;
      },
      // biome-ignore lint/suspicious/noExplicitAny: Fix later
      {} as Record<string, any[]>
    );

    // Group details by case ID
    const detailsByCaseId = caseDetails.reduce(
      (acc, cd) => {
        if (!acc[cd.caseId]) {
          acc[cd.caseId] = [];
        }
        acc[cd.caseId].push(cd);
        return acc;
      },
      // biome-ignore lint/suspicious/noExplicitAny: Fix later
      {} as Record<string, any[]>
    );

    // Combine everything
    return cases.map((caseItem) => ({
      ...caseItem,
      caseAssignments: assignmentsByCaseId[caseItem.id] || [],
      caseDetails: detailsByCaseId[caseItem.id] || [],
    }));
  }

  /**
   * Retrieves all cases for a specific student with related data
   * @param studentId - The ID of the student
   * @returns Array of cases with assignments and details
   */
  async getCompleteCasesByStudentId(
    studentId: string
  ): Promise<CaseWithAssignmentsAndDetails[]> {
    const cases = await this.tx
      .select({
        id: casesTable.id,
        status: casesTable.status,
        priority: casesTable.priority,
        caseType: casesTable.caseType,
        studentId: casesTable.studentId,
        isActive: casesTable.isActive,
        iepStatus: casesTable.iepStatus,
        iepStartDate: casesTable.iepStartDate,
        iepEndDate: casesTable.iepEndDate,
        referralDate: casesTable.referralDate,
        evaluationDueDate: casesTable.evaluationDueDate,
        meetingDate: casesTable.meetingDate,
        createdAt: casesTable.createdAt,
        updatedAt: casesTable.updatedAt,
      })
      .from(casesTable)
      .where(
        and(
          eq(casesTable.studentId, studentId),
          eq(casesTable.isDeleted, false)
        )
      );

    return await this.enrichCasesWithDetails(this.tx, cases);
  }

  async getCasesWithUpcomingEvaluations(
    daysAhead = 7,
    targetRoles: RoleEnum[] = [
      RoleEnum.SUPER_USER,
      RoleEnum.CASE_MANAGER,
      RoleEnum.CLINICAL_DIRECTOR,
      RoleEnum.PSYCHOLOGIST,
      RoleEnum.ASSISTANT,
    ]
  ): Promise<CaseForUpcomingEvaluations[]> {
    const now = new Date();
    const futureDate = new Date(
      now.getTime() + daysAhead * 24 * 60 * 60 * 1000
    );

    const results = await this.tx
      .select({
        caseId: casesTable.id,
        userId: usersTable.id,
        email: usersTable.email,
        fullName: usersTable.fullName,
        evaluationDueDate: casesTable.evaluationDueDate,
        roleName: userRolesTable.roleName,
      })
      .from(casesTable)
      .innerJoin(
        caseAssignmentsTable,
        and(
          eq(caseAssignmentsTable.caseId, casesTable.id),
          eq(caseAssignmentsTable.isDeleted, false)
        )
      )
      .innerJoin(usersTable, eq(usersTable.id, caseAssignmentsTable.userId))
      .innerJoin(userRolesTable, eq(userRolesTable.userId, usersTable.id))
      .where(
        and(
          eq(casesTable.isDeleted, false),
          eq(casesTable.isActive, true),
          isNotNull(casesTable.evaluationDueDate),
          gte(casesTable.evaluationDueDate, now),
          lte(casesTable.evaluationDueDate, futureDate),
          inArray(userRolesTable.roleName, targetRoles)
        )
      );

    // Filter out any results where evaluationDueDate is somehow null (extra safety)
    return results.filter(
      (result): result is typeof result & { evaluationDueDate: string } =>
        result.evaluationDueDate !== null
    );
  }

  async getCasesForPreparationTasks(
    hoursAhead = 48,
    bufferHours = 24
  ): Promise<CaseForPreparationTasks[]> {
    const now = new Date();
    const minTime = new Date(now.getTime() + bufferHours * 60 * 60 * 1000);
    const maxTime = new Date(now.getTime() + hoursAhead * 60 * 60 * 1000);

    const casesWithoutTasks = await this.tx
      .select({
        caseId: casesTable.id,
        userId: usersTable.id,
        email: usersTable.email,
        fullName: usersTable.fullName,
        evaluationDueDate: casesTable.evaluationDueDate,
      })
      .from(casesTable)
      .innerJoin(
        caseAssignmentsTable,
        and(
          eq(caseAssignmentsTable.caseId, casesTable.id),
          eq(caseAssignmentsTable.isDeleted, false)
        )
      )
      .innerJoin(usersTable, eq(usersTable.id, caseAssignmentsTable.userId))
      .innerJoin(
        userRolesTable,
        and(
          eq(userRolesTable.userId, usersTable.id),
          eq(userRolesTable.roleName, RoleEnum.PSYCHOLOGIST)
        )
      )
      .leftJoin(
        tasksTable,
        and(
          eq(tasksTable.caseId, casesTable.id),
          eq(tasksTable.assignedToId, usersTable.id),
          eq(tasksTable.taskType, TaskTypeEnum.PREPARE_FOR_EVALUATION),
          inArray(tasksTable.status, [
            TaskStatusEnum.PENDING,
            TaskStatusEnum.IN_PROGRESS,
          ])
        )
      )
      .where(
        and(
          eq(casesTable.isDeleted, false),
          eq(casesTable.isActive, true),
          isNotNull(casesTable.evaluationDueDate),
          gte(casesTable.evaluationDueDate, minTime),
          lte(casesTable.evaluationDueDate, maxTime),
          isNull(tasksTable.id)
        )
      );

    // Filter out any results where evaluationDueDate is somehow null (extra safety)
    return casesWithoutTasks.filter(
      (result): result is typeof result & { evaluationDueDate: string } =>
        result.evaluationDueDate !== null
    );
  }

  async getCasesForJoinEvaluationTasks(
    minutesAhead = 60,
    bufferMinutes = 30
  ): Promise<CaseForJoinEvaluationTasks[]> {
    const now = new Date();
    const minTime = new Date(now.getTime() + bufferMinutes * 60 * 1000);
    const maxTime = new Date(now.getTime() + minutesAhead * 60 * 1000);

    const psychologistResults = await this.tx
      .select({
        caseId: casesTable.id,
        userId: usersTable.id,
        email: usersTable.email,
        fullName: usersTable.fullName,
        evaluationDueDate: casesTable.evaluationDueDate,
        roleName: userRolesTable.roleName,
      })
      .from(casesTable)
      .innerJoin(
        caseAssignmentsTable,
        and(
          eq(caseAssignmentsTable.caseId, casesTable.id),
          eq(caseAssignmentsTable.isDeleted, false)
        )
      )
      .innerJoin(usersTable, eq(usersTable.id, caseAssignmentsTable.userId))
      .innerJoin(
        userRolesTable,
        and(
          eq(userRolesTable.userId, usersTable.id),
          eq(userRolesTable.roleName, RoleEnum.PSYCHOLOGIST)
        )
      )
      .leftJoin(
        tasksTable,
        and(
          eq(tasksTable.caseId, casesTable.id),
          eq(tasksTable.assignedToId, usersTable.id),
          eq(tasksTable.taskType, TaskTypeEnum.JOIN_EVALUATION_AS_PSYCHOLOGIST),
          inArray(tasksTable.status, [
            TaskStatusEnum.PENDING,
            TaskStatusEnum.IN_PROGRESS,
          ])
        )
      )
      .where(
        and(
          eq(casesTable.isDeleted, false),
          eq(casesTable.isActive, true),
          isNotNull(casesTable.evaluationDueDate),
          gte(casesTable.evaluationDueDate, minTime),
          lte(casesTable.evaluationDueDate, maxTime),
          isNull(tasksTable.id)
        )
      );

    const proctorResults = await this.tx
      .select({
        caseId: casesTable.id,
        userId: usersTable.id,
        email: usersTable.email,
        fullName: usersTable.fullName,
        evaluationDueDate: casesTable.evaluationDueDate,
        roleName: userRolesTable.roleName,
      })
      .from(casesTable)
      .innerJoin(
        caseAssignmentsTable,
        and(
          eq(caseAssignmentsTable.caseId, casesTable.id),
          eq(caseAssignmentsTable.isDeleted, false)
        )
      )
      .innerJoin(usersTable, eq(usersTable.id, caseAssignmentsTable.userId))
      .innerJoin(
        userRolesTable,
        and(
          eq(userRolesTable.userId, usersTable.id),
          eq(userRolesTable.roleName, RoleEnum.PROCTOR)
        )
      )
      .leftJoin(
        tasksTable,
        and(
          eq(tasksTable.caseId, casesTable.id),
          eq(tasksTable.assignedToId, usersTable.id),
          eq(tasksTable.taskType, TaskTypeEnum.JOIN_EVALUATION_AS_PROCTOR),
          inArray(tasksTable.status, [
            TaskStatusEnum.PENDING,
            TaskStatusEnum.IN_PROGRESS,
          ])
        )
      )
      .where(
        and(
          eq(casesTable.isDeleted, false),
          eq(casesTable.isActive, true),
          isNotNull(casesTable.evaluationDueDate),
          gte(casesTable.evaluationDueDate, minTime),
          lte(casesTable.evaluationDueDate, maxTime),
          isNull(tasksTable.id)
        )
      );

    // Filter out null dates and add task types
    const psychologistTasks = psychologistResults
      .filter(
        (result): result is typeof result & { evaluationDueDate: string } =>
          result.evaluationDueDate !== null
      )
      .map((task) => ({
        ...task,
        taskType: TaskTypeEnum.JOIN_EVALUATION_AS_PSYCHOLOGIST,
      }));

    const proctorTasks = proctorResults
      .filter(
        (result): result is typeof result & { evaluationDueDate: string } =>
          result.evaluationDueDate !== null
      )
      .map((task) => ({
        ...task,
        taskType: TaskTypeEnum.JOIN_EVALUATION_AS_PROCTOR,
      }));

    return [...psychologistTasks, ...proctorTasks];
  }

  async getCasesForIEPMeetingTasks(
    hoursAhead = 48,
    bufferHours = 24
  ): Promise<CaseForIEPMeetingTasks[]> {
    const now = new Date();
    const minTime = new Date(now.getTime() + bufferHours * 60 * 60 * 1000);
    const maxTime = new Date(now.getTime() + hoursAhead * 60 * 60 * 1000);

    const results = await this.tx
      .select({
        caseId: casesTable.id,
        userId: usersTable.id,
        email: usersTable.email,
        fullName: usersTable.fullName,
        meetingDate: casesTable.meetingDate,
      })
      .from(casesTable)
      .innerJoin(
        caseAssignmentsTable,
        and(
          eq(caseAssignmentsTable.caseId, casesTable.id),
          eq(caseAssignmentsTable.isDeleted, false)
        )
      )
      .innerJoin(usersTable, eq(usersTable.id, caseAssignmentsTable.userId))
      .innerJoin(
        userRolesTable,
        and(
          eq(userRolesTable.userId, usersTable.id),
          eq(userRolesTable.roleName, RoleEnum.PSYCHOLOGIST)
        )
      )
      .leftJoin(
        tasksTable,
        and(
          eq(tasksTable.caseId, casesTable.id),
          eq(tasksTable.assignedToId, usersTable.id),
          eq(tasksTable.taskType, TaskTypeEnum.PREPARE_FOR_IEP_MEETING),
          inArray(tasksTable.status, [
            TaskStatusEnum.PENDING,
            TaskStatusEnum.IN_PROGRESS,
          ])
        )
      )
      .where(
        and(
          eq(casesTable.isDeleted, false),
          eq(casesTable.isActive, true),
          isNotNull(casesTable.meetingDate),
          gte(casesTable.meetingDate, minTime),
          lte(casesTable.meetingDate, maxTime),
          isNull(tasksTable.id)
        )
      );

    // Filter out any results where meetingDate is somehow null (extra safety)
    return results.filter(
      (result): result is typeof result & { meetingDate: string } =>
        result.meetingDate !== null
    );
  }
}

export { CaseRepository };
