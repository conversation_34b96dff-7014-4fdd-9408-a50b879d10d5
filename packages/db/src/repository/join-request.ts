import type { DatabaseType } from '@lilypad/db/client';
import { joinRequestsTable, type NewJoinRequest } from '@lilypad/db/schema';

class JoinRequestRepository {
  private db: DatabaseType;

  constructor(db: DatabaseType) {
    this.db = db;
  }

  async createJoinRequest(joinRequest: NewJoinRequest) {
    await this.db.transaction(async (tx) => {
      await tx.insert(joinRequestsTable).values(joinRequest);
    });
  }
}

export { JoinRequestRepository };
