import type { TransactionType } from '../config/client';
import { addressesTable } from '../schema';
import type { AddressTypeEnum } from '../schema/enums';
import type { Address, NewAddress } from '../schema/types';

export interface CreateAddressData {
  type: AddressTypeEnum;
  address: string;
  address2?: string;
  city: string;
  state: string;
  zipcode: string;
}

export class AddressError extends Error {
  code: string;

  constructor(message: string, code: string) {
    super(message);
    this.name = 'AddressError';
    this.code = code;
  }
}

export class AddressesRepository {
  private tx: TransactionType;

  constructor(tx: TransactionType) {
    this.tx = tx;
  }

  async createAddress(addressData: NewAddress): Promise<Address> {
    const [address] = await this.tx
      .insert(addressesTable)
      .values(addressData)
      .returning();

    if (!address) {
      throw new AddressError(
        'Failed to create address',
        'ADDRESS_CREATION_FAILED'
      );
    }

    return address;
  }
}
