import type { TransactionType } from '../config/client';
import { schoolsTable } from '../schema';
import type { SchoolTypeEnum } from '../schema/enums';

export interface CreateSchoolData {
  name: string;
  slug: string;
  type: SchoolTypeEnum;
  website?: string | null;
  ncesId: string;
  districtId: string;
  addressId: string;
}

export class SchoolsRepository {
  private tx: TransactionType;

  constructor(tx: TransactionType) {
    this.tx = tx;
  }

  async createSchool(data: CreateSchoolData) {
    const [school] = await this.tx
      .insert(schoolsTable)
      .values(data)
      .returning();

    if (!school) {
      throw new Error(`Failed to create school: ${data.name}`);
    }

    return school;
  }

  async createSchools(schools: CreateSchoolData[]) {
    if (schools.length === 0) {
      return [];
    }

    const createdSchools = await this.tx
      .insert(schoolsTable)
      .values(schools)
      .returning();

    return createdSchools;
  }

  async getSchools() {
    return await this.tx
      .select({
        id: schoolsTable.id,
        name: schoolsTable.name,
        districtId: schoolsTable.districtId,
      })
      .from(schoolsTable);
  }
}
