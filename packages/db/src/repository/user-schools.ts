import type { TransactionType } from '../config/client';
import { userSchoolsTable } from '../schema';

export class UserSchoolsRepository {
  private tx: TransactionType;
  constructor(tx: TransactionType) {
    this.tx = tx;
  }

  async createAssociations(
    associations: Array<{
      userId: string;
      schoolId: string;
    }>
  ) {
    if (associations.length === 0) {
      return [];
    }

    const created = await this.tx
      .insert(userSchoolsTable)
      .values(associations)
      .returning();

    return created;
  }
}
