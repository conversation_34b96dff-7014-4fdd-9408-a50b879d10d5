import { and, eq } from 'drizzle-orm';
import type { TransactionType } from '../config/client';
import { studentLanguagesTable } from '../schema';
import type { NewStudentLanguage } from '../schema/types';

export class StudentLanguagesRepository {
  private tx: TransactionType;
  constructor(tx: TransactionType) {
    this.tx = tx;
  }

  async createStudentLanguages(studentLanguages: NewStudentLanguage[]) {
    if (studentLanguages.length === 0) {
      return [];
    }

    return await this.tx
      .insert(studentLanguagesTable)
      .values(studentLanguages)
      .returning();
  }

  async getLanguagesByStudentId(studentId: string) {
    return await this.tx
      .select()
      .from(studentLanguagesTable)
      .where(eq(studentLanguagesTable.studentId, studentId));
  }

  async getPrimaryLanguageByStudentId(studentId: string) {
    const [language] = await this.tx
      .select()
      .from(studentLanguagesTable)
      .where(
        and(
          eq(studentLanguagesTable.studentId, studentId),
          eq(studentLanguagesTable.isPrimary, true)
        )
      )
      .limit(1);

    return language || null;
  }

  async hasStudentLanguage(studentId: string, languageId: string) {
    const [language] = await this.tx
      .select()
      .from(studentLanguagesTable)
      .where(
        and(
          eq(studentLanguagesTable.studentId, studentId),
          eq(studentLanguagesTable.languageId, languageId)
        )
      )
      .limit(1);

    return !!language;
  }
}
