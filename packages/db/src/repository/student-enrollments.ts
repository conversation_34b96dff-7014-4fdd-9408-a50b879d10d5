import { and, eq } from 'drizzle-orm';
import type { TransactionType } from '../config/client';
import { studentEnrollmentsTable } from '../schema';
import type { NewStudentEnrollment } from '../schema/types';

export class StudentEnrollmentsRepository {
  private tx: TransactionType;

  constructor(tx: TransactionType) {
    this.tx = tx;
  }

  async createEnrollments(enrollments: NewStudentEnrollment[]) {
    if (enrollments.length === 0) {
      return [];
    }

    return await this.tx
      .insert(studentEnrollmentsTable)
      .values(enrollments)
      .returning();
  }

  async getEnrollmentsByStudentId(studentId: string) {
    return await this.tx
      .select()
      .from(studentEnrollmentsTable)
      .where(eq(studentEnrollmentsTable.studentId, studentId));
  }

  async getEnrollmentsBySchoolId(schoolId: string) {
    return await this.tx
      .select()
      .from(studentEnrollmentsTable)
      .where(eq(studentEnrollmentsTable.schoolId, schoolId));
  }

  async isStudentEnrolledInSchool(studentId: string, schoolId: string) {
    const [enrollment] = await this.tx
      .select()
      .from(studentEnrollmentsTable)
      .where(
        and(
          eq(studentEnrollmentsTable.studentId, studentId),
          eq(studentEnrollmentsTable.schoolId, schoolId)
        )
      )
      .limit(1);

    return !!enrollment;
  }
}
