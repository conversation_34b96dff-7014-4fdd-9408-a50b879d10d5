import { eq, sql } from 'drizzle-orm';
import type { TransactionType } from '../config/client';
import {
  type ParentRelationshipEnum,
  parentsTable,
  studentParentsTable,
} from '../schema';
import type { NewParent, NewStudentParent } from '../schema/types';

export interface CreateParentData {
  firstName: string;
  middleName?: string | null;
  lastName: string;
  primaryEmail?: string | null;
  secondaryEmail?: string | null;
  primaryPhone?: string | null;
  secondaryPhone?: string | null;
  relationshipType: ParentRelationshipEnum;
}

export interface ParentSearchKey {
  firstName: string;
  lastName: string;
  identifier: string; // primaryEmail or primaryPhone
}

export class ParentsRepository {
  private tx: TransactionType;

  constructor(tx: TransactionType) {
    this.tx = tx;
  }

  async findExistingParents(searchKeys: ParentSearchKey[]) {
    if (searchKeys.length === 0) {
      return [];
    }

    return await this.tx
      .select()
      .from(parentsTable)
      .where(
        sql`(${parentsTable.firstName}, ${parentsTable.lastName}) IN (${sql.join(
          searchKeys.map((p) => sql`(${p.firstName}, ${p.lastName})`),
          sql`, `
        )})`
      );
  }

  async createParents(parentsData: CreateParentData[]) {
    if (parentsData.length === 0) {
      return [];
    }

    const insertData: NewParent[] = parentsData.map((parent) => ({
      firstName: parent.firstName,
      middleName: parent.middleName,
      lastName: parent.lastName,
      primaryEmail: parent.primaryEmail,
      secondaryEmail: parent.secondaryEmail,
      primaryPhone: parent.primaryPhone,
      secondaryPhone: parent.secondaryPhone,
      relationshipType: parent.relationshipType,
    }));

    return await this.tx
      .insert(parentsTable)
      .values(insertData)
      .returning({ id: parentsTable.id });
  }

  async createStudentParentRelationships(relationships: NewStudentParent[]) {
    if (relationships.length === 0) {
      return [];
    }

    return await this.tx
      .insert(studentParentsTable)
      .values(relationships)
      .returning();
  }

  async getParentById(id: string) {
    const [parent] = await this.tx
      .select()
      .from(parentsTable)
      .where(eq(parentsTable.id, id))
      .limit(1);

    return parent || null;
  }
}
