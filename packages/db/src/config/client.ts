/** biome-ignore-all lint/performance/noNamespaceImport: Fix later */
import { getSupabaseServerClient } from '@lilypad/supabase/server';
import type { DrizzleConfig } from 'drizzle-orm';
import { drizzle } from 'drizzle-orm/node-postgres';
import { Pool } from 'pg';
import { keys } from '../../keys';
import * as schema from '../schema';
import { createDrizzle } from './drizzle';

export * from 'drizzle-orm';

const config = {
  casing: 'snake_case',
  schema,
} satisfies DrizzleConfig<typeof schema>;

// Protected by RLS
const poolClient = new Pool({ connectionString: keys().DATABASE_URL });
export const db = drizzle(poolClient, config);

// Bypass RLS
const poolAdmin = new Pool({ connectionString: keys().ADMIN_DATABASE_URL });
export const dbAdmin = drizzle(poolAdmin, config);

async function getSession() {
  const supabase = await getSupabaseServerClient();
  const {
    data: { session },
  } = await supabase.auth.getSession();
  return session;
}

export async function createDatabaseClient(params = { admin: false }) {
  if (params.admin) {
    return dbAdmin;
  }

  const session = await getSession();

  const token = session?.access_token ?? '';

  const client = createDrizzle(token, db);

  return client;
}

export type DatabaseType = Awaited<ReturnType<typeof createDatabaseClient>>;
export type TransactionType = Parameters<
  Parameters<DatabaseType['transaction']>[0]
>[0];
