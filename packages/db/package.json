{"name": "@lilypad/db", "private": true, "version": "0.0.0", "type": "module", "scripts": {"clean": "git clean -xdf node_modules", "format": "biome format --write .", "lint": "biome lint --write .", "typecheck": "tsc --noEmit", "generate": "drizzle-kit generate --config=drizzle.config.ts --name=migration", "migrate": "drizzle-kit migrate --config=drizzle.config.ts", "push": "drizzle-kit push --config=drizzle.config.ts", "studio": "drizzle-kit studio --config=drizzle.config.ts"}, "dependencies": {"@lilypad/shared": "workspace:*", "@t3-oss/env-nextjs": "^0.13.4", "drizzle-orm": "^0.43.1", "drizzle-zod": "^0.8.2", "jwt-decode": "^4.0.0", "pg": "^8.16.0", "zod": "^3.25.7"}, "devDependencies": {"@lilypad/supabase": "workspace:*", "@lilypad/typescript": "workspace:*", "@types/node": "^22.15.3", "@types/pg": "^8.15.2", "drizzle-kit": "^0.31.1"}, "exports": {".": "./src/index.ts", "./keys": "./keys.ts", "./client": "./src/config/client.ts", "./schema": "./src/schema/index.ts", "./auth-schema": "./src/schema/auth.ts", "./schema/*": "./src/schema/*.ts", "./repository": "./src/repository/index.ts", "./repository/*": "./src/repository/*.ts", "./types": "./src/schema/types.ts", "./utils": "./src/utils/index.ts", "./enums": "./src/schema/enums.ts", "./policies": "./src/policies/index.ts"}}