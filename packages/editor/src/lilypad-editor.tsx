import './styles/index.css';
import { Separator } from '@lilypad/ui/components/separator';
import { cn } from '@lilypad/ui/lib/utils';
import type { Content, Editor } from '@tiptap/react';
import { EditorContent } from '@tiptap/react';
import { LinkBubbleMenu } from './components/bubble-menu/link-bubble-menu';
import { MeasuredContainer } from './components/measured-container';
import { SectionFive } from './components/section/five';
import { SectionFour } from './components/section/four';
import { SectionOne } from './components/section/one';
import { SectionThree } from './components/section/three';
import { SectionTwo } from './components/section/two';
import type { UseLilypadEditorProps } from './hooks/use-minimal-tiptap';
import { useLilypadEditor } from './hooks/use-minimal-tiptap';

export interface LilypadProps extends Omit<UseLilypadEditorProps, 'onUpdate'> {
  value?: Content;
  onChange?: (value: Content) => void;
  className?: string;
  editorContentClassName?: string;
}

const Toolbar = ({ editor }: { editor: Editor }) => (
  <div className="flex h-12 shrink-0 overflow-x-auto border-border border-b p-2">
    <div className="flex w-max items-center gap-px">
      <SectionOne activeLevels={[1, 2, 3, 4, 5, 6]} editor={editor} />

      <Separator className="mx-2" orientation="vertical" />

      <SectionTwo
        activeActions={[
          'bold',
          'italic',
          'underline',
          'strikethrough',
          'code',
          'clearFormatting',
        ]}
        editor={editor}
        mainActionCount={3}
      />

      <Separator className="mx-2" orientation="vertical" />

      <SectionThree editor={editor} />

      <Separator className="mx-2" orientation="vertical" />

      <SectionFour
        activeActions={['orderedList', 'bulletList']}
        editor={editor}
        mainActionCount={0}
      />

      <Separator className="mx-2" orientation="vertical" />

      <SectionFive
        activeActions={['codeBlock', 'blockquote', 'horizontalRule']}
        editor={editor}
        mainActionCount={0}
      />
    </div>
  </div>
);

export const LilypadEditor = ({
  value,
  onChange,
  className,
  editorContentClassName,
  ...props
}: LilypadProps) => {
  const editor = useLilypadEditor({
    value,
    onUpdate: onChange,
    ...props,
  });

  if (!editor) {
    return null;
  }

  return (
    <MeasuredContainer
      as="div"
      className={cn(
        'flex h-auto w-full flex-col rounded-md border border-input shadow-xs focus-within:border-primary min-data-[orientation=vertical]:h-72',
        className
      )}
      name="editor"
    >
      <Toolbar editor={editor} />
      <EditorContent
        className={cn('lilypad-editor', editorContentClassName)}
        editor={editor}
      />
      <LinkBubbleMenu editor={editor} />
    </MeasuredContainer>
  );
};

LilypadEditor.displayName = 'LilypadEditor';

export default LilypadEditor;
