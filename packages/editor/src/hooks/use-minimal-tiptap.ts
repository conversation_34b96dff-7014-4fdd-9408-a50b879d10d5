import { toast } from '@lilypad/ui/components/sonner';
import { cn } from '@lilypad/ui/lib/utils';
import { Placeholder } from '@tiptap/extension-placeholder';
import { TextStyle } from '@tiptap/extension-text-style';
import { Typography } from '@tiptap/extension-typography';
import { Underline } from '@tiptap/extension-underline';
import type { Content, Editor, UseEditorOptions } from '@tiptap/react';
import { useEditor } from '@tiptap/react';
import { StarterKit } from '@tiptap/starter-kit';
import React from 'react';
import {
  CodeBlockLowlight,
  Color,
  FileHandler,
  HorizontalRule,
  Image,
  Link,
  ResetMarksOnEnter,
  Selection,
  UnsetAllMarks,
} from '../extensions';
import { useThrottle } from '../hooks/use-throttle';
import { fileToBase64, getOutput, randomId } from '../utils';

export interface UseLilypadEditorProps extends UseEditorOptions {
  value?: Content;
  output?: 'html' | 'json' | 'text';
  placeholder?: string;
  editorClassName?: string;
  throttleDelay?: number;
  onUpdate?: (content: Content) => void;
  onBlur?: (content: Content) => void;
}

const createExtensions = (placeholder: string) => [
  StarterKit.configure({
    horizontalRule: false,
    codeBlock: false,
    paragraph: { HTMLAttributes: { class: 'text-node' } },
    heading: { HTMLAttributes: { class: 'heading-node' } },
    blockquote: { HTMLAttributes: { class: 'block-node' } },
    bulletList: { HTMLAttributes: { class: 'list-node' } },
    orderedList: { HTMLAttributes: { class: 'list-node' } },
    code: { HTMLAttributes: { class: 'inline', spellcheck: 'false' } },
    dropcursor: { width: 2, class: 'ProseMirror-dropcursor border' },
  }),
  Link,
  Underline,
  Image.configure({
    allowedMimeTypes: ['image/*'],
    maxFileSize: 5 * 1024 * 1024,
    allowBase64: true,
    uploadFn: async (file) => {
      // NOTE: This is a fake upload function. Replace this with your own upload logic.
      // This function should return the uploaded image URL.

      // wait 3s to simulate upload
      await new Promise((resolve) => setTimeout(resolve, 3000));

      const src = await fileToBase64(file);

      // either return { id: string | number, src: string } or just src
      // return src;
      return { id: randomId(), src };
    },
    onToggle(editor, files, pos) {
      editor.commands.insertContentAt(
        pos,
        files.map((image) => {
          const blobUrl = URL.createObjectURL(image);
          const id = randomId();

          return {
            type: 'image',
            attrs: {
              id,
              src: blobUrl,
              alt: image.name,
              title: image.name,
              fileName: image.name,
            },
          };
        })
      );
    },
    onImageRemoved({ id, src }) {
      console.log('onImageRemoved', id, src);
    },
    onValidationError(errors) {
      for (const error of errors) {
        toast.error('Image validation error', {
          position: 'bottom-right',
          description: error.reason,
        });
      }
    },
    onActionSuccess({ action }) {
      const mapping = {
        copyImage: 'Copy Image',
        copyLink: 'Copy Link',
        download: 'Download',
      };
      toast.success(mapping[action], {
        position: 'bottom-right',
        description: 'Image action success',
      });
    },
    onActionError(error, { action }) {
      const mapping = {
        copyImage: 'Copy Image',
        copyLink: 'Copy Link',
        download: 'Download',
      };
      toast.error(`Failed to ${mapping[action]}`, {
        position: 'bottom-right',
        description: error.message,
      });
    },
  }),
  FileHandler.configure({
    allowBase64: true,
    allowedMimeTypes: ['image/*'],
    maxFileSize: 5 * 1024 * 1024,
    onDrop: async (editor, files, pos) => {
      for (const file of files) {
        // biome-ignore lint/nursery/noAwaitInLoop: Fix later
        const src = await fileToBase64(file);
        editor.commands.insertContentAt(pos, {
          type: 'image',
          attrs: { src },
        });
      }
    },
    onPaste: async (editor, files) => {
      for (const file of files) {
        // biome-ignore lint/nursery/noAwaitInLoop: Fix later
        const src = await fileToBase64(file);
        editor.commands.insertContent({
          type: 'image',
          attrs: { src },
        });
      }
    },
    onValidationError: (errors) => {
      for (const error of errors) {
        toast.error('Image validation error', {
          position: 'bottom-right',
          description: error.reason,
        });
      }
    },
  }),
  Color,
  TextStyle,
  Selection,
  Typography,
  UnsetAllMarks,
  HorizontalRule,
  ResetMarksOnEnter,
  CodeBlockLowlight,
  Placeholder.configure({ placeholder: () => placeholder }),
];

export const useLilypadEditor = ({
  value,
  output = 'html',
  placeholder = '',
  editorClassName,
  throttleDelay = 0,
  onUpdate,
  onBlur,
  ...props
}: UseLilypadEditorProps) => {
  const throttledSetValue = useThrottle(
    (_value: Content) => onUpdate?.(_value),
    throttleDelay
  );

  const handleUpdate = React.useCallback(
    (_editor: Editor) => throttledSetValue(getOutput(_editor, output)),
    [output, throttledSetValue]
  );

  const handleCreate = React.useCallback(
    (_editor: Editor) => {
      if (value && _editor.isEmpty) {
        _editor.commands.setContent(value);
      }
    },
    [value]
  );

  const handleBlur = React.useCallback(
    (_editor: Editor) => onBlur?.(getOutput(_editor, output)),
    [output, onBlur]
  );

  const editor = useEditor({
    extensions: createExtensions(placeholder),
    editorProps: {
      attributes: {
        autocomplete: 'off',
        autocorrect: 'off',
        autocapitalize: 'off',
        class: cn('focus:outline-hidden', editorClassName),
      },
    },
    onUpdate: ({ editor: _editor }) => handleUpdate(_editor),
    onCreate: ({ editor: _editor }) => handleCreate(_editor),
    onBlur: ({ editor: _editor }) => handleBlur(_editor),
    ...props,
  });

  return editor;
};

export default useLilypadEditor;
