import { cn } from '@lilypad/ui/lib/utils';
import type * as React from 'react';
import { getShortcutKey } from '../utils';

export interface ShortcutKeyProps extends React.ComponentProps<'span'> {
  keys: string[];
}

export const ShortcutKey = ({
  ref,
  className,
  keys,
  ...props
}: ShortcutKeyProps) => {
  const modifiedKeys = keys.map((key) => getShortcutKey(key));
  const ariaLabel = modifiedKeys
    .map((shortcut) => shortcut.readable)
    .join(' + ');

  return (
    // biome-ignore lint/a11y/useAriaPropsSupportedByRole: Fix later
    <span
      aria-label={ariaLabel}
      className={cn('inline-flex items-center gap-0.5', className)}
      ref={ref}
      {...props}
    >
      {modifiedKeys.map((shortcut) => (
        <kbd
          {...props}
          className={cn(
            'inline-block min-w-2.5 text-center align-baseline font-medium font-sans text-[rgb(156,157,160)] text-xs capitalize',

            className
          )}
          key={shortcut.symbol}
          ref={ref}
        >
          {shortcut.symbol}
        </kbd>
      ))}
    </span>
  );
};

ShortcutKey.displayName = 'ShortcutKey';
