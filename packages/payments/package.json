{"name": "@lilypad/payments", "private": true, "version": "0.0.0", "type": "module", "scripts": {"clean": "git clean -xdf node_modules", "format": "biome format --write .", "lint": "biome lint --write .", "typecheck": "tsc --noEmit"}, "devDependencies": {"@lilypad/typescript": "workspace:*", "@types/node": "^22.15.3"}, "dependencies": {"@stripe/stripe-js": "^7.3.0", "@t3-oss/env-nextjs": "^0.13.4", "client-only": "^0.0.1", "server-only": "^0.0.1", "stripe": "^18.1.1", "zod": "^3.25.7"}, "exports": {"./keys": "./keys.ts", "./types": "./src/types.ts", "./stripe-client": "./src/stripe-client.ts", "./stripe-server": "./src/stripe-server.ts", "./subscription": "./src/subscription.ts"}}