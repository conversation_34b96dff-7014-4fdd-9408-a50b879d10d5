import { keys } from '../keys';
import { type Subscription, Tier } from './types';

export function selectSubscription(
  subscriptions: Subscription[]
): Subscription | undefined {
  if (subscriptions && subscriptions.length > 0) {
    if (subscriptions.length > 1) {
      console.warn(
        'Multiple subscriptions found. Selecting the first subscription.'
      );
    }

    return subscriptions[0];
  }

  return;
}

export function mapSubscriptionToTier(subscription?: Subscription): Tier {
  if (
    subscription?.plan?.product &&
    subscription.plan.product === keys().BILLING_PRO_PRODUCT_ID
  ) {
    return subscription.cancel_at_period_end ? Tier.ProPendingCancel : Tier.Pro;
  }

  return Tier.Free;
}
