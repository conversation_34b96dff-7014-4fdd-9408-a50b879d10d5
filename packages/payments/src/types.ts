import type Stripe from 'stripe';

export type Subscription = Stripe.Subscription & {
  plan?: Stripe.Plan;
};

// biome-ignore lint/style/noEnum: Can be used as a type
enum BillingUnit {
  PerSeat = 'per_seat',
  PerOrganization = 'per_organization',
}

// biome-ignore lint/style/noEnum: Can be used as a type
enum Tier {
  Free = 'free',
  Pro = 'pro',
  ProPendingCancel = 'pro-pending-cancel',
}

export { BillingUnit, Tier };
