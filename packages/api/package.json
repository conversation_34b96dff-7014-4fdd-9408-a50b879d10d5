{"name": "@lilypad/api", "private": true, "version": "0.0.0", "type": "module", "scripts": {"clean": "git clean -xdf node_modules", "format": "biome format --write .", "lint": "biome lint --write .", "typecheck": "tsc --noEmit"}, "devDependencies": {"@lilypad/typescript": "workspace:*"}, "dependencies": {"@lilypad/core": "workspace:*", "@lilypad/db": "workspace:*", "@lilypad/jobs": "workspace:*", "@lilypad/kv": "workspace:*", "@lilypad/shared": "workspace:*", "@lilypad/supabase": "workspace:*", "@tanstack/react-query": "^5.81.2", "@trpc/client": "^11.4.2", "@trpc/server": "^11.4.2", "@trpc/tanstack-react-query": "^11.4.2", "@types/react": "19.1.0", "client-only": "^0.0.1", "react": "^19.1.0", "server-only": "^0.0.1", "superjson": "^2.2.2", "zod": "^3.25.7"}, "exports": {"./server": "./src/core/server.tsx", "./client": "./src/core/client.tsx", "./schemas/students": "./src/schemas/students.ts"}}