import {
  DocumentCategoryEnum,
  GenderEnum,
  ParentRelationshipEnum,
  SchoolGradeEnum,
} from '@lilypad/db/enums';
import z from 'zod';

export const getStudentsInputSchema = z.object({
  page: z.number().min(1).optional(),
  perPage: z.number().min(1).max(100).optional(),
  search: z.string().optional(),
  filters: z
    .array(
      z.object({
        id: z.string(),
        value: z.unknown(),
        isMulti: z.boolean().optional(),
      })
    )
    .optional(),
  joinOperator: z.enum(['and', 'or']).optional(),
  sort: z
    .array(
      z.object({
        id: z.string(),
        desc: z.boolean(),
      })
    )
    .optional(),
});

export const parentSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  middleName: z.string().optional(),
  lastName: z.string().min(1, 'Last name is required'),
  relationshipType: z.nativeEnum(ParentRelationshipEnum),
  primaryEmail: z.string().email().optional(),
  secondaryEmail: z.string().email().optional(),
  primaryPhone: z.string().optional(),
  secondaryPhone: z.string().optional(),
  isPrimaryContact: z.boolean(),
  hasPickupAuthorization: z.boolean(),
});

// Serializable file schema for API transmission
export const serializableFileSchema = z.object({
  name: z.string(),
  size: z.number(),
  type: z.string(),
  data: z.string(), // Base64 encoded file content
});

export const documentSchema = z.object({
  file: z.union([
    z.instanceof(File), // For client-side validation
    serializableFileSchema, // For server transmission
  ]),
  category: z
    .nativeEnum(DocumentCategoryEnum)
    .default(DocumentCategoryEnum.BACKGROUND),
});

export const studentSchema = z
  .object({
    id: z.string(), // Temporary ID for tracking
    studentIdNumber: z.string().min(1, 'Student ID is required'),
    firstName: z.string().min(1, 'First name is required'),
    middleName: z.string().optional(),
    lastName: z.string().min(1, 'Last name is required'),
    preferredName: z.string().min(1, 'Preferred name is required'),
    dateOfBirth: z.date({
      required_error: 'Date of birth is required',
    }),
    dateOfConsent: z.date({
      required_error: 'Date of consent is required',
    }),
    grade: z.nativeEnum(SchoolGradeEnum, {
      required_error: 'Grade is required',
    }),
    gender: z.nativeEnum(GenderEnum, {
      required_error: 'Gender is required',
    }),
    primarySchoolId: z.string().min(1, 'School is required'),
    languageIds: z
      .array(z.string())
      .min(1, 'At least one language is required'),
    primaryLanguageId: z.string().nullable().default(null),
    parents: z
      .array(parentSchema)
      .min(1, 'At least one parent/guardian is required'),
    documents: z.array(documentSchema).default([]),
    specialNeedsIndicator: z.boolean().default(false),
  })
  .refine(
    (data) => {
      // If there are languages selected, primaryLanguageId must be set
      if (data.languageIds.length > 0 && !data.primaryLanguageId) {
        return false;
      }
      // If primaryLanguageId is set, it must be one of the selected languages
      if (
        data.primaryLanguageId &&
        !data.languageIds.includes(data.primaryLanguageId)
      ) {
        return false;
      }
      return true;
    },
    {
      message:
        'Primary language must be selected and must be one of the selected languages',
      path: ['primaryLanguageId'],
    }
  );

export const bulkStudentSchema = z.object({
  students: z.array(studentSchema).min(1, 'At least one student is required'),
});

// Keep the old name for backwards compatibility
export const bulkCreateStudentsInputSchema = bulkStudentSchema;

export const studentSaveResultSchema = z.object({
  tempId: z.string(),
  success: z.boolean(),
  studentId: z.string().optional(),
  error: z.string().optional(),
});

export type SerializableFile = z.infer<typeof serializableFileSchema>;
export type Parent = z.infer<typeof parentSchema>;
export type Document = z.infer<typeof documentSchema>;
export type Student = z.infer<typeof studentSchema>;
export type BulkStudent = z.infer<typeof bulkStudentSchema>;
export type StudentSaveResult = z.infer<typeof studentSaveResultSchema>;

export type SerializableDocument = {
  file: SerializableFile;
  category: DocumentCategoryEnum;
};

export type BulkCreateStudentsInput = z.infer<
  typeof bulkCreateStudentsInputSchema
>;
