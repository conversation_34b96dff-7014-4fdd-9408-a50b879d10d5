import {
  TaskHistoryActionEnum,
  TaskPriorityEnum,
  TaskStatusEnum,
  TaskTypeEnum,
} from '@lilypad/db/enums';
import { z } from 'zod';

// Input schemas for queries
export const getTasksInputSchema = z.object({
  search: z.string().optional(),
  filters: z
    .array(
      z.object({
        id: z.string(),
        value: z.unknown(),
        variant: z.string(),
        operator: z.string(),
        filterId: z.string(),
      })
    )
    .optional(),
  joinOperator: z.enum(['and', 'or']).optional(),
  sort: z
    .array(
      z.object({
        id: z.string(),
        desc: z.boolean(),
      })
    )
    .optional(),
});

export const getTaskByIdInputSchema = z.string().uuid();

export const getTaskHistoryInputSchema = z.string().uuid();

export const getTaskDependenciesInputSchema = z.string().uuid();

// Mutation schemas - Keep only the ones we actually need (not creation)
export const updateTaskStatusInputSchema = z.object({
  taskId: z.string().uuid(),
  status: z.nativeEnum(TaskStatusEnum),
  notes: z.string().optional(),
});

export const updateTaskPriorityInputSchema = z.object({
  taskId: z.string().uuid(),
  priority: z.nativeEnum(TaskPriorityEnum),
  notes: z.string().optional(),
});

export const updateTaskDueDateInputSchema = z.object({
  taskId: z.string().uuid(),
  dueDate: z.date().nullable(),
  notes: z.string().optional(),
});

export const rejectTaskInputSchema = z.object({
  taskId: z.string().uuid(),
  reason: z.string().min(1, 'Rejection reason is required'),
});

export const reassignTaskInputSchema = z.object({
  taskId: z.string().uuid(),
  newAssigneeId: z.string().uuid(),
  notes: z.string().optional(),
});

export const completeTaskInputSchema = z.object({
  taskId: z.string().uuid(),
  notes: z.string().optional(),
});

export const createTaskDependencyInputSchema = z.object({
  predecessorTaskId: z.string().uuid(),
  successorTaskId: z.string().uuid(),
});

export const getUrgentTasksInputSchema = z.object({
  assignedToName: z.string().optional(), // Changed from userId to assignedToName for search
  limit: z.number().min(1).max(50).default(10),
});

export const getOverdueTasksInputSchema = z.object({
  assignedToName: z.string().optional(), // Changed from userId to assignedToName for search
  limit: z.number().min(1).max(50).default(10),
});

// Response schemas
export const taskHistoryEntrySchema = z.object({
  id: z.string(),
  action: z.nativeEnum(TaskHistoryActionEnum),
  previousStatus: z.nativeEnum(TaskStatusEnum).nullable(),
  newStatus: z.nativeEnum(TaskStatusEnum).nullable(),
  createdAt: z.date(),
  user: z.object({
    id: z.string(),
    fullName: z.string(),
    email: z.string(),
  }),
});

export const taskDependencySchema = z.object({
  id: z.string(),
  predecessorTaskId: z.string(),
  successorTaskId: z.string(),
  createdAt: z.date(),
  predecessorTask: z.object({
    id: z.string(),
    taskType: z.nativeEnum(TaskTypeEnum),
    status: z.nativeEnum(TaskStatusEnum),
    priority: z.nativeEnum(TaskPriorityEnum),
    dueDate: z.date().nullable(),
    completedAt: z.date().nullable(),
  }),
  successorTask: z.object({
    id: z.string(),
    taskType: z.nativeEnum(TaskTypeEnum),
    status: z.nativeEnum(TaskStatusEnum),
    priority: z.nativeEnum(TaskPriorityEnum),
    dueDate: z.date().nullable(),
    completedAt: z.date().nullable(),
  }),
});

// Type exports
export type GetTasksInput = z.infer<typeof getTasksInputSchema>;
export type UpdateTaskStatusInput = z.infer<typeof updateTaskStatusInputSchema>;
export type UpdateTaskPriorityInput = z.infer<
  typeof updateTaskPriorityInputSchema
>;
export type UpdateTaskDueDateInput = z.infer<
  typeof updateTaskDueDateInputSchema
>;
export type RejectTaskInput = z.infer<typeof rejectTaskInputSchema>;
export type ReassignTaskInput = z.infer<typeof reassignTaskInputSchema>;
export type CompleteTaskInput = z.infer<typeof completeTaskInputSchema>;
export type CreateTaskDependencyInput = z.infer<
  typeof createTaskDependencyInputSchema
>;
export type GetUrgentTasksInput = z.infer<typeof getUrgentTasksInputSchema>;
export type GetOverdueTasksInput = z.infer<typeof getOverdueTasksInputSchema>;
export type TaskHistoryEntry = z.infer<typeof taskHistoryEntrySchema>;
export type TaskDependency = z.infer<typeof taskDependencySchema>;
