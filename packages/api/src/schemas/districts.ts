import { z } from 'zod';
import {
  AddressTypeEnum,
  DistrictTypeEnum,
  RoleEnum,
  SchoolTypeEnum,
} from '@lilypad/db/enums';
import { dayScheduleSchema } from './district-availability';
import { addressSchema } from './addresses';
import type { CreateDistrictOnboardingInput } from '@lilypad/core/services/district-onboarding';

export const getDistrictsInputSchema = z.object({
  page: z.number().min(1).optional(),
  perPage: z.number().min(1).max(100).optional(),
  search: z.string().optional(),
  filters: z
    .array(
      z.object({
        id: z.string(),
        value: z.unknown(),
        isMulti: z.boolean().optional(),
      })
    )
    .optional(),
  joinOperator: z.enum(['and', 'or']).optional(),
  sort: z
    .array(
      z.object({
        id: z.string(),
        desc: z.boolean(),
      })
    )
    .optional(),
});

export const createDistrictOnboardingSchema = z.object({
  district: z.object({
    generalInfo: z.object({
      name: z.string(),
      slug: z.string(),
      type: z.nativeEnum(DistrictTypeEnum),
      website: z.string(),
      ncesId: z.string(),
      stateId: z.string(),
      county: z.string(),
      numSchools: z.number().optional(),
      numStudents: z.number().optional(),
      invoiceEmail: z.string(),
    }),
    address: z.object({
      type: z.nativeEnum(AddressTypeEnum),
      address: z.string(),
      address2: z.string().optional(),
      city: z.string(),
      state: z.string(),
      zipcode: z.string(),
    }),
    availabilities: z.object({
      availabilitySchedule: z.object({
        monday: dayScheduleSchema,
        tuesday: dayScheduleSchema,
        wednesday: dayScheduleSchema,
        thursday: dayScheduleSchema,
        friday: dayScheduleSchema,
        saturday: dayScheduleSchema,
        sunday: dayScheduleSchema,
      }),
      timezone: z.string(),
    }),
    preferences: z.object({
      chromebookSetup: z.boolean(),
      quietSpaceForEvaluation: z.boolean(),
      internetStability: z.boolean(),
    }),
  }),
  schools: z.array(
    z.object({
      tempId: z.string(),
      generalInfo: z.object({
        name: z.string(),
        slug: z.string(),
        type: z.nativeEnum(SchoolTypeEnum),
        website: z.string().optional(),
        ncesId: z.string(),
      }),
      addresses: z.object({
        physical: addressSchema,
        postal: addressSchema.extend({
          sameAsPhysical: z.boolean().optional(),
        }),
      }),
    })
  ),
  members: z.array(
    z.object({
      firstName: z.string(),
      lastName: z.string(),
      email: z.string().email(),
      role: z.nativeEnum(RoleEnum),
      schoolIds: z.array(z.string()),
    })
  ),
  currentUserId: z.string(),
}) satisfies z.ZodType<CreateDistrictOnboardingInput>;
