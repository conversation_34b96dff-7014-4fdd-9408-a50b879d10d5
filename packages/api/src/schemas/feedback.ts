import { FeedbackIssueTypeEnum, FeedbackTypeEnum } from '@lilypad/db/enums';
import { z } from 'zod';

export const bugFeedbackInput = z.object({
  type: z.nativeEnum(FeedbackTypeEnum, {
    errorMap: () => ({ message: 'Please select a type' }),
  }),
  description: z
    .string()
    .min(10, 'Description must be at least 10 characters')
    .max(1000, 'Description must be less than 1000 characters'),
  rating: z.number().min(1).max(5).optional(),
  issueType: z.nativeEnum(FeedbackIssueTypeEnum, {
    errorMap: () => ({ message: 'Please select an issue type' }),
  }),
  fileUrls: z.array(z.string()).optional(),
});

export const featureRequestInput = z.object({
  type: z.nativeEnum(FeedbackTypeEnum, {
    errorMap: () => ({ message: 'Please select a type' }),
  }),
  description: z
    .string()
    .min(10, 'Description must be at least 10 characters')
    .max(1000, 'Description must be less than 1000 characters'),
  rating: z.number().min(1).max(5).optional(),
  fileUrls: z.array(z.string()).optional(),
});

export const generalFeedbackInput = z.object({
  type: z.nativeEnum(FeedbackTypeEnum, {
    errorMap: () => ({ message: 'Please select a type' }),
  }),
  description: z
    .string()
    .min(10, 'Description must be at least 10 characters')
    .max(1000, 'Description must be less than 1000 characters'),
  rating: z.number().min(1).max(5).optional(),
  fileUrls: z.array(z.string()).optional(),
});
