import 'server-only';

import { HydrationBoundary, dehydrate } from '@tanstack/react-query';
import {
  type TRPCQueryOptions,
  createTRPCOptionsProxy,
} from '@trpc/tanstack-react-query';
import { cache } from 'react';
import { appRouter } from '../routers/_app';
import { createTRPCContext } from './init';
import { createQueryClient } from './query-client';

export const getQueryClient = cache(createQueryClient);

export const trpc = createTRPCOptionsProxy({
  ctx: createTRPCContext,
  router: appRouter,
  queryClient: getQueryClient,
});

export const caller = appRouter.createCaller(createTRPCContext);

export function HydrateClient(props: { children: React.ReactNode }) {
  const queryClient = getQueryClient();
  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      {props.children}
    </HydrationBoundary>
  );
}

// biome-ignore lint/suspicious/noExplicitAny: This is a workaround to avoid type errors
export function prefetch<T extends ReturnType<TRPCQueryOptions<any>>>(
  queryOptions: T
) {
  const queryClient = getQueryClient();
  if (queryOptions.queryKey[1]?.type === 'infinite') {
    // biome-ignore lint/suspicious/noExplicitAny: This is a workaround to avoid type errors
    queryClient.prefetchInfiniteQuery(queryOptions as any);
  } else {
    queryClient.prefetchQuery(queryOptions);
  }
}
