import { createDatabaseClient } from '@lilypad/db/client';
import { getSupabaseServerClient } from '@lilypad/supabase/server';
import { TRPCError, initTRPC } from '@trpc/server';
import superjson from 'superjson';
import { ZodError } from 'zod';

// const IS_SECURE = new URL(baseUrl.App).protocol === 'https:';

// const getClientIp = (headers: Headers) => {
//   const FALLBACK_IP_ADDRESS = '*********';
//   const forwardedFor = headers.get('x-forwarded-for');

//   if (forwardedFor) {
//     return forwardedFor.split(',')[0] ?? FALLBACK_IP_ADDRESS;
//   }

//   return headers.get('x-real-ip') ?? FALLBACK_IP_ADDRESS;
// };

export const createTRPCContext = async () => {
  const db = await createDatabaseClient();
  const supabase = await getSupabaseServerClient();

  return {
    db,
    supabase,
  };
};

export type Context = Awaited<ReturnType<typeof createTRPCContext>>;

const t = initTRPC.context<Context>().create({
  transformer: superjson,
  errorFormatter({ shape, error }) {
    return {
      ...shape,
      data: {
        ...shape.data,
        zodError:
          error.cause instanceof ZodError ? error.cause.flatten() : null,
      },
    };
  },
});

export const createTRPCRouter = t.router;
export const createCallerFactory = t.createCallerFactory;
export const baseProcedure = t.procedure;

// const rateLimitedProcedure = t.procedure.use(
// 	async ({ ctx, next, meta, path }) => {
// 		if (IS_SECURE) {
// 			const ip = ctx.ip;
// 			const uniqueIdentifier = `${ip}-${path}`;
// 			const { success, remaining } = await ratelimit.limit(uniqueIdentifier);

// 			if (!success) {
// 				logger.error({ remaining }, "Rate limit exceeded");
// 				throw new TRPCError({ code: "TOO_MANY_REQUESTS" });
// 			}

// 			return next();
// 		}

// 		return next();
// 	},
// );

export const authenticatedProcedure = baseProcedure.use(
  async ({ ctx, next }) => {
    const {
      data: { session },
    } = await ctx.supabase.auth.getSession();

    if (!session) {
      throw new TRPCError({ code: 'UNAUTHORIZED' });
    }

    const user = session.user;

    return next({
      ctx: {
        ...ctx,
        user,
      },
    });
  }
);
