import { CaseRepository } from '@lilypad/db/repository/cases';
import { z } from 'zod';
import { authenticatedProcedure, createTRPCRouter } from '../core/init';

export const casesRouter = createTRPCRouter({
  getCasesByStudentId: authenticatedProcedure
    .input(z.string())
    .query(async ({ ctx, input }) => {
      return await ctx.db.transaction(async (tx) => {
        const caseRepository = new CaseRepository(tx);
        return await caseRepository.getCompleteCasesByStudentId(input);
      });
    }),
});
