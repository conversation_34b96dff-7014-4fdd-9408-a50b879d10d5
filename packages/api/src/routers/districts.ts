import { DistrictOnboardingService } from '@lilypad/core/services/district-onboarding';
import { DistrictsRepository } from '@lilypad/db/repository/districts';
import type {
  GetDistrictsParams,
  GetDistrictsResult,
} from '@lilypad/db/repository/types/districts';
import { z } from 'zod';
import { inngest } from '@lilypad/jobs/client';
import { Events } from '@lilypad/jobs/events';

import { authenticatedProcedure, createTRPCRouter } from '../core/init';
import {
  createDistrictOnboardingSchema,
  getDistrictsInputSchema,
} from '../schemas/districts';

export const districtsRouter = createTRPCRouter({
  getDistricts: authenticatedProcedure
    .input(getDistrictsInputSchema)
    .query(async ({ ctx, input }): Promise<GetDistrictsResult> => {
      const result = await ctx.db.transaction(async (tx) => {
        const repository = new DistrictsRepository(tx);
        return await repository.getDistricts(input as GetDistrictsParams);
      });

      return result;
    }),

  getDistrictById: authenticatedProcedure
    .input(z.string())
    .query(async ({ ctx, input }) => {
      const result = await ctx.db.transaction(async (tx) => {
        const repository = new DistrictsRepository(tx);
        return await repository.getDistrictById(input);
      });

      return result;
    }),

  getDistrictBySlug: authenticatedProcedure
    .input(z.string())
    .query(async ({ ctx, input }) => {
      const result = await ctx.db.transaction(async (tx) => {
        const repository = new DistrictsRepository(tx);
        return await repository.getDistrictBySlug(input);
      });

      return result;
    }),

  getDistrictWithAddress: authenticatedProcedure
    .input(z.string())
    .query(async ({ ctx, input }) => {
      const result = await ctx.db.transaction(async (tx) => {
        const repository = new DistrictsRepository(tx);
        return await repository.getDistrictWithAddress(input);
      });

      return result;
    }),

  createOnboarding: authenticatedProcedure
    .input(createDistrictOnboardingSchema)
    .mutation(async ({ ctx, input }) => {
      const { emails, district } = await ctx.db.transaction(async (tx) => {
        const service = new DistrictOnboardingService(tx);
        return await service.createDistrictOnboarding(input);
      });

      await inngest.send({
        name: Events.DISTRICT_ONBOARDING_COMPLETED,
        data: {
          encrypted: {
            userId: ctx.user.id,
            districtId: district.id,
            emails,
          },
        },
      });
    }),
});
