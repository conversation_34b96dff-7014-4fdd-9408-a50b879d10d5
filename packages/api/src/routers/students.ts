import { StudentsService } from '@lilypad/core/services/students';
import { StudentRepository } from '@lilypad/db/repository/students';
import type {
  GetStudentsParams,
  GetStudentsResult,
} from '@lilypad/db/repository/types/students';
import { z } from 'zod';

import { authenticatedProcedure, createTRPCRouter } from '../core/init';
import {
  bulkStudentSchema,
  getStudentsInputSchema,
  type StudentSaveResult,
} from '../schemas/students';

export const studentsRouter = createTRPCRouter({
  getStudents: authenticatedProcedure
    .input(getStudentsInputSchema)
    .query(async ({ ctx, input }): Promise<GetStudentsResult> => {
      return await ctx.db.transaction(async (tx) => {
        const repository = new StudentRepository(tx);
        return await repository.getStudents(input as GetStudentsParams);
      });
    }),

  getStudentById: authenticatedProcedure
    .input(z.string())
    .query(async ({ ctx, input }) => {
      return await ctx.db.transaction(async (tx) => {
        const repository = new StudentRepository(tx);
        return await repository.getStudentById(input);
      });
    }),

  getStudentByStudentIdNumber: authenticatedProcedure
    .input(z.string())
    .query(async ({ ctx, input }) => {
      return await ctx.db.transaction(async (tx) => {
        const repository = new StudentRepository(tx);
        return await repository.getStudentByStudentIdNumber(input);
      });
    }),

  getStudentProfileById: authenticatedProcedure
    .input(z.string())
    .query(async ({ ctx, input }) => {
      return await ctx.db.transaction(async (tx) => {
        const repository = new StudentRepository(tx);
        return await repository.getStudentProfileById(input);
      });
    }),

  bulkCreateStudents: authenticatedProcedure
    .input(bulkStudentSchema)
    .mutation(async ({ ctx, input }): Promise<StudentSaveResult[]> => {
      return await ctx.db.transaction(async (tx) => {
        const service = new StudentsService(tx);
        return await service.bulkCreateStudents(input, ctx.user);
      });
    }),
});
