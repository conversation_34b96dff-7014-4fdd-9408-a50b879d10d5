import { createCallerFactory, createTRPCRouter } from '../core/init';
import { casesRouter } from './cases';
import { districtsRouter } from './districts';
import { feedbackRouter } from './feedback';
import { languagesRouter } from './languages';
import { schoolsRouter } from './schools';
import { studentsRouter } from './students';
import { tasksRouter } from './tasks';

export const appRouter = createTRPCRouter({
  students: studentsRouter,
  cases: casesRouter,
  districts: districtsRouter,
  schools: schoolsRouter,
  feedback: feedbackRouter,
  languages: languagesRouter,
  tasks: tasksRouter,
});

// export type definition of API
export type AppRouter = typeof appRouter;

/**
 * Create a server-side caller for the tRPC API.
 * @example
 * const trpc = createCaller(createContext);
 * const res = await trpc.post.all();
 *       ^? Post[]
 */
export const createCaller = createCallerFactory(appRouter);
