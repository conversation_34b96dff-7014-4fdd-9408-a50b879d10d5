import { LanguagesRepository } from '@lilypad/db/repository/languages';
import { authenticatedProcedure, createTRPCRouter } from '../core/init';

export const languagesRouter = createTRPCRouter({
  getAllLanguages: authenticatedProcedure.query(async ({ ctx }) => {
    const result = await ctx.db.transaction(async (tx) => {
      const repository = new LanguagesRepository(tx);
      return await repository.getAllLanguages();
    });

    return result;
  }),
});
