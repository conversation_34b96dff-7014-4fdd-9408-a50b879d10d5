import { TasksRepository } from '@lilypad/db/repository/tasks';
import type {
  GetTasksParams,
  TaskWithRelations,
} from '@lilypad/db/repository/types/tasks';

import { authenticatedProcedure, createTRPCRouter } from '../core/init';
import {
  completeTaskInputSchema,
  getOverdueTasksInputSchema,
  getTaskByIdInputSchema,
  getTaskDependenciesInputSchema,
  getTaskHistoryInputSchema,
  getTasksInputSchema,
  getUrgentTasksInputSchema,
  reassignTaskInputSchema,
  rejectTaskInputSchema,
  updateTaskDueDateInputSchema,
  updateTaskPriorityInputSchema,
  updateTaskStatusInputSchema,
} from '../schemas/tasks';

export const tasksRouter = createTRPCRouter({
  // Query procedures
  getTasks: authenticatedProcedure
    .input(getTasksInputSchema)
    .query(async ({ ctx, input }) => {
      return await ctx.db.transaction(async (tx) => {
        const repository = new TasksRepository(tx);
        return await repository.getTasksByUserId(
          ctx.user.id,
          input as GetTasksParams
        );
      });
    }),

  getTaskById: authenticatedProcedure
    .input(getTaskByIdInputSchema)
    .query(async ({ ctx, input }): Promise<TaskWithRelations> => {
      return await ctx.db.transaction(async (tx) => {
        const repository = new TasksRepository(tx);
        return await repository.getTaskById(input);
      });
    }),

  getTaskHistory: authenticatedProcedure
    .input(getTaskHistoryInputSchema)
    .query(async ({ ctx, input }) => {
      return await ctx.db.transaction(async (tx) => {
        const repository = new TasksRepository(tx);
        return await repository.getTaskHistory(input);
      });
    }),

  getTaskDependencies: authenticatedProcedure
    .input(getTaskDependenciesInputSchema)
    .query(async ({ ctx, input }) => {
      return await ctx.db.transaction(async (tx) => {
        const repository = new TasksRepository(tx);
        return await repository.getTaskDependencies(input);
      });
    }),

  canStartTask: authenticatedProcedure
    .input(getTaskByIdInputSchema)
    .query(async ({ ctx, input }) => {
      return await ctx.db.transaction(async (tx) => {
        const repository = new TasksRepository(tx);
        return await repository.canStartTask(input);
      });
    }),

  getBlockingTasks: authenticatedProcedure
    .input(getTaskByIdInputSchema)
    .query(async ({ ctx, input }) => {
      return await ctx.db.transaction(async (tx) => {
        const repository = new TasksRepository(tx);
        return await repository.getBlockingTasks(input);
      });
    }),

  getUrgentTasks: authenticatedProcedure
    .input(getUrgentTasksInputSchema)
    .query(async ({ ctx, input }) => {
      return await ctx.db.transaction(async (tx) => {
        const repository = new TasksRepository(tx);
        return await repository.getUrgentTasks(
          input.assignedToName,
          input.limit
        );
      });
    }),

  getOverdueTasks: authenticatedProcedure
    .input(getOverdueTasksInputSchema)
    .query(async ({ ctx, input }) => {
      return await ctx.db.transaction(async (tx) => {
        const repository = new TasksRepository(tx);
        return await repository.getOverdueTasks(
          input.assignedToName,
          input.limit
        );
      });
    }),

  // Mutation procedures
  updateTaskStatus: authenticatedProcedure
    .input(updateTaskStatusInputSchema)
    .mutation(async ({ ctx, input }) => {
      return await ctx.db.transaction(async (tx) => {
        const repository = new TasksRepository(tx);
        await repository.updateTaskStatus(
          input.taskId,
          input.status,
          ctx.user.id,
          input.notes
        );
        return { success: true };
      });
    }),

  updateTaskPriority: authenticatedProcedure
    .input(updateTaskPriorityInputSchema)
    .mutation(async ({ ctx, input }) => {
      return await ctx.db.transaction(async (tx) => {
        const repository = new TasksRepository(tx);
        await repository.updateTaskPriority(
          input.taskId,
          input.priority,
          ctx.user.id,
          input.notes
        );
        return { success: true };
      });
    }),

  updateTaskDueDate: authenticatedProcedure
    .input(updateTaskDueDateInputSchema)
    .mutation(async ({ ctx, input }) => {
      return await ctx.db.transaction(async (tx) => {
        const repository = new TasksRepository(tx);
        await repository.updateTaskDueDate(
          input.taskId,
          input.dueDate,
          ctx.user.id,
          input.notes
        );
        return { success: true };
      });
    }),

  rejectTask: authenticatedProcedure
    .input(rejectTaskInputSchema)
    .mutation(async ({ ctx, input }) => {
      return await ctx.db.transaction(async (tx) => {
        const repository = new TasksRepository(tx);
        await repository.logTaskRejected(
          input.taskId,
          ctx.user.id,
          input.reason
        );
        return { success: true };
      });
    }),

  reassignTask: authenticatedProcedure
    .input(reassignTaskInputSchema)
    .mutation(async ({ ctx, input }) => {
      return await ctx.db.transaction(async (tx) => {
        const repository = new TasksRepository(tx);
        await repository.logTaskReassigned({
          taskId: input.taskId,
          reassignedTo: input.newAssigneeId,
          reassignedBy: ctx.user.id,
          notes: input.notes,
        });
        return { success: true };
      });
    }),

  completeTask: authenticatedProcedure
    .input(completeTaskInputSchema)
    .mutation(async ({ ctx, input }) => {
      return await ctx.db.transaction(async (tx) => {
        const repository = new TasksRepository(tx);
        await repository.logTaskCompleted(input.taskId, ctx.user.id);
        return { success: true };
      });
    }),
});
