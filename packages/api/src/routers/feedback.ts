import { FeedbackIssueTypeEnum } from '@lilypad/db/enums';
import { FeedbackRepository } from '@lilypad/db/repository/feedback';

import { authenticatedProcedure, createTRPCRouter } from '../core/init';
import {
  bugFeedbackInput,
  featureRequestInput,
  generalFeedbackInput,
} from '../schemas/feedback';

export const feedbackRouter = createTRPCRouter({
  submitBugFeedback: authenticatedProcedure
    .input(bugFeedbackInput)
    .mutation(async ({ ctx, input }) => {
      const { fileUrls, ...feedbackData } = input;

      const feedbackRepository = new FeedbackRepository(ctx.db);
      const feedback = await feedbackRepository.createFeedback(
        {
          ...feedbackData,
          userId: ctx.user.id,
        },
        fileUrls
      );

      return {
        success: true,
        feedbackId: feedback.id,
      };
    }),

  submitFeatureRequest: authenticatedProcedure
    .input(featureRequestInput)
    .mutation(async ({ ctx, input }) => {
      const { fileUrls, ...feedbackData } = input;

      const feedbackRepository = new FeedbackRepository(ctx.db);
      const feedback = await feedbackRepository.createFeedback(
        {
          ...feedbackData,
          userId: ctx.user.id,
          issueType: FeedbackIssueTypeEnum.OTHER,
        },
        fileUrls
      );

      return {
        success: true,
        feedbackId: feedback.id,
      };
    }),

  submitOtherFeedback: authenticatedProcedure
    .input(generalFeedbackInput)
    .mutation(async ({ ctx, input }) => {
      const { fileUrls, ...feedbackData } = input;

      const feedbackRepository = new FeedbackRepository(ctx.db);
      const feedback = await feedbackRepository.createFeedback(
        {
          ...feedbackData,
          userId: ctx.user.id,
          issueType: FeedbackIssueTypeEnum.OTHER,
        },
        fileUrls
      );

      return {
        success: true,
        feedbackId: feedback.id,
      };
    }),
});
