import { SchoolsRepository } from '@lilypad/db/repository/schools';
import { authenticatedProcedure, createTRPCRouter } from '../core/init';

export const schoolsRouter = createTRPCRouter({
  getSchools: authenticatedProcedure.query(async ({ ctx }) => {
    const result = await ctx.db.transaction(async (tx) => {
      const repository = new SchoolsRepository(tx);
      return await repository.getSchools();
    });

    return result;
  }),
});
