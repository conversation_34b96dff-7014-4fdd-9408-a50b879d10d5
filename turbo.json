{"$schema": "https://turborepo.com/schema.json", "ui": "tui", "globalDependencies": [".env"], "globalEnv": ["DATABASE_URL", "ADMIN_DATABASE_URL", "SUPABASE_SERVICE_ROLE_KEY", "NEXT_PUBLIC_SUPABASE_URL", "NEXT_PUBLIC_SUPABASE_ANON_KEY", "SUPABASE_RECOVERY_CODE_SECRET"], "tasks": {"dev": {"cache": false, "persistent": true}, "build": {"inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".next/**", "!.next/cache/**"]}, "start": {"cache": false, "persistent": true}, "clean": {"cache": false}, "//#clean": {"cache": false}, "format": {"dependsOn": ["^format"]}, "lint": {"dependsOn": ["^lint"]}, "typecheck": {"dependsOn": ["^typecheck"]}, "analyze": {"dependsOn": ["^analyze"]}, "seed": {"cache": false, "inputs": ["$TURBO_DEFAULT$", ".env"], "env": ["DATABASE_URL", "ADMIN_DATABASE_URL", "SUPABASE_SERVICE_ROLE_KEY", "NEXT_PUBLIC_SUPABASE_URL"]}}}