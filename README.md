# Lilypad

TBD

## 🏗️ Architecture

Lilypad is built using **Feature-Sliced Design (FSD)** methodology within a **Turborepo monorepo**, ensuring scalable, maintainable, and modular code organization.

### Layer Hierarchy
```
app/ → widgets/ → features/ → entities/ → shared/
```

**Import Rule**: Higher layers can import from lower layers, but never in reverse.

## 📦 Project Structure

### Applications
- **`apps/web/`** - Main Next.js application with FSD architecture
- **`apps/docs/`** - Documentation site

### Packages
- **`@lilypad/ui`** - Shared component library built on Shadcn/UI
- **`@lilypad/db`** - Database schemas and operations with Drizzle ORM
- **`@lilypad/supabase`** - Supabase client configuration and auth utilities
- **`@lilypad/analytics`** - Analytics tracking and reporting
- **`@lilypad/payments`** - Stripe payment processing
- **`@lilypad/email`** - Email templates and delivery service
- **`@lilypad/monitoring`** - Application performance monitoring
- **`@lilypad/jobs`** - Background job processing
- **`@lilypad/kv`** - Key-value store and rate limiting
- **`@lilypad/editor`** - Rich text editor components
- **`@lilypad/shared`** - Common utilities, types, and configurations
- **`@lilypad/scripts`** - Database seeding and utility scripts

### Tooling
- **`tooling/typescript/`** - Shared TypeScript configurations
- **`tooling/tailwindcss/`** - Shared Tailwind CSS configuration
- **`tooling/requirements/`** - Development environment requirements

## 🛠️ Tech Stack

### Frontend
- **Next.js 15** with App Router and React 19
- **TypeScript** for type safety
- **Tailwind CSS 4** for styling
- **Shadcn/UI** component library
- **React Hook Form** with Zod validation
- **Zustand** for state management

### Backend & Database  
- **Supabase** for backend services, authentication, and PostgreSQL hosting
- **Drizzle ORM** for type-safe database operations
- **Next.js API routes** for server-side logic
- **Server Actions** for form handling and mutations

### Infrastructure & Services
- **Render** deployment and hosting
- **Stripe** for payment processing  
- **Turborepo** for monorepo management
- **Biome** for code formatting and linting
- **Docker** for local development (Supabase)

## 🚦 Getting Started

### Prerequisites

- Node.js 20+ 
- pnpm 10.11.0+
- Docker (for local Supabase)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd lilypad
   ```

2. **Install dependencies**
   ```bash
   pnpm install
   ```

3. **Set up environment variables**
   ```bash
   # Configure environment variables
   cp .env.example .env.local
   cp apps/web/.env.example apps/web/.env.local
   cp packages/db/.env.example packages/db/.env
   cp packages/email/.env.example packages/email/.env
   cp packages/scripts/.env.example packages/scripts/.env
   ```

4. **Start local services**
   ```bash
   # Start Supabase locally
   pnpm supabase:start
   
   # Run database migrations and seed data
   pnpm seed
   ```

5. **Start development servers**
   ```bash
   # Start all applications
   pnpm dev
   
   # Or start specific applications
   pnpm dev:web    # Main web application
   pnpm dev:docs   # Documentation site
   ```

### Quick Setup (All-in-one)
```bash
pnpm assemble  # Starts Supabase, seeds data, and runs dev server
```

## 📋 Available Scripts

### Development
- `pnpm dev` - Start all applications in development mode
- `pnpm dev:web` - Start only the web application  
- `pnpm dev:docs` - Start only the documentation site
- `pnpm build` - Build all applications and packages
- `pnpm typecheck` - Run TypeScript type checking

### Database Operations
- `pnpm db:generate` - Generate Drizzle migrations
- `pnpm db:push` - Push schema changes to database
- `pnpm db:studio` - Open Drizzle Studio
- `pnpm seed` - Seed database with sample data

### Supabase Management
- `pnpm supabase:start` - Start local Supabase instance
- `pnpm supabase:stop` - Stop and cleanup Supabase
- `pnpm supabase:reset` - Reset local database
- `pnpm supabase:typegen` - Generate TypeScript types from database

### Code Quality
- `pnpm format` - Format code with Biome
- `pnpm lint` - Lint code and check package dependencies
- `pnpm clean` - Clean build artifacts and dependencies

### Utilities
- `pnpm update` - Update all dependencies recursively
- `pnpm analyze` - Analyze bundle size and performance
- `pnpm test` - Run test suites

## 🗄️ Database Schema

Lilypad uses a robust PostgreSQL schema managed through Drizzle ORM with the following core entities:

### Core Entities
- **Users** - Platform users with role-based permissions
- **Districts** - Educational districts and their configurations
- **Schools** - Schools within districts
- **Students** - Student information and profiles

### Data Management
- **Row Level Security (RLS)** policies for data protection
- **Generated columns** for computed fields (e.g., full names)
- **Foreign key constraints** for data integrity
- **Optimized indexes** for query performance

## 🔐 Authentication & Authorization

- **Supabase Auth** for user authentication
- **Row Level Security** policies for data access control
- **Role-based permissions** (SuperUser, Special Ed Director, School Admin, etc.)
- **Multi-tenant architecture** supporting multiple districts

## 🎨 UI Components

The UI package provides a comprehensive component library:

- **Base Components** - Buttons, inputs, dialogs, forms
- **Data Display** - Tables, charts, cards, badges  
- **Navigation** - Menus, tabs, breadcrumbs
- **Feedback** - Alerts, toasts, loading states
- **Advanced** - Data tables, command palette, rich text editor

All components are built with:
- **Accessibility** (ARIA compliant)
- **Theme support** (light/dark mode)
- **Responsive design** (mobile-first)
- **TypeScript** definitions

## 📊 Feature-Sliced Design Structure

The main web application follows FSD principles:

```
src/
├── app/          # Next.js App Router pages and layouts
├── widgets/      # Complex UI compositions
├── features/     # User scenarios and business logic
│   ├── auth/               # Authentication flows
│   ├── district-onboarding/ # District setup
│   ├── student-profile/    # Student management
│   └── student-sheet/      # Student data sheets
├── entities/     # Business entities and operations  
│   ├── districts/  # District-related operations
│   ├── schools/    # School-related operations
│   ├── students/   # Student-related operations
│   └── users/      # User-related operations
└── shared/       # Reusable utilities and components
```

## 🔧 Package Development

### Adding New Packages

1. Create package directory: `packages/new-package/`
2. Add package.json with `@lilypad/new-package` name
3. Update `pnpm-workspace.yaml`
4. Export functionality through `src/index.ts`

### Package Import Rules

```typescript
// ✅ Apps can import from any package
import { Button } from '@lilypad/ui/components'
import { db } from '@lilypad/db/client'

// ✅ Packages can import shared utilities  
import { logger } from '@lilypad/shared/logger'

// ❌ Avoid package-to-package dependencies
import { getUser } from '@lilypad/some-other-package' // Avoid
```

## 🚀 Deployment

### Production Deployment

1. **Environment Setup**
   - Configure production environment variables
   - Set up Supabase production project
   - Configure Stripe for production

2. **Database Migration**
   ```bash
   pnpm db:push  # Apply schema changes
   ```

3. **Build & Deploy**
   ```bash
   pnpm build    # Build all packages and applications
                 # Deploy to hosting platform
   ```

### Environment Variables

Key environment variables needed:

```bash
# Database
DATABASE_URL=
ADMIN_DATABASE_URL=

# Supabase
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=

# Additional service keys (analytics, payments, etc.)
```

## 📈 Performance

- **Bundle Analysis** - Built-in bundle analyzer
- **Core Web Vitals** monitoring
- **Database query optimization** with prepared statements
- **Image optimization** with Next.js Image component
- **Code splitting** for optimal loading

### Development Guidelines

- Follow **Feature-Sliced Design** architecture
- Use **TypeScript** for all code
- Follow **package import rules**
- Use **conventional commits**

## 🔗 Links & Resources

- [Feature-Sliced Design Documentation](https://feature-sliced.design/)
- [Next.js Documentation](https://nextjs.org/docs)
- [Supabase Documentation](https://supabase.com/docs)
- [Drizzle ORM Documentation](https://orm.drizzle.team/)
- [Turborepo Documentation](https://turbo.build/repo/docs)

---
