# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# Content Collections
.content-collections

# Dependencies
node_modules
.pnp
.pnp.js

# Testing
coverage

# Turbo
.turbo

# Vercel
.vercel

# Build Outputs
.next/
out/
build
dist

# env files (can opt-in for commiting if needed)
.env*
!.env.example

# TypeScript
*.tsbuildinfo

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Misc
.DS_Store
*.pem
.tasks/
user-permissions.csv