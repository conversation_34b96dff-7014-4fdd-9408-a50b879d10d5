{"editor.defaultFormatter": "biomejs.biome", "[javascript][typescript][javascriptreact][typescriptreact][json][jsonc][graphql][markdown]": {"editor.defaultFormatter": "biomejs.biome"}, "editor.codeActionsOnSave": {"quickfix.biome": "explicit", "source.organizeImports.biome": "explicit"}, "editor.formatOnSave": true, "editor.formatOnPaste": true, "typescript.enablePromptUseWorkspaceTsdk": true, "typescript.tsdk": "node_modules/typescript/lib", "typescript.preferences.autoImportFileExcludePatterns": ["next/router.d.ts", "next/dist/client/router.d.ts"], "terminal.integrated.localEchoStyle": "dim", "search.exclude": {"**/node_modules": true, "**/.next": true}, "typescript.preferences.autoImportSpecifierExcludeRegexes": ["@radix-ui", "next/dist", "next/document", "next/head", "next/router"]}