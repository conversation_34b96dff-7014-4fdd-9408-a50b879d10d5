<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html dir="ltr" lang="en">
  <head>
    <link
      rel="preload"
      as="image"
      href="http://localhost:3000/logo/logo-full.png" />
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type" />
    <meta name="x-apple-disable-message-reformatting" />
  </head>
  <body
    style="margin:auto;background-color:rgb(248,250,252);color:rgb(15,23,42);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;--primary:#1c5551;--primary-foreground:#ffffff;--secondary:#f1f5f9;--secondary-foreground:#334155;--accent:#0f766e;--accent-foreground:#ffffff;--muted:#f8fafc;--muted-foreground:#64748b;--border:#e2e8f0;--ring:#0f766e;font-family:-apple-system,BlinkMacSystemFont,&#x27;Segoe UI&#x27;,Roboto,Oxygen-Sans,Ubuntu,Cantarell,&#x27;Helvetica Neue&#x27;,sans-serif">
    <!--$-->
    <div class="hidden">Your one-time passcode: {{ .Token }}</div>
    <table
      align="center"
      width="100%"
      border="0"
      cellpadding="0"
      cellspacing="0"
      role="presentation"
      style="margin-left:auto;margin-right:auto;margin-top:3rem;margin-bottom:3rem;width:100%;max-width:580px;border-radius:0.75rem;border-width:1px;border-style:solid;border-color:rgb(226,232,240);background-color:rgb(255,255,255);padding:2rem;box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), 0 0 #0000">
      <tbody>
        <tr style="width:100%">
          <td>
            <table
              align="center"
              width="100%"
              border="0"
              cellpadding="0"
              cellspacing="0"
              role="presentation"
              style="padding-bottom:1.5rem;border-bottom-width:1px;border-style:solid;border-color:rgb(226,232,240)">
              <tbody>
                <tr>
                  <td>
                    <table
                      align="center"
                      width="100%"
                      border="0"
                      cellpadding="0"
                      cellspacing="0"
                      role="presentation">
                      <tbody style="width:100%">
                        <tr style="width:100%">
                          <td class="" data-id="__react-email-column">
                            <a
                              href="{{ .SiteURL }}"
                              style="color:#067df7;text-decoration-line:none"
                              target="_blank"
                              ><img
                                alt="Lilypad"
                                height="36"
                                src="http://localhost:3000/logo/logo-full.png"
                                style="display:block;outline:none;border:none;text-decoration:none"
                                width="140"
                            /></a>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </td>
                </tr>
              </tbody>
            </table>
            <p
              style="font-size:1.5rem;line-height:2rem;font-weight:700;color:rgb(3,7,18);margin-bottom:1.5rem;margin-top:16px">
              Confirm Your Email
            </p>
            <p
              class="undefined"
              style="color:rgb(51,65,85);font-size:1rem;line-height:1.625;margin-bottom:1rem;margin-top:16px">
              Hi<!-- -->
              <a
                href="mailto:{{ .Email }}"
                class="hover:text-[var(--accent)]"
                style="color:var(--primary);text-decoration-line:underline;text-underline-offset:2px;font-weight:500;transition-property:color, background-color, border-color, text-decoration-color, fill, stroke;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms"
                target="_blank"
                >{{ .Email }}</a
              >,
            </p>
            <p
              class="undefined"
              style="color:rgb(51,65,85);font-size:1rem;line-height:1.625;margin-bottom:1rem;margin-top:16px">
              Welcome to
              <!-- -->Lilypad<!-- -->! To complete your account setup, please
              use the confirmation code below:
            </p>
            <table
              align="center"
              width="100%"
              border="0"
              cellpadding="0"
              cellspacing="0"
              role="presentation"
              style="margin-top:1rem;margin-bottom:1rem;text-align:center">
              <tbody>
                <tr>
                  <td>
                    <div
                      style="display:inline-block;border-radius:0.5rem;border-width:1px;border-style:solid;border-color:rgb(153,246,228);background-color:rgb(240,253,250);padding-left:1rem;padding-right:1rem">
                      <p
                        style="font-size:0.75rem;line-height:1rem;font-weight:500;color:rgb(15,118,110);margin-bottom:0.5rem;text-transform:uppercase;letter-spacing:0.025em;margin-top:16px">
                        Your Confirmation Code
                      </p>
                      <p
                        style="font-size:1.875rem;line-height:2.25rem;font-weight:700;color:rgb(17,94,89);letter-spacing:0.05em;margin-top:16px;margin-bottom:16px">
                        {{ .Token }}
                      </p>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
            <table
              align="center"
              width="100%"
              border="0"
              cellpadding="0"
              cellspacing="0"
              role="presentation"
              style="margin-bottom:1rem;border-radius:0.5rem;border-width:1px;border-style:solid;border-color:rgb(253,230,138);background-color:rgb(255,251,235);padding-left:1rem;padding-right:1rem">
              <tbody>
                <tr>
                  <td>
                    <p
                      style="font-size:0.875rem;line-height:1.25rem;font-weight:600;color:rgb(146,64,14);margin-bottom:0.5rem;margin-top:16px">
                      Time Sensitive
                    </p>
                    <p
                      style="font-size:0.875rem;line-height:1.25rem;color:rgb(180,83,9);margin-top:0.5rem;margin-bottom:16px">
                      This code will only be valid for the next 5 minutes. If
                      the code doesn&#x27;t work, you can use the button below
                      instead.
                    </p>
                  </td>
                </tr>
              </tbody>
            </table>
            <table
              align="center"
              width="100%"
              border="0"
              cellpadding="0"
              cellspacing="0"
              role="presentation"
              style="text-align:center">
              <tbody>
                <tr>
                  <td>
                    <a
                      href="{{ .SiteURL }}/auth/callback?tokenHash={{ .TokenHash }}&amp;type=signup&amp;next={{ .RedirectTo }}"
                      rel="noopener noreferrer"
                      class="disabled:pointer-events-none focus-visible:ring-2 focus-visible:ring-[var(--ring)] focus-visible:ring-offset-2 hover:bg-[var(--accent)] hover:border-[var(--accent)] hover:shadow-md"
                      style="display:inline-flex;align-items:center;justify-content:center;gap:0.5rem;white-space:nowrap;border-radius:0.5rem;font-size:1rem;line-height:1.5rem;font-weight:600;transition-property:all;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms;cursor:pointer;flex-shrink:0;outline:2px solid transparent;outline-offset:2px;background-color:var(--primary);color:var(--primary-foreground);border-width:2px;border-style:solid;border-color:var(--primary);padding-left:1rem;padding-right:1rem;padding-top:0.5rem;padding-bottom:0.5rem;box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), 0 0 #0000;text-decoration-line:none"
                      target="_blank"
                      >Confirm Email Address</a
                    >
                  </td>
                </tr>
              </tbody>
            </table>
            <p
              class="undefined"
              style="color:rgb(51,65,85);font-size:1rem;line-height:1.625;margin-bottom:1rem;margin-top:16px">
              Thank you for joining
              <!-- -->Lilypad<!-- -->! We&#x27;re excited to have you as part of
              our community.
            </p>
            <p
              class="undefined"
              style="color:rgb(51,65,85);font-size:1rem;line-height:1.625;margin-bottom:1rem;margin-top:16px">
              Best regards,<br />The
              <!-- -->Lilypad<!-- -->
              Team
            </p>
            <table
              align="center"
              width="100%"
              border="0"
              cellpadding="0"
              cellspacing="0"
              role="presentation"
              style="margin-top:1.5rem;border-top-width:1px;border-style:solid;border-color:rgb(226,232,240)">
              <tbody>
                <tr>
                  <td>
                    <table
                      align="center"
                      width="100%"
                      border="0"
                      cellpadding="0"
                      cellspacing="0"
                      role="presentation">
                      <tbody style="width:100%">
                        <tr style="width:100%">
                          <td
                            data-id="__react-email-column"
                            style="text-align:center">
                            <p
                              style="font-size:0.875rem;line-height:1.625;color:rgb(100,116,139);margin-top:1.5rem;margin-bottom:1rem">
                              If you didn&#x27;t request this, just ignore and
                              delete this message. To keep your account secure,
                              please don&#x27;t forward this email to anyone.
                            </p>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                    <table
                      align="center"
                      width="100%"
                      border="0"
                      cellpadding="0"
                      cellspacing="0"
                      role="presentation">
                      <tbody style="width:100%">
                        <tr style="width:100%">
                          <td
                            data-id="__react-email-column"
                            style="text-align:center;margin-bottom:0px">
                            <a
                              href="{{ .SiteURL }}"
                              style="display:inline-block;color:#067df7;text-decoration-line:none"
                              target="_blank"
                              ><img
                                alt="Lilypad"
                                height="30"
                                src="http://localhost:3000/logo/logo-full.png"
                                style="margin-left:auto;margin-right:auto;margin-top:0.5rem;display:block;outline:none;border:none;text-decoration:none"
                                width="120"
                            /></a>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                    <table
                      align="center"
                      width="100%"
                      border="0"
                      cellpadding="0"
                      cellspacing="0"
                      role="presentation">
                      <tbody style="width:100%">
                        <tr style="width:100%">
                          <td
                            data-id="__react-email-column"
                            style="text-align:center">
                            <p
                              style="font-size:0.875rem;line-height:1.25rem;color:rgb(100,116,139);margin-top:0.5rem;margin-bottom:0.5rem">
                              Copyright © Lilypad
                            </p>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                    <table
                      align="center"
                      width="100%"
                      border="0"
                      cellpadding="0"
                      cellspacing="0"
                      role="presentation">
                      <tbody style="width:100%">
                        <tr style="width:100%">
                          <td
                            data-id="__react-email-column"
                            style="text-align:center">
                            <p
                              style="font-size:0.75rem;line-height:1rem;color:rgb(100,116,139);margin-top:16px;margin-bottom:16px">
                              <a
                                href="http://localhost:3001/privacy-policy"
                                style="color:rgb(100,116,139);text-decoration-line:none"
                                target="_blank"
                                >Privacy Policy</a
                              >
                              •
                              <a
                                href="http://localhost:3001/terms-of-use"
                                style="color:rgb(100,116,139);text-decoration-line:none"
                                target="_blank"
                                >Terms of Service</a
                              >
                              •
                              <a
                                href="http://localhost:3001/contact"
                                style="color:rgb(100,116,139);text-decoration-line:none"
                                target="_blank"
                                >Contact Us</a
                              >
                            </p>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </td>
                </tr>
              </tbody>
            </table>
          </td>
        </tr>
      </tbody>
    </table>
    <!--7--><!--/$-->
  </body>
</html>
