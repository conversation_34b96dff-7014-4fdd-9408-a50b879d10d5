-- Auth User Role
create user protected with login password 'postgres';

-- Auth User Policies
grant anon to protected;
grant authenticated to protected;

insert into public.roles (name) values 
('SUPER_USER'),
('SPECIAL_ED_DIRECTOR'),
('SCHOOL_COORDINATOR'),
('SCHOOL_ADMIN'),
('PROCTOR'),
('CASE_MANAGER'),
('CLINICAL_DIRECTOR'),
('PSYCHOLOGIST'),
('ASSISTANT');

insert into public.permissions (name) values  
('users:create'),
('users:read'),
('users:update'),
('users:delete'),
('districts:create'),
('districts:read'),
('districts:update'),
('districts:delete'),
('district_prefs:update'),
('schools:create'),
('schools:read'),
('schools:update'),
('schools:delete'),
('students:create'),
('students:read'),
('students:update'),
('students:delete'),
('parents:create'),
('parents:read'),
('parents:update'),
('parents:delete'),
('cases:create'),
('cases:read'),
('cases:update'),
('cases:delete'),
('case_details:create'),
('case_details:read'),
('case_details:update'),
('case_details:delete'),
('docs:create'),
('docs:read'),
('docs:update'),
('docs:delete'),
('reports:create'),
('reports:read'),
('reports:update'),
('reports:delete'),
('plans:create'),
('plans:read'),
('plans:update'),
('plans:delete'),
('sessions:create'),
('sessions:read'),
('sessions:update'),
('sessions:delete'),
('test_admin:create'),
('test_admin:read'),
('test_admin:update'),
('test_admin:delete'),
('tasks:create'),
('tasks:read'),
('tasks:update'),
('tasks:delete'),
('task_history:read'),
('coordinator:assign'),
('psychologists:assign'),
('assistants:assign');

insert into public.role_permissions (role_id, permission_id) 
select r.id, p.id 
from public.roles r, public.permissions p 
where r.name = 'SUPER_USER';

insert into public.role_permissions (role_id, permission_id) 
select r.id, p.id 
from public.roles r, public.permissions p 
where r.name = 'SPECIAL_ED_DIRECTOR';

insert into public.role_permissions (role_id, permission_id) 
select r.id, p.id 
from public.roles r, public.permissions p 
where r.name = 'CLINICAL_DIRECTOR'
and p.name in (
  'students:read',
  'students:update',
  'schools:read',
  'docs:read',
  'reports:create',
  'reports:read',
  'reports:update',
  'cases:create',
  'cases:read',
  'cases:update',
  'sessions:create',
  'sessions:read',
  'sessions:update',
  'plans:create',
  'plans:read',
  'plans:update',
  'psychologists:assign',
  'assistants:assign'
);

insert into public.role_permissions (role_id, permission_id) 
select r.id, p.id 
from public.roles r, public.permissions p 
where r.name = 'SCHOOL_COORDINATOR'
and p.name in (
  'students:create',
  'students:read',
  'students:update',
  'schools:read',
  'schools:update',
  'docs:create',
  'docs:read',
  'docs:update',
  'reports:read',
  'coordinator:assign',
  'cases:create',
  'cases:read',
  'cases:update',
  'sessions:read',
  'plans:read',
  'sessions:create',
  'sessions:read',
  'sessions:update'
);

insert into public.role_permissions (role_id, permission_id) 
select r.id, p.id 
from public.roles r, public.permissions p 
where r.name = 'SCHOOL_ADMIN'
and p.name in (
  'students:read',
  'schools:read',
  'docs:read',
  'reports:read',
  'users:read',
  'district_prefs:update',
  'cases:read'
);

insert into public.role_permissions (role_id, permission_id) 
select r.id, p.id 
from public.roles r, public.permissions p 
where r.name = 'CASE_MANAGER'
and p.name in (
  'students:read',
  'students:update',
  'docs:read',
  'docs:create',
  'reports:read',
  'cases:create',
  'cases:read',
  'cases:update',
  'sessions:read',
  'plans:read',
  'plans:update',
  'sessions:create',
  'sessions:read',
  'sessions:update'
);

insert into public.role_permissions (role_id, permission_id) 
select r.id, p.id 
from public.roles r, public.permissions p 
where r.name = 'PSYCHOLOGIST'
and p.name in (
  'students:read',
  'docs:read',
  'docs:create',
  'reports:create',
  'reports:read',
  'reports:update',
  'cases:read',
  'sessions:create',
  'sessions:read',
  'sessions:update',
  'plans:read',
  'plans:update'
);

insert into public.role_permissions (role_id, permission_id) 
select r.id, p.id 
from public.roles r, public.permissions p 
where r.name = 'PROCTOR'
and p.name in (
  'students:read',
  'sessions:read',
  'sessions:create',
  'sessions:read',
  'sessions:update'
);

insert into public.role_permissions (role_id, permission_id) 
select r.id, p.id 
from public.roles r, public.permissions p 
where r.name = 'ASSISTANT'
and p.name in (
  'students:read',
  'docs:read',
  'reports:read',
  'cases:read',
  'sessions:read',
  'plans:read'
);

insert into public.languages (name, code, emoji) values
('Afrikaans', 'af', '🇿🇦'),
('Albanian', 'sq', '🇦🇱'),
('Amharic', 'am', '🇪🇹'),
('Arabic', 'ar', '🇸🇦'),
('Armenian', 'hy', '🇦🇲'),
('Azerbaijani', 'az', '🇦🇿'),
('Basque', 'eu', '🏴'),
('Belarusian', 'be', '🇧🇾'),
('Bengali', 'bn', '🇧🇩'),
('Bosnian', 'bs', '🇧🇦'),
('Bulgarian', 'bg', '🇧🇬'),
('Catalan', 'ca', '🏴'),
('Cebuano', 'ceb', '🇵🇭'),
('Chinese (Simplified)', 'zh-CN', '🇨🇳'),
('Chinese (Traditional)', 'zh-TW', '🇹🇼'),
('Corsican', 'co', '🇫🇷'),
('Croatian', 'hr', '🇭🇷'),
('Czech', 'cs', '🇨🇿'),
('Danish', 'da', '🇩🇰'),
('Dutch', 'nl', '🇳🇱'),
('English', 'en', '🇺🇸'),
('Esperanto', 'eo', '🌍'),
('Estonian', 'et', '🇪🇪'),
('Finnish', 'fi', '🇫🇮'),
('French', 'fr', '🇫🇷'),
('Frisian', 'fy', '🇳🇱'),
('Galician', 'gl', '🇪🇸'),
('Georgian', 'ka', '🇬🇪'),
('German', 'de', '🇩🇪'),
('Greek', 'el', '🇬🇷'),
('Gujarati', 'gu', '🇮🇳'),
('Haitian Creole', 'ht', '🇭🇹'),
('Hausa', 'ha', '🇳🇬'),
('Hawaiian', 'haw', '🇺🇸'),
('Hebrew', 'he', '🇮🇱'),
('Hindi', 'hi', '🇮🇳'),
('Hmong', 'hmn', '🇨🇳'),
('Hungarian', 'hu', '🇭🇺'),
('Icelandic', 'is', '🇮🇸'),
('Igbo', 'ig', '🇳🇬'),
('Indonesian', 'id', '🇮🇩'),
('Irish', 'ga', '🇮🇪'),
('Italian', 'it', '🇮🇹'),
('Japanese', 'ja', '🇯🇵'),
('Javanese', 'jv', '🇮🇩'),
('Kannada', 'kn', '🇮🇳'),
('Kazakh', 'kk', '🇰🇿'),
('Khmer', 'km', '🇰🇭'),
('Kinyarwanda', 'rw', '🇷🇼'),
('Korean', 'ko', '🇰🇷'),
('Kurdish', 'ku', '🇮🇶'),
('Kyrgyz', 'ky', '🇰🇬'),
('Lao', 'lo', '🇱🇦'),
('Latin', 'la', '🇻🇦'),
('Latvian', 'lv', '🇱🇻'),
('Lithuanian', 'lt', '🇱🇹'),
('Luxembourgish', 'lb', '🇱🇺'),
('Macedonian', 'mk', '🇲🇰'),
('Malagasy', 'mg', '🇲🇬'),
('Malay', 'ms', '🇲🇾'),
('Malayalam', 'ml', '🇮🇳'),
('Maltese', 'mt', '🇲🇹'),
('Maori', 'mi', '🇳🇿'),
('Marathi', 'mr', '🇮🇳'),
('Mongolian', 'mn', '🇲🇳'),
('Myanmar (Burmese)', 'my', '🇲🇲'),
('Nepali', 'ne', '🇳🇵'),
('Norwegian', 'no', '🇳🇴'),
('Nyanja (Chichewa)', 'ny', '🇲🇼'),
('Odia (Oriya)', 'or', '🇮🇳'),
('Pashto', 'ps', '🇦🇫'),
('Persian', 'fa', '🇮🇷'),
('Polish', 'pl', '🇵🇱'),
('Portuguese', 'pt', '🇵🇹'),
('Punjabi', 'pa', '🇮🇳'),
('Romanian', 'ro', '🇷🇴'),
('Russian', 'ru', '🇷🇺'),
('Samoan', 'sm', '🇼🇸'),
('Scots Gaelic', 'gd', '🏴󠁧󠁢󠁳󠁣󠁴󠁿'),
('Serbian', 'sr', '🇷🇸'),
('Sesotho', 'st', '🇱🇸'),
('Shona', 'sn', '🇿🇼'),
('Sindhi', 'sd', '🇵🇰'),
('Sinhala (Sinhalese)', 'si', '🇱🇰'),
('Slovak', 'sk', '🇸🇰'),
('Slovenian', 'sl', '🇸🇮'),
('Somali', 'so', '🇸🇴'),
('Spanish', 'es', '🇪🇸'),
('Sundanese', 'su', '🇮🇩'),
('Swahili', 'sw', '🇹🇿'),
('Swedish', 'sv', '🇸🇪'),
('Tagalog (Filipino)', 'tl', '🇵🇭'),
('Tajik', 'tg', '🇹🇯'),
('Tamil', 'ta', '🇮🇳'),
('Tatar', 'tt', '🇷🇺'),
('Telugu', 'te', '🇮🇳'),
('Thai', 'th', '🇹🇭'),
('Turkish', 'tr', '🇹🇷'),
('Turkmen', 'tk', '🇹🇲'),
('Ukrainian', 'uk', '🇺🇦'),
('Urdu', 'ur', '🇵🇰'),
('Uyghur', 'ug', '🇨🇳'),
('Uzbek', 'uz', '🇺🇿'),
('Vietnamese', 'vi', '🇻🇳'),
('Welsh', 'cy', '🏴󠁧󠁢󠁷󠁬󠁳󠁿'),
('Xhosa', 'xh', '🇿🇦'),
('Yiddish', 'yi', '🇮🇱'),
('Yoruba', 'yo', '🇳🇬'),
('Zulu', 'zu', '🇿🇦');