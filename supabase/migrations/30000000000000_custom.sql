/*
 * -----------------------------------------------------------------------------
 * SECTION: Custom Column Level Permissions
 * -----------------------------------------------------------------------------
 */

revoke update (is_onboarded) on table public.users from authenticated;

/*
 * -----------------------------------------------------------------------------
 * SECTION: Custom Functions
 * -----------------------------------------------------------------------------
 */

create or replace function create_user_record() 
  returns trigger language plpgsql security definer as $$ 
  begin
    if new.raw_app_meta_data->>'provider' = 'google' then
      insert into public.user (id, image, name, email)
      values (
        new.id, 
        coalesce(new.raw_user_meta_data->>'avatar_url', null), 
        coalesce(new.raw_user_meta_data->>'full_name', ''), 
        new.email
      );
    end if;
    return new;
  end;
$$;

create trigger insert_database_user after insert on auth.users 
for each row execute function create_user_record();

alter table public.student_enrollments alter column district_id set not null;

create function set_enrollment_district() returns trigger
language plpgsql security definer as $$
begin
  select school.district_id
    into new.district_id
    from public.schools school
  where school.id = new.school_id;

  return new;
end $$;

create trigger trg_set_enrollment_district
before insert or update of school_id
on public.student_enrollments
for each row
execute function set_enrollment_district();


/*
 * -----------------------------------------------------------------------------
 * SECTION: Storage Buckets
 * -----------------------------------------------------------------------------
 */

insert into storage.buckets (id, name, public) values ('logos', 'logos', true);

insert into storage.buckets (id, name, public) values ('avatars', 'avatars', true);

insert into storage.buckets (id, name, public) values ('feedback', 'feedback', true);

insert into storage.buckets (id, name, public) values ('documents', 'documents', false);

/*
 * -----------------------------------------------------------------------------
 * SECTION: Buckets RLS Policies 
 * -----------------------------------------------------------------------------
 */

-- Storage Logo Bucket Policies
create policy "Logos can be read by any user" on storage.objects 
for select using (bucket_id = 'logos');

create policy "Logos can be inserted only by the authenticated users" on storage.objects 
for insert to authenticated with check (bucket_id = 'logos' and auth.uid() is not null);

create policy "Logos can be updated only by the permitted users" on storage.objects 
for update to authenticated using (bucket_id = 'logos' and owner_id = auth.uid()::text);

create policy "Logos can be deleted only by the permitted users" on storage.objects 
for delete to authenticated using (bucket_id = 'logos' and owner_id = auth.uid()::text);

-- Storage Avatar Bucket Policies
create policy "Avatars can be read by any user" on storage.objects 
for select using (bucket_id = 'avatars');

create policy "Avatars can be inserted only by the authenticated users" on storage.objects 
for insert to authenticated with check (bucket_id = 'avatars' and auth.uid() is not null);

create policy "Avatars can be updated only by the owner" on storage.objects 
for update to authenticated using (bucket_id = 'avatars' and owner_id = auth.uid()::text);

create policy "Avatars can be deleted only by the owner" on storage.objects 
for delete to authenticated using (bucket_id = 'avatars' and owner_id = auth.uid()::text);

-- Storage Feedback Bucket Policies
create policy "Allow public read access to files" on storage.objects
for select using (bucket_id = 'feedback');

create policy "Feedback file can be inserted by the user that owns the file" on storage.objects
for insert with check (bucket_id = 'feedback' and (split_part(storage.filename(name), '_', 1)::uuid) = auth.uid ());

create policy "Feedback file can be deleted by the user that owns the file" on storage.objects
for delete to authenticated using (bucket_id = 'feedback' and (split_part(storage.filename(name), '_', 1)::uuid) = auth.uid ());

-- Storage Documents Bucket Policies
create policy "Documents can be read by any authenticated user" on storage.objects 
for select to authenticated using (bucket_id = 'documents');

create policy "Documents can be inserted by any authenticated user" on storage.objects 
for insert to authenticated with check (bucket_id = 'documents');

create policy "Documents can be updated by any authenticated user" on storage.objects 
for update to authenticated using (bucket_id = 'documents');

create policy "Documents can be deleted by any authenticated user" on storage.objects 
for delete to authenticated using (bucket_id = 'documents');

/*
 * -----------------------------------------------------------------------------
 * SECTION: Enable Realtime
 * -----------------------------------------------------------------------------
 */
alter publication supabase_realtime add table public.notifications;

/*
 * -----------------------------------------------------------------------------
 * SECTION: Cron Jobs 
 * -----------------------------------------------------------------------------
 */
create extension if not exists pg_cron;

select cron.schedule('delete-expired-notifications', '0 0 * * 0', $$
  delete from public.notifications
  where expires_at < current_timestamp
    or (is_archived = true and archived_at < (current_timestamp - interval '30 days'));
$$);

