create extension if not exists "pg_cron";
create extension if not exists "vector";

create or replace function uuid_generate_v7()
returns uuid
language sql
volatile
as $$
  -- use random v4 uuid as starting point (which has the same variant we need)
  -- then overlay timestamp
  -- then set version 7 by flipping the 2 and 1 bit in the version 4 string
select encode(
    set_bit(
      set_bit(
        overlay(uuid_send(gen_random_uuid())
                placing substring(int8send(floor(extract(epoch from clock_timestamp()) * 1000)::bigint) from 3)
                from 1 for 6
        ),
        52, 1
      ),
      53, 1
    ),
    'hex')::uuid;
$$;

 -- An immutable function for name concatenation
create or replace function public.concat_names(first_name varchar, middle_name varchar, last_name varchar)
returns varchar
immutable
language sql
as $$
  select btrim(concat_ws(' ', first_name, middle_name, last_name));
$$;